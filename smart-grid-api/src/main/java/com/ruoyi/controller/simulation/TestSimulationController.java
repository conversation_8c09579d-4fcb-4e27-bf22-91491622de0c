package com.ruoyi.controller.simulation;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.calc.CalcParams;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.text.TrendTextCalcServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 方案
 */
@RestController
@RequestMapping("/testSimulation")
@SaIgnore
public class TestSimulationController {

    @Autowired
    IPlanTestService iPlanTestService;


    @Autowired
    TrendTextCalcServiceImpl textTrendCalcService;

    /**
     * 用于测试供电范围
     */
    @GetMapping("/findPowerRangeByFeeder")
    public R<?> loadFlowCalculation( @RequestParam String feederId, @RequestParam(required = false) Integer type) throws Exception {

        // 设备类型 208 配变,209 负荷
        R<?> dkx = textTrendCalcService.findPowerRangeByFeeder(ZnapUtils.getZnapPsrId(feederId, "dkx"), type == null ? new ArrayList<>(): Arrays.asList(type));
        return R.ok(dkx);
    }

    /**
     * 用于测试潮流计算
     */
    @GetMapping("/calc")
    public R<?> calc( @RequestParam String feederId) throws Exception {
        CalcParams calcParams = new CalcParams();
        calcParams.setFeeder_id(ZnapUtils.getZnapPsrId(feederId, "dkx"));
        R<?> calc = textTrendCalcService.calc(calcParams);
        return R.ok(calc);
    }
}
