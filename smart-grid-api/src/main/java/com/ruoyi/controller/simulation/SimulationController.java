package com.ruoyi.controller.simulation;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.simulation.*;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 仿真数据查询控制层
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/simulation")
@SaIgnore
public class SimulationController {

    @Autowired
    ISimulationService iSimulationService;

    /**
     * 根据仿真主表id查询——配网开关时序潮流计算结果
     */
    @GetMapping("/selectSimRetPfDmsBreaker/{retId}")
    public R<List<SimRetPfDmsBreaker>> selectSimRetPfDmsBreaker(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfDmsBreaker(retId, null));
    }

    /**
     * 根据仿真主表id查询——主网出线开关时序潮流计算结果
     */
    @GetMapping("/selectSimRetPfEmsBreaker/{retId}")
    public R<List<SimRetPfEmsBreaker>> selectSimRetPfEmsBreaker(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfEmsBreaker(retId, null));
    }

    /**
     * 根据仿真主表id查询——配网刀闸时序潮流计算结果
     */
    @GetMapping("/selectSimRetPfDmsDisconnector/{retId}")
    public R<List<SimRetPfDmsDisconnector>> selectSimRetPfDmsDisconnector(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfDmsDisconnector(retId, null));
    }

    /**
     * 根据仿真主表id查询——馈线段潮流计算结果
     */
    @GetMapping("/selectSimRetPfSegment/{retId}")
    public R<List<SimRetPfDmsSegment>> selectSimRetPfSegment(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfSegment(retId, null));
    }

    /**
     * 根据仿真主表id查询——母线潮流计算结果
     */
    @GetMapping("/selectSimRetPfDmsBusbar/{retId}")
    public R<List<SimRetPfDmsBusbar>> selectSimRetPfDmsBusbar(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfDmsBusbar(retId, null));
    }

    /**
     * 根据仿真主表id查询——配变潮流计算结果
     */
    @GetMapping("/selectSimRetPfDmsDt/{retId}")
    public R<List<SimRetPfDmsDt>> selectSimRetPfDmsDt(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfDmsDt(retId, null));
    }

    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    @GetMapping("/selectSimRetPfDmsLoad/{retId}")
    public R<List<SimRetPfDmsLoad>> selectSimRetPfDmsLoad(@PathVariable Long retId) {
        return R.ok(iSimulationService.selectSimRetPfDmsLoad(retId, null));
    }


    /**
     * 根据msgId查询——负荷潮流计算结果
     */
    @GetMapping("/selectMsgBySim/{msgId}")
    public R<SimMsg> selectMsgBySim(@PathVariable String msgId) {
        SimMsg simMsg = iSimulationService.selectMsgBySim(msgId);
        if (simMsg != null) {
            return R.ok(iSimulationService.selectMsgBySim(msgId));
        }
        return R.fail("该msgId未查到相关仿真数据");
    }
}
