package com.ruoyi.controller.cost;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.cost.Cost;
import com.ruoyi.entity.cost.CostType;
import com.ruoyi.service.cost.ICostService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 查询不同类型的造价
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cost")
@SaIgnore
public class CostController {
    @Autowired
    ICostService costService;


    /**
     * 查询造价总和
     * @param costList
     * @return
     */
    @GetMapping("/selectCost")
    public R<Cost> selectCost(@RequestBody List<CostType> costList) {
        return R.ok(costService.selectCost(costList));
    }



}
