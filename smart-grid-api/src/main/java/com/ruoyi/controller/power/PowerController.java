package com.ruoyi.controller.power;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceFeederCable;
import com.ruoyi.entity.device.DeviceFeederJk;
import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.bo.DeviceFeederTransformerVolBo;
import com.ruoyi.entity.power.bo.LoadBo;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.service.power.IPowerService;
import com.ruoyi.trans.core.TranslateResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * 电力资源信息
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/power")
@SaIgnore
public class PowerController extends BaseController {

    @Autowired
    IPowerService powerService;


    /**
     * 网格资源——查询电力线路资源信息，
     */
    @GetMapping("/selectGridPower/{code}")
    public R<PowerGridVo> selectCost(@NotNull(message = "网格编码不能为空") @PathVariable String code) {
        PowerGridVo powerGridVo = powerService.selectPower(code);
        return R.ok(powerGridVo);
    }

    /**
     * 网格资源——查询网格下的配变专变信息
     */
    @TranslateResponse(extractPropertiesToTile = true)
    @PostMapping("/selectDeviceFeederTransformerVol")
    public TableDataInfo<PBEntity> selectDeviceFeederTransformerVol(@RequestBody DeviceFeederTransformerVolBo bo) {
        return powerService.selectDeviceFeederTransformerVol(bo);
    }

    /**
     * 网格资源——查询网格下大馈线详情
     */
    @GetMapping("/selectDKX/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceFeeder> selectDKX(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                    @NotNull(message = "网格编码不能为空") @PathVariable Integer pageNum,
                                                    @NotNull(message = "网格编码不能为空") @PathVariable Integer pageSize) {
        return powerService.selectDKX(code,pageNum,pageSize);
    }
    /**
     * 网格资源——查询网格下架空线详情
     */
    @GetMapping("/selectJKX/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceFeederJk> selectJKX(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                   @NotNull(message = "网格编码不能为空") @PathVariable Integer pageNum,
                                                   @NotNull(message = "网格编码不能为空") @PathVariable Integer pageSize) {
        return powerService.selectJKX(code,pageNum,pageSize);
    }

    /**
     * 网格资源——查询网格下电缆线详情
     */
    @GetMapping("/selectDLX/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceFeederCable> selectDLX(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                      @NotNull(message = "网格编码不能为空") @PathVariable Integer pageNum,
                                                      @NotNull(message = "网格编码不能为空") @PathVariable Integer pageSize) {
        return powerService.selectDLX(code,pageNum,pageSize);
    }

    /**
     * 网格资源——查询变电站的统计信息，
     */
    @GetMapping("/selectGridSubstation/{code}/{strTime}/{endTime}")
    public R<SubstationVo> selectGridSubstation(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                @NotNull(message = "起始时间不能为空") @PathVariable String strTime,
                                                @NotNull(message = "结束时间不能为空") @PathVariable String endTime) throws IOException, ParseException {


        return R.ok(powerService.selectSubstation(code, strTime, endTime));


    }

    /**
     * 网格资源——查询网格下的所有变电站下拉列表
     */
    @GetMapping("/pullDownMenuSubstation/{code}")
    public R<List<PullDownMenuStringSon>> pullDownMenuSubstation(@NotNull(message = "网格编码不能为空") @PathVariable String code) {
        return R.ok(powerService.pullDownMenuSubstation(code));
    }

    /**
     * 网格资源——查询网格下的所有变电站坐标
     */
    @GetMapping("/substationGeoPositon/{code}")
    public R<List<PullDownMenuStringSon>> substationGeoPositon(@NotNull(message = "网格编码不能为空") @PathVariable String code) {
        return R.ok(powerService.substationGeoPositon(code));
    }


    /**
     * 网格资源——查询网格下的所有变电站以及主变
     */
    @GetMapping("/subAndTransformer/{code}/{pageNum}/{pageSize}")
    public R<Page<SubAndTransformerVo>> subAndTransformer(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                          @NotNull(message = "页码不能为空") @PathVariable Integer pageNum,
                                                          @NotNull(message = "页数不能为空") @PathVariable Integer pageSize) {
        return R.ok(powerService.subAndTransformer(code, pageNum, pageSize));
    }

    /**
     * 网格资源——查询变电站下的所有主变下拉列表
     */
    @GetMapping("/pullDownMenuTransformer/{code}")
    public R<List<PullDownMenuStringSon>> pullDownMenuTransformer(@NotNull(message = "网格编码不能为空") @PathVariable String code) {
        return R.ok(powerService.pullDownMenuTransformer(code));
    }


    /**
     * 网格资源——典型日负荷数据展示，
     */
    @PostMapping("/selectLoad")
    public R<LoadVo> selectLoad(@RequestBody LoadBo bo) throws IOException, ParseException {
        return R.ok(powerService.selectLoad(bo));
    }




    /**
     * 网格资源——主变运行状态评价
     */
    @GetMapping("/transformerState/{code}/{strTime}/{endTime}")
    public R<TransformerVo> transformerState(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                             @NotNull(message = "起始时间不能为空") @PathVariable String strTime,
                                             @NotNull(message = "结束时间不能为空") @PathVariable String endTime) throws IOException, ParseException {
        return R.ok(powerService.transformerState(code,strTime,endTime));
    }

    @PostMapping("/from-file")
    public R<Void> createOrderFromFile(@RequestParam("file") MultipartFile file) throws IOException {
        String json = new String(file.getBytes());
        return powerService.saveOrderFromJsonString(json);
    }

    /**
     * 变电站——出线间隔分析
     */
    @GetMapping("/intervalAnalysis/{code}")
    public R<IntervalAnalysisVo> intervalAnalysis(@NotNull(message = "网格编码不能为空") @PathVariable String code
                                           ) {
        return R.ok(powerService.intervalAnalysis(code));
    }
}
