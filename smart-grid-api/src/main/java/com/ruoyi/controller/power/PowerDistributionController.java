package com.ruoyi.controller.power;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.power.vo.CapacityAnalysisVo;
import com.ruoyi.entity.power.vo.FeederHorizontalAnalysisVo;
import com.ruoyi.service.power.IPowerDistributionService;
import com.ruoyi.service.power.IPowerTransformerService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.ParseException;

/**
 * 配电网
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/powerDistribution")
@SaIgnore
public class PowerDistributionController extends BaseController {

    @Autowired
    IPowerDistributionService iPowerDistributionService;

    /**
     *配电网——配电网设备水平分析
     */
    @GetMapping("/feederHorizontalAnalysis/{psrId}")
    public R<FeederHorizontalAnalysisVo> feederHorizontalAnalysis(
                                                            @NotNull(message = "线路id") @PathVariable String psrId)  {
        return R.ok(iPowerDistributionService.feederHorizontalAnalysis(psrId));
    }

    /**
     *配电网——配电网容量分析
     */
    @GetMapping("/capacityAnalysis/{code}/{pageNum}/{pageSize}")
    public R<CapacityAnalysisVo> capacityAnalysis(
            @NotNull(message = "网格编码") @PathVariable String code,
            @NotNull(message = "页码") @PathVariable Integer pageNum,
            @NotNull(message = "最大数") @PathVariable Integer pageSize)  {
        return R.ok(iPowerDistributionService.capacityAnalysis(code,pageNum,pageSize));
    }

    /**
     *配电网——供电能力分析（负载分析）
     */
    @GetMapping("/loadAnalysis/{code}/{time}/{pageNum}/{pageSize}")
    public R<CapacityAnalysisVo> loadAnalysis(
            @NotNull(message = "网格编码") @PathVariable String code,
            @NotNull(message = "时间") @PathVariable String time,
            @NotNull(message = "页码") @PathVariable Integer pageNum,
            @NotNull(message = "最大数") @PathVariable Integer pageSize) throws ParseException, JsonProcessingException {
        return R.ok(iPowerDistributionService.loadAnalysis(code,time,pageNum,pageSize));
    }


}
