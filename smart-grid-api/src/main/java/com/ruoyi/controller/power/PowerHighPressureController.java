package com.ruoyi.controller.power;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.device.DevicePoleTransformer;
import com.ruoyi.entity.device.DeviceStationTransformer;
import com.ruoyi.entity.device.DeviceZB;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.service.power.IPowerHighPressureService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

/**
 * 高压评估统计
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/powerHighPressure")
@SaIgnore
public class PowerHighPressureController {

    @Autowired
    IPowerHighPressureService iPowerHighPressureService;


    /**
     * 查询网格统计高压评估主变基础模块
     *
     * @param code
     * @return
     */
    @GetMapping("/transformerFoundation/{code}")
    public R<TransformerFoundationVo> transformerFoundation(@PathVariable String code) throws Exception {
        return R.ok(iPowerHighPressureService.transformerFoundation(code));
    }

    /**
     * 查询网格统计高压评估出线
     *
     * @param code
     * @return
     */
    @GetMapping("/transformerQualify/{code}")
    public R<TransformerQualifyVo> transformerQualify(@PathVariable String code) {
        return R.ok(iPowerHighPressureService.transformerQualify(code));
    }

    /**
     * 查询网格统计高压评估主变最大值最小值模块
     *
     * @param code
     * @return
     */
    @GetMapping("/transformerMaxAndMin/{code}")
    public R<TransformerPeakVo> transformerMaxAndMin(@PathVariable String code) throws ParseException, JsonProcessingException {
        return R.ok(iPowerHighPressureService.transformerMaxAndMin(code));
    }

    /**
     * 查询网格统计高压评估线路基础模块
     *
     * @param code
     * @return
     */
    @GetMapping("/feederFoundation/{code}")
    public R<FeederFoundationVo> feederFoundation(@PathVariable String code) throws Exception {
        return R.ok(iPowerHighPressureService.feederFoundation(code));
    }

    /**
     * 查询网格统计高压评估线路供电区域
     *
     * @param code
     * @return
     */
    @GetMapping("/feederSupplyArea/{code}")
    public R<FeederSupplyAreaVo> feederSupplyArea(@PathVariable String code) throws ParseException, JsonProcessingException {
        return R.ok(iPowerHighPressureService.feederSupplyArea(code));
    }

    /**
     * 查询网格统计高压评估主变最大值最小值模块
     *
     * @param code
     * @return
     */
    @GetMapping("/feederMaxAndMin/{code}")
    public R<TransformerPeakVo> feederMaxAndMin(@PathVariable String code) throws ParseException, JsonProcessingException {
        return R.ok(iPowerHighPressureService.feederMaxAndMin(code));
    }

    /**
     * 查询中压的主变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/publicSpecialized/{code}")
    public R<PublicSpecializedVo> mediumPublicSpecialized(@PathVariable String code) throws Exception {
        return R.ok(iPowerHighPressureService.mediumPublicSpecialized(code));
    }

    /**
     * 查询中压的线路信息
     *
     * @param code
     * @return
     */
    @GetMapping("/mediumFeeder/{code}")
    public R<MediumFeederFoundationVo> mediumFeeder(@PathVariable String code) {
        return R.ok(iPowerHighPressureService.mediumFeeder(code));
    }


    /**
     * 查询主变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/selectZB/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceZB> selectZB(@PathVariable String code,@PathVariable Integer pageNum,@PathVariable Integer pageSize) throws JsonProcessingException {
        return iPowerHighPressureService.selectZB(code,pageNum,pageSize);
    }

    /**
     * 查询柱上公变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/selectPubPoleTransformer/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DevicePoleTransformer> selectPubPoleTransformer(@PathVariable String code, @PathVariable Integer pageNum, @PathVariable Integer pageSize) throws JsonProcessingException {
        return iPowerHighPressureService.selectPubPoleTransformer(code,pageNum,pageSize);
    }

    /**
     * 查询柱上专变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/selectPrvPoleTransformer/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DevicePoleTransformer> selectPrvPoleTransformer(@PathVariable String code,@PathVariable Integer pageNum,@PathVariable Integer pageSize) throws JsonProcessingException {
        return iPowerHighPressureService.selectPrvPoleTransformer(code,pageNum,pageSize);
    }

    /**
     * 查询站内公变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/selectPubStationTransformer/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceStationTransformer> selectPubStationTransformer(@PathVariable String code, @PathVariable Integer pageNum, @PathVariable Integer pageSize) throws JsonProcessingException {
        return iPowerHighPressureService.selectPubStationTransformer(code,pageNum,pageSize);
    }
    /**
     * 查询站内专变信息
     *
     * @param code
     * @return
     */
    @GetMapping("/selectPrvStationTransformer/{code}/{pageNum}/{pageSize}")
    public TableDataInfo<DeviceStationTransformer> selectPrvStationTransformer(@PathVariable String code, @PathVariable Integer pageNum, @PathVariable Integer pageSize) throws JsonProcessingException {
        return iPowerHighPressureService.selectPrvStationTransformer(code,pageNum,pageSize);
    }


}
