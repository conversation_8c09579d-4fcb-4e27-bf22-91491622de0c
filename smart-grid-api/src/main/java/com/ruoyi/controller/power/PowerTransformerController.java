package com.ruoyi.controller.power;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.power.bo.DailyLoadUtilizationBo;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.power.bo.AnalysisBo;
import com.ruoyi.entity.power.bo.HistoryAnalysisBo;
import com.ruoyi.entity.power.bo.LoadAnalysisBo;
import com.ruoyi.entity.power.bo.LoadBo;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.bo.LoadMWExcelBo;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.service.power.IPowerTransformerService;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/powerTransformer")
@SaIgnore
public class PowerTransformerController extends BaseController {

    @Autowired
    IPowerTransformerService powerTransformerService;
    /**
     * 主变——根据网格id查询所有网格
     */
    @GetMapping("/girdTransformer/{code}/{pageNum}/{pageSize}")
    public R<Page<DeviceNtHighTransformer>> girdTransformer(@NotNull(message = "网格编码不能为空") @PathVariable String code,
                                                            @NotNull(message = "页码不能为空") @PathVariable Integer pageNum,
                                                            @NotNull(message = "页数不能为空") @PathVariable Integer pageSize) throws IOException {
        return R.ok(powerTransformerService.girdTransformer(code,pageNum,pageSize));
    }

    /**
     * 主变——主变分析
     */
    @PostMapping("/analysisTransformer")
    public R<PowerTransformerVo> analysisTransformer(@RequestBody AnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.analysisTransformer(bo));
    }

    /**
     * 主变—根据id查主变详情
     */
    @GetMapping("/byId/{id}")
    public R<DeviceNtHighTransformer> byId(@NotNull(message = "网格编码不能为空") @PathVariable String id)  {
        return R.ok(powerTransformerService.byId(id));
    }

    /**
     * 主变——线路分析
     */
    @PostMapping("/analysisFeeder")
    public R<PowerTransformerVo> analysisFeeder(@RequestBody AnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.analysisFeeder(bo));
    }


    /**
     * 主变——负载率历史记录查询
     */
    @PostMapping("/selectLoad")
    public R<Page<PowerTransformerSelectVo>> selectLoad(@RequestBody LoadBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.selectLoad(bo));
    }
    /**
     * 主变——历史负载率分析
     */
    @PostMapping("/selectLoadHistoryAnalysis")
    public R<HistoryAnalysisVo> selectLoadHistoryAnalysis(@RequestBody HistoryAnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.selectLoadHistoryAnalysis(bo));
    }

    /**
     * 主变——高峰负载率分析
     */
    @PostMapping("/peakLoadAnalysis")
    public R<LoadAnalysisVo> peakLoadAnalysis(@RequestBody LoadAnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.peakLoadAnalysis(bo));
    }

    /**
     * 主变——低谷负载率分析
     */
    @PostMapping("/valleyLoadAnalysis")
    public R<LoadAnalysisVo> valleyLoadAnalysis(@RequestBody LoadAnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.valleyLoadAnalysis(bo));
    }


    /**
     * 线路——高峰负载率分析
     */
    @PostMapping("/peakLoadAnalysisFeeder")
    public R<LoadAnalysisVo> peakLoadAnalysisFeeder(@RequestBody LoadAnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.peakLoadAnalysisFeeder(bo));
    }

    /**
     * 线路——低谷负载率分析
     */
    @PostMapping("/valleyLoadAnalysisFeeder")
    public R<LoadAnalysisVo> valleyLoadAnalysisFeeder(@RequestBody LoadAnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.valleyLoadAnalysisFeeder(bo));
    }

    /**
     * 典型日负荷利用率
     */
    @PostMapping("/dailyLoadUtilization")
    public R<DailyLoadUtilizationVo> dailyLoadUtilization(@RequestBody DailyLoadUtilizationBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.dailyLoadUtilization(bo));
    }

    /**
     * 最大日负荷利用率
     */
    @PostMapping("/maxLoadUtilization")
    public R<MaxLoadUtilizationVo> maxLoadUtilization(@RequestBody DailyLoadUtilizationBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.maxLoadUtilization(bo));
    }


    /**
     * 制造线路测试数据
     */
    @GetMapping("/testFeeder")
    public R<Void> testFeeder() throws Exception {
        return toAjax(powerTransformerService.testFeeder() ? 1 : 0);
    }

    /**
     * 制造线路测试数据
     */
    @GetMapping("/testFeederMW")
    public R<Void> testFeederMW() throws Exception {
        return toAjax(powerTransformerService.testFeederMW() ? 1 : 0);
    }

    /**
     * 制造主变测试数据
     */
    @GetMapping("/testTransformer")
    public R<Void> testTransformer() throws Exception {
        return toAjax(powerTransformerService.testTransformer() ? 1 : 0);
    }

    /**
     * 制造主变测试数据
     */
    @GetMapping("/testTransformerMW")
    public R<Void> testTransformerMW() throws Exception {
        return toAjax(powerTransformerService.testTransformerMW() ? 1 : 0);
    }


    /**
     * 同步线路，主变容量
     */
    @GetMapping("/synchronous")
    public R<Void> synchronous() throws Exception {
        return toAjax(powerTransformerService.synchronous() ? 1 : 0);
    }

    /**
     * 主变——负荷变化趋势展示和特征分析
     */
    @PostMapping("/transformerLoadMWTrendAndAnalysis")
    public R<LoadMWTrendAndAnalysisVo> transformerLoadMWTrendAndAnalysis(@RequestBody AnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.transformerLoadMWTrendAndAnalysis(bo));
    }

    /**
     * 线路——负荷变化趋势展示和特征分析
     */
    @PostMapping("/feederLoadMWTrendAndAnalysis")
    public R<LoadMWTrendAndAnalysisVo> feederLoadMWTrendAndAnalysis(@RequestBody AnalysisBo bo) throws IOException, ParseException {
        return R.ok(powerTransformerService.feederLoadMWTrendAndAnalysis(bo));
    }
    /**
     * 主变——主变负荷导出
     */
    @PostMapping("/transformerLoadMWExcel")
    public void transformerLoadMWExcel(@RequestBody LoadMWExcelBo bo, HttpServletResponse response) throws Exception {
        powerTransformerService.transformerLoadMWExcel(bo,response);

    }

    /**
     * 主变——线路负荷导出
     */
    @PostMapping("/feederLoadMWExcel")
    public void feederLoadMWExcel(@RequestBody LoadMWExcelBo bo, HttpServletResponse response) throws Exception {
        powerTransformerService.feederLoadMWExcel(bo,response);
    }


}
