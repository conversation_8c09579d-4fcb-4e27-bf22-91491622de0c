package com.ruoyi.controller.Psm;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.power.vo.PowerGridVo;
import com.ruoyi.service.psm.IPsmSelectDeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/psm")
@SaIgnore
public class PsmSelectDeviceController extends BaseController {
    @Autowired
    IPsmSelectDeviceService iPsmSelectDeviceService;

    /**
     * 内网电网一张图查找网格下的变电站
     */
    @GetMapping("/psmSelectDeviceSubstation/{code}")
    public R<List<DeviceSubstation>> psmSelectDeviceSubstation(@NotNull(message = "网格编码不能为空") @PathVariable String code) throws Exception {
        List<DeviceSubstation> deviceSubstation = iPsmSelectDeviceService.psmSelectDeviceSubstation(code);
        return R.ok(deviceSubstation);
    }


    /**
     * 内网电网一张图查找网格下的配网设备数据
     */
    @GetMapping("/psmSelectDevice/{code}")
    public void psmSelectDevice(@NotNull(message = "网格编码不能为空") @PathVariable String code) throws Exception {
        List<Map<String, List<Map<String, Object>>>> list =   iPsmSelectDeviceService.psmSelectDevice(code,null);
    }

    /**
     * 根据网格id查询网格坐标
     */
    @GetMapping("/getCoordinatesByGridCode/{gridCode}")
    public R<List<List<Double>>> getCoordinatesByGridCode(@NotNull(message = "网格编码不能为空") @PathVariable String gridCode) throws Exception {
        List<List<Double>> coordinates = iPsmSelectDeviceService.getCoordinatesByGridCode(gridCode);
        return R.ok(coordinates);
    }

}
