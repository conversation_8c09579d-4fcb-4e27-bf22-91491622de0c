package com.ruoyi.controller.problem;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.entity.problem.bo.ProblemEffectBo;
import com.ruoyi.entity.problem.vo.ProblemEffectVo;
import com.ruoyi.service.problem.IProblemEffectService;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;

import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 问题影响的设备情况
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/effect")
@SaIgnore
public class ProblemEffectController extends BaseController {

    private final IProblemEffectService iProblemEffectService;

    /**
     * 查询问题影响的设备情况列表
     */
    @SaCheckPermission("problem:effect:list")
    @PostMapping("/list")
    public TableDataInfo<ProblemEffectVo> list(ProblemEffectBo bo, PageQuery pageQuery) {
        return iProblemEffectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出问题影响的设备情况列表
     */
    @SaCheckPermission("problem:effect:export")
    @Log(title = "问题影响的设备情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProblemEffectBo bo, HttpServletResponse response) {
        List<ProblemEffectVo> list = iProblemEffectService.queryList(bo);
        TransUtil.doTranslate(list);
        ExcelUtil.exportExcel(list, "问题影响的设备情况", ProblemEffectVo.class, response);
    }

    /**
     * 获取问题影响的设备情况详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("problem:effect:query")
    @GetMapping("/{id}")
    public R<ProblemEffectVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) throws JsonProcessingException {
        return R.ok(iProblemEffectService.queryById(id));
    }

    /**
     * 新增问题影响的设备情况
     */
    @SaCheckPermission("problem:effect:add")
    @Log(title = "问题影响的设备情况", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemEffectBo bo) {
        return toAjax(iProblemEffectService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改问题影响的设备情况
     */
    @SaCheckPermission("problem:effect:edit")
    @Log(title = "问题影响的设备情况", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProblemEffectBo bo) {
        return toAjax(iProblemEffectService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除问题影响的设备情况
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:effect:remove")
    @Log(title = "问题影响的设备情况", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemEffectService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
