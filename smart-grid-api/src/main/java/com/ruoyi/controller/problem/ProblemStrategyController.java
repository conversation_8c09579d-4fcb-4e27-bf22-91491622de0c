package com.ruoyi.controller.problem;

import java.util.List;
import java.util.Arrays;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.entity.problem.bo.ProblemStrategyBo;
import com.ruoyi.entity.problem.vo.ProblemStrategyVo;
import com.ruoyi.service.problem.IProblemStrategyService;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;

import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 故障问题策略
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/strategy")
@SaIgnore
public class ProblemStrategyController extends BaseController {

    private final IProblemStrategyService iProblemStrategyService;

    /**
     * 查询故障问题策略列表
     */
    @SaCheckPermission("problem:strategy:list")
    @PostMapping("/list")
    public TableDataInfo<ProblemStrategyVo> list(@RequestBody ProblemStrategyBo bo) {
        return iProblemStrategyService.queryPageList(bo);
    }

    /**
     * 导出故障问题策略列表
     */
    @SaCheckPermission("problem:strategy:export")
    @Log(title = "故障问题策略", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody ProblemStrategyBo bo, HttpServletResponse response) {
        List<ProblemStrategyVo> list = iProblemStrategyService.queryList(bo);
        TransUtil.doTranslate(list);
        ExcelUtil.exportExcel(list, "故障问题策略", ProblemStrategyVo.class, response);
    }

    /**
     * 按Id获取故障问题策略详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("problem:strategy:query")
    @GetMapping("/byId/{id}")
    public R<ProblemStrategyVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProblemStrategyService.queryById(id));
    }

    /**
     * 按二级分类获取故障问题策略详细信息
     *
     * @param categoryLevel2 二级分类
     */
    @SaCheckPermission("problem:strategy:byType")
    @GetMapping("/byType")
    public R<List<ProblemStrategyVo>> getInfo(@NotNull(message = "主键不能为空")
                                        @RequestParam String categoryLevel2) {
        return R.ok(iProblemStrategyService.byType(categoryLevel2));
    }


    /**
     * 新增故障问题策略
     */
    @SaCheckPermission("problem:strategy:add")
    @Log(title = "故障问题策略", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemStrategyBo bo) {
        return toAjax(iProblemStrategyService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改故障问题策略
     */
    @SaCheckPermission("problem:strategy:edit")
    @Log(title = "故障问题策略", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProblemStrategyBo bo) {
        return toAjax(iProblemStrategyService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除故障问题策略
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:strategy:remove")
    @Log(title = "故障问题策略", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemStrategyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
