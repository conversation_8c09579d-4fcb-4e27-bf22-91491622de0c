package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.gc.dto.GridChangeRequest;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.gc.IGridChangeService;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 网架结构变更控制器
 */
@Slf4j
@RestController
@RequestMapping("/problem/scheme")
@SaIgnore
@Validated
public class ProblemSchemeController {

    @Resource
    private IZnapTopologyService znapTopologyService;

    @Resource
    private ProblemSchemeMapper problemSchemeMapper;

    @Resource
    private IGridChangeService gridChangeService;


    /**
     * 保存网架变更拓扑结构
     * @param request 网架变更请求
     * @return 保存结果
     */
    @PostMapping("/saveTopologyChange")
    public R saveTopologyChange(@Valid @RequestBody GridChangeRequest request) {
        try {
            log.info("开始保存网架变更拓扑结构，请求参数：{}", request);
            // 获取变更数据
            String jsonStr = problemSchemeMapper.selectById(request.getSchemaId()).getOperateData();
            // TODO 代码已更改  暂时注释掉
//
//            ZnapTopology topology = znapTopologyService.generateNode(request.getFeederId());
//            SingAnalysis singAnalysis = NodeUtils.generateNewTopology(jsonStr, topology);
//
//            // 保存网架变更数据
//            Long versionId = gridChangeService.saveGridChangeTopology(request.getProblemId(), singAnalysis, request.getSchemaName(), request.getDescription());
//
//            // 如果需要设为当前版本
//            if (Boolean.TRUE.equals(request.getSetAsCurrent())) {
//                gridChangeService.setCurrentVersion(versionId, request.getProblemId());
//            }

//            log.info("网架变更拓扑结构保存成功，versionId：{}", versionId);
            return R.ok();

        } catch (Exception e) {
            log.error("保存网架变更拓扑结构失败", e);
            return R.fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 删除网架变更拓扑结构
     * @param versionId 版本ID
     * @return 删除结果
     */
    @DeleteMapping("/deleteTopologyChange/{versionId}")
    public R<Void> deleteTopologyChange(@PathVariable Long versionId) {
        try {
            log.info("删除网架变更拓扑结构，versionId：{}", versionId);

            boolean success = gridChangeService.deleteGridChangeTopology(versionId);

            if (success) {
                return R.ok("删除成功");
            } else {
                return R.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("删除网架变更拓扑结构失败", e);
            return R.fail("删除失败：" + e.getMessage());
        }
    }


}