package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.problem.bo.ProblemSchemeHistoryBo;
import com.ruoyi.entity.problem.vo.ProblemSchemeHistoryVo;
import com.ruoyi.service.problem.IProblemSchemeHistoryService;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;

/**
 * 故障解决方案
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/schemeHistory")
@SaIgnore
public class ProblemSchemeHistoryController extends BaseController {

    private final IProblemSchemeHistoryService iProblemSchemeHistoryService;

    /**
     * 查询故障解决历史方案列表
     */
    @SaCheckPermission("problem:schemeHistory:list")
    @PostMapping("/list")
    public TableDataInfo<ProblemSchemeHistoryVo> list(@RequestBody ProblemSchemeHistoryBo bo) throws ParseException {
        return iProblemSchemeHistoryService.queryPageList(bo);
    }

    /**
     * 导出故障解决历史方案列表
     */
    @SaCheckPermission("problem:schemeHistory:export")
    @Log(title = "故障解决方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody ProblemSchemeHistoryBo bo, HttpServletResponse response) throws ParseException {
        List<ProblemSchemeHistoryVo> list = iProblemSchemeHistoryService.queryList(bo);
        TransUtil.doTranslate(list);
        ExcelUtil.exportExcel(list, "故障解决方案", ProblemSchemeHistoryVo.class, response);
    }

    /**
     * 获取故障解决历史方案详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("problem:schemeHistory:query")
    @GetMapping("/{id}")
    public R<ProblemSchemeHistoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProblemSchemeHistoryService.queryById(id));
    }

    /**
     * 新增故障解决历史方案
     */
    @SaCheckPermission("problem:schemeHistory:add")
    @Log(title = "故障解决历史方案", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemSchemeHistoryBo bo) throws ParseException {
        return toAjax(iProblemSchemeHistoryService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改故障解决历史方案
     */
    @SaCheckPermission("problem:schemeHistory:edit")
    @Log(title = "故障解决历史方案", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProblemSchemeHistoryBo bo) throws ParseException {
        return toAjax(iProblemSchemeHistoryService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除故障解决历史方案
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:schemeHistory:remove")
    @Log(title = "故障解决历史方案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemSchemeHistoryService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    /**
     * 根据问题id查询相关所有历史方案
     * @param id
     * @return
     */
    @GetMapping("/byProblemId/{id}")
    public R<List<ProblemSchemeHistoryVo>> byProblemId(@NotNull(message = "主键不能为空")
                                                @PathVariable Long id) {
        return R.ok(iProblemSchemeHistoryService.byProblemId(id));
    }
}
