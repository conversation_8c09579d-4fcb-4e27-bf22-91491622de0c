package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.problem.bo.ProblemRuleConfigurationBo;
import com.ruoyi.entity.problem.vo.ProblemRuleConfigurationVo;
import com.ruoyi.entity.problem.vo.SelectRuleVo;
import com.ruoyi.service.problem.IProblemRuleConfigurationService;
import com.ruoyi.trans.core.TranslateResponse;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 问题规则
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/conditionStrategy")
@SaIgnore
public class ProblemRuleConfigurationController extends BaseController {

    private final IProblemRuleConfigurationService iProblemConditionStrategyService;

    /**
     * 查询问题规则列表
     */
    @TranslateResponse(extractPropertiesToTile = true)
    @SaCheckPermission("problem:conditionStrategy:list")
    @PostMapping("/list")
    public TableDataInfo<ProblemRuleConfigurationVo> list(@RequestBody ProblemRuleConfigurationBo bo) {
        return iProblemConditionStrategyService.queryPageList(bo);
    }

    /**
     * 导出问题规则列表
     */
    @SaCheckPermission("problem:conditionStrategy:export")
    @Log(title = "问题规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody ProblemRuleConfigurationBo bo, HttpServletResponse response) {
        List<ProblemRuleConfigurationVo> list = iProblemConditionStrategyService.queryList(bo);
        TransUtil.doTranslate(list);
        ExcelUtil.exportExcel(list, "问题条件决策", ProblemRuleConfigurationVo.class, response);
    }

    /**
     * 获取问题规则详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("problem:conditionStrategy:query")
    @GetMapping("/{id}")
    public R<ProblemRuleConfigurationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProblemConditionStrategyService.queryById(id));
    }

    /**
     * 新增问题规则
     */
    @SaCheckPermission("problem:conditionStrategy:add")
    @Log(title = "问题规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemRuleConfigurationBo bo) {
        return toAjax(iProblemConditionStrategyService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改问题条件决策
     */
    @SaCheckPermission("problem:conditionStrategy:edit")
    @Log(title = "问题规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody List<ProblemRuleConfigurationBo> boList) {
        return toAjax(iProblemConditionStrategyService.updateByBo(boList) ? 1 : 0);
    }

    /**
     * 删除问题规则
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:conditionStrategy:remove")
    @Log(title = "问题规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemConditionStrategyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 获取所有二级分类的list
     * @return
     */
    @GetMapping("/level2")
    public R<List<String>> level2() {
        return R.ok(iProblemConditionStrategyService.level2());
    }

    /**
     * 获取问题规则详细信息
     *
     * @param code 主键
     */
    @GetMapping("/selectRule/{code}")
    public R<SelectRuleVo> selectRule(@NotNull(message = "主键不能为空")
                                          @PathVariable Integer code) {
        return R.ok(iProblemConditionStrategyService.selectRule(code));
    }
}
