package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.problem.bo.ProblemSchemeAnalysisBo;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;
import com.ruoyi.service.problem.IProblemSchemeAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 方案分析过程
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/schemeAnalysis")
@SaIgnore
public class ProblemSchemeAnalysisController extends BaseController {

    private final IProblemSchemeAnalysisService iProblemSchemeAnalysisService;


    /**
     * 查询方案分析过程列表
     */
    @SaCheckPermission("problem:schemeAnalysis:list")
    @GetMapping("/list")
    public TableDataInfo<ProblemSchemeAnalysisVo> list(ProblemSchemeAnalysisBo bo, PageQuery pageQuery) {
        return iProblemSchemeAnalysisService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出方案分析过程列表
     */
    @SaCheckPermission("problem:schemeAnalysis:export")
    @Log(title = "方案分析过程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProblemSchemeAnalysisBo bo, HttpServletResponse response) {
        List<ProblemSchemeAnalysisVo> list = iProblemSchemeAnalysisService.queryList(bo);
        ExcelUtil.exportExcel(list, "方案分析过程", ProblemSchemeAnalysisVo.class, response);
    }

    /**
     * 获取方案分析过程详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("problem:schemeAnalysis:query")
    @GetMapping("/{id}")
    public R<ProblemSchemeAnalysisVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProblemSchemeAnalysisService.queryById(id));
    }

    /**
     * 新增方案分析过程
     */
    @SaCheckPermission("problem:schemeAnalysis:add")
    @Log(title = "方案分析过程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemSchemeAnalysisBo bo) {
        return toAjax(iProblemSchemeAnalysisService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改方案分析过程
     */
    @SaCheckPermission("problem:schemeAnalysis:edit")
    @Log(title = "方案分析过程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProblemSchemeAnalysisBo bo) {
        return toAjax(iProblemSchemeAnalysisService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除方案分析过程
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:schemeAnalysis:remove")
    @Log(title = "方案分析过程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemSchemeAnalysisService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }



}
