package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.service.problem.IProblemIdentifyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;

/**
 * 问题辨别
 *
 * <AUTHOR>
 * @date 2025-06-4
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/Identify")
@SaIgnore
public class ProblemIdentifyController extends BaseController {

    @Autowired

    IProblemIdentifyService iProblemIdentifyService;

    /**
     * 传入问题id判断，其是否符合我们规则
     */
    @GetMapping("/checkProblem/{problemId}")
    public R<Integer> checkProblem(@PathVariable Long problemId) throws IOException {
        return R.ok(iProblemIdentifyService.checkProblem(problemId));
    }

    /**
     * 根据线路id分析其是否是分段配变不合理的问题
     */
    @GetMapping("/unreasonablePBSegmentation/{feederId}/{deviceId}")
    public R<Void> unreasonablePBSegmentation(@PathVariable String feederId, @PathVariable String deviceId) {
        Integer state = iProblemIdentifyService.unreasonablePBSegmentation(feederId, deviceId);
        if (state == 1) {
            return R.ok("该线路无分段配变不合理的问题");
        } else if (state == 2) {
            return R.ok("该线路有分段配变不合理的问题");
        }
        return R.ok("线路拓扑关系解析异常");
    }

    /**
     * 根据线路id分析其是否是挂架配变过多识别
     */
    @GetMapping("/pylonsPBSegmentation/{feederId}")
    public R<Void> pylonsPBSegmentation(@PathVariable String feederId) {
        Integer state = iProblemIdentifyService.pylonsPBSegmentation(feederId);
        if (state == 1) {
            return R.ok("该线路无挂架配变过多的问题");
        } else if (state == 2) {
            return R.ok("该线路有挂架配变过多的问题");
        }
        return R.ok("线路拓扑关系解析异常");
    }

    /**
     * 根据线路id分析其是否是单辐射无联络问题
     */
    @GetMapping("/singleRadiationSegmentation/{feederId}")
    public R<Void> singleRadiationSegmentation(@PathVariable String feederId) {
        Integer state = iProblemIdentifyService.singleRadiationSegmentation(feederId);
        if (state == 1) {
            return R.ok("该线路无单辐射无联络问题");
        } else if (state == 2) {
            return R.ok("该线路有单辐射无联络问题");
        }
        return R.ok("线路拓扑关系解析异常");
    }

    /**
     * 根据线路id分析其是否是大分支无联络问题
     */
    @GetMapping("/bigBranchSegmentation/{feederId}/{deviceId}")
    public R<Void> bigBranchSegmentation(@PathVariable String feederId, @PathVariable String deviceId) {
        Integer state = iProblemIdentifyService.bigBranchSegmentation(feederId, deviceId);
        if (state == 1) {
            return R.ok("该线路无大分支无联络问题");
        } else if (state == 2) {
            return R.ok("该线路有大分支无联络问题");
        }
        return R.ok("线路拓扑关系解析异常");
    }


    /**
     * 根据线路id分析其是否是线路重过载问题
     */
    @GetMapping("/lineOverload/{feederId}")
    public R<Void> lineOverload(@PathVariable String feederId) throws Exception {
        Integer state = iProblemIdentifyService.lineOverload(feederId);
        if (state == 1) {
            return R.ok("该线路无线路重过载问题");
        } else if (state == 2) {
            return R.ok("该线路有线路重过载问题");
        }
        return R.ok("线路拓扑关系解析异常");
    }


    /**
     * 根据线路id分析其是否是同母联络问题
     */
    @GetMapping("/sameContact/{feederId}")
    public R<Void> sameContact(@PathVariable String feederId) throws IOException {
        Integer state = iProblemIdentifyService.sameContact(feederId);
        if (state == 1) {
            return R.ok("该线路无同母联络问题");
        } else if (state == 2) {
            return R.ok("该线路有同母联络问题");
        }
        return R.ok("线路关系解析异常");
    }


}
