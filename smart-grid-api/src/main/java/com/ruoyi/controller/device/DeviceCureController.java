package com.ruoyi.controller.device;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.vo.DeviceCurveVo;
import com.ruoyi.entity.device.vo.DeviceLoadMaxVo;
import com.ruoyi.service.device.impl.DeviceCurve;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.ruoyi.constant.DeviceCurveType.*;

/**
 * 查询设备曲线
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/device")
@SaIgnore
public class DeviceCureController extends BaseController {

    @Autowired
    DeviceCurve deviceCurve;


    /**
     * 查询设备曲线
     */
    @GetMapping("/selectDeviceCurve/{deviceId}/{defectTime}/{deviceType}")
    public R<List<DeviceCurveVo>> list(@PathVariable String deviceId, @PathVariable String defectTime, @PathVariable String deviceType) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false);
        Date time = sdf.parse(defectTime);
        List<DeviceCurveVo> list = deviceCurve.selectTimeAndElectricCurrent(time, deviceId, deviceType, Load + "," + ElectricCurrent_A_phs + "," + ElectricCurrent_A_phsA
                + "," + ElectricCurrent_A_phsB + "," + ElectricCurrent_A_phsC + "," + Power_TotW + "," + Power_TotVar);
        return R.ok(list);
    }
    /**
     * 根据时间范围查询设备负载率每天的最大值
     *
     * @param deviceId 设备ID
     * @param startDate 开始时间 (格式: yyyy-MM-dd)
     * @param endDate 结束时间 (格式: yyyy-MM-dd)
     * @param deviceType 设备类型
     * @return 时间范围内每天的负载率最大值列表，格式：日期：最大值，找不到数据时返回0
     */
    @GetMapping("/selectDeviceCurveMaxValues/{deviceId}/{startDate}/{endDate}/{deviceType}")
    public R<List<DeviceLoadMaxVo>> getDeviceCurveMaxValues(
            @PathVariable String deviceId,
            @PathVariable String startDate,
            @PathVariable String endDate,
            @PathVariable String deviceType) {
        try {
            // 参数验证
            if (deviceId == null || deviceId.trim().isEmpty()) {
                return R.fail("设备ID不能为空");
            }
            if (deviceType == null || deviceType.trim().isEmpty()) {
                return R.fail("设备类型不能为空");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setLenient(false);
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            // 验证日期范围
            if (start.after(end)) {
                return R.fail("开始时间不能晚于结束时间");
            }
            // 调用服务方法获取每天的最大值数据
            List<DeviceLoadMaxVo> maxValuesList = deviceCurve.selectDeviceLoadMaxValuesByDateRange(
                    start, end, deviceId, deviceType);
            return R.ok(maxValuesList);
        } catch (Exception e) {
            log.error("查询设备曲线最大值失败: deviceId={}, startDate={}, endDate={}, deviceType={}",
                    deviceId, startDate, endDate, deviceType, e);
            return R.fail("查询设备曲线最大值失败: " + e.getMessage());
        }
    }

}
