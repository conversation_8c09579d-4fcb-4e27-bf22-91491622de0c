package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.bo.PlanBo;
import com.ruoyi.entity.plan.vo.PlanGroupResultVo;
import com.ruoyi.entity.plan.vo.PlanVo;
import com.ruoyi.entity.plan.PlanAnalysisState;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.BranchNodeVo;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.entity.plan.vo.PlanCost;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;
import com.ruoyi.service.plan.IPlanService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.vo.BusbarSwitchVo;
import com.ruoyi.vo.ContactSwitchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 故障解决方案
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/plan")
@SaIgnore
public class PlanController extends BaseController {

    private final IPlanService iProblemSchemeService;

    private final PlanProcessServiceImpl planProcessService;

    /**
     * 获取各个分段
     */
    @GetMapping("/getSegList")
    public R<ArrayList<SegBetweenVo>> getSegList(@RequestParam String feederId, @RequestParam(required = false) String deviceId) {
        ArrayList<SegBetween> segBetweenList = iProblemSchemeService.getSegBetweenList(feederId, deviceId);
        return R.ok(NodeUtils.toSegBetweenVoList(segBetweenList));
    }

    /**
     * 通过feederId查询大分支列表
     * @param feederId 线路ID
     * @return 大分支列表
     */
    @GetMapping("/bigBranch/{feederId}")
    public R<List<BranchNodeVo>> getBigBranch(@PathVariable String feederId) {
        return R.ok(iProblemSchemeService.getBigBranch(feederId));
    }

    /**
     * 获取主干路径
     */
    @GetMapping("/getMainPath")
    public R<List<NodeVo>> getMainPath(@RequestParam String feederId) {
        return R.ok(NodeUtils.toNodeVos(iProblemSchemeService.getMainPath(feederId)));
    }

    /**
     * 根据实列id查询状态实例
     */
    @GetMapping("/selectState/{id}")
    public R<PlanAnalysisState> selectState(@PathVariable String id) {
        return R.ok(iProblemSchemeService.selectState(id));
    }


    /**
     * 获取故障详细信息
     *
     * @param problemId 主键
     */
    @GetMapping("/queryPlanByProblemId/{problemId}")
    public R<PlanGroupResultVo> queryPlanByProblemId(@PathVariable Long problemId) {
        return R.ok(iProblemSchemeService.queryByProblemId(problemId));
    }


    /**
     * 查询故障解决方案列表
     */
    @PostMapping("/list")
    public TableDataInfo<PlanVo> list(@RequestBody PlanBo bo) {
        return iProblemSchemeService.queryPageList(bo);
    }

    /**
     * 获取故障详细信息
     *
     * @param problemId 主键
     */
    @GetMapping("/getProcessList/{problemId}")
    public R<List<ProblemSchemeAnalysisVo>> getProcessList(@PathVariable Long problemId) {
        return R.ok(planProcessService.getByProblemId(problemId));
    }

    /**
     * 导出故障解决方案列表
     */
    @SaCheckPermission("problem:scheme:export")
    @Log(title = "故障解决方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody PlanBo bo, HttpServletResponse response) {
        List<PlanVo> list = iProblemSchemeService.queryList(bo);
        ExcelUtil.exportExcel(list, "故障解决方案", PlanVo.class, response);
    }

    /**
     * 获取故障解决方案详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<PlanVo> getInfo(@NotNull(message = "主键不能为空")
                             @PathVariable Long id) {
        return R.ok(iProblemSchemeService.queryById(id));
    }

    /**
     * 新增故障解决方案
     */
    @SaCheckPermission("problem:scheme:add")
    @Log(title = "故障解决方案", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PlanBo bo) {
        return toAjax(iProblemSchemeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改故障解决方案
     */
    @SaCheckPermission("problem:scheme:edit")
    @Log(title = "故障解决方案", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PlanBo bo) {
        return toAjax(iProblemSchemeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除故障解决方案
     *
     * @param ids 主键串
     */
    @SaCheckPermission("problem:scheme:remove")
    @Log(title = "故障解决方案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProblemSchemeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

//    /**
//     * 分析
//     *
//     * @return
//     */
//    @GetMapping("/stop/{problemId}")
//    public R<Void> analysisPlan(@PathVariable String problemId) {
//     //   Boolean b = iProblemSchemeService.stopTask(taskId);
//        return R.ok("");
//    }

    /**
     * 根据问题id查询相关所有方案
     *
     * @param id
     * @return
     */
    @GetMapping("/byProblemId/{id}")
    public R<List<PlanVo>> byProblemId(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(iProblemSchemeService.byProblemId(id));
    }

//================================== 任务开启关闭流程=================================

    /**
     * 用于分析生成方案
     */
    @PostMapping("/generatePlan")
    public R<String> analysisGeneratePlan(@RequestBody GeneratePlanBo generatePlanBo) {
        return R.ok("200", iProblemSchemeService.analysisGeneratePlan(generatePlanBo));
    }


    /**
     * 暂停任务
     *
     * @param instanceId
     * @return
     */
    @GetMapping("/pause/{instanceId}")
    public R<Void> pauseTask(@PathVariable Long instanceId) {
        Boolean b = iProblemSchemeService.pauseTask(instanceId);
        if (b) {
            return R.ok("任务已暂停");
        }
        return R.fail("没找到该任务");

    }

    /**
     * 恢复任务
     *
     * @param instanceId
     * @return
     */
    @GetMapping("/resume/{instanceId}")
    public R<Void> resumeTask(@PathVariable Long instanceId) {
        Boolean b = iProblemSchemeService.resumeTask(instanceId);
        if (b) {
            return R.ok("任务已恢复");
        }
        return R.fail("没找到该任务");
    }

    /**
     * 停止任务
     *
     * @param instanceId
     * @return
     */
    @GetMapping("/stop/{instanceId}")
    public R<Void> stopTask(@PathVariable String instanceId) {
        Boolean b = iProblemSchemeService.stopTask(instanceId);
        if (b) {
            return R.ok("任务已停止");
        }
        return R.fail("没找到该任务");
    }

    /**
     * 根据问题id查询所有相关方案造价
     *
     * @param
     * @return
     */
    @GetMapping("/planCost/{planId}")
    public R<PlanCost> planCost(@PathVariable Long planId) throws JsonProcessingException {
        return R.ok(iProblemSchemeService.planCost(planId));


    }
    /**
     * 根据问题id查询馈线，根据馈线查询对应的联络线，以及联络开关
     *
     * @param
     * @return
     */
    @GetMapping("/getBusbarSwitch/{problemId}")
    public R<List<ContactSwitchVo>> getBusbarSwitchByProblemId(@PathVariable Long problemId) {
        return R.ok(iProblemSchemeService.getBusbarSwitchByProblemId(problemId));
    }


}
