package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.plan.vo.MarkVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.IPlanProblemDescribeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;

/**
 * 故障解决方案相关问题描述查询
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/plan/planProblemDescribe")
@SaIgnore
public class PlanProblemDescribeController extends BaseController {

    @Autowired
    IPlanProblemDescribeService planProblemDescribeService;

    /**
     * 获取故障解决方案详细信息
     *
     * @param id 主键
     */
    @GetMapping("selectDetails/{id}")
    public R<List<Object>> selectDetails(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        List<Object> list = planProblemDescribeService.selectDetails(id);
        return R.ok(list);
    }

    /**
     * 获取故障解决方案相关问题的不合理标记设备
     *
     * @param id 主键
     */
    @GetMapping("selectFaultMark/{id}")
    public R<List<HashMap<String, Object>>> selectFaultMark(@NotNull(message = "主键不能为空")
                                                            @PathVariable Long id) {
        List<HashMap<String, Object>> markVoList = planProblemDescribeService.selectFaultMark(id);
        return R.ok(markVoList);
    }


}
