package com.ruoyi.controller.calc;

import com.ruoyi.bo.ZhOptPlanBo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.service.calc.IZhOptPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 辅助决策方案
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/optPlan")
public class ZhOptPlanController extends BaseController {


    private final IZhOptPlanService iZhOptPlanService;


    /**
     * 新增辅助决策方案
     */
    @PostMapping()
    public R add(@Validated(AddGroup.class) @RequestBody ZhOptPlanBo bo) {
        return iZhOptPlanService.insertByBo(bo);
    }

    /**
     * 查询辅助决策方案详情列表
     */
    @GetMapping("/list")
    public R list(ZhOptPlanBo zhOptPlanBo) {
        return iZhOptPlanService.queryList(zhOptPlanBo);
    }

}
