package com.ruoyi.controller.calc;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.aop.anno.IgnoreRestControllerResponseAdvice;
import com.ruoyi.dto.*;
import com.ruoyi.entity.calc.CalcRelationShip;
import com.ruoyi.entity.calc.SimPfRet;
import com.ruoyi.mapper.calc.CalcRelationShipMapper;
import com.ruoyi.mapper.calc.SimPfRetMapper;
import com.ruoyi.service.calc.PowerFlowCurveService;
import com.ruoyi.service.calc.TrendCalcService;
import com.ruoyi.service.calc.impl.PowerAnalysisService;
import com.ruoyi.service.device.impl.strategy.DeviceQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 馈线潮流计算
 * 提供潮流计算结果的各种分析接口
 * 主要功能：
 * - 电压越线分析
 * - 电流越线分析
 * - 配变超容分析
 * - 短路电流分析
 * - 线路仿真分析
 * - 负载率统计分析
 *
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/calc")
@RequiredArgsConstructor
@Validated
@Tag(name = "馈线潮流计算", description = "馈线潮流计算结果分析接口")
public class PowerAnalysisController {

    private final PowerAnalysisService powerAnalysisService;
    private final TrendCalcService trendCalcService;
    private final CalcRelationShipMapper calcRelationShipMapper;
    private final SimPfRetMapper simPfRetMapper;
    private final DeviceQueryService deviceQueryService;
    private final PowerFlowCurveService powerFlowCurveService;

    @PostMapping("/feederCalc")
    @IgnoreRestControllerResponseAdvice()
    public R<?> feederCalc(@RequestBody Map<String, Object> params) {
        return trendCalcService.feederCalc(params);
    }

    /**
     * 返回潮流计算状态
     * @param problemId
     * @return
     */
    @GetMapping("/calcStatus/{problemId}/{date}")
    @Operation(summary = "潮流计算状态", description = "查询潮流计算的状态")
    public ResponseEntity<ApiResponse<ProblemCalcStatusDto>> calcStatus(@Parameter(description = "问题id", required = true) @PathVariable("problemId") Long problemId,
                                                                        @Parameter(description = "date", required = true) @PathVariable("date") String date) {
        if (problemId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到问题id"));
        }
        if (StringUtils.isEmpty(date)){
            return ResponseEntity.ok(ApiResponse.error("未选择日期"));
        }
        log.info("请求潮流计算状态，problemId: {}", problemId);
        try {
            ProblemCalcStatusDto status = powerAnalysisService.calcStatus(problemId,date);
            log.info("潮流计算状态查询完成，problemId: {}, 状态: {}", problemId, status);
            return ResponseEntity.ok(ApiResponse.success(status, "潮流计算状态查询完成"));
        } catch (Exception e) {
            log.error("潮流计算状态查询失败，problemId: {}", problemId, e);
            return ResponseEntity.ok(ApiResponse.error("潮流计算状态查询失败: " + e.getMessage()));
        }
    }

    /**
     * 电压越线分析
     * 检测系统中所有节点的电压是否在规定范围内
     *
     * @return 电压越线设备列表及详细信息
     */
    @GetMapping("/voltage-violations/{instanceId}")
    @Operation(summary = "电压越线分析", description = "分析系统中电压超出正常范围的节点")
    public ResponseEntity<ApiResponse<List<VoltageViolationDto>>> analyzeVoltageViolations(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求电压越线分析，retId: {}", retId);
        try {
            List<VoltageViolationDto> violations = powerAnalysisService.analyzeVoltageViolations(retId);
            log.info("电压越线分析完成，retId: {}, 越线节点数: {}", retId, violations.size());
            return ResponseEntity.ok(ApiResponse.success(violations, String.format("电压越线分析完成，发现 %d 个越线节点", violations.size())));
        } catch (Exception e) {
            log.error("电压越线分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("电压越线分析失败: " + e.getMessage()));
        }
    }

    /**
     * 电流越线分析
     * 检测开关设备和线路段的电流负载情况
     *
     * @return 电流越线设备列表及负载详情
     */
    @GetMapping("/current-violations/{instanceId}")
    @Operation(summary = "电流越线分析", description = "分析系统中电流负载超标的设备")
    public ResponseEntity<ApiResponse<List<CurrentViolationDto>>> analyzeCurrentViolations(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {

        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求电流越线分析，retId: {}", retId);
        try {
            List<CurrentViolationDto> violations = powerAnalysisService.analyzeCurrentViolations(retId);
            log.info("电流越线分析完成，retId: {}, 越线设备数: {}", retId, violations.size());
            return ResponseEntity.ok(ApiResponse.success(violations, String.format("电流越线分析完成，发现 %d 个超载设备", violations.size())));
        } catch (Exception e) {
            log.error("电流越线分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("电流越线分析失败: " + e.getMessage()));
        }
    }

    private Long getRetId(String instanceId) {
        LambdaQueryWrapper<CalcRelationShip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CalcRelationShip::getInstanceId, instanceId);
        CalcRelationShip calcRelationShip = calcRelationShipMapper.selectOne(queryWrapper);
        if (calcRelationShip == null) {
            return null;
        }
        String msgId = calcRelationShip.getMsgId();
        if (StringUtils.isEmpty(msgId)) {
            return null;
        }
        SimPfRet simPfRet = simPfRetMapper.findByMsgId(msgId);
        if (simPfRet == null) {
            return null;
        }
        return simPfRet.getId();
    }

    /**
     * 配变超容分析
     * 检测配电变压器的容量使用情况
     *
     * @return 配变超容设备列表及容量详情
     */
    @GetMapping("/transformer-overloads/{instanceId}")
    @Operation(summary = "配变超容分析", description = "分析配电变压器的容量使用情况")
    public ResponseEntity<ApiResponse<List<TransformerOverloadDto>>> analyzeTransformerOverloads(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求配变超容分析，retId: {}", retId);
        try {
            List<TransformerOverloadDto> overloads = powerAnalysisService.analyzeTransformerOverloads(retId);
            log.info("配变超容分析完成，retId: {}, 超容设备数: {}", retId, overloads.size());
            return ResponseEntity.ok(ApiResponse.success(overloads, String.format("配变超容分析完成，发现 %d 个超容配变", overloads.size())));
        } catch (Exception e) {
            log.error("配变超容分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("配变超容分析失败: " + e.getMessage()));
        }
    }

    /**
     * 短路电流分析
     * 检测短路电流是否超过设备承受能力
     *
     * @return 短路电流分析结果及设备承受能力对比
     */
    @GetMapping("/short-circuit-analysis/{instanceId}")
    @Operation(summary = "短路电流分析", description = "分析短路电流对设备安全的影响")
    public ResponseEntity<ApiResponse<List<ShortCircuitAnalysisDto>>> analyzeShortCircuitViolations(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求短路电流分析，retId: {}", retId);
        try {
            List<ShortCircuitAnalysisDto> analysis = powerAnalysisService.analyzeShortCircuitViolations(retId);
            long violationCount = analysis.stream().filter(ShortCircuitAnalysisDto::getIsViolation).count();
            log.info("短路电流分析完成，retId: {}, 总节点数: {}, 越线节点数: {}", retId, analysis.size(), violationCount);
            return ResponseEntity.ok(ApiResponse.success(analysis, String.format("短路电流分析完成，%d 个节点中有 %d 个存在风险", analysis.size(), violationCount)));
        } catch (Exception e) {
            log.error("短路电流分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("短路电流分析失败: " + e.getMessage()));
        }
    }

    /**
     * 线路仿真分析
     * 提供线路段的详细运行参数和状态评估
     *
     * @return 线路段运行参数详情
     */
    @GetMapping("/line-simulation/{instanceId}")
    @Operation(summary = "线路仿真分析", description = "获取线路段详细运行参数和状态")
    public ResponseEntity<ApiResponse<List<LineSimulationDto>>> analyzeLineSimulations(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求线路仿真分析，retId: {}", retId);
        try {
            List<LineSimulationDto> simulations = powerAnalysisService.analyzeLineSimulations(retId);
            long warningCount = simulations.stream().filter(s -> "WARNING".equals(s.getStatus())).count();
            long criticalCount = simulations.stream().filter(s -> "CRITICAL".equals(s.getStatus())).count();
            log.info("线路仿真分析完成，retId: {}, 总线路数: {}, 告警: {}, 严重: {}", retId, simulations.size(), warningCount, criticalCount);
            return ResponseEntity.ok(ApiResponse.success(simulations, "线路仿真分析完成"));
        } catch (Exception e) {
            log.error("线路仿真分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("线路仿真分析失败: " + e.getMessage()));
        }
    }

    /**
     * 负载率统计分析
     * 提供系统整体负载率分布统计
     *
     * @return 负载率分布统计数据
     */
    @GetMapping("/load-rate-analysis/{instanceId}")
    @Operation(summary = "负载率统计分析", description = "统计系统各设备的负载率分布情况")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLoadRateAnalysis(@Parameter(description = "潮流计算结果ID", required = true) @PathVariable String instanceId) {
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return ResponseEntity.ok(ApiResponse.error("未找到指定的计算结果"));
        }
        log.info("请求负载率统计分析，retId: {}", retId);
        try {
            Map<String, Object> analysis = powerAnalysisService.getLoadRateAnalysis(retId);
            log.info("负载率统计分析完成，retId: {}, 总设备数: {}", retId, analysis.get("totalDevices"));
            return ResponseEntity.ok(ApiResponse.success(analysis, "负载率统计分析完成"));
        } catch (Exception e) {
            log.error("负载率统计分析失败，retId: {}", retId, e);
            return ResponseEntity.ok(ApiResponse.error("负载率统计分析失败: " + e.getMessage()));
        }
    }


    /**
     * 查询设备潮流曲线
     */
    @GetMapping("/curve/{instanceId}/{psrType}/{psrId}")
    @Operation(summary = "查询设备潮流曲线", description = "根据设备类型和PSR ID查询设备的潮流曲线数据")
    public R<List<PowerFlowCurveDto>> getPowerFlowCurve(@Parameter(description = "计算实例ID", required = true) @PathVariable String instanceId, @Parameter(description = "设备类型", required = true) @PathVariable String psrType, @Parameter(description = "设备PSR ID", required = true) @PathVariable String psrId) {

        // 获取潮流计算结果ID
        Long retId = getRetId(instanceId);
        if (retId == null) {
            return R.ok("查询潮流曲线失败");
        }
        try {
            List<PowerFlowCurveDto> curveData = powerFlowCurveService.queryPowerFlowCurve(retId, psrType, psrId);
            return R.ok(curveData);
        } catch (Exception e) {
            log.error("查询潮流曲线失败", e);
            return R.ok("查询潮流曲线失败: " + e.getMessage());
        }
    }


    /**
     * 查询设备信息
     */
    @GetMapping("/device/info")
    @Operation(summary = "查询设备信息", description = "根据设备类型和PSR ID查询设备的资产信息")
    public R<Object> queryDevice(
            @RequestParam String psrType,
            @RequestParam String psrId) {
        try {
            Object result = deviceQueryService.queryDevice(psrType, psrId);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

}