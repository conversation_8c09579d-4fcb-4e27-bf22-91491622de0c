package com.ruoyi.controller.calc;

import com.ruoyi.bo.CalcAlarmRuleBo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.service.calc.ICalcAlarmRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 实例告警规则
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/alarmRule")
public class CalcAlarmRuleController extends BaseController {

    private final ICalcAlarmRuleService iCalcAlarmRuleService;

    /**
     * 查询实例告警规则列表
     */
    @GetMapping("/list")
    public R list(CalcAlarmRuleBo bo) {
        return R.ok(iCalcAlarmRuleService.queryList(bo));
    }
}
