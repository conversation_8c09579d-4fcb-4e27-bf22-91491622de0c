package com.ruoyi.controller.calc;

import com.ruoyi.bo.CalcRecycleRuleBo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.service.calc.ICalcRecycleRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计算实例回收规则
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/recycleRule")
public class CalcRecycleRuleController extends BaseController {

    private final ICalcRecycleRuleService iCalcRecycleRuleService;

    /**
     * 查询计算实例回收规则列表
     */
    @GetMapping("/list")
    public R list(CalcRecycleRuleBo bo) {
        return R.ok(iCalcRecycleRuleService.queryList(bo));
    }


    /**
     * 新增计算实例回收规则
     */
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody List<CalcRecycleRuleBo> calcRecycleRuleBos) {
        return toAjax(iCalcRecycleRuleService.insertByBo(calcRecycleRuleBos) ? 1 : 0);
    }
}
