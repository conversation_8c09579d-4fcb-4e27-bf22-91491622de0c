package com.ruoyi.controller.calc;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.CalcAlarmInfoBo;
import com.ruoyi.entity.calc.CalcAlarmInfoVo;
import com.ruoyi.service.calc.ICalcAlarmInfoService;
import com.ruoyi.vo.AlarmStatisticsVo;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 手动计算实例告警
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/alarmInfo")
public class CalcAlarmInfoController extends BaseController {

    private final ICalcAlarmInfoService iCalcAlarmInfoService;

    /**
     * 查询手动计算实例告警列表
     */
    @GetMapping("/list")
    public TableDataInfo<CalcAlarmInfoVo> list(CalcAlarmInfoBo bo, PageQuery pageQuery) {
        return iCalcAlarmInfoService.queryPageList(bo, pageQuery);
    }
    /**
     * 查询手动计算实例告警列表
     */
    @GetMapping("/history/list")
    public TableDataInfo<CalcAlarmInfoVo> historyList(CalcAlarmInfoBo bo, PageQuery pageQuery) {
        return iCalcAlarmInfoService.historyList(bo, pageQuery);
    }

    @GetMapping("history/statistics")
    public R<List<Map<String, Object>>> historyStatistics(Integer dimensionality) {
        return R.ok(iCalcAlarmInfoService.historyStatistics(dimensionality));
    }

    /**
     * 导出告警
     */
    @GetMapping("history/export")
    public void export(@NotNull(message = "开始时间不能为空") @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                       @NotNull(message = "结束时间不能为空") @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                       HttpServletResponse response) throws IOException {
        List<CalcAlarmInfoVo> list = iCalcAlarmInfoService.historyExportList(startTime, endTime);
        ServletOutputStream os = response.getOutputStream();
        ExcelWriterSheetBuilder builder = EasyExcel.write(os, CalcAlarmInfoVo.class).sheet("告警历史");
        builder.doWrite(list);
    }

    /**
     * 查询手动计算实例告警列表
     */
    @GetMapping("/plan")
    public R plan(CalcAlarmInfoBo bo) {
        return iCalcAlarmInfoService.plan(bo);
    }


    /**
     * 统计告警信息
     */
    @GetMapping("/statistics")
    public R<AlarmStatisticsVo> statistics(CalcAlarmInfoBo bo) {
        return R.ok(iCalcAlarmInfoService.statistics(bo));
    }

    /**
     * 查询手动计算实例告警列表
     */
    @GetMapping("/listNoPage")
    public R<List<Map<String,Object>>> list(CalcAlarmInfoBo bo) {
        return R.ok(iCalcAlarmInfoService.queryList(bo));
    }

    /**
     * 获取手动计算实例告警详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<CalcAlarmInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iCalcAlarmInfoService.queryById(id));
    }

    /**
     *查询网格最新的计算实例（成功的）
     * */
    @GetMapping("/getInstanceIdByGridCode")
    public R getInstanceIdByGridCode(String gridCode) {
        return iCalcAlarmInfoService.getInstanceIdByGridCode(gridCode);
    }



}
