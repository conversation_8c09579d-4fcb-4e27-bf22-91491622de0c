package com.ruoyi.controller.calc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.service.calc.IZhTransferInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 转供方案
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/transferInfo")
public class ZhTransferInfoController extends BaseController {

    private final IZhTransferInfoService iZhTransferInfoService;

    /**
     * 查询转供方案列表
     */
    @GetMapping("/list")
    public R list(String calcId) {
        return iZhTransferInfoService.list(calcId);
    }

    /**
     * N-1分析统计（定时计算：全区域或者单个网格/手动计算：单个网格）
     */
    @GetMapping("/statistics")
    public R statistics(String instanceId, String gridCode) {
        return iZhTransferInfoService.statistics(instanceId, gridCode);
    }


    /**
     * N-1分析统计详情（手动计算/定时计算）
     *
     * @param type       n-1类型
     * @param instanceId 实例id
     * @param gridCode   网格id
     */
    @GetMapping("/statisticsByType")
    public R statisticsByType(String type, String instanceId, String gridCode) {
        return iZhTransferInfoService.statisticsByType(type, instanceId, gridCode);
    }


}
