package com.ruoyi.controller.calc;

import com.ruoyi.bo.GridOptimizePolicyBo;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.service.calc.IGridOptimizePolicyService;
import com.ruoyi.vo.GridOptimizePolicyVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 网格优化策略
 *
 * <AUTHOR> developer
 * @date 2024-12-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/optimizePolicy")
public class GridOptimizePolicyController extends BaseController {
    private final IGridOptimizePolicyService iGridOptimizePolicyService;

    /**
     * 查询网格优化策略列表
     */
    @GetMapping("/list")
    public R<List<GridOptimizePolicyVo>> list(GridOptimizePolicyBo bo) {
        return R.ok(iGridOptimizePolicyService.queryList(bo));
    }

    /**
     * 获取网格优化策略详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<GridOptimizePolicyVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iGridOptimizePolicyService.queryById(id));
    }

    /**
     * 新增网格优化策略
     */
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GridOptimizePolicyBo bo) {
        return toAjax(iGridOptimizePolicyService.insertByBo(bo) ? 1 : 0);
    }
}
