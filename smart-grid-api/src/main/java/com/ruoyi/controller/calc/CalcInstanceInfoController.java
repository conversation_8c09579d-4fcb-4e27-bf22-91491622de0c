package com.ruoyi.controller.calc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.CalcInstanceInfoBo;
import com.ruoyi.entity.calc.CalcInstanceInfoVo;
import com.ruoyi.service.calc.ICalcInstanceInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 计算实例
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/instanceInfo")
public class CalcInstanceInfoController extends BaseController {

    private final ICalcInstanceInfoService iCalcInstanceInfoService;

    /**
     * 查询计算实例列表
     */
    @GetMapping("/list")
    public TableDataInfo<CalcInstanceInfoVo> list(CalcInstanceInfoBo bo, PageQuery pageQuery) {
        return iCalcInstanceInfoService.queryPageList(bo, pageQuery);
    }


    /**
     * 获取计算实例详细信息
     *
     * @param instanceId 主键
     */
    @GetMapping("/{instanceId}")
    public R<CalcInstanceInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable String instanceId) {
        return R.ok(iCalcInstanceInfoService.queryById(instanceId));
    }


    /**
     * 删除计算实例
     *
     * @param instanceIds 主键串
     */
    @DeleteMapping("/{instanceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String instanceIds) {
        return toAjax(iCalcInstanceInfoService.deleteWithValidByIds(instanceIds, true) ? 1 : 0);
    }
}
