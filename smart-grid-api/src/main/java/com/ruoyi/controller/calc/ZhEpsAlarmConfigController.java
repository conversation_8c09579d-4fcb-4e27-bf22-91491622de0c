package com.ruoyi.controller.calc;

import com.ruoyi.bo.ZhEpsAlarmConfigBo;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.entity.calc.EpsAlarmConfig;
import com.ruoyi.service.calc.IZhEpsAlarmConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 告警信息
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grid/epsAlarmConfig")
public class ZhEpsAlarmConfigController extends BaseController {
    private final IZhEpsAlarmConfigService iZhEpsAlarmConfigService;

    /**
     * 查询告警信息列表
     */
    @GetMapping("/list")
    public TableDataInfo<EpsAlarmConfig> list(ZhEpsAlarmConfigBo bo, PageQuery pageQuery) {
        return iZhEpsAlarmConfigService.queryPageList(bo, pageQuery);
    }



    @GetMapping("configList")
    public R<List<EpsAlarmConfig>> configList() {
        return R.ok(iZhEpsAlarmConfigService.queryList(new ZhEpsAlarmConfigBo()));
    }

    /**
     * 获取告警信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<EpsAlarmConfig> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iZhEpsAlarmConfigService.queryById(id));
    }

    /**
     * 修改告警信息
     */
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody List<ZhEpsAlarmConfigBo> bo) {
        return toAjax(iZhEpsAlarmConfigService.updateBatchByBoList(bo) ? 1 : 0);
    }
}
