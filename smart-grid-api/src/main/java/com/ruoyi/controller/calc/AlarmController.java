package com.ruoyi.controller.calc;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.ruoyi.bo.ZhEpsAlarmBo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.entity.calc.EpsEvent;
import com.ruoyi.service.calc.AlarmService;
import com.ruoyi.vo.AlarmHistoryVo;
import com.ruoyi.vo.AlarmStatisticsVo;
import com.ruoyi.vo.ZhEpsAlarmVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vo.ResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/alarm")
public class AlarmController extends BaseController {
    private final AlarmService alarmService;

    /**
     * 查询告警信息列表
     */
    @GetMapping("/pageList")
    public TableDataInfo<ZhEpsAlarmVo> list(ZhEpsAlarmBo bo, PageQuery pageQuery) {
        return alarmService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询告警设备曲线相关数据
     */
    @GetMapping("/curveListById")
    public R curveList(String id) {
        return alarmService.curveListById(id);
    }

    /**
     * 查询告警设备曲线相关数据
     */
    @GetMapping("/curveListByCaId")
    public R curveListByCaId(String id) {
        return alarmService.curveListByCaId(id);
    }

    /**
     * 周期列表
     */
    @GetMapping("periodList")
    public TableDataInfo<ZhEpsAlarmVo> periodList(@Validated(QueryGroup.class) ZhEpsAlarmBo bo, PageQuery pageQuery) {
        return alarmService.periodList(bo, pageQuery);
    }

    @GetMapping("history/statistics")
    public R<List<Map<String, Object>>> historyStatistics(Integer dimensionality) {
        return R.ok(alarmService.historyStatistics(dimensionality));
    }

    /**
     * 导出告警
     */
    @GetMapping("history/export")
    public void export(@NotNull(message = "开始时间不能为空") @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startTime,
                       @NotNull(message = "结束时间不能为空") @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime,
                       HttpServletResponse response) throws IOException {
        List<AlarmHistoryVo> list = alarmService.queryHistoryList(startTime, endTime);
        ServletOutputStream os = response.getOutputStream();
        ExcelWriterSheetBuilder builder = EasyExcel.write(os, AlarmHistoryVo.class).sheet("告警历史");
        builder.doWrite(list);
    }

    /**
     * 查询告警历史信息列表
     */
    @GetMapping("/history/pageList")
    public TableDataInfo<ZhEpsAlarmVo> historyList(ZhEpsAlarmBo bo, PageQuery pageQuery) {
        return alarmService.historyList(bo, pageQuery);
    }

    @GetMapping("equipmentList")
    public R<List<Map<String, Object>>> equipmentList(String grid) {
        return R.ok(alarmService.equipmentList(grid));
    }

    @PostMapping("processed")
    public R<Void> processed(@RequestBody ZhEpsAlarmBo bo) {
        return toAjax(alarmService.processed(bo.getId()));
    }

    /**
     * 最后计算时间
     */
    @GetMapping("/lastCalcTime")
    public R<Date> lastCalcTime() {
        return R.ok(alarmService.lastCalcTime());
    }

    /**
     * 统计告警信息
     */
    @GetMapping("/statistics")
    public R<AlarmStatisticsVo> statistics(ZhEpsAlarmBo bo) {
        return R.ok(alarmService.statistics(bo));
    }


    /**
     * 告警事件列表
     */
    @GetMapping("/detail/list")
    public R<List<EpsEvent>> detailList(@NotNull(message = "警告id不能为空") Long alarmId) {
        return R.ok(alarmService.detailList(alarmId));
    }


    @GetMapping("/configList")
    public ResultVO listConfig() {
        return alarmService.listConfig();
    }

    /**
     * 告警事件列表
     */
    @GetMapping("/event/list")
    public ResultVO listEvent(@RequestParam Long alarmId) {
        return alarmService.listEvent(alarmId);
    }

    /**
     * 删除警告
     */
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable Integer id) {
        return toAjax(alarmService.deleteWithValidById(id));
    }
}
