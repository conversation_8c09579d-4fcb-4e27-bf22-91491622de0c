package com.ruoyi.controller.calc;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.aop.anno.IgnoreRestControllerResponseAdvice;
import com.ruoyi.entity.calc.AlarmDoMain;
import com.ruoyi.entity.calc.CalcParams;
import com.ruoyi.service.calc.TrendCalcService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 潮流计算模块
 * <AUTHOR> developer
 * @date 2024-09-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/trend")
@SaIgnore
public class TrendCalcController extends BaseController {
    private final TrendCalcService trendCalcService;

    /**
     * 根据线路id查询供电范围
     */
    @GetMapping("/findPowerRangeByFeeder")
    public R<?> findPowerRangeByFeeder(String feederId) {
        return trendCalcService.findPowerRangeByFeeder(feederId);
    }


    /**
     * 潮流计算接口
     */
    @PostMapping("/calc")
    @IgnoreRestControllerResponseAdvice()
    public  R<?>  calc(@RequestBody CalcParams param) {
        return trendCalcService.calc(param);
    }

    /**
     * 潮流计算接口（根据网格计算）
     */
    @PostMapping("/calcSimulation")
    @IgnoreRestControllerResponseAdvice()
    public  R<?>  calcSimulation(@RequestBody Map<String,Object> params) {
        return trendCalcService.calcSimulation(params);
    }


    /**
     * 根据杆塔id查询连接的导线段信息
     */
    @GetMapping("/querySegmentByPsrIdAndPsrType")
    public R querySegmentByPsrIdAndPsrType(String psrId, String psrType) {
        return trendCalcService.querySegmentByPsrIdAndPsrType(psrId, psrType);
    }


    /**
     * 根据msgId查询告警
     */
    @GetMapping("/alarmByMsgId")
    public TableDataInfo<AlarmDoMain> alarmByMsgId(PageQuery pageQuery, String msgId) {
        return trendCalcService.alarmByMsgId(pageQuery, msgId);
    }



}
