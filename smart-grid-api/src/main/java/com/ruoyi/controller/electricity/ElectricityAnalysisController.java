package com.ruoyi.controller.electricity;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.electricity.bo.AnalysisCustBo;
import com.ruoyi.entity.electricity.bo.IndustryPeakAnalysisBo;
import com.ruoyi.entity.electricity.vo.AnalysisCustVo;
import com.ruoyi.entity.electricity.vo.IndustryPeakAnalysisVo;
import com.ruoyi.entity.electricity.bo.LoadMWDimensionAnalysisBo;
import com.ruoyi.entity.electricity.vo.LoadMWDimensionAnalysisVo;
import com.ruoyi.entity.electricity.vo.SingleUserAnalysisVo;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.Statistics;
import com.ruoyi.service.electricity.ElectricityAnalysisService;
import com.ruoyi.trans.core.TranslateResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用电分析
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/electricity")
@SaIgnore
public class ElectricityAnalysisController extends BaseController {


    private final ElectricityAnalysisService electricityAnalysisService;



    /**
     * 查询行业类别的下拉菜单
     */
    @GetMapping("/pullDownMenuIndustry")
    public R<List<PullDownMenuStringSon>> pullDownMenuIndustry() {
        return R.ok(electricityAnalysisService.pullDownMenuIndustry());
    }

    /**
     * 查询单用户的用电分析
     */
    @GetMapping("/singleUserElectricityAnalysis/{userCode}")
    public R<SingleUserAnalysisVo> singleUserElectricityAnalysis(@NotNull(message = "用户编码不能为空") @PathVariable Long userCode) {
        return R.ok(electricityAnalysisService.singleUserElectricityAnalysis(userCode));
    }

    /**
     * 行业用户用电高峰时段分析
     */
    @PostMapping("/industryElectricityPeakAnalysis")
    public R<IndustryPeakAnalysisVo> industryElectricityPeakAnalysis(@RequestBody IndustryPeakAnalysisBo bo) {
        return R.ok(electricityAnalysisService.industryElectricityPeakAnalysis(bo));
    }

    /**
     * 负荷多维度聚合
     */
    @PostMapping("/LoadMWDimensionAnalysis")
    public R<Page<LoadMWDimensionAnalysisVo>> LoadMWDimensionAnalysis(@RequestBody LoadMWDimensionAnalysisBo bo) {
        return R.ok(electricityAnalysisService.LoadMWDimensionAnalysis(bo));
    }

    /**
     * 网格下所有用户，加上筛选条件
     */
    @PostMapping("/selectGridByAnalysisCust")
    @TranslateResponse(extractPropertiesToTile = true)
    public TableDataInfo<AnalysisCustVo> selectGridByAnalysisCust(@RequestBody AnalysisCustBo bo) {
        return electricityAnalysisService.selectGridByAnalysisCust(bo);
    }

    /**
     * 行业类别
     */
    @GetMapping("/selectElectricityAnalysisIndustry")
    public  R<List<Statistics>> selectElectricityAnalysisIndustry() {
        return R.ok(electricityAnalysisService.selectElectricityAnalysisIndustry());
    }

    /**
     * 行业类别
     */
    @GetMapping("/selectElectricityAnalysisType")
    public   R<List<Statistics>> selectElectricityAnalysisType() {
        return R.ok(electricityAnalysisService.selectElectricityAnalysisType());
    }

    /**
     * 电压等级
     */
    @GetMapping("/selectVoltage")
    public   R<List<Statistics>> selectVoltage() {
        return R.ok(electricityAnalysisService.selectVoltage());
    }

    /**
     * 用户重要程度
     */
    @GetMapping("/selectUserImp")
    public   R<List<Statistics>> selectUserImp() {
        return R.ok(electricityAnalysisService.selectUserImp());
    }

    /**
     *配电网——添加假的用电记录
     */
    @GetMapping("/testElectricity")
    public R<Void> testElectricity() throws Exception {
        return toAjax(electricityAnalysisService.testElectricity()? 1: 0);
    }
}
