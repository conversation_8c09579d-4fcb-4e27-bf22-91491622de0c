package com.ruoyi.controller.electricity;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.vo.RouteVo;
import com.ruoyi.service.electricity.ISynchronousGridService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 同步数据
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/synchronous/grid")
@SaIgnore
public class SynchronousGridController extends BaseController {
    private final ISynchronousGridService iSynchronousGridService;

    /**
     * 同步行业用户的网格
     */
    @GetMapping("/synchronousCustAsGrid")
    public R<Void> synchronousCustAsGrid() {
        return toAjax(iSynchronousGridService.synchronousCustAsGrid() ? 1 : 0);
    }


    /**
     * 物理杆塔的坐标换成国网坐标
     */
    @GetMapping("/wlgtCoordinates")
    public void wlgtCoordinates() throws Exception {
       iSynchronousGridService.wlgtCoordinates();

    }

    /**
     * 终端的坐标换成国网坐标
     */
    @GetMapping("/cableTerminalJointcoordinates")
    public void cableTerminalJointcoordinates() throws Exception {
       iSynchronousGridService.cableTerminalJointcoordinates();
    }
    /**
     * 同步一个线路附近的线路
     */
    @GetMapping("/nearFeeder/{feederId}")
    public void nearFeeder(@PathVariable String feederId) {
        iSynchronousGridService.nearFeeder(feederId);
    }


    /**
     * 同步一个线路附近的线路
     */
    @GetMapping("/test")
    public void test() {
        iSynchronousGridService.test();
    }

    /**
     * 再此确认导入的问题是否是我们配置的规则下的问题
     */
    @GetMapping("/problemMyRule")
    public void problemMyRule() {
        iSynchronousGridService.problemMyRule();
    }

}
