<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhr.myapp</groupId>
        <artifactId>smart-grid</artifactId>
        <version>1.0-SNAPSHOT</version>

    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>com.ruoyi.WebApplication</mainClass> <!-- 替换为你的主类全限定名 -->
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <artifactId>smart-grid-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-service</artifactId>
        </dependency>
    </dependencies>
</project>
