--- # 安全配置
# 生产环境关闭swagger
swagger:
  enabled: false
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
# 默认关闭Spring Actuator
znap-service:
  powerRangeUrl: http://*************:8184/trend_calc/system/trend/powerRange
  calcUrl: http://*************:8184/trend_calc/system/trend/clac
management:
  server:
    port: -1
  endpoints:
    enabled-by-default: false
security:
  excludes:
    - /magic/**

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
#    port: 6077
    # 数据库索引
    database: 12
    # 密码
    password: 123456
#    password: spueo0tIzzwkAvAfIj30ROpJA8aM6UJz
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

--- # 数据源配置（按照设计应用开发模块只需要设计自己的yml配置中数据源参数，其他都统一设计，便于按照微服务方式统一部署）
spring:
  datasource:
    #    sql监控 http://localhost:8080/smart-grid/druid/index.html
    druid:
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
        resetEnable: false
    # type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: org.postgresql.Driver
          url: *********************************************
          username: znap_bs_zj
          password: 123456
        slave:
          lazy: true
          driverClassName: org.postgresql.Driver
          url: *****************************************************************
          username: znap_bs_pw
          password: 123456

--- # magic-api 配置
magic-api:
  security: # 安全配置
    username: admin # 登录用的用户名
    password: admin@qwer!@#$1234 # 登录用的密码
  secret-key: admin@qwer!@#$1234 # 远程推送时的秘钥，未配置则不开启推送
