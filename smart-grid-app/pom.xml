<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhr.myapp</groupId>
        <artifactId>smart-grid</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>smart-grid-app</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhr</groupId>
            <artifactId>ruoyi-admin-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zhr</groupId>
                    <artifactId>ruoyi-workflow</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
