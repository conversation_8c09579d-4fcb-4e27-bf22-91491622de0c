<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.zhr</groupId>
    <artifactId>ruoyi-vue-zboot</artifactId>
    <version>2.0-SNAPSHOT</version>
  </parent>
  <artifactId>smart-grid</artifactId>
  <groupId>com.zhr.myapp</groupId>
  <description>zboot Web Application Archetype</description>
  <version>1.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.zhr.myapp</groupId>
        <artifactId>smart-grid-api</artifactId>
        <version>1.0-SNAPSHOT</version>

      </dependency>
      <dependency>
        <groupId>com.zhr.myapp</groupId>
        <artifactId>smart-grid-service</artifactId>
        <version>1.0-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <profiles.active>dev</profiles.active>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <profiles.active>prod</profiles.active>
      </properties>
    </profile>
  </profiles>
  <modules>
    <module>smart-grid-api</module>
    <module>smart-grid-service</module>
    <module>smart-grid-app</module>
    <module>smart-grid-common</module>
  </modules>
</project>
