/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 03/06/2025 16:58:02
*/


-- ----------------------------
-- Table structure for problem_rule_configuration
-- ----------------------------
DROP TABLE IF EXISTS "public"."problem_rule_configuration";
CREATE TABLE "public"."problem_rule_configuration" (
  "id" int8 NOT NULL,
  "category_level2" varchar(255) COLLATE "pg_catalog"."default",
  "problem_situation" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "type" int2,
  "condition1" varchar(255) COLLATE "pg_catalog"."default",
  "condition_max1" int4,
  "condition_min1" int4,
  "condition2" varchar(255) COLLATE "pg_catalog"."default",
  "condition_max2" int4,
  "condition_min2" int4,
  "unit1" varchar(255) COLLATE "pg_catalog"."default",
  "unit2" varchar(255) COLLATE "pg_catalog"."default",
  "category_level2_code" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."problem_rule_configuration"."id" IS '主键id';
COMMENT ON COLUMN "public"."problem_rule_configuration"."category_level2" IS '二级分类';
COMMENT ON COLUMN "public"."problem_rule_configuration"."problem_situation" IS '问题情况';
COMMENT ON COLUMN "public"."problem_rule_configuration"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."problem_rule_configuration"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."problem_rule_configuration"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."problem_rule_configuration"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."problem_rule_configuration"."type" IS '计算方式（0：条件+数量，1：条件+条件+数量，2：条件，3：条件+数量，条件+数量）';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition1" IS '条件1';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition_max1" IS '最大数量1（不包含）';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition_min1" IS '最小数量1（包含）';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition2" IS '条件2';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition_max2" IS '最大数量2（不包含）';
COMMENT ON COLUMN "public"."problem_rule_configuration"."condition_min2" IS '最小数量2（包含）';
COMMENT ON TABLE "public"."problem_rule_configuration" IS '规则配置表';

-- ----------------------------
-- Records of problem_rule_configuration
-- ----------------------------
INSERT INTO "public"."problem_rule_configuration" VALUES (1912697230228381697, 'test', 'test', 'admin', '2025-04-17 10:38:50.54', 'admin', '2025-04-17 10:38:50.54', 1, 'test', NULL, NULL, 'test', 80, NULL, NULL, NULL, NULL);
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061594819543042, '站房母线馈供用户过多', '一般问题', 'admin', '2025-04-15 16:33:03.26', 'admin', '2025-04-15 16:33:03.26', 0, '母线馈供用户', 10, 5, NULL, NULL, NULL, '户', NULL, '2');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061501060071425, '站房母线馈供用户过多', '重点问题', 'admin', '2025-04-15 16:32:40.909', 'admin', '2025-04-15 16:32:40.909', 0, '母线馈供用户', NULL, 10, NULL, NULL, NULL, '户', NULL, '2');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912072196271030273, '多回架空线路同杆架设', '重点问题', 'admin', '2025-04-15 17:15:10.843', 'admin', '2025-04-15 17:15:10.843', 0, '同杆', NULL, 5, NULL, NULL, NULL, '回', NULL, '9');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912072474043006977, '多回架空线路同杆架设', '一般问题', 'admin', '2025-04-15 17:16:17.069', 'admin', '2025-04-15 17:16:17.069', 0, '同杆', 5, 4, NULL, NULL, NULL, '回', NULL, '9');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063917193084930, '同母联络', '一般问题', 'admin', '2025-04-15 16:42:16.955', 'admin', '2025-04-15 16:42:16.955', 2, 'D类', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063896112513025, '同母联络', '一般问题', 'admin', '2025-04-15 16:42:11.933', 'admin', '2025-04-15 16:42:11.933', 2, 'C类', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063853129285634, '同母联络', '重点问题', 'admin', '2025-04-15 16:42:01.684', 'admin', '2025-04-15 16:42:01.684', 2, 'B类', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063835152498689, '同母联络', '重点问题', 'admin', '2025-04-15 16:41:57.398', 'admin', '2025-04-15 16:41:57.398', 2, 'A类', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063809730822146, '同母联络', '重点问题', 'admin', '2025-04-15 16:41:51.336', 'admin', '2025-04-15 16:41:51.336', 2, 'A+类', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912073771848744961, '线路可开放能力不足', '重点问题', 'admin', '2025-04-15 17:21:26.488', 'admin', '2025-04-15 17:21:26.488', 0, '拟新增负荷接入后最大负载率', NULL, 100, NULL, NULL, NULL, '%', NULL, '11');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912062115395584002, '单辐射线路', '重点问题', 'admin', '2025-04-15 16:35:07.375', 'admin', '2025-04-15 16:35:07.375', 1, 'A类', NULL, NULL, '挂接用户', NULL, 3, NULL, '户', '3');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912062160664707074, '单辐射线路', '重点问题', 'admin', '2025-04-15 16:35:18.167', 'admin', '2025-04-15 16:35:18.167', 1, 'D类', NULL, NULL, '挂接用户', NULL, 3, NULL, '户', '3');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912063307559387137, '单辐射线路', '一般问题', 'admin', '2025-04-15 16:39:51.608', 'admin', '2025-04-15 16:39:51.608', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '3');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912073818510376962, '线路可开放能力不足', '一般问题', 'admin', '2025-04-15 17:21:37.613', 'admin', '2025-04-15 17:21:37.613', 0, '拟新增负荷接入后最大负载率', NULL, 70, NULL, NULL, NULL, '%', NULL, '11');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912073376925663234, '线路挂接配变过多', '一般问题', 'admin', '2025-04-15 17:19:52.331', 'admin', '2025-04-15 17:19:52.331', 0, '挂接配变', NULL, 50, NULL, NULL, NULL, '台', NULL, '10');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912073099426316290, '线路挂接配变过多', '重点问题', 'admin', '2025-04-15 17:18:46.171', 'admin', '2025-04-15 17:18:46.171', 3, '挂接配变', NULL, 80, '最大负载率', NULL, 60, '台', '%', '10');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912074823138459649, '配变重过载', '重点问题', 'admin', '2025-04-15 17:25:37.135', 'admin', '2025-04-15 17:25:37.135', 3, '负载率', NULL, 95, '持续时间', NULL, 2, '%', '小时', '18');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912075086352007169, '配变重过载', '一般问题', 'admin', '2025-04-15 17:26:39.89', 'admin', '2025-04-15 17:26:39.89', 3, '负载率', 95, 80, '持续时间', NULL, 2, '%', '小时', '18');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912075576963940353, '配变可开放容量不足', '重点问题', 'admin', '2025-04-15 17:28:36.863', 'admin', '2025-04-15 17:28:36.863', 0, '拟新增负荷接入后最大负载率', NULL, 100, NULL, NULL, NULL, '%', NULL, '17');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912075620416929793, '配变可开放容量不足', '一般问题', 'admin', '2025-04-15 17:28:47.221', 'admin', '2025-04-15 17:28:47.221', 0, '拟新增负荷接入后最大负载率', 100, 80, NULL, NULL, NULL, '%', NULL, '17');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912074528991920129, '线路重过载', '一般问题', 'admin', '2025-04-15 17:24:27.005', 'admin', '2025-04-15 17:24:27.005', 3, '负载率', NULL, 70, '持续时间', 2, 0, '%', '小时', '13');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912074468992401410, '线路重过载', '重点问题', 'admin', '2025-04-15 17:24:12.7', 'admin', '2025-04-15 17:24:12.7', 3, '负载率', NULL, 70, '持续时间', NULL, 2, '%', '小时', '13');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060287891185666, '分段内配变数量不合理', '重点问题', 'admin', '2025-04-15 16:27:51.653', 'admin', '2025-04-15 16:27:51.653', 0, 'A类', NULL, 15, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060325027553281, '分段内配变数量不合理', '重点问题', 'admin', '2025-04-15 16:28:00.518', 'admin', '2025-04-15 16:28:00.518', 0, 'B类', NULL, 20, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060390819405826, '分段内配变数量不合理', '重点问题', 'admin', '2025-04-15 16:28:16.203', 'admin', '2025-04-15 16:28:16.203', 0, 'D类', NULL, 25, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060826016202754, '分段内配变数量不合理', '一般问题', 'admin', '2025-04-15 16:29:59.951', 'admin', '2025-04-15 16:29:59.951', 0, 'A+类', 15, 10, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061171245170690, '分段内配变数量不合理', '一般问题', 'admin', '2025-04-15 16:31:22.26', 'admin', '2025-04-15 16:31:22.26', 0, 'B类', 20, 15, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061102278230018, '分段内配变数量不合理', '一般问题', 'admin', '2025-04-15 16:31:05.832', 'admin', '2025-04-15 16:31:05.832', 0, 'A类', 15, 10, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061239171923969, '分段内配变数量不合理', '一般问题', 'admin', '2025-04-15 16:31:38.468', 'admin', '2025-04-15 16:31:38.468', 0, 'C类', 25, 20, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060369126465537, '分段内配变数量不合理', '重点问题', 'admin', '2025-04-15 16:28:11.032', 'admin', '2025-04-15 16:28:11.032', 0, 'C类', NULL, 25, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912061254929924097, '分段内配变数量不合理', '一般问题', 'admin', '2025-04-15 16:31:42.225', 'admin', '2025-04-18 10:10:22.606', 0, 'D类', 25, 17, '', NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912060244303978497, '分段内配变数量不合理', '重点问题', 'admin', '2025-04-15 16:27:41.26', 'admin', '2025-04-18 10:06:04.286', 0, 'A+类', NULL, 17, NULL, NULL, NULL, '台', NULL, '1');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064580077666305, '大分支无联络', '一般问题', 'admin', '2025-04-15 16:44:55', 'admin', '2025-04-15 16:44:55', 0, '大分支挂接配变', 20, 10, NULL, NULL, NULL, '台', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064455175487490, '大分支无联络', '重点问题', 'admin', '2025-04-15 16:44:25.223', 'admin', '2025-04-15 16:44:25.223', 0, '最大负荷', NULL, 3, NULL, NULL, NULL, 'MW', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064740090363906, '大分支无联络', '一般问题', 'admin', '2025-04-15 16:45:33.149', 'admin', '2025-04-15 16:45:33.149', 0, '最大负荷', 3, 2, NULL, NULL, NULL, 'MW', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064382328815618, '大分支无联络', '重点问题', 'admin', '2025-04-15 16:44:07.853', 'admin', '2025-04-15 16:44:07.853', 0, '装机容量', NULL, 5000, NULL, NULL, NULL, 'kVA', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064657403854849, '大分支无联络', '一般问题', 'admin', '2025-04-15 16:45:13.435', 'admin', '2025-04-15 16:45:13.435', 0, '装机容量', 5000, 3000, NULL, NULL, NULL, 'kVA', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912064254687756289, '大分支无联络', '重点问题', 'admin', '2025-04-15 16:43:37.421', 'admin', '2025-04-15 16:43:37.421', 0, '大分支挂接配变', NULL, 20, NULL, NULL, NULL, '台', NULL, '7');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912076170919329793, '不满足节能降损要求', '一般问题', 'admin', '2025-04-15 17:30:58.472', 'admin', '2025-04-15 17:30:58.472', 1, 'S9及以下型号', NULL, NULL, '负载', 80, NULL, NULL, NULL, '42');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912062144596328449, '单辐射线路', '重点问题', 'admin', '2025-04-15 16:35:14.336', 'admin', '2025-04-15 16:35:14.336', 1, 'B类', NULL, NULL, '挂接用户', NULL, 3, NULL, '户', '3');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912062096340860929, '单辐射线路', '重点问题', 'admin', '2025-04-15 16:35:02.826', 'admin', '2025-04-15 16:35:02.826', 1, 'A+类', NULL, NULL, '挂接用户', NULL, 3, NULL, '户', '3');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912066855789576194, '线路供电半径过长', '重点问题', 'admin', '2025-04-15 16:53:57.573', 'admin', '2025-04-15 16:53:57.573', 3, '线路总长', NULL, 15, '最大负载率', NULL, 60, 'Km', '%', '8');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912071628454543361, '线路供电半径过长', '一般问题', 'admin', '2025-04-15 17:12:55.464', 'admin', '2025-04-15 17:12:55.464', 3, '供电半径', NULL, 15, '最大负载率', 60, NULL, 'Km', '%', '8');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912071747774103553, '线路供电半径过长', '一般问题', 'admin', '2025-04-15 17:13:23.912', 'admin', '2025-04-15 17:13:23.912', 3, '线路总长', NULL, 30, '最大负载率', 60, NULL, 'Km', '%', '8');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912066771651837953, '线路供电半径过长', '重点问题', 'admin', '2025-04-15 16:53:37.512', 'admin', '2025-04-15 16:53:37.512', 3, '供电半径', NULL, 15, '最大负载率', NULL, 60, 'Km', '%', '8');
INSERT INTO "public"."problem_rule_configuration" VALUES (1912076099611967490, '不满足节能降损要求', '重点问题', 'admin', '2025-04-15 17:30:41.471', 'admin', '2025-04-15 17:30:41.471', 1, 'S9及以下型号', NULL, NULL, '负载', NULL, 80, NULL, NULL, '42');

-- ----------------------------
-- Primary Key structure for table problem_rule_configuration
-- ----------------------------
ALTER TABLE "public"."problem_rule_configuration" ADD CONSTRAINT "problem_condition_strategy_pkey" PRIMARY KEY ("id");
