/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 03/06/2025 16:49:01
*/


-- ----------------------------
-- Table structure for plan_analysis_state
-- ----------------------------
DROP TABLE IF EXISTS "public"."plan_analysis_state";
CREATE TABLE "public"."plan_analysis_state" (
  "id" int8 NOT NULL,
  "problem_id" int8,
  "state" int2,
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "version" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."plan_analysis_state"."problem_id" IS '问题id';
COMMENT ON COLUMN "public"."plan_analysis_state"."state" IS '分析过程状态(0分析异常，1是分析结束，2是分析中，3是分析停止)';
COMMENT ON COLUMN "public"."plan_analysis_state"."start_time" IS '起始时间';
COMMENT ON COLUMN "public"."plan_analysis_state"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."plan_analysis_state"."version" IS '版本号';

-- ----------------------------
-- Primary Key structure for table plan_analysis_state
-- ----------------------------
ALTER TABLE "public"."plan_analysis_state" ADD CONSTRAINT "plan_analysis_state_pkey" PRIMARY KEY ("id");
