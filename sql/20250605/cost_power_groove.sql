/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:29
*/


-- ----------------------------
-- Table structure for cost_power_groove
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_power_groove";
CREATE TABLE "public"."cost_power_groove" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(255) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(255) COLLATE "pg_catalog"."default",
  "max_current" varchar(255) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_power_groove"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_power_groove"."design_name" IS '名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_power_groove"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_power_groove"."max_current" IS '孔数规模';
COMMENT ON COLUMN "public"."cost_power_groove"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_power_groove"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_power_groove"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_power_groove"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_power_groove"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_power_groove"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_power_groove"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."cost_power_groove" IS '电力沟槽造价表';

-- ----------------------------
-- Records of cost_power_groove
-- ----------------------------
INSERT INTO "public"."cost_power_groove" VALUES ('1', 'B-6-24', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧6层24根', 300, 240, 27, 21, 12, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('2', 'B-5-20', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧5层20根', 250, 200, 22.5, 17.5, 10, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('3', 'B-2-8', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧2层8根', 100, 80, 9, 7, 4, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('4', 'B-6-12', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '单侧6层12根', 150, 120, 13.5, 10.5, 6, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('5', 'B-5-10', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '单侧5层10根', 125, 100, 11.25, 8.75, 5, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('6', 'A-3-6', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '单侧3层6根', 75, 60, 6.75, 5.25, 3, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('7', 'A-2-4', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '单侧2层4根', 50, 40, 4.5, 3.5, 2, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('8', 'A-4-8', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '单侧4层8根', 100, 80, 9, 7, 4, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('9', 'B-4-16', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧4层16根', 200, 160, 18, 14, 8, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('10', 'B-3-12', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧3层12根', 150, 120, 13.5, 10.5, 6, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('11', 'D-4模块（四回路）', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧4层16根', 5300, 1500, 2500, 700, 600, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
INSERT INTO "public"."cost_power_groove" VALUES ('12', 'D-2模块（双回路）', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '双侧2层8根', 5300, 1500, 2500, 700, 600, '2025-03-28 16:10:55.459384+08', '2025-03-28 16:10:55.459384+08');
