/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:35
*/


-- ----------------------------
-- Table structure for cost_power_tunnel
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_power_tunnel";
CREATE TABLE "public"."cost_power_tunnel" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(255) COLLATE "pg_catalog"."default",
  "duct_type" varchar(50) COLLATE "pg_catalog"."default",
  "max_current" varchar(255) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now(),
  "construct_form" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."cost_power_tunnel"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_power_tunnel"."design_name" IS '名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_power_tunnel"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_power_tunnel"."duct_type" IS '排管类型';
COMMENT ON COLUMN "public"."cost_power_tunnel"."max_current" IS '孔数规模';
COMMENT ON COLUMN "public"."cost_power_tunnel"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_power_tunnel"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_power_tunnel"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_power_tunnel"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_power_tunnel"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_power_tunnel"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_power_tunnel"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."cost_power_tunnel"."construct_form" IS '建设形式';
COMMENT ON TABLE "public"."cost_power_tunnel" IS '电力隧道造价表';

-- ----------------------------
-- Records of cost_power_tunnel
-- ----------------------------
INSERT INTO "public"."cost_power_tunnel" VALUES ('1', 'A-1-≥12', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '明开', '单侧≥12根', 4900, 3920, 441, 343, 196, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
INSERT INTO "public"."cost_power_tunnel" VALUES ('2', 'A-2-≥16', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '明开', '双侧≥16根', 7000, 5600, 630, 490, 280, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
INSERT INTO "public"."cost_power_tunnel" VALUES ('3', 'B-1-≥12', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '暗挖', '单侧≥12根', 8000, 6400, 720, 560, 320, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
INSERT INTO "public"."cost_power_tunnel" VALUES ('4', 'B-2-≥16', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '暗挖', '双侧≥16根', 10800, 8640, 972, 756, 432, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
INSERT INTO "public"."cost_power_tunnel" VALUES ('5', 'C-1-≥12', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '盾构', '单侧≥12根', 12800, 10240, 1152, 896, 512, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
INSERT INTO "public"."cost_power_tunnel" VALUES ('6', 'C-2-≥16', '交流10kv,交流35kv,交流66kv,交流110kv,交流220kv', '盾构', '双侧≥16根', 16700, 13360, 1503, 1169, 668, '2025-03-28 16:13:05.015009+08', '2025-03-28 16:13:05.015009+08', NULL);
