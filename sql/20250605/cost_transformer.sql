/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:56
*/


-- ----------------------------
-- Table structure for cost_transformer
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_transformer";
CREATE TABLE "public"."cost_transformer" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "capacity" int8,
  "voltage_ratio" varchar(20) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_transformer"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_transformer"."design_name" IS '名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_transformer"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_transformer"."capacity" IS '容量';
COMMENT ON COLUMN "public"."cost_transformer"."voltage_ratio" IS '电压变比';
COMMENT ON COLUMN "public"."cost_transformer"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_transformer"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_transformer"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_transformer"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_transformer"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_transformer"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_transformer"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."cost_transformer" IS '变压器造价表';

-- ----------------------------
-- Records of cost_transformer
-- ----------------------------
INSERT INTO "public"."cost_transformer" VALUES ('1', '10-200-10/0.4', '10kV', 200, '10/0.4', 7.36, 6.94, 0, 0.27, 0.15, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('2', '10-400-10/0.4', '10kV', 400, '10/0.4', 7.99, 7.52, 0, 0.31, 0.16, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('3', '10-500-10/0.4', '10kV', 500, '10/0.4', 11.27, 10.8, 0, 0.31, 0.16, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('4', '10-800-10/0.4', '10kV', 800, '10/0.4', 13.58, 13.02, 0, 0.4, 0.16, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('5', '10-630-10/0.4', '10kV', 630, '10/0.4', 11.53, 10.97, 0, 0.4, 0.16, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('6', '10-1000-10/0.4', '10kV', 1000, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('7', '10-1250-10/0.4', '10kV', 1250, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('8', '10-630-10/0.4-1', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('9', '10-630-10/0.4-2', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('10', '10-630-10/0.4-3', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('11', '10-630-10/0.4-4', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('12', '10-630-10/0.4-5', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('13', '10-630-10/0.4-6', '10kV', 630, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('14', '10-500-10/0.4-1', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('15', '10-500-10/0.4-2', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('16', '10-500-10/0.4-3', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('17', '10-500-10/0.4-4', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('18', '10-500-10/0.4-5', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('19', '10-500-10/0.4-6', '10kV', 500, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('20', '10-400-10/0.4-1', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('21', '10-400-10/0.4-2', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('22', '10-400-10/0.4-6', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('23', '10-400-10/0.4-3', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('24', '10-400-10/0.4-4', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
INSERT INTO "public"."cost_transformer" VALUES ('25', '10-400-10/0.4-5', '10kV', 400, '10/0.4', 0, 0, 0, 0, 0, '2025-03-28 16:18:51.751406+08', '2025-03-28 16:18:51.751406+08');
