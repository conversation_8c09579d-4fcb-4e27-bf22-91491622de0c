/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:14:38
*/


-- ----------------------------
-- Table structure for cost_box_type_substation
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_box_type_substation";
CREATE TABLE "public"."cost_box_type_substation" (
  "id" int8 NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "bus_connection" varchar(20) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now(),
  "current_number" int8,
  "end_number" int8,
  "capacity" int8,
  "inline_int" int8,
  "outline_int" int8
)
;
COMMENT ON COLUMN "public"."cost_box_type_substation"."id" IS '自增主键';
COMMENT ON COLUMN "public"."cost_box_type_substation"."design_name" IS '设计名称(XA-2-B)';
COMMENT ON COLUMN "public"."cost_box_type_substation"."voltage_level" IS '电压等级(10kV)';
COMMENT ON COLUMN "public"."cost_box_type_substation"."bus_connection" IS '母线连接方式(单母线)';
COMMENT ON COLUMN "public"."cost_box_type_substation"."total_cost" IS '综合造价（万元/座）';
COMMENT ON COLUMN "public"."cost_box_type_substation"."purchase_cost" IS '设备购置费（万元/座）';
COMMENT ON COLUMN "public"."cost_box_type_substation"."building_cost" IS '建筑工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_box_type_substation"."install_cost" IS '安装工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_box_type_substation"."other_cost" IS '其他费用（万元/座）';
COMMENT ON COLUMN "public"."cost_box_type_substation"."current_number" IS '配变台数-本期';
COMMENT ON COLUMN "public"."cost_box_type_substation"."end_number" IS '配变台数-终期';
COMMENT ON COLUMN "public"."cost_box_type_substation"."capacity" IS '容量(单位：kVA)';
COMMENT ON COLUMN "public"."cost_box_type_substation"."inline_int" IS '进线间隔';
COMMENT ON COLUMN "public"."cost_box_type_substation"."outline_int" IS '出线间隔';
COMMENT ON TABLE "public"."cost_box_type_substation" IS '箱式变电站造价表';

-- ----------------------------
-- Records of cost_box_type_substation
-- ----------------------------
INSERT INTO "public"."cost_box_type_substation" VALUES (1, 'XA-2-B', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 500, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (2, 'XA-2-C', '10kV', '单母线', 33.36, 27.62, 2.61, 1.93, 1.2, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (3, 'XA-1', '10kV', '单母线', 28.24, 22.69, 2.61, 1.88, 1.06, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 0, 0);
INSERT INTO "public"."cost_box_type_substation" VALUES (4, 'XA-2', '10kV', '单母线', 28.24, 22.69, 2.61, 1.88, 1.06, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (5, 'XA-1-C', '10kV', '单母线', 33.36, 27.62, 2.61, 1.93, 1.2, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 0, 0);
INSERT INTO "public"."cost_box_type_substation" VALUES (6, 'XA-1-B', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 500, 0, 0);
INSERT INTO "public"."cost_box_type_substation" VALUES (7, 'XA-2-3', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (9, 'XA-2-C-2', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (10, 'XA-2-C-4', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (11, 'XA-2-6', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (12, 'XA-2-5', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (13, 'XA-2-4', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (15, 'XA-2-C-3', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (16, 'XA-2-C-1', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 630, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (17, 'XA-2-0', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
INSERT INTO "public"."cost_box_type_substation" VALUES (18, 'XA-2-1', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:21:08.544241+08', '2025-03-28 16:21:08.544241+08', 1, 1, 400, 1, 1);
