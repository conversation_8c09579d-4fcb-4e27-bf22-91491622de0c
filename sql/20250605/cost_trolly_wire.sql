/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:16:01
*/


-- ----------------------------
-- Table structure for cost_trolly_wire
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_trolly_wire";
CREATE TABLE "public"."cost_trolly_wire" (
  "id" int8 NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "section" int8,
  "loop_number" varchar(20) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now(),
  "method" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."cost_trolly_wire"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_trolly_wire"."design_name" IS '设计名称(如10-4-120)';
COMMENT ON COLUMN "public"."cost_trolly_wire"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_trolly_wire"."section" IS '截面(如120)';
COMMENT ON COLUMN "public"."cost_trolly_wire"."loop_number" IS '回路数(如四回)';
COMMENT ON COLUMN "public"."cost_trolly_wire"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_trolly_wire"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_trolly_wire"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_trolly_wire"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_trolly_wire"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_trolly_wire"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_trolly_wire"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."cost_trolly_wire"."method" IS '架设方式';
COMMENT ON TABLE "public"."cost_trolly_wire" IS '架空线造价表';

-- ----------------------------
-- Records of cost_trolly_wire
-- ----------------------------
INSERT INTO "public"."cost_trolly_wire" VALUES (1, '10-A4-70', '10kV', 70, '四回', 54.18, 0, 21.33, 24.83, 8.02, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (2, '10-A4-95', '10kV', 95, '四回', 56.67, 0, 21.33, 27.1, 8.24, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (3, '10-A4-120', '10kV', 120, '四回', 59.54, 0, 21.33, 29.73, 8.48, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (4, '10-B1-150', '10kV', 150, '单回', 7.46, 0, 0, 6.69, 0.77, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (5, '10-B1-70', '10kV', 70, '单回', 3.25, 0, 0, 2.91, 0.34, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (6, '10-B1-185', '10kV', 185, '单回', 7.79, 0, 0, 6.98, 0.81, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (7, '10-A1-240', '10kV', 240, '单回', 26.03, 0, 4.32, 18.26, 3.45, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (8, '10-A1-50', '10kV', 50, '单回', 20.28, 0, 4.32, 13.07, 2.89, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (9, '10-A1-150', '10kV', 150, '单回', 24.45, 0, 4.32, 16.83, 3.3, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (10, '10-B1-50', '10kV', 50, '单回', 3.25, 0, 0, 2.91, 0.34, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (11, '10-B1-95', '10kV', 95, '单回', 3.25, 0, 0, 2.91, 0.34, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (12, '10-B1-120', '10kV', 120, '单回', 6.14, 0, 0, 5.5, 0.64, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (13, '10-B1-240', '10kV', 240, '单回', 9.04, 0, 0, 8.1, 0.94, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (14, '10-A1-70', '10kV', 70, '单回', 20.28, 0, 4.32, 13.07, 2.89, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (15, '10-A1-95', '10kV', 95, '单回', 22.79, 0, 4.32, 15.33, 3.14, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (16, '10-A1-120', '10kV', 120, '单回', 23.16, 0, 4.32, 15.67, 3.17, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (17, '10-A2-50', '10kV', 50, '双回', 21.19, 0, 4.32, 13.89, 2.98, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (18, '10-A2-70', '10kV', 70, '双回', 21.55, 0, 4.32, 14.21, 3.02, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (19, '10-A2-95', '10kV', 95, '双回', 22.81, 0, 4.32, 15.35, 3.14, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (20, '10-A2-120', '10kV', 120, '双回', 24.46, 0, 4.32, 16.84, 3.3, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (21, '10-A1-185', '10kV', 185, '单回', 24.79, 0, 4.32, 17.14, 3.33, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (22, '10-A2-150', '10kV', 150, '双回', 25.71, 0, 4.32, 17.97, 3.42, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (23, '10-A2-185', '10kV', 185, '双回', 27.34, 0, 4.32, 19.44, 3.58, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (24, '10-A2-240', '10kV', 240, '双回', 29.67, 0, 4.32, 21.55, 3.8, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (25, '10-A4-50', '10kV', 50, '四回', 47.51, 0, 21.33, 18.74, 7.44, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (26, '10-A4-150', '10kV', 150, '四回', 59.54, 0, 21.33, 29.73, 8.48, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (27, '10-A4-185', '10kV', 185, '四回', 64.92, 0, 21.33, 34.65, 8.94, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (28, '10-A4-240', '10kV', 240, '四回', 69.53, 0, 21.33, 38.87, 9.33, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (29, '10-A1-150-1', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (30, '10-A1-240-1', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (31, '10-A1-240-2', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (32, '10-A2-240-2', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (33, '10-A3-240-3', '10kV', 240, '三回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (34, '10-B1-240-1', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (35, '10-B1-240-2', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (36, '10-B1-150-1', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (37, '10-B1-150-2', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (38, '10-B1-50-1', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (39, '10-B1-50-2', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (40, '10-A1-150-2', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (41, '10-A2-240-2-2', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (42, '10-A1-50-3', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (43, '10-A1-50-2', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (44, '10-A1-150-4', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (45, '10-A1-150-5', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (46, '10-A1-240-5', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (47, '10-A2-240-3', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (48, '10-A2-240-4', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (49, '10-B2-240-1', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (50, '10-B2-240-2', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (51, '10-A1-240-6', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (52, '10-B1-240-3', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (53, '10-A1-240-4', '10kV', 240, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (54, '10-A1-150-6', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (55, '10-A1-50-4', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '新开');
INSERT INTO "public"."cost_trolly_wire" VALUES (56, '10-B1-150-3', '10kV', 150, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (57, '10-B1-50-3', '10kV', 50, '单回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (58, '10-B2-240-3', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (59, '10-B1-240-4', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
INSERT INTO "public"."cost_trolly_wire" VALUES (60, '10-B1-185-1', '10kV', 240, '双回', 0, 0, 0, 0, 0, '2025-03-28 15:38:31.899371+08', '2025-03-28 15:38:31.899371+08', '挂线');
