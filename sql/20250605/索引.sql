CREATE INDEX idx_feeder_load_psr_time ON feeder_load (psr_id, recording_time);
CREATE INDEX idx_feeder_load_mw_psr_time ON feeder_load_mw (psr_id, recording_time);
CREATE INDEX idx_transformer_load_psr_time ON transformer_load (psr_id, recording_time);
CREATE INDEX idx_transformer_load_mw_psr_time ON transformer_load_mw (psr_id, recording_time);

CREATE INDEX idx_device_station_isolate_kg_station ON device_station_isolate_kg (station );
CREATE INDEX idx_device_station_breaker_station ON device_station_breaker (station );
CREATE INDEX idx_device_station_load_kg_station ON device_station_load_kg (station );

CREATE INDEX idx_device_feeder_jk_start_pole ON device_feeder_jk (start_pole );
CREATE INDEX idx_device_feeder_jk_stop_pole ON device_feeder_jk (stop_pole );


CREATE INDEX idx_device_feeder_cable_start_position ON device_feeder_cable (start_position );
CREATE INDEX idx_device_feeder_cable_end_position ON device_feeder_cable (end_position );


