/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:05
*/


-- ----------------------------
-- Table structure for cost_pole_ubstation
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_pole_ubstation";
CREATE TABLE "public"."cost_pole_ubstation" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "capacity" int8,
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_pole_ubstation"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."design_name" IS '单元名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."capacity" IS '容量(如10)';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_pole_ubstation"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."cost_pole_ubstation" IS '柱上变电箱造价表';

-- ----------------------------
-- Records of cost_pole_ubstation
-- ----------------------------
INSERT INTO "public"."cost_pole_ubstation" VALUES ('1', '10-Z-30', '10kV', 30, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('2', '10-Z-50', '10kV', 50, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('3', '10-Z-200', '10kV', 200, 9.93, 7.24, 0, 1.94, 0.75, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('4', '10-Z-400', '10kV', 400, 12.41, 9.52, 0, 1.98, 0.91, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('5', '10-Z-100', '10kV', 100, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('6', '10-Z-400-1', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('7', '10-Z-400-2', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('8', '10-Z-400-3', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('9', '10-Z-400-4', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('10', '10-Z-400-5', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('11', '10-Z-400-6', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('12', '10-Z-400-7', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('13', '10-Z-400-8', '10kV', 400, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('14', '10-Z-200-1', '10kV', 200, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('15', '10-Z-200-2', '10kV', 200, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('16', '10-Z-200-3', '10kV', 200, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('17', '10-Z-200-4', '10kV', 200, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
INSERT INTO "public"."cost_pole_ubstation" VALUES ('18', '10-Z-315', '10kV', 315, 0, 0, 0, 0, 0, '2025-03-28 16:24:52.805315+08', '2025-03-28 16:24:52.805315+08');
