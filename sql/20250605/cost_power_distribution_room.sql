/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:17
*/


-- ----------------------------
-- Table structure for cost_power_distribution_room
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_power_distribution_room";
CREATE TABLE "public"."cost_power_distribution_room" (
  "id" int8 NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "bus_connection" varchar(20) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now(),
  "current_number" int8,
  "end_number" int8,
  "capacity" int8,
  "inline_int" int8,
  "outline_int" int8
)
;
COMMENT ON COLUMN "public"."cost_power_distribution_room"."id" IS '自增主键';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."design_name" IS '设计名称(XA-2-B)';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."voltage_level" IS '电压等级(10kV)';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."bus_connection" IS '母线连接方式(单母线)';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."total_cost" IS '综合造价（万元/座）';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."purchase_cost" IS '设备购置费（万元/座）';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."building_cost" IS '建筑工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."install_cost" IS '安装工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."other_cost" IS '其他费用（万元/座）';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."current_number" IS '配变台数-本期';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."end_number" IS '配变台数-终期';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."capacity" IS '容量(单位：kVA)';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."inline_int" IS '进线间隔';
COMMENT ON COLUMN "public"."cost_power_distribution_room"."outline_int" IS '出线间隔';
COMMENT ON TABLE "public"."cost_power_distribution_room" IS '配电室造价表';

-- ----------------------------
-- Records of cost_power_distribution_room
-- ----------------------------
INSERT INTO "public"."cost_power_distribution_room" VALUES (1, 'PB-1', '10kV', '单母线', 0, 0, 0, 0, 0, '2025-03-28 16:23:16.079845+08', '2025-03-28 16:23:16.079845+08', 2, 2, 630, 1, 1);
INSERT INTO "public"."cost_power_distribution_room" VALUES (2, 'PB-2', '10kV', '单母线', 97.9, 84.14, 0, 11.06, 2.7, '2025-03-28 16:23:16.079845+08', '2025-03-28 16:23:16.079845+08', 2, 2, 800, 1, 0);
INSERT INTO "public"."cost_power_distribution_room" VALUES (3, 'PB-5', '10kV', '单母线分段', 97.9, 84.14, 0, 11.06, 2.7, '2025-03-28 16:23:16.079845+08', '2025-03-28 16:23:16.079845+08', 6, 6, 800, 2, 6);
INSERT INTO "public"."cost_power_distribution_room" VALUES (4, 'PB-3', '10kV', '单母线分段', 0, 0, 0, 0, 0, '2025-03-28 16:23:16.079845+08', '2025-03-28 16:23:16.079845+08', 4, 4, 630, 2, 4);
INSERT INTO "public"."cost_power_distribution_room" VALUES (5, 'PB-4', '10kV', '单母线分段', 97.9, 84.14, 0, 11.06, 2.7, '2025-03-28 16:23:16.079845+08', '2025-03-28 16:23:16.079845+08', 4, 4, 800, 2, 0);
