/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:42
*/


-- ----------------------------
-- Table structure for cost_ring_cabinet
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_ring_cabinet";
CREATE TABLE "public"."cost_ring_cabinet" (
  "id" int8 NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "bus_connection" varchar(20) COLLATE "pg_catalog"."default",
  "inline_int" int8 DEFAULT 1,
  "outline_int" int8 DEFAULT 1,
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_ring_cabinet"."id" IS '自增主键';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."design_name" IS '设计名称(XA-2-B)';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."voltage_level" IS '电压等级(10kV)';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."bus_connection" IS '母线连接方式(单母线)';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."inline_int" IS '进线间隔';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."outline_int" IS '出线间隔';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."total_cost" IS '综合造价（万元/座）';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."purchase_cost" IS '设备购置费（万元/座）';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."building_cost" IS '建筑工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."install_cost" IS '安装工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_ring_cabinet"."other_cost" IS '其他费用（万元/座）';
COMMENT ON TABLE "public"."cost_ring_cabinet" IS '环柜造价表';

-- ----------------------------
-- Records of cost_ring_cabinet
-- ----------------------------
INSERT INTO "public"."cost_ring_cabinet" VALUES (1, 'HA-2', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (2, 'HB-2-B', '10kV', '单母线', 4, 8, 91.73, 70, 12, 1.75, 7.98, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (3, 'HB-2-A', '10kV', '单母线', 4, 8, 91.73, 70, 12, 1.75, 7.98, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (4, 'HB-1-B', '10kV', '单母线分段', 2, 12, 86.45, 65, 12, 1.75, 7.7, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (5, 'HA-2-B', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (6, 'HB-1', '10kV', '单母线分段', 2, 12, 86.45, 65, 12, 1.75, 7.7, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (7, 'HA-10-2-4-6', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (8, 'HA-10-1-2-2', '10kV', '单母线', 1, 2, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (9, 'HA-10-1-3', '10kV', '单母线', 1, 3, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (10, 'HA-10-1-2-1', '10kV', '单母线', 1, 2, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (11, 'HA-10-2-2', '10kV', '单母线', 2, 2, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (12, 'HA-10-1-4', '10kV', '单母线', 1, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (13, 'HA-10-2-4-5', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (14, 'HA-10-2-4-4', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (15, 'HA-10-2-4-3', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (16, 'HA-10-2-4-2', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (17, 'HA-10-2-4-1', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (18, 'HA-20-2-12', '20kV', '单母线分段', 2, 12, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (19, 'HA-10-2-12', '10kV', '单母线分段', 2, 12, 0, 0, 0, 0, 0, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (20, 'HB-3', '10kV', '单母线三分段', 4, 16, 111.01, 85, 15, 1.75, 9.26, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
INSERT INTO "public"."cost_ring_cabinet" VALUES (21, 'HA-1', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 15:58:39.010658+08', '2025-03-28 15:58:39.010658+08');
