/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:15:49
*/


-- ----------------------------
-- Table structure for cost_switch_station
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_switch_station";
CREATE TABLE "public"."cost_switch_station" (
  "id" int8 NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "bus_connection" varchar(20) COLLATE "pg_catalog"."default",
  "inline_int" int4 DEFAULT 1,
  "outline_int" int4 DEFAULT 1,
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_switch_station"."id" IS '自增主键';
COMMENT ON COLUMN "public"."cost_switch_station"."design_name" IS '设计名称(XA-2-B)';
COMMENT ON COLUMN "public"."cost_switch_station"."voltage_level" IS '电压等级(10kV)';
COMMENT ON COLUMN "public"."cost_switch_station"."bus_connection" IS '母线连接方式(单母线)';
COMMENT ON COLUMN "public"."cost_switch_station"."inline_int" IS '进线间隔';
COMMENT ON COLUMN "public"."cost_switch_station"."outline_int" IS '出线间隔';
COMMENT ON COLUMN "public"."cost_switch_station"."total_cost" IS '综合造价（万元/座）';
COMMENT ON COLUMN "public"."cost_switch_station"."purchase_cost" IS '设备购置费（万元/座）';
COMMENT ON COLUMN "public"."cost_switch_station"."building_cost" IS '建筑工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_switch_station"."install_cost" IS '安装工程费（万元/座）';
COMMENT ON COLUMN "public"."cost_switch_station"."other_cost" IS '其他费用（万元/座）';
COMMENT ON TABLE "public"."cost_switch_station" IS '开关站造价表';

-- ----------------------------
-- Records of cost_switch_station
-- ----------------------------
INSERT INTO "public"."cost_switch_station" VALUES (1, 'HA-2', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (2, 'HB-2-B', '10kV', '单母线', 4, 8, 91.73, 70, 12, 1.75, 7.98, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (3, 'HB-2-A', '10kV', '单母线', 4, 8, 91.73, 70, 12, 1.75, 7.98, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (4, 'HB-1-B', '10kV', '单母线分段', 2, 12, 86.45, 65, 12, 1.75, 7.7, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (5, 'HA-2-B', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (6, 'HB-1', '10kV', '单母线分段', 2, 12, 86.45, 65, 12, 1.75, 7.7, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (7, 'HA-10-2-4-6', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (8, 'HA-10-1-2-2', '10kV', '单母线', 1, 2, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (9, 'HA-10-1-3', '10kV', '单母线', 1, 3, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (10, 'HA-10-1-2-1', '10kV', '单母线', 1, 2, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (11, 'HA-10-2-2', '10kV', '单母线', 2, 2, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (12, 'HA-10-1-4', '10kV', '单母线', 1, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (13, 'HA-10-2-4-5', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (14, 'HA-10-2-4-4', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (15, 'HA-10-2-4-3', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (16, 'HA-10-2-4-2', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (17, 'HA-10-2-4-1', '10kV', '单母线', 2, 4, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (18, 'HA-20-2-12', '20kV', '单母线分段', 2, 12, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (19, 'HA-10-2-12', '10kV', '单母线分段', 2, 12, 0, 0, 0, 0, 0, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (20, 'HB-3', '10kV', '单母线三分段', 4, 16, 111.01, 85, 15, 1.75, 9.26, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
INSERT INTO "public"."cost_switch_station" VALUES (21, 'HA-1', '10kV', '单母线', 2, 4, 33.68, 25, 5, 1.45, 2.23, '2025-03-28 16:00:55.816629+08', '2025-03-28 16:00:55.816629+08');
