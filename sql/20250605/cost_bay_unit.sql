/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:14:29
*/


-- ----------------------------
-- Table structure for cost_bay_unit
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_bay_unit";
CREATE TABLE "public"."cost_bay_unit" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "gap_type" varchar(50) COLLATE "pg_catalog"."default",
  "facility_type" varchar(50) COLLATE "pg_catalog"."default",
  "capacity" varchar(20) COLLATE "pg_catalog"."default",
  "max_current" float8,
  "max_cut_current" float8,
  "distribution_form" varchar(50) COLLATE "pg_catalog"."default",
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cost_bay_unit"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_bay_unit"."design_name" IS '单元名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_bay_unit"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_bay_unit"."gap_type" IS '间隔类型(如电抗器单元)';
COMMENT ON COLUMN "public"."cost_bay_unit"."facility_type" IS '设备类型';
COMMENT ON COLUMN "public"."cost_bay_unit"."capacity" IS '容量(如10)';
COMMENT ON COLUMN "public"."cost_bay_unit"."max_current" IS '最大电流';
COMMENT ON COLUMN "public"."cost_bay_unit"."max_cut_current" IS '最大切断电流';
COMMENT ON COLUMN "public"."cost_bay_unit"."distribution_form" IS '配电形式';
COMMENT ON COLUMN "public"."cost_bay_unit"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_bay_unit"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_bay_unit"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_bay_unit"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_bay_unit"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_bay_unit"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_bay_unit"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."cost_bay_unit" IS '间隔单元造价表';

-- ----------------------------
-- Records of cost_bay_unit
-- ----------------------------
INSERT INTO "public"."cost_bay_unit" VALUES ('7', '10-A-630-20', '10kV', '出线单元', '站内断路器', '', 630, 20, '', 5.71, 5, 0, 0.4, 0.31, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('8', '10-A-3150-40', '10kV', '出线单元', '站内断路器', '', 3150, 40, '', 12.59, 12, 0, 0.4, 0.19, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('9', '10-A-1250-31.5', '10kV', '出线单元', '站内断路器', '', 1250, 31.5, '', 8.08, 7.5, 0, 0.4, 0.18, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('10', '10-A-1250-25', '10kV', '出线单元', '站内断路器', '', 1250, 25, '', 7.29, 6.5, 0, 0.4, 0.39, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('11', '10-A-4000-40', '10kV', '出线单元', '站内断路器', '', 4000, 40, '', 15.63, 15, 0, 0.4, 0.23, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('20', '20-A-1250-25', '20kV', '出线单元', '站内断路器', '', 1250, 25, '全户型开关柜', 0, 0, 0, 0, 0, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('21', '10-A-4000-40-变压器', '10kV', '变压器单元', '站内断路器', '', 4000, 40, '全户型开关柜', 0, 0, 0, 0, 0, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('23', '20-A-4000-40-变压器', '20kV', '变压器单元', '站内断路器', '', 4000, 40, '全户型开关柜', 0, 0, 0, 0, 0, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('5', '10-C-630-20', '10kV', '出线单元', '站内负荷开关', '', 630, 20, '', 5.41, 5, 0, 0.25, 0.16, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('1', '10-D-10', '10kV', '电抗器单元', '', '10', 0, 0, '', 91.28, 89.78, 0, 0.83, 0.67, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('2', '10-D-6', '10kV', '电抗器单元', '', '6', 0, 0, '', 67.67, 66.43, 0, 0.56, 0.68, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('3', '10-D-5', '10kV', '电抗器单元', '', '5', 0, 0, '', 66.98, 65.74, 0, 0.56, 0.68, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('4', '10-D-3.33', '10kV', '电抗器单元', '', '3.33', 0, 0, '', 43.56, 42.64, 0, 0.56, 0.36, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('12', '10-E-1', '10kV', '电容器单元', '', '1', 0, 0, '', 10.14, 8.07, 0, 1.46, 0.61, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('13', '10-E-2', '10kV', '电容器单元', '', '2', 0, 0, '', 11.62, 9.48, 0, 1.46, 0.68, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('14', '10-E-3', '10kV', '电容器单元', '', '3', 0, 0, '', 14.22, 11.62, 0, 1.77, 0.83, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('15', '10-E-3.6/4', '10kV', '电容器单元', '', '3.6/4', 0, 0, '', 15.33, 12.68, 0, 1.77, 0.88, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('16', '10-E-4.8/5', '10kV', '电容器单元', '', '4.8/5', 0, 0, '', 18.79, 15.74, 0, 1.99, 1.06, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('17', '10-E-6', '10kV', '电容器单元', '', '6', 0, 0, '', 19.8, 16.71, 0, 1.99, 1.1, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('18', '10-E-8', '10kV', '电容器单元', '', '8', 0, 0, '', 25.32, 21.64, 0, 2.31, 1.37, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('19', '10-E-10', '10kV', '电容器单元', '', '10', 0, 0, '', 29.49, 25.64, 0, 2.31, 1.54, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('22', '20-E-6', '20kV', '电容器单元', '站内断路器', '6', 0, 0, '', 0, 0, 0, 0, 0, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
INSERT INTO "public"."cost_bay_unit" VALUES ('24', '20-D-5', '20kV', '电抗器单元', '', '5', 0, 0, '', 0, 0, 0, 0, 0, '2025-03-28 16:16:57.177875+08', '2025-03-28 16:16:57.177875+08');
