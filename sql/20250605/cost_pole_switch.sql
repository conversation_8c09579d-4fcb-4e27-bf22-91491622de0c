/*
 Navicat Premium Data Transfer

 Source Server         : **************_6000
 Source Server Type    : PostgreSQL
 Source Server Version : 100015 (100015)
 Source Host           : **************:6000
 Source Catalog        : smart_grid
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100015 (100015)
 File Encoding         : 65001

 Date: 05/06/2025 16:14:58
*/


-- ----------------------------
-- Table structure for cost_pole_switch
-- ----------------------------
DROP TABLE IF EXISTS "public"."cost_pole_switch";
CREATE TABLE "public"."cost_pole_switch" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "design_name" varchar(50) COLLATE "pg_catalog"."default",
  "voltage_level" varchar(20) COLLATE "pg_catalog"."default",
  "switch_type" varchar(50) COLLATE "pg_catalog"."default",
  "max_current" int8,
  "total_cost" float8 DEFAULT 0.00,
  "purchase_cost" float8 DEFAULT 0.00,
  "building_cost" float8 DEFAULT 0.00,
  "install_cost" float8 DEFAULT 0.00,
  "other_cost" float8 DEFAULT 0.00,
  "create_time" timestamptz(6) DEFAULT now(),
  "update_time" timestamptz(6) DEFAULT now(),
  "cut_current" int8
)
;
COMMENT ON COLUMN "public"."cost_pole_switch"."id" IS '主键ID';
COMMENT ON COLUMN "public"."cost_pole_switch"."design_name" IS '名称(如10-D-10)';
COMMENT ON COLUMN "public"."cost_pole_switch"."voltage_level" IS '电压等级(如10kV)';
COMMENT ON COLUMN "public"."cost_pole_switch"."switch_type" IS '开关类型';
COMMENT ON COLUMN "public"."cost_pole_switch"."max_current" IS '最大电流';
COMMENT ON COLUMN "public"."cost_pole_switch"."total_cost" IS '总成本';
COMMENT ON COLUMN "public"."cost_pole_switch"."purchase_cost" IS '采购成本';
COMMENT ON COLUMN "public"."cost_pole_switch"."building_cost" IS '建设成本';
COMMENT ON COLUMN "public"."cost_pole_switch"."install_cost" IS '安装成本';
COMMENT ON COLUMN "public"."cost_pole_switch"."other_cost" IS '其他成本';
COMMENT ON COLUMN "public"."cost_pole_switch"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."cost_pole_switch"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."cost_pole_switch"."cut_current" IS '开断电流';
COMMENT ON TABLE "public"."cost_pole_switch" IS '柱上开关造价表';

-- ----------------------------
-- Records of cost_pole_switch
-- ----------------------------
INSERT INTO "public"."cost_pole_switch" VALUES ('1', '10-B20-630', '10kV', '柱上负荷开关', 630, 4.51, 3.6, 0.41, 0.32, 0.18, '2025-03-28 16:03:02.594594+08', '2025-03-28 16:03:02.594594+08', 20);
INSERT INTO "public"."cost_pole_switch" VALUES ('2', '10-A20-630', '10kV', '柱上断路器', 630, 7.51, 6, 0.68, 0.53, 0.3, '2025-03-28 16:03:02.594594+08', '2025-03-28 16:03:02.594594+08', 20);
