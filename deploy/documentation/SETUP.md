# 后端项目部署指南

## 项目概述

本文档描述了位于 `/appdata/plan-analysis` 的后端项目的部署流程和维护方法。

## 项目结构

```
/appdata/plan-analysis/
├── docker-compose.yml                  # Docker容器编排配置文件
├── images/                            # 基础镜像目录
│   ├── ruoyi_admin/                   # 权限认证框架镜像
│   ├── web_server/                    # 业务代码镜像
│   └── znap_base_image/               # 潮流计算服务镜像
└── volumes/                           # 数据卷挂载目录
    ├── ruoyi_admin/                   # 认证框架数据
    │   ├── application.yaml           # 配置文件
    │   ├── logs/                      # 日志目录
    │   │   ├── sys-console.2025-06-02.log
    │   │   ├── sys-console.log
    │   │   └── sys-info.log
    │   └── ruoyi-admin.jar            # 认证框架JAR包
    ├── web_server/                    # 业务代码数据
    │   ├── application.yaml           # 配置文件
    │   ├── bak/                       # 备份目录
    │   ├── logs/                      # 日志目录
    │   └── smart-grid-app.jar         # 业务代码JAR包
    └── znap/                          # 潮流计算服务数据
        ├── pms_import_server_zj.ini
        ├── start.sh
        ├── trend-calc.jar
        ├── zj_model/
        └── znap_sim.ini
```

## 容器服务说明

### 1. ruoyi_admin (权限认证框架)
- **镜像**: `ruoyi_admin:1.0`
- **用途**: 提供用户权限认证和管理功能
- **维护频率**: 一般不需要修改
- **更新方式**: 替换 `volumes/ruoyi_admin/ruoyi-admin.jar` 文件

### 2. web_server (业务代码)
- **镜像**: `smart_grid:1.0`
- **用途**: 核心业务逻辑处理
- **维护频率**: 最常更新的服务
- **更新方式**: 替换 `volumes/web_server/smart-grid-app.jar` 文件

### 3. znap_base (潮流计算服务)
- **镜像**: `znap_base:1.0`
- **用途**: 提供潮流计算功能
- **维护频率**: 除非军哥修改代码，否则一般不变动
- **更新方式**: 需要重新构建镜像

## 日常部署流程


### 业务代码更新部署（最常见场景）

#### 1. 备份当前版本
```bash
# 进入项目目录
cd /appdata/plan-analysis

# 创建备份（以当前时间为后缀）
cp volumes/web_server/smart-grid-app.jar volumes/web_server/bak/smart-grid-app.jar$(date +%Y%m%d_%H%M%S)
```

#### 2. 替换业务JAR包
```bash
# 替换新的JAR包
cp /tmp/smart-grid-app.jar volumes/web_server/smart-grid-app.jar
```

#### 3. 查看容器状态
```bash
# 查看所有运行中的容器
docker ps
```

示例输出：
```
CONTAINER ID   IMAGE               COMMAND                  CREATED      STATUS       PORTS                              NAMES
ffaede8ce68e   smart_grid:1.0      "java -jar /root/web…"   5 days ago   Up 5 hours   8081/tcp, 0.0.0.0:8183->8080/tcp  smart-grid
efc0093e2f28   ruoyi_admin:1.0     "java -jar /root/web…"   5 days ago   Up 5 days    0.0.0.0:8007->8007/tcp             ruoyi_admin
d865277be20f   znap_base:1.0       "sh -c /root/znap/bi…"   5 days ago   Up 2 days    8081/tcp, 0.0.0.0:8184->8080/tcp  plan-analysis_znap_base_1
```

#### 4. 重启业务容器
```bash
# 方式1: 使用容器ID重启
docker restart ffaede8ce68e

# 方式2: 使用容器名称重启
docker restart smart-grid
```

#### 5. 验证部署结果
```bash
# 查看容器启动状态
docker ps | grep smart-grid

# 查看容器日志
docker logs -f smart-grid
```

## 完整项目启动

### 初次部署或完整重启
```bash
# 进入项目目录
cd /appdata/plan-analysis

# 停止所有相关容器
docker-compose down

# 启动所有服务
docker-compose up -d

# 查看启动状态
docker-compose ps
```

## 监控和维护

### 日志查看
```bash
# 查看业务容器日志
docker logs -f smart-grid

# 查看认证容器日志
docker logs -f ruoyi_admin

# 查看潮流计算容器日志
docker logs -f plan-analysis_znap_base_1

# 查看本地日志文件
tail -f volumes/web_server/logs/sys-error.log
tail -f volumes/ruoyi_admin/logs/sys-error.log
```