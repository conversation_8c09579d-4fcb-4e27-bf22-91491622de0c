version: '3.3'
services:
  znap_base:
    image: znap_base:1.0
    network_mode: bridge
    restart: always
    ports:
      - "8184:8080"
    environment:
      TZ: Asia/Shanghai  
    volumes:
        - C:\Users\<USER>\Desktop\plan-analysis\flow\start.sh:/root/znap/bin/start.sh
        - C:\Users\<USER>\Desktop\plan-analysis\flow\znap_sim.ini:/root/znap/conf/znap_sim.ini
        - C:\Users\<USER>\Desktop\plan-analysis\flow\trend-calc.jar:/root/znap/bin/trend-calc.jar
        - C:\Users\<USER>\Desktop\plan-analysis\flow\pms_import_server_zj.ini:/root/znap/conf/pms_import_server_zj.ini
        - C:\Users\<USER>\Desktop\plan-analysis\flow\zj_model:/root/znap/model_data
  web_server:
    image: smart-grid:1.0
    container_name: smart-grid
    restart: always
    ports:
      - "9998:8080"
    volumes:
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\web_server\application.yml:/root/web_server/application.yml
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\web_server\logs:/root/web_server/logs
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\web_server\smart-grid-app.jar:/root/web_server/smart-grid-app.jar
    command: ["--Dspring.config.location=/application.yml"]
  ruoyi_admin:
    image: ruoyi_admin:1.0
    container_name: ruoyi_admin
    restart: always
    ports:
      - "9999:8080"
    volumes:
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\ruoyi_admin\application.yml:/root/web_server/application.yml
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\ruoyi_admin\logs:/root/web_server/logs
      - D:\workspace\project-plan-analysis-nj\Docker\docker_compose\ruoyi_admin\ruoyi_admin.jar:/root/web_server/ruoyi_admin.jar
    command: ["--Dspring.config.location=/application.yml"]        
