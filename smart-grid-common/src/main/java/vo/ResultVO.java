
package vo;

public class ResultVO {
    public static final String SUCCESS = "success";
    private Integer total;
    private Integer pageIndex;
    private Integer pageSize;
    private Object data;
    private Integer code;
    private String tooltip;
    private String status;

    public ResultVO() {
    }

    public ResultVO(String tooltip) {
        this.tooltip = tooltip;
    }

    public ResultVO(Integer total, Object data, Integer code) {
        this.total = total;
        this.data = data;
        this.code = code;
    }

    public Integer getTotal() {
        return this.total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Object getData() {
        return this.data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTooltip() {
        return this.tooltip;
    }

    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isSuccess() {
        return "success".equalsIgnoreCase(this.status);
    }

    public Integer getPageIndex() {
        return this.pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String toString() {
        return "ResultVO{total=" + this.total + ", pageIndex=" + this.pageIndex + ", pageSize=" + this.pageSize + ", data=" + this.data + ", code=" + this.code + ", tooltip='" + this.tooltip + '\'' + ", status='" + this.status + '\'' + '}';
    }
}
