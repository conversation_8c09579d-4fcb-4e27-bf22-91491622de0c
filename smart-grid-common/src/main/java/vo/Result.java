package vo;


import java.util.List;

/**
 * list、pageData 返回数据结构
 */
public class Result extends ResultVO {
    public Result() {
    }

    public Result(String tooltip) {
        super(tooltip);
    }

    public Result(Integer total, Object data, Integer code) {
        super(total, data, code);
    }

    public static Result success(List data) {
        Result result = new Result(0, null, 200);
        if (data != null) {
            result = new Result(data.size(), data, 200);
        }
        result.setTooltip("查询正常！");
        result.setStatus("success");
        return result;
    }

    public static Result success(Object data) {
        Result result = new Result(0, null, 200);
        if (data != null) {
            result = new Result(1, data, 200);
        }
        result.setTooltip("查询正常！");
        result.setStatus("success");
        return result;
    }

    public static Result fail(String message) {
        Result result = new Result(0, null, 500);
        result.setTooltip(message);
        result.setStatus("fail");
        return result;
    }
}
