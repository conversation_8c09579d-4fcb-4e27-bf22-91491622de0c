package com.ruoyi.common.utils.util;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.List;

/**
 * 查询外部api
 */
public class SelectExternalUtil {

    public static String SelectExternalUtil(String url,String json){
        System.out.println(json);
        String  result = "";
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost request = new HttpPost(url);

            // 设置 JSON 请求体
            request.setEntity(new StringEntity(json));

            // 设置请求头
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");
            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                result = EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result ;
    }


}
