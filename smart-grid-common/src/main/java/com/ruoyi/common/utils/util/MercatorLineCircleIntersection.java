package com.ruoyi.common.utils.util;

import org.locationtech.jts.geom.*;
import org.locationtech.proj4j.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

public class MercatorLineCircleIntersection {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    private static final CoordinateTransformFactory CT_FACTORY = new CoordinateTransformFactory();
    private static final CRSFactory CRS_FACTORY = new CRSFactory();

    // 定义墨卡托坐标系(EPSG:3857)和WGS84坐标系(EPSG:4326)
    private static final CoordinateReferenceSystem MERCATOR_CRS;
    private static final CoordinateReferenceSystem WGS84_CRS;
    private static final CoordinateTransform MERCATOR_TO_WGS84;

    static {
        try {
            MERCATOR_CRS = CRS_FACTORY.createFromName("EPSG:3857");
            WGS84_CRS = CRS_FACTORY.createFromName("EPSG:4326");
            MERCATOR_TO_WGS84 = CT_FACTORY.createTransform(MERCATOR_CRS, WGS84_CRS);
        } catch (Exception e) {
            throw new RuntimeException("初始化坐标系失败", e);
        }
    }

    /**
     * 判断多个线段是否与圆相交
     *
     * @param lineSegments 线段列表，每个线段是墨卡托坐标点的列表
     * @param center       圆心坐标(墨卡托)
     * @param radius       圆半径(km)
     * @return 相交的线段索引列表
     */
    public static List<Integer> findIntersectingLines(List<List<Coordinate>> lineSegments,
                                                      Coordinate center,
                                                      double radius) {
        List<Integer> result = new ArrayList<>();

        // 创建圆(缓冲区)
        Point centerPoint = GEOMETRY_FACTORY.createPoint(center);
        Geometry circle = centerPoint.buffer(radius * 1000);

        // 检查每条线段
        for (int i = 0; i < lineSegments.size(); i++) {
            List<Coordinate> segment = lineSegments.get(i);
            if (segment == null || segment.size() < 2) continue;

            LineString line = GEOMETRY_FACTORY.createLineString(segment.toArray(new Coordinate[0]));
            if (line.intersects(circle)) {
                result.add(i);
            }
        }

        return result;
    }

    /**
     * 判断一个点是否在多边形内
     *
     * @param x             要判断的点
     * @param y             要判断的点
     * @param polygonCoords 多边形的顶点集合（按顺序）
     * @return true 表示点在多边形内，false 表示不在
     */
    public static boolean isPointInPolygon(double x, double y, List<Coordinate> polygonCoords) {
        GeometryFactory geometryFactory = new GeometryFactory();

        // 构建多边形
        Coordinate[] coordinates = polygonCoords.toArray(new Coordinate[0]);
        Polygon polygon = geometryFactory.createPolygon(coordinates);

        // 构建点
        Point point = geometryFactory.createPoint(new Coordinate(x, y));

        return polygon.contains(point);
    }

    /**
     * 将WGS84经纬度转换为墨卡托坐标
     */
    public static Coordinate wgs84ToMercator(double lon, double lat) {
        ProjCoordinate src = new ProjCoordinate(lon, lat);
        ProjCoordinate dst = new ProjCoordinate();
        try {
            CT_FACTORY.createTransform(WGS84_CRS, MERCATOR_CRS).transform(src, dst);
            return new Coordinate(dst.x, dst.y);
        } catch (Exception e) {
            throw new RuntimeException("坐标转换失败", e);
        }
    }

    /**
     * 将墨卡托坐标转换为WGS84经纬度
     */
    public static Coordinate mercatorToWgs84(double x, double y) {
        ProjCoordinate src = new ProjCoordinate(x, y);
        ProjCoordinate dst = new ProjCoordinate();
        try {
            MERCATOR_TO_WGS84.transform(src, dst);
            return new Coordinate(dst.x, dst.y);
        } catch (Exception e) {
            throw new RuntimeException("坐标转换失败", e);
        }
    }

    /**
     * 墨卡托转经纬度
     * @return 经纬度[经度，纬度]
     */
    public static double [] mercatorToLngLat(double x,double y){
        double lng = x / 20037508.34 * 180;
        double lat = y / 20037508.34 * 180;
        lat = 180 / Math.PI * (2 * Math.atan(Math.exp(lat * Math.PI / 180)) - Math.PI / 2);
        return new double []{lng,lat}; //[12727039.383734727, 3579066.6894065146]
    }

    /**
     * 将WGS84经纬度转换为墨卡托坐标
     */
    public static List<BigDecimal> wgs84ToMercatorList(double lon, double lat) {
        ProjCoordinate src = new ProjCoordinate(lon, lat);
        ProjCoordinate dst = new ProjCoordinate();
        try {
            List<BigDecimal> coords = new ArrayList<>();
            CT_FACTORY.createTransform(WGS84_CRS, MERCATOR_CRS).transform(src, dst);
            coords.add(new BigDecimal(dst.x));
            coords.add(new BigDecimal(dst.y));
            return coords;
        } catch (Exception e) {
            throw new RuntimeException("坐标转换失败", e);
        }
    }
}
