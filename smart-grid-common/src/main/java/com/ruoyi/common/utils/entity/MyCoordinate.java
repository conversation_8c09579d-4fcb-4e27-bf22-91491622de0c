package com.ruoyi.common.utils.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class MyCoordinate {
    /**
     * 维度
     */
    private Double Lat;

    /**
     * 经度
     */
    private Double Lon;

    /**
     * 是否是钢塔
     */
    private Boolean isGT;



    private String deviceId;

}
