package com.ruoyi.common.utils.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 将string时间转成date时间
 */
public class StringByDate {


    /**
     * 将string时间转换成 "yyyy-MM-dd" 格式的时间
     * @param dateString
     * @return
     * @throws ParseException
     */
    public static Date stringByDate(String dateString) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = simpleDateFormat.parse(dateString);
        return date;
    }
}
