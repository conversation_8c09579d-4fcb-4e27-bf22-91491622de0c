package com.ruoyi.common.utils.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

public class NoNullSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider provider)
            throws IOException {
        if (value.equals("null")) {
            gen.writeString(""); // 或者使用其他表示方式}
        }else {
            gen.writeString(value); // 或者使用其他表示方式}
        }
    }
    }
