package com.ruoyi.common.utils.util;

import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ExcelImportUtil {

    public static <T> List<T> importExcel(MultipartFile file, ExcelDataParser<T> parser)
        throws IOException {

        List<T> dataList = new ArrayList<>();

        try (InputStream is = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(is)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // 跳过标题行
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                T data = parser.parseRow(row);
                if (data != null) {
                    dataList.add(data);
                }
            }
        }

        return dataList;
    }

    public interface ExcelDataParser<T> {
        T parseRow(Row row);
    }
}
