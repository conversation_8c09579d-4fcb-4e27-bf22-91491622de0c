package com.ruoyi.common.utils.aop;


import com.ruoyi.common.utils.aop.anno.IgnoreRestControllerResponseAdvice;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import vo.Result;
import vo.ResultVO;

import java.util.ArrayList;
import java.util.Objects;

/**
 * 响应实体封装切面
 *
 * <AUTHOR>
 */
@RestControllerAdvice(basePackages = {"com.ruoyi.grid.controller"})
public class GlobalResponseAdvice implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter returnType, @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 方法没有IgnoreRestControllerResponseAdvice注解时启用beforeBodyWrite
        return !returnType.hasMethodAnnotation(IgnoreRestControllerResponseAdvice.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType, @NotNull ServerHttpRequest request,
                                  @NotNull ServerHttpResponse response) {
        // 兼容前端框架问题，当status为200时，data为null时，会有问题，所以这里做一下兼容
        // 不知道实际返回应该是空数组还是空对象，所以这里先都返回空数组
        if (body instanceof ResultVO) {
            ResultVO resultVO = (ResultVO) body;
            if (Objects.equals(resultVO.getCode(), 200) && resultVO.getData() == null) {
                return Result.success(new ArrayList<>());
            }
        }
        return body;
    }
}

