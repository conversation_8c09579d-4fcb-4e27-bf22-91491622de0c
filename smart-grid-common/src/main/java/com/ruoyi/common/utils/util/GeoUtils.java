package com.ruoyi.common.utils.util;

import com.ruoyi.common.utils.entity.MyCoordinate;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图计算
 */
public class GeoUtils {
    private static final double EARTH_RADIUS = 6371.0; // 地球半径，单位千米

    /**
     * 计算两个经纬度坐标之间的距离
     * @param lat1 点1纬度
     * @param lon1 点1经度
     * @param lat2 点2纬度
     * @param lon2 点2经度
     * @return 距离(千米)
     */
    public static double haversineDistance(double lat1, double lon1,
                                         double lat2, double lon2) {
        // 将经纬度转换为弧度
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                 + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                 * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 检查目标点是否在指定半径范围内
     * @param centerLat 中心点纬度
     * @param centerLon 中心点经度
     * @param targetLat 目标点纬度
     * @param targetLon 目标点经度
     * @param radiusKm 半径(千米)
     * @return 是否在范围内
     */
    public static boolean isInRange(double centerLon,double centerLat,
                                    double targetLon, double targetLat,
                                  double radiusKm) {
        double distance = haversineDistance(centerLat, centerLon, targetLat, targetLon);
        return distance <= radiusKm;
    }


    /**
     * 检查目标点是否在指定半径范围内
     * @param centerLat 中心点纬度
     * @param centerLon 中心点经度
     * @param radiusKm 半径(千米)
     * @return 是否在范围内
     */
    public static List<String > isInRange(double centerLon,double centerLat,
                                          double radiusKm, List<MyCoordinate> coordinateList) {
        List<String> deviceIdList =new ArrayList<>();
        for (MyCoordinate coordinate : coordinateList) {
            double distance = haversineDistance(centerLat, centerLon, coordinate.getLat(), coordinate.getLon());
          if(distance <= radiusKm/1000){
              deviceIdList.add(coordinate.getDeviceId());
          }
        }
        return deviceIdList;
    }
}
