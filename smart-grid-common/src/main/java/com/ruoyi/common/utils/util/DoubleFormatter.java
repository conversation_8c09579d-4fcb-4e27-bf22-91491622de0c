package com.ruoyi.common.utils.util;

import java.text.DecimalFormat;

public class DoubleFormatter {
    public static double formatToThreeDecimals(double value) {
        DecimalFormat df = new DecimalFormat("#.###");
        return Double.parseDouble(df.format(value));
    }
    public static double formatToThreeDecimals1(double value) {
        DecimalFormat df = new DecimalFormat("#.#");
        return Double.parseDouble(df.format(value));
    }
    public static double formatToThreeDecimals7(double value) {
        DecimalFormat df = new DecimalFormat("#.#######");
        return Double.parseDouble(df.format(value));
    }
    public static double formatToThreeDecimals2(double value) {
        DecimalFormat df = new DecimalFormat("#.##");
        return Double.parseDouble(df.format(value));
    }
    public static double formatToThreeDecimals3(double value) {
        DecimalFormat df = new DecimalFormat("#.###");
        return Double.parseDouble(df.format(value));
    }
    public static double formatToThreeDecimals4(double value) {
        DecimalFormat df = new DecimalFormat("#.####");
        return Double.parseDouble(df.format(value));
    }
}
