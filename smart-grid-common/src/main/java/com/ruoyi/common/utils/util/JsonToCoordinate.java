package com.ruoyi.common.utils.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.entity.MyCoordinate;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


public class JsonToCoordinate {
    public static List<String> JsonToCoordinate(String jsonStr, Double centerLon, Double centerLat, Double radius) throws JsonProcessingException {
        List<String> psrIdList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonStr);
        JsonNode resultArray = root.path("result");
        //解析json
        for (JsonNode resultItem : resultArray) {
            //获取设备的list
            JsonNode psrList = resultItem.path("psrList");
            for (JsonNode psr : psrList) {
                //从设备的list中获取坐标对象，判断对象是否是线
                String coordinateString = psr.path("coordinate").asText();
                String type = psr.path("psrType").asText();
                if (!coordinateString.isEmpty()) {
                    //如果是线，用线的判断方式
                    if (type.equals("dkx")) {
                        List<List<Coordinate>> list = CoordinateParser.parseCoordinates(coordinateString);
                        Coordinate center = new Coordinate(centerLon, centerLat);
                        // 查找相交的线段
                        List<Integer> intersectingLines = MercatorLineCircleIntersection.findIntersectingLines(
                                list, center, radius);
                        if (intersectingLines.size() > 0) {
                            psrIdList.add(psr.path("psrId").asText());
                        }
                    } else {
                        //如果是不是线，就用点判断
                        String[] coords = coordinateString.split(" ");
                        double x = Double.parseDouble(coords[0]);
                        double y = Double.parseDouble(coords[1]);
                        Coordinate coordinate = MercatorLineCircleIntersection.mercatorToWgs84(x, y);
                        if (GeoUtils.isInRange(centerLon, centerLat, coordinate.x, coordinate.y, radius)) {
                            psrIdList.add(psr.path("psrId").asText());
                        }

                    }

                }


            }
        }
        return psrIdList;
    }


    public static List<Double> JsonToCoordinateSingle(String jsonStr) throws JsonProcessingException {

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonStr);
        JsonNode resultArray = root.path("result");
        List<Double> list = new ArrayList<>();
        //解析json
        for (JsonNode resultItem : resultArray) {
            //获取设备的list
            JsonNode psrList = resultItem.path("psrList");
            for (JsonNode psr : psrList) {//从设备的list中获取坐标对象，判断对象是否是线
                String coordinateString = psr.path("coordinate").asText();
                String type = psr.path("psrType").asText();
                if (!coordinateString.isEmpty()) {
                    //如果解析的类型是dkx则取中心点
                    if (type.equals("dkx")) {
                        List<List<Coordinate>> dkxCoordinateList = CoordinateParser.parseCoordinates(coordinateString);
                        Coordinate coordinate =  calculateCenterPointParallel(dkxCoordinateList);
                        list.add(coordinate.getX());
                        list.add(coordinate.getY());

                    } else {
                        String[] coordinates = coordinateString.split("\\s+");
                        Coordinate coordinate = MercatorLineCircleIntersection.mercatorToWgs84(Double.parseDouble(coordinates[0]), Double.parseDouble(coordinates[1]));
                        double centerLon = coordinate.x;
                        double centerLat = coordinate.y;
                        list.add(centerLon);
                        list.add(centerLat);
                    }
                }
            }
        }
        return list;
    }
    /**
     * 使用Java 8并行流计算几何中心点（适用于大量坐标点的情况）
     *
     * 注意：并行流在数据量较小时可能比顺序处理更慢
     *
     * @param coordinateList 多层嵌套的坐标列表
     * @return 中心点坐标，如果输入为空则返回null
     */
    public static Coordinate calculateCenterPointParallel(List<List<Coordinate>> coordinateList) {
        if (coordinateList == null || coordinateList.isEmpty()) {
            return null;
        }

        // 使用并行流处理：
        // 1. 过滤空的子列表
        // 2. 将多层列表展平为单层流
        // 3. 过滤空元素
        // 4. 并行累加坐标值
        CoordinateSum sum = coordinateList.parallelStream()
                .filter(subList -> subList != null && !subList.isEmpty())
                .flatMap(List::stream)
                .filter(coordinate -> coordinate != null)
                .collect(
                        CoordinateSum::new,  // 创建累加器
                        (s, c) -> { s.totalX += c.getX(); s.totalY += c.getY(); s.count++; },  // 累加操作
                        (s1, s2) -> { s1.totalX += s2.totalX; s1.totalY += s2.totalY; s1.count += s2.count; }  // 合并结果
                );

        if (sum.count == 0) {
            return null;
        }

        return new Coordinate(sum.totalX / sum.count, sum.totalY / sum.count);
    }

    /**
     * 辅助类：用于并行计算时累加坐标值
     * （静态内部类，避免外部依赖）
     */
    private static class CoordinateSum {
        double totalX = 0;  // 并行累加的X值总和
        double totalY = 0;  // 并行累加的Y值总和
        int count = 0;      // 坐标点数量
    }

}
