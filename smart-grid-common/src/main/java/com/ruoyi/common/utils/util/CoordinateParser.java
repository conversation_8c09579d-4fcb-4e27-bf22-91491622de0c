package com.ruoyi.common.utils.util;

import org.locationtech.jts.geom.Coordinate;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class CoordinateParser {

    // 正则表达式匹配坐标对（允许科学计数法）
    private static final Pattern COORD_PATTERN =
        Pattern.compile("(-?\\d+\\.?\\d*(?:[Ee][+-]?\\d+)?)\\s+(-?\\d+\\.?\\d*(?:[Ee][+-]?\\d+)?)");

    /**
     * 将坐标字符串解析为线段列表
     * @param coordString 原始坐标字符串
     * @return List<List<Coordinate>> 结构
     */
    public static List<List<Coordinate>> parseCoordinates(String coordString) {
        List<List<Coordinate>> result = new ArrayList<>();

        if (coordString == null || coordString.trim().isEmpty()) {
            return result;
        }

        // 按逗号分割不同的线段
        String[] segments = coordString.split("\\s*,\\s*");

        for (String segment : segments) {
            List<Coordinate> coordinates = parseSegment(segment.trim());
            if (!coordinates.isEmpty()) {
                result.add(coordinates);
            }
        }

        return result;
    }

    /**
     * 解析单个线段字符串
     */
    private static List<Coordinate> parseSegment(String segment) {
        List<Coordinate> coordinates = new ArrayList<>();
        Matcher matcher = COORD_PATTERN.matcher(segment);

        while (matcher.find()) {
            try {
                Coordinate coordinate = MercatorLineCircleIntersection.mercatorToWgs84(Double.parseDouble(matcher.group(1)), Double.parseDouble(matcher.group(2)));
                double x = coordinate.x;
                double y = coordinate.y;
                coordinates.add(new Coordinate(x, y));
            } catch (NumberFormatException e) {
                System.err.println("忽略无效坐标: " + matcher.group());
            }
        }

        return coordinates;
    }


}
