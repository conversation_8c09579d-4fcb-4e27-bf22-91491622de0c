# 设备距离信息计算接口文档

## 接口概述

新增了一个用于计算两个设备坐标之间距离信息和获取B设备详细信息的接口。

## 接口详情

### 请求信息

- **接口路径**: `/map/calculateDeviceDistanceInfo`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

### 请求参数

```json
{
    "aLng": 118.5748355357,      // A设备经度（必填）
    "aLat": 31.794086767539,     // A设备纬度（必填）
    "bLng": 118.57488253887,     // B设备经度（必填）
    "bLat": 31.794242812889,     // B设备纬度（必填）
    "bPsrId": "device_001",      // B设备的psrId（必填）
    "bPsrType": "0103"           // B设备的psrType（必填）
}
```

### 响应参数

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "bPsrId": "device_001",                    // B设备ID
        "bPsrType": "0103",                        // B设备类型
        "bPsrName": "运行杆塔-001",                // B设备名称
        "straightLineDistance": 156.78,            // 直线距离（米）
        "plannedRouteDistance": 188.14,            // 规划路径长度（米）
        "feederId": "10DKX-481441",               // B设备所属线路ID
        "feederName": "10kV金陵村线161",          // B设备所属线路名称
        "transformerId": "TR001",                  // B设备所属主变ID
        "transformerName": "金陵变电站-主变",      // B设备所属主变名称
        "substationId": "SUB001",                  // B设备所属变电站ID
        "substationName": "金陵变电站",            // B设备所属变电站名称
        "installedCapacity": 2500.0,               // 装机容量（kVA）
        "maxHistoryCurrent": 144.34,               // 历史最大电流（A）
        "maxHistoryLoadRate": 0.85,                // 历史最大负载率
        "maxHistoryLoadRateDate": "2024-07-15",    // 历史最大负载率发生时间
        "errorMessage": null                       // 错误信息（如果有）
    }
}
```

## 支持的设备类型

- `0103`: 运行杆塔
- `wlgt`: 物理杆塔
- `zf07`: 环网柜
- `zf04`: 开关站

## 功能说明

1. **直线距离计算**: 使用Haversine公式计算两点间的球面距离
2. **规划路径长度**: 优先使用高德地图API计算实际路径长度，失败时使用直线距离的1.2倍作为估算
3. **设备信息查询**: 根据设备类型和ID查询设备的详细信息
4. **线路信息**: 获取设备所属的线路信息，包括装机容量、历史负载率等
5. **变电站信息**: 获取设备所属的变电站和主变信息

## 错误处理

如果查询过程中出现错误，响应中的 `errorMessage` 字段会包含具体的错误信息，其他字段可能为空或包含部分信息。

## 使用示例

```bash
curl -X POST "http://localhost:8080/map/calculateDeviceDistanceInfo" \
  -H "Content-Type: application/json" \
  -d '{
    "aLng": 118.5748355357,
    "aLat": 31.794086767539,
    "bLng": 118.57488253887,
    "bLat": 31.794242812889,
    "bPsrId": "device_001",
    "bPsrType": "0103"
  }'
```
