package com.ruoyi.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 实例告警规则业务对象 calc_alarm_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CalcAlarmRuleBo extends BaseEntity {

    /**
     * 实例id
     */
    @NotBlank(message = "实例id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String instanceId;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 设定值
     */
    @NotNull(message = "设定值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double limitValue;


}
