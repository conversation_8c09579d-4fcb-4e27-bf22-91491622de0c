package com.ruoyi.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 辅助决策方案详情业务对象 zh_opt_plan_info
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ZhOptPlanInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String id;

    /**
     * 方案id
     */
    @NotBlank(message = "方案id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String planId;

    /**
     * 步骤信息
     */
    @NotBlank(message = "步骤信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stepInfo;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operateType;


}
