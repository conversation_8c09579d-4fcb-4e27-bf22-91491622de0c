package com.ruoyi.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 计算实例回收规则业务对象 calc_recycle_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CalcRecycleRuleBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 实例状态
     */
    @NotBlank(message = "实例状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long state;

    /**
     * 回收天数
     */
    @NotNull(message = "回收天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long recycleDay;

    /**
     * 回收小时数
     */
    @NotNull(message = "回收小时数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long recycleHour;

    /**
     * 回收分钟数
     */
    @NotNull(message = "回收分钟数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long recycleMin;

    /**
     * 是否启用，0否1是
     */
    @NotNull(message = "是否启用，0否1是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isEnable;


}
