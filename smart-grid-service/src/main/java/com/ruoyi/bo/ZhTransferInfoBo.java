package com.ruoyi.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 转供方案业务对象 zh_transfer_info
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */

@Data
public class ZhTransferInfoBo  {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    private String sourceFeeder;
    private String linkFeeder;
    /**
     * 源线路名称
     */
    private String sourceFeederName;
    private String linkFeederName;

    /**
     * 源线路最高电流
     */
    @NotNull(message = "源线路最高电流不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double sourceFeederCurrent;

    /**
     * 联络馈线额定电流
     */
    @NotNull(message = "联络馈线额定电流不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double linkFeederCurrent;

    /**
     * 转供状态
     */
    @NotBlank(message = "转供状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transferStatus;

    /**
     * 源馈线当前负载
     */
    private double sourceFeederRate;
    /**
     * 联络馈线原来的负载
     */
    private double linkFeederOriginalRate;
    /**
     * 源线路联络开关
     */
    @NotBlank(message = "源线路联络开关不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceFeederBreak;

    /**
     * 源线路联络开关名称
     */
    @NotBlank(message = "源线路联络开关名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceFeederBreakName;

    /**
     * 实例id
     */
    @NotBlank(message = "实例id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String calcId;

    /**
     * 计算结果id
     */
    @NotNull(message = "计算结果id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long retId;


    /**
     * 计算转供时间
     */
    @NotNull(message = "计算转供时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date time;

    /**
     * 联络馈线负载
     */
    @NotNull(message = "联络馈线负载不能为空", groups = { AddGroup.class, EditGroup.class })
    private double linkFeederRate;


}
