package com.ruoyi.bo;

import com.ruoyi.common.core.validate.QueryGroup;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 告警信息业务对象 zh_eps_alarm
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Data
public class ZhEpsAlarmBo {
    /**
     * 主键
     */
    private Long id;
    /**
     *
     */
    private String modelId;
    /**
     * 网格编号:仿真实例号
     */
    private String simuCaseNo;
    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 告警内容
     */
    private String alarmContext;
    /**
     * 告警类型
     */
    private Integer alarmType;
    /**
     * 网格id
     */
    private String grid;
    /**
     * 网格名称
     */
    private String gridName;
    /**
     * 状态查询字段
     */
    private Boolean status;
    private Boolean isXn;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;
    /**
     * 查询的周期类型 1 短期 2 长期
     */
    @NotNull(groups = {QueryGroup.class})
    private Integer period;
}
