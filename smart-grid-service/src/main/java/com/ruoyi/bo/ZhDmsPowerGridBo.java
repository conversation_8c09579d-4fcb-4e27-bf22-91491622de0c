package com.ruoyi.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 供电网格台账数据业务对象 zh_dms_power_grid
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Accessors(chain = true)
@Data
public class ZhDmsPowerGridBo {
    /**
     * 网格编码
     */
    @NotBlank(message = "网格编码不能为空", groups = { EditGroup.class })
    private String psrId;

    /**
     * 网格名称
     */
    @NotBlank(message = "网格名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridName;

    /**
     * 地市ID
     */
    @NotBlank(message = "地市ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 地市名称
     */
    @NotBlank(message = "地市名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityName;

    /**
     * 区县ID
     */
    @NotBlank(message = "区县ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String maintOrg;

    /**
     * 区县名称
     */
    @NotBlank(message = "区县名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String maintName;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String coord;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zoom;

    /**
     * 网格范围多边形坐标点
     */
    @NotBlank(message = "网格范围多边形坐标点不能为空", groups = { AddGroup.class, EditGroup.class })
    private String wkt;
}
