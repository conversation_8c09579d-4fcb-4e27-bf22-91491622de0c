package com.ruoyi.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 辅助决策方案业务对象 zh_opt_plan
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ZhOptPlanBo extends BaseEntity {

    /**
     * 主键id
     */
    private Integer id;


    /**
     * 决策类型
     */
    private String policyPlanType;
    private String decisionId;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 方案列表
     */
    private List<Map<String, Object>> programmeList;

}
