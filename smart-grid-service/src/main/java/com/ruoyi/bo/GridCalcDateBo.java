package com.ruoyi.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class GridCalcDateBo {
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(groups = {EditGroup.class}, message = "编辑的日期不能为空")
    private List<LocalDate> dateList;
    /**
     * 日期类型 1 典型日、2 负荷最大日、3 光伏大发、4 电网故障日、5 设备退役日、6 精准切负向
     */
    @NotNull(groups = {EditGroup.class}, message = "日期类型不能为空")
    private Integer dateType;
    /**
     * 网格id
     */
    @NotBlank(groups = {EditGroup.class}, message = "网格id不能为空")
    private String grid;
}
