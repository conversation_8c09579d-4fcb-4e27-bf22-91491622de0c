package com.ruoyi.bo;

import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 告警信息业务对象 zh_eps_alarm_config
 * <AUTHOR> developer
 * @date 2024-09-27
 */
@Data
public class ZhEpsAlarmConfigBo {
    /**
     * 主键
     */
    @NotNull(message = "是否启用不能为空", groups = {EditGroup.class})
    private Long id;
    /**
     * 警告类型描述
     */
    private String description;
    /**
     * 阈值
     */
    private Double limitValue;
    /**
     * 低中临界值
     */
    private Double lowLimit;
    /**
     * 中高临界值
     */
    private Double highLimit;
    /**
     * 短期临界值
     */
    private Integer lowPeriod;
    /**
     * 长期临界值
     */
    private Integer highPeriod;
}
