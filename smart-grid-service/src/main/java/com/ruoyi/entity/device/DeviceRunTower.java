package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 输电运行杆塔 device_run_tower
 */
@EqualsAndHashCode()
@Data
public class DeviceRunTower  {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    private Date startTime;
    /**
     * 退运日期
     */
    private Date stopTime;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 是否终端
     */
    private String isTerminal;
    /**
     * 档距
     */
    private String span;
    /**
     * 所属主干/分支线
     */
    private String branchFeeder;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 关联物理杆类型
     */
    private String physicalpoleType;
    /**
     * 所属开关段
     */
    private String switchSegment;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 杆塔性质
     */
    private String poleNature;
    /**
     * 所属导线
     */
    private String wire;
    /**
     * 同杆线路位置
     */
    private String linePosition;
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
    /**
     * 现场编号
     */
    private String poleNum;

}
