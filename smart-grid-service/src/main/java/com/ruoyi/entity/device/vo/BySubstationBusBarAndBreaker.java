package com.ruoyi.entity.device.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;

/**
 * 变电站下的，线路，母线，间隔（区分再使用或者备用）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BySubstationBusBarAndBreaker {
    /**
     * 母线信息
     */
    private List<BusBar> busBarList;

    /**
     * 间隔信息
     */
    private List<Breaker> breakerList;



}
