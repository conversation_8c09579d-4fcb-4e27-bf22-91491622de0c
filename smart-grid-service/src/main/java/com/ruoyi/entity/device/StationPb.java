package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

@Data
public class StationPb {

    /**
     * 所属线路id
     */
    private String feeder;

    /**
     * 所属线路id
     */
    private String substationId;


    /**
     * 所属线路id
     */
    private String substationName;

    /**
     * 公变专变（0公 1专）
     */
    private String pubPrivFlag;

    private String pubPrivFlagLabel;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备id
     */
    private String psrId;

    /**
     * 设备状态
     */
    private String psrState;

    private String psrStateLabel;


    /**
     * 设备类型
     */
    private String psrType;

    private String psrTypeLabel;


    private String ratedCapacity;
    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 关联中压用户接入点
     */
    private String joinEc;

    /**
     * 所属站房
     */
    private String station;

    /**
     * 站房类型
     */
    private String stationType;

    /**
     * 所属间隔
     */
    private String bay;

    public double[] getCoords() {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude)) {
            return null;
        }
        return new double[]{Double.parseDouble(longitude), Double.parseDouble(latitude)};
    }

}
