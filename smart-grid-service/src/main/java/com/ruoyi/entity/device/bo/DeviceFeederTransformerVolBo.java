package com.ruoyi.entity.device.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceFeederTransformerVolBo  extends BaseEntity {

    /**
     * 网格编码
     */
    private String code;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 最大数
     */
    private Integer pageSize;

    /**
     * 类型（0 公变，1专变，2是全部）
     */
    private String type;
}
