package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 变电站内断路器(0305)对象 device_station_breaker
 */
@Data
@TableName("device_station_breaker")
public class DeviceStationBreaker  {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 运行编号
     */
    private String runDevName;
    /**
     * 全路径名称
     */
    private String fullPathName;
    /**
     * 所属城市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 所属站房
     */
    private String station;
    /**
     * 所属间隔
     */
    private String bay;
    /**
     * 所属站房类型
     */
    private String stationType;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    private Date startTime;
    /**
     * 退运日期
     */
    private Date stopTime;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 重要程度
     */
    private String importance;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 供电区域
     */
    private String supplyArea;
    /**
     * 开关作用
     */
    private String switchRole;
    /**
     * 计划开关状态
     */
    private String normalOpen;
    /**
     * 管辖机构
     */
    private String dispatchJurisdiction;
    /**
     * 操作机构
     */
    private String dispatchOperation;
    /**
     * 许可机构
     */
    private String dispatchPermission;
    /**
     * 监控机构
     */
    private String dispatchMonitor;
    /**
     * "所属主干/分支线(用于存量数据点设备)"
     */
    private String branchFeeder;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 开关子类型
     */
    private String switchSubtype;
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
    /**
     * 所属可靠性分段
     */
    private String reliableSegment;

}
