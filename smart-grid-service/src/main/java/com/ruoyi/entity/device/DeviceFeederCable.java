package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 配电电缆线段（0201）
 */
@Data
@TableName("device_feeder_cable")
public class DeviceFeederCable {
    // 基础信息
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId; // 配电资源唯一标识
    private String psrState; // 配电资源状态代码
    private String feeder; // 馈线编号/名称
    private String name; // 馈线完整名称
    private String astId; // 资产ID
    private String branchFeeder; // 分支馈线ID

    // 地理位置信息
    private String regionalism; // 行政区划代码
    private String city; // 城市ID
    private String supplyArea; // 供电区域代码
    private String isRural; // 是否农村电网(0-否,1-是)

    // 拓扑信息
    private String line; // 线路ID
    private String startPosition; // 起始位置ID
    private String endPosition; // 结束位置ID
    private String startType; // 起始节点类型
    private String endType; // 结束节点类型
    private String cable; // 电缆ID

    // 电气特性
    private String voltageLevel; // 电压等级(kV)
    private String importance; // 重要等级(1-5)
    private Double referenceLength; // 参考长度(km)
    private Integer length; // 线路长度(米)

    // 管理信息
    private String maintOrg; // 运维单位ID
    private String maintGroup; // 运维班组ID
    private String equipmentOwner; // 设备所有者编号
    private String pubPrivFlag; // 公有私有标志(0-公有,1-私有)
    private String model;          // 设备型号
    // 时间信息
    private Timestamp ctime; // 创建时间
    private Timestamp startTime; // 投运时间
    private Long lastUpdateTime; // 最后更新时间戳(毫秒)
    private String coordinate;// 最后更新时间戳(毫秒级Unix时间戳)
}
