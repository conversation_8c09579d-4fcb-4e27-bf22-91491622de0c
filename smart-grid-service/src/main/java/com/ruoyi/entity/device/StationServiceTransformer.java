package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import java.sql.Timestamp;

/**
 * 对应数据库表 station_service_transformer 的实体类
 * 用于映射表中的字段信息，方便在 Java 代码中操作该表的数据 0303
 */
@Data
@TableName("station_service_transformer")
public class StationServiceTransformer {

    /**
     * 资源ID，对应表中 psr_id 字段，类型为 varchar，长度50，非空，主键
     */
    private String psrId;

    /**
     * 设备名称，对应表中 name 字段，类型为 varchar，长度30
     */
    private String name;

    /**
     * 运行编号，对应表中 run_dev_name 字段，类型为 varchar，长度50
     */
    private String runDevName;

    /**
     * 资产ID，对应表中 ast_id 字段，类型为 varchar，长度50
     */
    private String astId;

    /**
     * 所属主干/分支线，对应表中 branch_feeder 字段，类型为 varchar，长度50
     */
    private String branchFeeder;

    /**
     * 使用性质
     */
    @TableField(exist = false)
    private String useNature;

    /**
     * 维护班组，对应表中 maint_group 字段，类型为 varchar，长度40
     */
    private String maintGroup;

    /**
     * 设备主人，对应表中 equipment_owner 字段，类型为 varchar，长度64
     */
    private String equipmentOwner;

    /**
     * 所属间隔，对应表中 bay 字段，类型为 varchar，长度50
     */
    private String bay;

    /**
     * 运行状态，对应表中 psr_state 字段，类型为 varchar，长度2
     */
    private String psrState;

    /**
     * 地区特征，对应表中 regionalism 字段，类型为 varchar，长度6
     */
    private String regionalism;

    /**
     * 运维单位，对应表中 maint_org 字段，类型为 varchar，长度40
     */
    private String maintOrg;

    /**
     * 所属地市，对应表中 city 字段，类型为 varchar，长度40
     */
    private String city;

    /**
     * 所属馈线，对应表中 feeder 字段，类型为 varchar，长度50
     */
    private String feeder;

    /**
     * 所属站房类型，对应表中 station_type 字段，类型为 varchar，长度6
     */
    private String stationType;

    /**
     * 是否农网，对应表中 is_rural 字段，类型为 varchar，长度2
     */
    private String isRural;

    /**
     * 所属站房，对应表中 station 字段，类型为 varchar，长度42
     */
    private String station;

    /**
     * 创建时间，对应表中 ctime 字段，类型为 timestamp，长度6
     */
    @JsonIgnore
    private Timestamp ctime;

    /**
     * 投运日期，对应表中 start_time 字段，类型为 timestamp，长度6
     */
    @JsonIgnore
    private Timestamp startTime;

    /**
     * 营配标识，对应表中 pub_priv_flag 字段，类型为 varchar，长度2
     */
    private String pubPrivFlag;

    /**
     * 电压等级，对应表中 voltage_level 字段，类型为 varchar，长度2
     */
    private String voltageLevel;

    /**
     * 最新更新时间，对应表中 last_update_time 字段，类型为 timestamp，长度6
     */
    @JsonIgnore
    private Timestamp lastUpdateTime;

    /**
     * 经度，对应表中 longitude 字段，类型为 float8（对应 Java 的 Double 类型），长度53
     */
    private Double longitude;

    /**
     * 纬度，对应表中 latitude 字段，类型为 float8（对应 Java 的 Double 类型），长度53
     */
    private Double latitude;

    /**
     * 额定容量，对应表中 rated_capacity 字段，类型为 varchar，长度255
     */
    private String ratedCapacity;

    /**
     * 所属接入点，对应表中 join_ec 字段，类型为 varchar，长度42
     */
    private String joinEc;
}
