package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

/**
 * 公变/专变信息
 */
@Data
@TableName("device_feeder_transformer_vol")
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class DeviceFeederTransformerVol{
    /**
     * 所属线路id
     */
    private String feeder;

    /**
     * 公变专变（0公 1专）
     */
    private String pubPrivFlag;

    /**
     * 额定容量
     */
    private String ratedCapacity;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String psrId;

    /**
     * 设备状态
     */
    private String psrState;


    /**
     * 设备类型
     */
    private String psrType;


    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 关联中压用户接入点
     */
    private String joinEc;

    public double[] getCoords() {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude)) {
            return null;
        }
        return new double[]{Double.parseDouble(longitude), Double.parseDouble(latitude)};
    }

}
