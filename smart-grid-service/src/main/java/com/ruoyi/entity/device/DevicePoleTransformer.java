package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * 柱上变压器表(0110) device_pole_transformer
 *
 * <AUTHOR> developer
 * @date 2025-04-11
 */
@Data
@TableName("device_pole_transformer")
public class DevicePoleTransformer {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 运行编号
     */
    private String runDevName;
    /**
     * 全路径名称
     */
    private String fullPathName;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 组织机构
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 所属杆塔
     */
    private String pole;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行编码
     */
    private String psrState;
    /**
     * 投运日期
     */
    @JsonIgnore
    private Date startTime;
    /**
     * 退运日期
     */
    @JsonIgnore
    private Date stopTime;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 重要程度
     */
    private String importance;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 供电区域
     */
    private String supplyArea;
    /**
     * 使用性质
     */
    private String useNature;
    /**
     * 管辖机构
     */
    private String dispatchJurisdiction;
    /**
     * 操作机构
     */
    private String dispatchOperation;
    /**
     * 许可机构
     */
    private String dispatchPermission;
    /**
     * 监控机构
     */
    private String dispatchMonitor;
    /**
     * 所属主干/分支线
     */
    private String branchFeeder;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 创建时间
     */
    @JsonIgnore
    private Date ctime;
    /**
     * 所属开关段
     */
    private String switchSegment;
    /**
     * 营配标识
     */
    private String pubPrivFlag;

    /**
     * 用电客户唯一标识
     */
    private String customerId;
    /**
     * 安装地址
     */
    private String installationAddress;
    /**
     * 所属接入点
     */
    private String joinEc;
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
    /**
     * 所属可靠性分段
     */
    private String reliableSegment;
    /**
     * 营销用户户号
     */
    private String consNo;
    /**
     * 营配配变运行状态
     */
    private String cmsState;
    /**
     * 营销运维单位
     */
    private String cmsMaintOrg;

    /**
     * 容量
     */
    private String ratedCapacity;

    private String geoPositon;

    private Double longitude;
    private Double latitude;


}
