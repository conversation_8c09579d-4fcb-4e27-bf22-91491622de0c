package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 配电架空线（dxd）
 */
@Data
@TableName("device_feeder_jk")
@EqualsAndHashCode()
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class DeviceFeederJk {
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    // 主键和基础标识字段
    private String psrId;             // 配电资源唯一标识ID
    private String astId;             // 资产ID
    private String branchFeeder;      // 分支馈线编号

    // 状态和分类字段
    private String psrState;          // 资源状态代码(20=正常)
    private String voltageLevel;      // 电压等级(22=22kV)
    private String wireArrangement;   // 导线排列方式(04=...)
    private String pubPrivFlag;       // 公有私有标志(0=公有)
    private String isRural;           // 是否农村电网(1=是)
    private String supplyArea;        // 供电区域代码(04=...)

    // 地理和组织信息字段
    private String city;              // 所属城市ID
    private String maintOrg;          // 运维单位ID
    private String maintGroup;        // 运维班组ID
    private String equipmentOwner;    // 设备所有者编号

    // 设备信息字段
    private String feeder;            // 馈线编号
    private String line;              // 所属线路编号
    private String name;              // 馈线完整名称
    private String startPole;         // 起始电杆编号
    private String stopPole;          // 终止电杆编号
    private String wire;              // 导线ID

    private String model;          // 设备型号
    private String wireType;              // 导线类型
          // 导线类型

    // 技术参数字段
    private Double referenceLength; // 参考长度(km)
    private Integer length;           // 线路长度(米)

    // 时间相关字段
    private Timestamp ctime;          // 数据创建时间
    private Timestamp startTime;      // 设备投运时间
    private Long lastUpdateTime;
    private String coordinate;// 最后更新时间戳(毫秒级Unix时间戳)
}
