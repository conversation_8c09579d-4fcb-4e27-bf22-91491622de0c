package com.ruoyi.entity.device.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据线路查询母线，间隔，变电站的拓扑关系
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ByFeederBusBarAndBreaker {
    /**
     * 变电站id
     */
    private String substationId;

    /**
     * 变电站名称
     */
    private String substationName;
    /**
     * 母线id
     */
    private String busBarId;

    /**
     * 母线名称
     */
    private String busBarName;

    /**
     * 间隔id
     */
    private String breakerId;

    /**
     * 间隔名称
     */
    private String breakerName;

    /**
     * 间隔是否在使用
     */
    private Boolean isInUse;

}
