package com.ruoyi.entity.device;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class SegFeeder {
    /**
     * 设备id
     */
    private String psrId;


    /**
     * 设备类型
     */
    private String psrType;

    /**
     * 设备名称
     */
    private String name;

    private String branchFeeder;      // 分支馈线编号

    private String line;  // 所属线路编号

    private String startPosition; // 起始位置ID

    private String endPosition; // 结束位置ID

    private String startType; // 起始节点类型

    private String endType; // 结束节点类型

    /**
     * 坐标
     */
    private String coordinate;

    private Integer length;           // 线路长度(米)

}
