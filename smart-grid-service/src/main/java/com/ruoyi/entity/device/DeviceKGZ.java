package com.ruoyi.entity.device;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * 电力开关站资源信息实体
 */
@Data
public class DeviceKGZ {

    // 基础标识信息
    private String psrId;            // 资源ID，电网设备唯一标识
    private String astId;            // 资产ID（可选）
    private String name;             // 设备名称
    private String runDevName;       // 运行编号，调度系统唯一标识
    private String stationType;      // 站房类型
    private String fullPathName;     // 全路径名称

    // 组织架构信息
    private String city;             // 所属地市，引用Organization字典
    private String maintOrg;         // 运维单位，引用Organization字典
    private String maintGroup;       // 维护班组，引用Organization字典
    private String equipmentOwner;   // 设备主人，引用User表

    // 技术参数
    private String voltageLevel;     // 电压等级，引用010401字典
    private String psrState;         // 运行状态，引用010402字典
    private Integer backupLinebayQua; // 备用进出线间隔数
    private String arrangement;      // 布置方式，引用010502字典
    private String contaminationLevel; // 污秽等级，引用010101字典
    private String isLargeStation;   // 是否大型开关站，引用010599字典

    // 位置信息
    private String address;          // 站址
    private String geoPosition;      // 坐标位置

    // 时间信息
    @JsonIgnore
    private Date startTime;          // 投运日期
    @JsonIgnore
    private Date stopTime;           // 退运日期
    @JsonIgnore
    private Date ctime;              // 创建时间
    @JsonIgnore
    private Date lastUpdateTime;     // 最新更新时间(带毫秒)

    // 分类特征
    private String isRural;          // 是否农网，引用010599字典
    private String importance;       // 重要程度，引用0199003字典
    private String regionalism;      // 地区特征，引用0199001字典
    private String supplyArea;       // 供电区域，引用0199028字典
    private String isRingNetwork;    // 是否环网，引用010599字典

    // 调度信息
    private String dispatchJurisdiction; // 管辖机构，引用omsOrganization字典
    private String dispatchOperation;    // 操作机构，引用omsOrganization字典
    private String dispatchPermission;   // 许可机构，引用omsOrganization字典
    private String dispatchMonitor;      // 监控机构，引用omsOrganization字典

    // 电网拓扑
    private String line;             // 所属线路，引用xl字典
    private String branchFeeder;     // 所属主干/分支线，引用zgfz字典
    private String feeder;           // 所属馈线，引用dkx字典

    // 营销信息
    private String pubPrivFlag;      // 营配标识，引用ypbs字典
}
