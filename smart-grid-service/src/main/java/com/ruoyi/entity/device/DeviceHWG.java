package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 环网柜对象 device_hwg
 *
 * <AUTHOR> developer
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("device_hwg")
public class DeviceHWG  extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 运行编码
     */
    private String runDevName;
    /**
     * 站房类型
     */
    private String psrType;
    /**
     * 全路径名称
     */
    private String fullPathName;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    @JsonIgnore
    private Date startTime;
    /**
     * 退运日期
     */
    @JsonIgnore
    private Date stopTime;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 重要程度
     */
    private String importance;
    /**
     * 备用进出线间隔数
     */
    private Long backupLinebayQua;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 供电区域
     */
    private String supplyArea;
    /**
     * 布置方式
     */
    private String arrangement;
    /**
     * 是否环网
     */
    private String isRingNetwork;
    /**
     * 站址
     */
    private String address;
    /**
     * 污秽等级
     */
    private String contaminationLevel;
    /**
     * 管辖机构
     */
    private String dispatchJurisdiction;
    /**
     * 操作机构
     */
    private String dispatchOperation;
    /**
     * 许可机构
     */
    private String dispatchPermission;
    /**
     * 监控机构
     */
    private String dispatchMonitor;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 创建时间
     */
    @JsonIgnore
    private Date ctime;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 坐标位置
     */
    private String geoPositon;
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
    /**
     * "所属主干/分支线(用于存量数据点设备)"
     */
    private String branchFeeder;
    /**
     * 所属馈线(用于存量数据点设备)
     */
    private String feeder;
    /**
     * 是否大型开关站
     */
    private String isLargeStation;
    /**
     * 是否标准化
     */
    private String isStandardized;
    /**
     * 高程
     */
    private String posZ;
    /**
     * 换流站类型
     */
    private String converterStationType;
    /**
     * 额定直流输送能力
     */
    private String ratedDcTransmissionCapacity;
    /**
     * 是否一二次融合成套
     */
    private String isPmrScdrIntegration;
    /**
     * 所属网格
     */
    private String gridCode;
    /**
     * 历史最高水位
     */
    private String hisCrestStage;
    /**
     * 历史最大雨量（24h）
     */
    private String hisLargestRainfall;
    /**
     * 是否重要防汛站房
     */
    private String isFloodImportance;
    /**
     * 用户编号
     */
    private String consNo;
    /**
     * 定位方式
     */
    private String locateMode;
    /**
     * 坐标来源
     */
    private String updateMode;

}
