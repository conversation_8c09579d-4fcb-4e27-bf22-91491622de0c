package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 配电电缆终端接头信息表
 * 对应数据库表: device_cable_terminal_joint
 */
@Data
@TableName("device_cable_terminal_joint")
public class DeviceCableTerminalJoint {
    // 主键和基础标识字段
    private String psrId;         // 配电资源唯一标识ID
    private String astId;         // 资产ID

    // 状态和分类字段
    private String psrState;      // 资源状态代码（如20表示正常）
    private String voltageLevel;  // 电压等级（如22表示22kV）
    private String installationLocation; // 安装位置类型代码（如02表示某种位置类型）
    private String pubPrivFlag;   // 公有/私有标志（0表示公有）
    private String importance;    // 重要程度等级

    // 地理信息字段
    private String city;          // 所属城市标识ID
    private String geoPosition;   // 地理位置坐标（经度,纬度）

    // 设备信息字段
    private String feeder;        // 馈线编号/名称
    private String line;          // 所属线路编号
    private String name;          // 馈线名称/描述
    private String cable;         // 关联电缆ID

    // 管理信息字段
    private String maintOrg;      // 运维单位标识ID
    private String maintGroup;    // 运维班组ID
    private String equipmentOwner; // 设备所有者编号
    private String branchFeeder;  // 分支馈线ID

    // 时间相关字段
    private Timestamp ctime;      // 数据创建时间（YYYY-MM-DD HH:MI:SS）
    private Timestamp startTime;  // 设备投运开始时间
    private Long lastUpdateTime;  // 最后更新时间戳（毫秒级Unix时间戳）
    private String state;  // 最后更新时间戳（毫秒级Unix时间戳）
}
