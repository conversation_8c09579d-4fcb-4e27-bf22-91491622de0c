package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("middle_pole_break")
public class MiddlePoleBreak {
    /**
     * 主键ID
     */
    @TableId(value = "psr_id", type = IdType.INPUT)
    private String psrId;

    /**
     * psr类型
     */
    @TableField("psr_type")
    private String psrType;

    /**
     * ast_id
     */
    @TableField("ast_id")
    private String astId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;


    @TableField("dispatch_permission")
    private String dispatchPermission;


    @TableField("switch_role")
    private String switchRole;

    /**
     * psr状态
     */
    @TableField("psr_state")
    private String psrState;


    @TableField("regionalism")
    private String regionalism;

    /**
     * 维护组织
     */
    @TableField("maint_org")
    private String maintOrg;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 馈线
     */
    @TableField("feeder")
    private String feeder;

    /**
     * 线路
     */
    @TableField("line")
    private String line;


    @TableField("importance")
    private String importance;

    /**
     * 供电区域
     */
    @TableField("supply_area")
    private String supplyArea;

    /**
     * 调度监控
     */
    @TableField("dispatch_monitor")
    private String dispatchMonitor;

    /**
     * 杆塔
     */
    @TableField("pole")
    private String pole;


    @TableField("dispatch_operation")
    private String dispatchOperation;

    /**
     * 运行设备名称
     */
    @TableField("run_dev_name")
    private String runDevName;


    @TableField("is_rural")
    private String isRural;

    /**
     * 创建时间
     */
    @TableField("ctime")
    private LocalDateTime ctime;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;


    @TableField("pub_priv_flag")
    private String pubPrivFlag;

    /**
     * 设备所有者
     */
    @TableField("equipment_owner")
    private String equipmentOwner;

    /**
     * 维护班组
     */
    @TableField("maint_group")
    private String maintGroup;

    /**
     * 调度管辖
     */
    @TableField("dispatch_jurisdiction")
    private String dispatchJurisdiction;

    /**
     * 分支馈线
     */
    @TableField("branch_feeder")
    private String branchFeeder;

    /**
     * 电压等级
     */
    @TableField("voltage_level")
    private String voltageLevel;

    /**
     * 常开状态
     */
    @TableField("normal_open")
    private String normalOpen;

    /**
     * 可靠区段
     */
    @TableField("reliable_segment")
    private String reliableSegment;

    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     * 经度
     */
    @TableField("longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private Double latitude;
}