package com.ruoyi.entity.device.vo;

import com.ruoyi.entity.device.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceApiCollector {
    //线路
    private List<DeviceFeeder> deviceFeeders;
    //环网柜
    private List<DeviceHWG> hwgList;
    //开关站
    private List<DeviceKGZ> kgzList;
    //配电室
    private List<DevicePDS> pdsList;
    //柱上变
    private List<DevicePoleTransformer> poleTransformerList;
    //站内变
    private List<DeviceStationTransformer> deviceStationTransformerList;
    //所有变
    private List<StationServiceTransformer> stationServiceTransformerList;
    //主变
    private List<DeviceZB> zbList;
}
