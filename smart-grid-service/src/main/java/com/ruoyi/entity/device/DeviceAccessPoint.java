package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 中压用户接入点对象 device_access_point
 *
 * <AUTHOR> developer
 * @date 2025-04-11
 */
@Data
@TableName("device_access_point")
public class DeviceAccessPoint {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 运行编号
     */
    private String runDevName;
    /**
     * 所属城市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 所属电站
     */
    private String station;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 并网形式
     */
    private String gridForm;
    /**
     * 电源形式
     */
    private String powerForm;
    /**
     * 供电方式
     */
    private Long powerSupply;
    /**
     * 关联用户
     */
    private String userId;
    /**
     * 容量
     */
    private String capacity;
    /**
     * 所属主干/分支线
     */
    private String branchFeeder;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    private Date startTime;
    /**
     * 退运日期
     */
    private Date stopTime;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 坐标位置
     */
    private String geoPositon;
    /**
     * 最新更新时间
     */
    private String lastUpdateTime;
    /**
     * 所属可靠性分段
     */
    private String reliableSegment;
    /**
     * 连接运检设备ID
     */
    private String joinDevId;
    /**
     * 连接运检设备类型
     */
    private String joinDevType;
    //忽略sql查询
    @TableField(exist = false)
    private String gridCode;

}
