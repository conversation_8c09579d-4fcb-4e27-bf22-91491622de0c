package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 配电跌落式熔断器
 * 对应数据库表: drop-out fuse
 */
@Data
@TableName("device_drop_out_fuse")
public class DeviceDropOutFuse {
    // 主键和基础标识字段
    private String psrId;             // 配电资源唯一标识ID
    private String astId;             // 资产ID
    private String branchFeeder;      // 分支馈线ID
    private String feederSegment;     // 馈线段ID
    private String reliableSegment;   // 可靠线段ID

    // 状态和分类字段
    private String psrState;          // 资源状态代码（20表示正常）
    private String regionalism;       // 行政区划代码
    private String voltageLevel;      // 电压等级（22表示22kV）
    private String importance;        // 重要程度等级
    private String isRural;           // 是否农村电网（0否1是）
    private String pubPrivFlag;       // 公有/私有标志（0公有1私有）
    private String isStandardized;    // 是否标准化（0否1是）
    private String normalOpen;        // 正常开断状态标识

    // 地理和组织信息字段
    private String city;              // 所属城市标识ID
    private String maintOrg;          // 运维单位标识ID
    private String maintGroup;        // 运维班组ID
    private String equipmentOwner;    // 设备所有者编号

    // 设备信息字段
    private String feeder;            // 馈线编号/名称
    private String line;              // 所属线路编号
    private String pole;              // 电杆/杆塔编号
    private String runDevName;        // 运行设备名称
    private String name;              // 设备名称/描述
    private String cable;             // 关联电缆ID

    // 时间相关字段
    private Timestamp ctime;          // 数据创建时间（YYYY-MM-DD HH:MI:SS）
    private Timestamp startTime;      // 设备投运开始时间
    private Long lastUpdateTime;      // 最后更新时间戳（毫秒级Unix时间戳）
}
