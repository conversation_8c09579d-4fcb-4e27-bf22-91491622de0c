package com.ruoyi.entity.device;

import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class PublicSpecializedTransformer {

    /**
     * 所属线路id
     */
    private String feeder;

    /**
     * 所属变电站id
     */
    private String substationId;


    /**
     * 所属变电站名称
     */
    private String substationName;

    /**
     * 公变专变（0公 1专）
     */
    @TransField(type = TransType.EXCEL_DICT, resultHolderField = "pubPrivFlagLabel")
    @ExcelDictFormat(readConverterExp = "0=公变,1=专变")
    private String pubPrivFlag;

    private String pubPrivFlagLabel;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备id
     */
    private String psrId;

    /**
     * 设备状态
     */
    @TransField(type = TransType.DICTIONARY, key = "device_state", resultHolderField = "psrStateLabel")
    private String psrState;

    private String psrStateLabel;


    /**
     * 设备类型
     */
    @TransField(type = TransType.DICTIONARY, key = "device_type", resultHolderField = "psrTypeLabel")
    private String psrType;

    private String psrTypeLabel;


    private String ratedCapacity;

    private String voltageLevel;
    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;



    /**
     * 关联中压用户接入点
     */
    private String joinEc;

    public double[] getCoords() {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude)) {
            return null;
        }
        return new double[]{Double.parseDouble(longitude), Double.parseDouble(latitude)};
    }

}
