package com.ruoyi.entity.device.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备负载率最大值实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DeviceLoadMaxVo {
    
    /**
     * 日期
     */
    private String date;
    
    /**
     * 负载率最大值
     */
    private Double maxValue;
}
