package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.OneToMany;

/**
 * 变电站对象 device_substation
 *
 * <AUTHOR> developer
 * @date 2025-04-14
 */

@Data
@TableName("device_substation")
public class DeviceSubstation {

    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id", type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 站房类型
     */
    private String stationType;
    /**
     * 运行编号
     */
    private String runDevName;


    /**
     * 所属地市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    private Date startTime;

    /**
     * 重要级别
     */
    private String importantLevel;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 供电区域
     */
    private String supplyArea;
    /**
     * 值班方式
     */
    private String dutyMode;

    /**
     * 电站海拔
     */
    private String altitude;
    /**
     * 占地面积
     */
    private Double coverArea;

    /**
     * 电站地址
     */
    private String address;
    /**
     * 污秽等级
     */
    private String contaminationLevel;
    /**
     * 主变台数
     */
    private Long transformerQuantity;
    /**
     * 电站容量
     */
    private Double stationCapacity;
    /**
     * 是否GIS站
     */
    private String isGis;

    /**
     * 是否枢纽站
     */
    private String isJunctionStation;
    /**
     * 是否智能站
     */
    private String isSmartStation;


    /**
     * 是否集中监控
     */
    private String isCentralMonitor;
    /**
     * 布置方式
     */
    private String arrangement;


    /**
     * 坐标位置
     */
    private String geoPositon;
    /**
     * 消防验收情况
     */
    private String fireAcceptance;
    /**
     * 站房消防类型
     */
    private String fireType;

    /**
     * 关联d5000数据库的变电站id
     */
    private String sgId;

    private String emsId;

    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 最后更新时间
     */
    private Long lastUpdateTime;

    @OneToMany(mappedBy = "MiddleSubstationNew", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonProperty("mainTransformer")
    @TableField(exist = false)
    private List<DeviceNtHighTransformer> mainTransformer;
}
