package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 配电柱上负荷开关（0112）、配电柱上断  路器（0111）对象 device_pole_break
 *
 * <AUTHOR> developer
 * @date 2025-06-06
 */
@Data
@TableName("device_pole_break")
public class DevicePoleBreak  {

    private static final long serialVersionUID=1L;

    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * $column.columnComment
     */
    private String psrType;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 许可机构
     */
    private String dispatchPermission;
    /**
     * 开关作用
     */
    private String switchRole;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 重要程度
     */
    private String importance;
    /**
     * 供电区域
     */
    private String supplyArea;
    /**
     * 监控机构
     */
    private String dispatchMonitor;
    /**
     * 所属杆塔
     */
    private String pole;
    /**
     * 操作机构
     */
    private String dispatchOperation;
    /**
     * 运行编号
     */
    private String runDevName;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 创建时间
     */
    private String ctime;
    /**
     * 投运日期
     */
    private String startTime;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 管辖机构
     */
    private String dispatchJurisdiction;
    /**
     * 所属主干/分支线
     */
    private String branchFeeder;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 计划开关状态
     */
    private String normalOpen;
    /**
     * 所属可靠性分段
     */
    private String reliableSegment;
    /**
     * 最新更新时间
     */
    private String lastUpdateTime;
    /**
     * $column.columnComment
     */
    private String longitude;
    /**
     * $column.columnComment
     */
    private String latitude;

}
