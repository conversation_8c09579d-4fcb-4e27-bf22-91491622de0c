package com.ruoyi.entity.device;

import com.alibaba.druid.sql.visitor.functions.If;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 站内开关
 */
@Data
public class StationKg {

    /**
     * 资源ID
     */
    private String psrId;

    /**
     * 设备类型
     */
    private String psrType;

    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;


    /**
     * 所属站房
     */
    private String station;

    /**
     * 所属间隔
     */
    private String bay;

    /**
     * 所属站房类型
     */
    private String stationType;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 运行状态
     */
    private String psrState;


    /**
     * "所属主干/分支线(用于存量数据点设备)"
     */
    private String branchFeeder;
    /**
     * 所属馈线
     */
    private String feeder;

    /**
     * 营配标识
     */
    private String pubPrivFlag;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    public boolean isNotEmptyLngLat() {
        return longitude != null && longitude != 0.0 && latitude != null && latitude != 0.0;
    }
}
