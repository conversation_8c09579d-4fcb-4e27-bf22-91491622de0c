package com.ruoyi.entity.device.vo;

import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.Data;

/**
 * 公变/专变信息
 */
@Data
public class DeviceFeederTransformerVolVo {
    /**
     * 所属线路id
     */
    private String feeder;

    /**
     * 公变专变（0公 1专）
     */
    private String pubPrivFlag;

    /**
     * 额定容量
     */
    private String ratedCapacity;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备id
     */
    private String psrId;

    /**
     * 设备状态
     */
    @TransField(type = TransType.DICTIONARY, key = "device_state", resultHolderField = "psrStateLabel")
    private String psrState;

    private String psrStateLabel;

    /**
     * 设备类型
     */
    @TransField(type = TransType.DICTIONARY, key = "device_type", resultHolderField = "psrTypeLabel")
    private String psrType;

    private String psrTypeLabel;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;



}
