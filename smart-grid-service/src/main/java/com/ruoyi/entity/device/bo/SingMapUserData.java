package com.ruoyi.entity.device.bo;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

@Data
public class SingMapUserData {

    /**
     * gis的ID
     */
    String GIS_OID;


    /**
     * gis的ID
     */
    String GIS_OID_TMP;

    /**
     * gis的设备类型
     */
    String GIS_SBZLX;

    /**
     * gis的关联ID“id1,id2”拼接的
     */
    String GIS_TERMINAL;

    String HasReconn;

    String TopType;

    String connectionType;

    /**
     * gis的设备ID如果没有PSRId 就使用该该ID
     */
    String geoPsrId;

    String gis_sbid;

    /**
     * 设备ID（有可能为空，为空就使用geoPsrId）
     */
    String PSRID;

    /**
     * 设备类型
     */
    String PSRType;

    String PSRName;

    /**
     * 联络单设备ID
     */
    String SuperLinkDiagramPSRID;

    /**
     * 联络单设备类型
     */
    String SuperLinkDiagramPSRType;

    String SuperLinkMode;

    String SuperLinkTip;


//    public String getPsrId(boolean isGisPsrId) {
//        return StringUtils.isNotBlank(PSRID) ? PSRID : isGisPsrId ? GIS_OID : null;
//    }
//
//    public String getPsrType(boolean isGisPsrType) {
//        return StringUtils.isNotBlank(PSRType) ? PSRType : isGisPsrType ? GIS_SBZLX : null;
//    }

    /**
     * 根据设备ID 比较
     *
     * @param psrId   设备ID
     * @param isGisId 些设备通过PSRID是没法找的 isGisId：true 表示可以通过GIS_OID比较
     */
    public boolean psrEquals(String psrId, boolean isGisId) {
        return StringUtils.equals(PSRID, psrId) || (isGisId && StringUtils.equals(geoPsrId, psrId));
    }
}
