package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 物理杆塔设备信息实体类
 * 对应数据库表: device_wlgt
 */
@Data
@TableName("device_wlgt")
public class DeviceWlgt {
    // 主键和基础标识字段
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String astId;               // 资产ID
    private String equipCode;           // 设备编码
    private String astOrg;              // 资产所属单位ID

    // 设备基本信息字段
    private String basicForm;           // 基础形式(12=...)
    private String astNature;           // 资产性质(03=...)
    private String isCommission;        // 是否投运(0=未投运)
    private Integer loopQuantity;       // 回路数量
    private String deployState;         // 部署状态(20=...)

    // 物理特性字段
    private String crossArmMaterial;    // 横担材料(1=...)
    private Double poleHigh;            // 杆塔高度(米)
    private String poleMaterial;        // 杆塔材料(4=...)
    private String isSamePole;          // 是否同杆(0=否)
    private String model;               // 型号规格

    // 生产信息字段
    private String manufacturer;        // 制造商编号
    private Timestamp manufactureDate;  // 生产日期

    // 地理信息字段
    private String geoPositon;         // 地理坐标(WGS84格式)

    // 管理信息字段
    private String astOrgName;          // 资产所属单位名称
    private String remark;              // 备注信息

    // 时间相关字段
    private Timestamp ctime;            // 创建时间
    private Timestamp operateDate;      // 投运日期
    private Timestamp lastUpdateTime;   // 最后更新时间
    private Integer state;   // 最后更新时间
}
