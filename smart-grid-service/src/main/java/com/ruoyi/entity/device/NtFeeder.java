package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableId;

import java.util.Date;

/**
 * 线路最大相关最大值 对象 nt_feeder
 */
@Data
@TableName("nt_feeder")
public class NtFeeder {

    /**
     * 可开放容量
     */
    private String allowMaxCur;
    /**
     * $column.columnComment
     */
    private String amprating;
    /**
     * 在途申请容量
     */
    private String applyCap;
    /**
     * 区域Id
     */
    private String areaId;
    /**
     * 所属区域
     */
    private String areaName;
    /**
     * 资产类型
     */
    private String assetType;
    /**
     * 母线Id
     */
    private String busbarId;
    /**
     * 母线名称
     */
    private String busbarName;
    /**
     * 变压器容量
     */
    private String byqCap;
    /**
     * 变压器数量
     */
    private Long byqNum;
    /**
     * 可开放容量
     */
    private String cap;
    /**
     * 可开放免费容量
     */
    private String capFree;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * $column.columnComment
     */
    private String ctRatedCurrent;
    /**
     * $column.columnComment
     */
    private String ctRatio;
    /**
     * $column.columnComment
     */
    private String dPowerCap;
    /**
     * $column.columnComment
     */
    private String dPowerMaxCur;
    /**
     * $column.columnComment
     */
    private Long dPowerNum;
    /**
     * $column.columnComment
     */
    private String describe;
    /**
     * $column.columnComment
     */
    private String existTgCap;
    /**
     * $column.columnComment
     */
    private Long existTgNum;
    /**
     * $column.columnComment
     */
    private String fileUrl;
    /**
     * $column.columnComment
     */
    private Long gbNum;
    /**
     * $column.columnComment
     */
    private String heavyCurrent;
    /**
     * 最大历史电流
     */
    private String hisMaxCurrent;
    /**
     * 最大历史电流日期
     */
    private String hisMaxDate;
    /**
     * 负载率
     */
    private String hisMaxLoadRate;
    /**
     * Id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * $column.columnComment
     */
    private String inom;
    /**
     * $column.columnComment
     */
    private String isAccess;
    /**
     * $column.columnComment
     */
    private String isRelated;
    /**
     * $column.columnComment
     */
    private String lineConnect;
    /**
     * 线路名称
     */
    private String lineName;
    /**
     * $column.columnComment
     */
    private String lineProperty;
    /**
     * 线路类型
     */
    private String lineType;
    /**
     * $column.columnComment
     */
    private String loadType;
    /**
     * 主变裕度
     */
    private String mainByqCap;
    /**
     * 主变Id
     */
    private String mainByqId;
    /**
     * 主变名称
     */
    private String mainByqName;
    /**
     * $column.columnComment
     */
    private String notExistTgCap;
    /**
     * $column.columnComment
     */
    private Long notExistTgNum;
    /**
     * $column.columnComment
     */
    private Long num;
    /**
     * $column.columnComment
     */
    private Long occTime;
    /**
     * $column.columnComment
     */
    private String oldBsName;
    /**
     * $column.columnComment
     */
    private String oldMainByqName;
    /**
     * 页码
     */
    private Long pageNum;
    /**
     * 页数
     */
    private String pageSize;
    /**
     * 配变容量
     */
    private String pbCap;
    /**
     * 配变数量
     */
    private Long pbNum;
    /**
     * 变压器数量
     */
    private Long pmsByqNum;
    /**
     * 配变数量
     */
    private Long pmsPdByqNum;
    /**
     * 柱上变压器数量
     */
    private Long pmsZsByqNum;
    /**
     * $column.columnComment
     */
    private String privateByqCap;
    /**
     * $column.columnComment
     */
    private Long privateByqNum;
    /**
     * $column.columnComment
     */
    private String publicByqCap;
    /**
     * $column.columnComment
     */
    private Long publicByqNum;
    /**
     * 额定电流
     */
    private String ratedCurrent;
    /**
     * d5000额定电流
     */
    private String ratedCurrentD5000;
    /**
     * 默认额定电流
     */
    private String ratedCurrentDefault;
    /**
     * 手工额定电流
     */
    private String ratedCurrentManual;
    /**
     * 额定电流类型
     */
    private String ratedCurrentType;
    /**
     * $column.columnComment
     */
    private String ratedPower;
    /**
     * $column.columnComment
     */
    private Long readyByqNum;
    /**
     * $column.columnComment
     */
    private String readyByqCap;
    /**
     * 实际容量
     */
    private String realCap;
    /**
     * $column.columnComment
     */
    private Date realTimeCurrent;
    /**
     * $column.columnComment
     */
    private Long recordNumber;
    /**
     * $column.columnComment
     */
    private String relayCurrent;
    /**
     * $column.columnComment
     */
    private String status;
    /**
     * $column.columnComment
     */
    private String statusManual;
    /**
     * 所属变电站Id
     */
    private String substationId;
    /**
     * 所属变电站名称
     */
    private String substationName;
    /**
     * 电压等级
     */
    private String substationVolGrade;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 电压等级Id
     */
    private String volGradeId;
    /**
     * 线路电压等级
     */
    private String volGradeName;
    /**
     * $column.columnComment
     */
    private String ycAId;
    /**
     * $column.columnComment
     */
    private String ycBId;
    /**
     * $column.columnComment
     */
    private String ycCId;
    /**
     * $column.columnComment
     */
    private String ycId;
    /**
     * $column.columnComment
     */
    private Long zbNum;
    /**
     * 线路psr_id
     */
    private String psrId;
    /**
     * $column.columnComment
     */
    private String rdfId;
    /**
     * 线路最大电流值
     */
    private String lineMaxValN;
    /**
     * 线路最大电流日期
     */
    private String lineMaxDate;
    /**
     * $column.columnComment
     */
    private String lineMaxVal;
    /**
     * $column.columnComment
     */
    private String center;
    /**
     * $column.columnComment
     */
    private String assetoid;
    /**
     * $column.columnComment
     */
    private String assetsubtype;
    /**
     * 数据来源，本次是人工的还是平台同步的
     */
    private String origin;
    /**
     * 该数据是否与人工标冲突,1-是，0-否
     */
    private Long conflictWith;

}
