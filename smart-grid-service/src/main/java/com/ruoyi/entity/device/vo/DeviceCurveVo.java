package com.ruoyi.entity.device.vo;

import com.ruoyi.entity.problem.Statistics;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备曲线list实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class DeviceCurveVo {
    /**
     * 曲线list
     */
    private List<Statistics> statisticsList;

    /**
     * 曲线list
     */
    private String type;

    private String name;
}
