package com.ruoyi.entity.device.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.utils.util.DoubleFormatter;
import lombok.Data;
import org.apache.commons.collections4.Get;
import org.locationtech.jts.geom.Coordinate;

import java.util.Date;
import java.util.List;

/**
 * 线路对应相关运行值
 */
@Data
public class FeederNtVo {

    /**
     * 资源ID，电网设备唯一标识
     */
    @TableId(value = "psr_id", type = IdType.ASSIGN_UUID)
    private String psrId;

    /**
     * 线路名称
     */
    private String name;

    /**
     * 装机容量
     */
    private Double feederRateCapacity;

    /**
     * 负载率
     */
    private Double hisMaxLoadRate;

    /**
     * 发生时间
     * @return
     */
    private String hisMaxDate;

    /**
     * 历史最大电流
     * @return
     */
    private Double hisMaxCurrent;

    /**
     * 主变名称
     */
    private String mainByqName;


    public Double getHisMaxLoadRate() {
        // TODO 临时以防查不到出问题  后面nt_feeder表 需要重新刷
        return hisMaxLoadRate == null ? 0.7 : hisMaxLoadRate;
    }

    /**
     * 获取百分比的历史最大负载率 字符串
     */
    public String getHisMaxLoadRateStr() {
        return String.valueOf(DoubleFormatter.formatToThreeDecimals3(getHisMaxLoadRate() * 100));
    }
}
