package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceZB {
    /**
     * 资源ID
     */
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;

    private String station;
    private String stationName;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 运行编号
     */
    private String runDevName;
    /**
     * 全路径名称
     */
    private String fullPathName;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 组织机构
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;

    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 运行编码
     */
    private String psrState;

    /**
     * 容量
     */
    private Double ratedCapacity;

    /**
     * 状态(1重载，2中载。3轻载)
     */
    private Integer type;

    /**
     * 负载率
     */
    private Double loadRate;

    /**
     * 历史最大功率
     */
    private Double hisMaxLoad;
    /**
     * 坐标位置
     */
    private String geoPositon;
}
