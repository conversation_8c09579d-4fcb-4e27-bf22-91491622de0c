package com.ruoyi.entity.cost;


import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = CostBayUnit.class, name = "CostBayUnit"),
        @JsonSubTypes.Type(value = CostBoxTypeSubstation.class, name = "CostBoxTypeSubstation"),
        @JsonSubTypes.Type(value = CostCableLine.class, name = "CostCableLine"),
        @JsonSubTypes.Type(value = CostPoleSwitch.class, name = "CostPoleSwitch"),
        @JsonSubTypes.Type(value = CostPoleUbstation.class, name = "CostPoleUbstation"),
        @JsonSubTypes.Type(value = CostPowerDistributionRoom.class, name = "CostPowerDistributionRoom"),
        @JsonSubTypes.Type(value = CostPowerDuct.class, name = "CostPowerDuct"),
        @JsonSubTypes.Type(value = CostPowerGroove.class, name = "CostPowerGroove"),
        @JsonSubTypes.Type(value = CostPowerTunnel.class, name = "CostPowerTunnel"),
        @JsonSubTypes.Type(value = CostRingCabinet.class, name = "CostRingCabinet"),
        @JsonSubTypes.Type(value = CostSwitchStation.class, name = "CostSwitchStation"),
        @JsonSubTypes.Type(value = CostTransformer.class, name = "CostTransformer"),
        @JsonSubTypes.Type(value = CostTrollyWire.class, name = "CostTrollyWire"),
})

@Data
public class CostType {
    /**
     * 总成本
     */
    private Double totalCost;

    /**
     * 采购成本
     */
    private Double purchaseCost;

    /**
     * 建设成本
     */
    private Double buildingCost;

    /**
     * 安装成本
     */
    private Double installCost;

    /**
     * 其他成本
     */
    private Double otherCost;

    /**
     * 数量
     */
    private Double num;



}
