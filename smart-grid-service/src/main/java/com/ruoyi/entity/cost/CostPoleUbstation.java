package com.ruoyi.entity.cost;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 柱上变
 */
@Data
@TableName("cost_pole_ubstation")
@NoArgsConstructor
@AllArgsConstructor
public class CostPoleUbstation extends CostType{
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设计名称
     */
    private String designName;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 容量
     */
    private Integer capacity;




}
