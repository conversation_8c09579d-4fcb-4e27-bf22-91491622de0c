package com.ruoyi.entity.cost;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 电力隧道
 */
@Data
@TableName("cost_power_tunnel")
@NoArgsConstructor
@AllArgsConstructor
public class CostPowerTunnel extends CostType{
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设计名称
     */
    private String designName;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 排管类型
     */
    private String ductType;

    /**
     * 孔数规模
     */
    private String maxCurrent;

    /**
     * 建设形式
     */
    private String constructForm;







}
