package com.ruoyi.entity.cost;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 柱上开关
 */
@Data
@TableName("cost_pole_switch")
@NoArgsConstructor
@AllArgsConstructor
public class CostPoleSwitch extends CostType{
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设计名称
     */
    private String designName;



    /**
     * 开关类型
     */
    private String switchType;

    /**
     * 最大电流
     */
    private Integer maxCurrent;

    /**
     *开断电流
     */

    private Integer cutCurrent;
    /**
     * 电压等级
     */
    private String voltageLevel;


}
