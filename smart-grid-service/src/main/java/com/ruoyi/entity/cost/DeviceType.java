package com.ruoyi.entity.cost;

import lombok.Data;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
public class DeviceType {
    //
    public static final String RUNGT = "0103";    // 运行杆塔

    public static final String WLGT = "wlgt"; // 物理杆塔

    public static final String HWG1 = "0324"; // 环网柜1

    public static final String HWG2 = "zf07"; // 环网柜2

    public static final String KGZ = "zf04"; // 开关站

    public static final String ZSFHKG = "0112"; // 柱上负荷开关

    public static final String ZSDLQ = "0111"; // 柱上断路器

    public static final String ZNFHKG = "0307"; //站内负荷开关

    public static final String ZNDLQ = "0305"; // 站内断路器

    public static final String ZNGLKG = "0306"; // 站内隔离开关

    public static final String ZNPDBYQ = "0302"; // 站内配电变压器

    public static final String XB1 = "0323"; // 箱变1
    public static final String XB2 = "zf08"; // 箱变2

    public static final String PDS = "zf10"; // 配电室（配电配电站）

    public static final String ZSB = "0110"; // 柱上变

//    public static final String DLPG = "1010003"; // 电力排管

    public static final String JK = "dxd"; // 架空线

    public static final String CABLE = "0201"; // 电缆线

    public static final String SYB = "0303"; // 所有变

    public static final String ZB = "0301"; // 主变

    public static final String BUS = "0311"; // 母线

    public static final String SDDLDZT = "0202"; // 输电电缆终端头

    public static final String SDDLZJJT = "0203"; // 输电电缆中间接头

    public static final String DKX = "dkx"; //

    public static final String BDZ = "zf01"; //



    // 设备类型编码到名称的映射
    private static final Map<String, String> DEVICE_TYPE_MAP = new HashMap<>();

    static {
        // 初始化映射关系
        DEVICE_TYPE_MAP.put(RUNGT, "运行杆塔");
        DEVICE_TYPE_MAP.put(WLGT, "物理杆塔");
        DEVICE_TYPE_MAP.put(HWG1, "环网柜1");
        DEVICE_TYPE_MAP.put(HWG2, "环网柜2");
        DEVICE_TYPE_MAP.put(KGZ, "开关站");
        DEVICE_TYPE_MAP.put(ZSFHKG, "柱上负荷开关");
        DEVICE_TYPE_MAP.put(ZSDLQ, "柱上断路器");
        DEVICE_TYPE_MAP.put(ZNFHKG, "变电站内负荷开关");
        DEVICE_TYPE_MAP.put(ZNDLQ, "变电站内断路器");
        DEVICE_TYPE_MAP.put(ZNGLKG, "变电站内隔离开关");
        DEVICE_TYPE_MAP.put(ZNPDBYQ, "变电站内配电变压器");
        DEVICE_TYPE_MAP.put(XB1, "箱变1");
        DEVICE_TYPE_MAP.put(XB2, "箱变2");
        DEVICE_TYPE_MAP.put(PDS, "配电室");
        DEVICE_TYPE_MAP.put(ZSB, "柱上变");
        DEVICE_TYPE_MAP.put(JK, "架空线");
        DEVICE_TYPE_MAP.put(CABLE, "电缆线");
        DEVICE_TYPE_MAP.put(SYB, "所有变");
        DEVICE_TYPE_MAP.put(BUS, "母线");
        DEVICE_TYPE_MAP.put(SDDLDZT, "输电电缆终端头");
        DEVICE_TYPE_MAP.put(SDDLZJJT, "0303");
    }

    /**
     * 根据设备类型编码获取设备类型名称
     * @param deviceTypeCode 设备类型编码
     * @return 设备类型名称，如果未找到则返回null
     */
    public static String getDeviceTypeName(String deviceTypeCode) {
        return DEVICE_TYPE_MAP.get(deviceTypeCode);
    }

    /**
     * 根据设备类型编码获取设备类型名称，未找到时返回默认值
     * @param deviceTypeCode 设备类型编码
     * @param defaultValue 默认值
     * @return 设备类型名称，如果未找到则返回defaultValue
     */
    public static String getDeviceTypeName(String deviceTypeCode, String defaultValue) {
        return DEVICE_TYPE_MAP.getOrDefault(deviceTypeCode, defaultValue);
    }

    /**
     * 获取所有支持的设备类型编码
     * @return 设备类型编码集合
     */
    public static Set<String> getAllDeviceTypeCodes() {
        return DEVICE_TYPE_MAP.keySet();
    }

    /**
     * 获取所有支持的设备类型名称
     * @return 设备类型名称集合
     */
    public static Collection<String> getAllDeviceTypeNames() {
        return DEVICE_TYPE_MAP.values();
    }


}
