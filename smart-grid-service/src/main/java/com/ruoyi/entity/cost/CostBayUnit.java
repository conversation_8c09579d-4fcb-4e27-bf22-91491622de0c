package com.ruoyi.entity.cost;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 间隔单元
 */
@Data
@TableName("cost_bay_unit")
@NoArgsConstructor
@AllArgsConstructor
public class CostBayUnit extends CostType {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设计名称
     */
    private String designName;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 间隔类型
     */
    private String gapType;

    /**
     * 设备类型
     */
    private String facilityType;

    /**
     * 容量
     */
    private String capacity;

    /**
     * 最大电流
     */
    private Double maxCurrent;

    /**
     * 最大切断电流
     */
    private Double maxCutCurrent;

    /**
     *配电形式
     */
    private String distributionForm;

}
