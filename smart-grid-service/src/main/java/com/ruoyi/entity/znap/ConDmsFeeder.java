package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网馈线容器对象 con_dms_feeder
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("con_dms_feeder")
public class ConDmsFeeder {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * $column.columnComment
     */
    private String feederType;
    /**
     * 所属变电站，con_substation表id
     */
    private Long subId;
    /**
     * 所属区域，con_subregion表id
     */
    private Long subregionId;
    /**
     * $column.columnComment
     */
    private Long headTerminalId;
    /**
     * 首节点id
     */
    private Long headNd;
    /**
     * 首开关id
     */
    private Long headBrkId;

}
