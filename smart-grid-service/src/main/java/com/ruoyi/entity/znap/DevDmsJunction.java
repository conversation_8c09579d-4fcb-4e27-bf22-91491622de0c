package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 dev_dms_junction
 *
 * <AUTHOR> developer
 * @date 2025-06-18
 */
@Data
@TableName("dev_dms_junction")
public class DevDmsJunction {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private Long terminalId;
    /**
     * $column.columnComment
     */
    private Long nd;
    /**
     * $column.columnComment
     */
    private Long bvId;

}
