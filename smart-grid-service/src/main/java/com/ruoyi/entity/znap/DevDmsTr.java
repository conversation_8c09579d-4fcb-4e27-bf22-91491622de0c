package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网变压器对象 dev_dms_tr
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("dev_dms_tr")
public class DevDmsTr {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String aliasName;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private Long terminalId;
    /**
     * 节点id
     */
    private Long nd;
    /**
     * 所属环网柜，con_dms_cabinet表id
     */
    private Long cabinetId;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;
    /**
     * 额定容量
     */
    private String ratedS;
    /**
     * 连接方式
     */
    private String vectorGroup;
    /**
     * $column.columnComment
     */
    private Long jnd;

}
