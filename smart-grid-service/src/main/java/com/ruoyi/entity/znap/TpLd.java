package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 中压负荷拓扑对象 tp_ld
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("tp_ld")
public class TpLd {

    private static final long serialVersionUID=1L;

    /**
     * 中压负荷id
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 中压负荷psrid
     */
    private String psrid;
    /**
     * 分支首开关id
     */
    private Long branchCb;
    /**
     * 分支首开关类型
     */
    private Long branchCbType;
    /**
     * 分支首开关psrid
     */
    private String branchCbPsrid;
    /**
     * 主干开关id
     */
    private Long mainCb;
    /**
     * 主干开关类型
     */
    private Long mainCbType;
    /**
     * 主干开关psrid
     */
    private String mainCbPsrid;
    /**
     * 所属馈线
     */
    private Long feederId;
    /**
     * 馈线psrid
     */
    private String feederPsrid;

}
