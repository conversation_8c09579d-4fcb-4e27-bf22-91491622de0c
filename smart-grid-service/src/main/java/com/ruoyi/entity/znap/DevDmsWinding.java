package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网绕组对象 dev_dms_winding
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("dev_dms_winding")
public class DevDmsWinding{

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String sourceId;
    /**
     * $column.columnComment
     */
    private String ratedS;
    /**
     * 电阻
电阻
     */
    private String r;
    /**
     * 电抗
     */
    private String x;
    /**
     * 电导
     */
    private String g;
    /**
     * 绕组编号
     */
    private Long endNumber;
    /**
     * 连接方式
     */
    private String connectType;
    /**
     * 所属变压器，dev_dms_tr表id
     */
    private Long trId;
    /**
     * $column.columnComment
     */
    private Long terminalId;
    /**
     * 节点id
     */
    private Long nd;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;

}
