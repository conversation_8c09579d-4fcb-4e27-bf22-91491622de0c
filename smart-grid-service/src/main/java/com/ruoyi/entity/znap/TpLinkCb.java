package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 联络开关对象 tp_link_cb
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("tp_link_cb")
public class TpLinkCb {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 设备类型
     */
    private Long devType;
    /**
     * 馈线1
     */
    private Long feederId0;
    /**
     * 馈线2
     */
    private Long feederId1;
    /**
     * 联络开关 psrid
     */
    private String psrid;
    /**
     * 馈线1 psrid
     */
    private String feederPsrid0;
    /**
     * 馈线2 psrid
     */
    private String feederPsrid1;

}
