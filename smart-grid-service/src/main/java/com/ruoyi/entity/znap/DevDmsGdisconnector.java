package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网接地刀闸对象 dev_dms_gdisconnector
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("dev_dms_gdisconnector")
public class DevDmsGdisconnector {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * 是否常开   0 否  1 是
     */
    private Long normalOpen;
    /**
     * 开关位置   0 分  1 合
     */
    private Long posValue;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private Long terminalId;
    /**
     * 节点id
     */
    private Long nd;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;
    /**
     * 所属组合开关，con_dms_combined表id
     */
    private Long combinedId;
    /**
     * 所属环网柜，con_dms_cabinet表id
     */
    private Long cabinetId;

}
