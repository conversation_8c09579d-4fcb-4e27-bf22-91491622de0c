package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.graph.utils.ZnapUtils;
import lombok.Data;

/**
 * ems_ld_link表对象
 *
 */
@Data
@TableName("ems_ld_link")
public class EmsLdLink {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 线路名称
     */
    private String ldName;

    /**
     * 馈线名称
     */
    private String fdName;

    /**
     * PSR ID
     */
    private String psrid;

    /**
     * 更新时间
     */
    private String updateTime;

    public String getPmsPsrId() {
        if (StringUtils.isNotBlank(psrid)) {
           return ZnapUtils.parsePsrId(psrid);
        }
        return null;
    }
}
