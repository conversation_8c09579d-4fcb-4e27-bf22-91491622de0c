package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * topo_load_nj表对象
 *
 */
@Data
@TableName("topo_load_nj")
public class TopoLoadNj {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String num;

    /**
     * 线路ID
     */
    private String mrid;

    /**
     * 线路名称
     */
    private String name;

    /**
     * 路径名称
     */
    private String pathname;

    /**
     * 变电站ID
     */
    private String substation;

    /**
     * I节点
     */
    private String iNode;

    /**
     * 基础电压
     */
    private String basevoltage;

    /**
     * 电压等级
     */
    private String voltagelevel;

    /**
     * 间隔
     */
    private String bay;

    /**
     * 有功功率
     */
    private String p;

    /**
     * 无功功率
     */
    private String q;

    /**
     * 创建时间
     */
    private LocalDate createtime;
}
