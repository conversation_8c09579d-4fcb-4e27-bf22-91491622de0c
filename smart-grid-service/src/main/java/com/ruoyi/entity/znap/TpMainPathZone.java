package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 主干路径分区对象 tp_main_path_zone
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("tp_main_path_zone")
public class TpMainPathZone  extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 区段首开关id
     */
    private Long startCb;
    /**
     * 区段末开关id
     */
    private Long endCb;
    /**
     * start_cb 类型
     */
    private Long startCbType;
    /**
     * end_cb类型
     */
    private Long endCbType;
    /**
     * 馈线psrid
     */
    private Long feederId;
    /**
     * 开关序号，从出线开关开始
     */
    private Long idx;
    /**
     * 路径id
     */
    private Long pathId;
    /**
     * 馈线id
     */
    private String feederPsrid;
    /**
     * $column.columnComment
     */
    private String startCbPsrid;
    /**
     * $column.columnComment
     */
    private String endCbPsrid;

}
