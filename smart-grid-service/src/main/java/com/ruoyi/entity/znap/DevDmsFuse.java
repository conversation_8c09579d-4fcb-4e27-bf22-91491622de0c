package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 配网熔断器对象 dev_dms_fuse
 *
 */
@Data
@TableName("dev_dms_fuse")
public class DevDmsFuse {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * 是否常开   0 否  1 是
     */
    private Long normalOpen;
    /**
     * 开关位置   0 分  1 合
     */
    private Long posValue;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private Long terminalId0;
    /**
     * $column.columnComment
     */
    private Long terminalId1;
    /**
     * 节点id
     */
    private Long ind;
    /**
     * 节点id
     */
    private Long jnd;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;
    /**
     * $column.columnComment
     */
    private String aliasName;
    /**
     * 所属环网柜，con_dms_cabinet表id
     */
    private Long cabinetId;
    /**
     * 所属组合开关，con_dms_combined表id
     */
    private Long combinedId;
}
