package com.ruoyi.entity.znap.vo;

import com.ruoyi.entity.znap.*;
import lombok.Data;

import java.util.List;

@Data
public class QueryFeederDevVo {

    public QueryFeederDevVo() {
    }

    public QueryFeederDevVo(List<DevDmsBreaker> devDmsBreakers, List<DevDmsFuse> devDmsFuses, List<DevDmsDisconnector> devDmsDisconnectors, List<DevDmsBusbar> devDmsBusbars, List<DevDmsSegment> devDmsSegments, List<DevDmsPole> devDmsPoles, List<DevDmsTr> devDmsTrs, List<DevDmsLd> devDmsLds, List<ConDmsCabinet> conDmsCabinets, List<DevDmsJunction> devDmsJunctions) {
        this.devDmsBreakers = devDmsBreakers;
        this.devDmsFuses = devDmsFuses;
        this.devDmsDisconnectors = devDmsDisconnectors;
        this.devDmsBusbars = devDmsBusbars;
        this.devDmsSegments = devDmsSegments;
        this.devDmsPoles = devDmsPoles;
        this.devDmsTrs = devDmsTrs;
        this.devDmsLds = devDmsLds;
        this.conDmsCabinets = conDmsCabinets;
        this.devDmsJunctions = devDmsJunctions;
    }

    // 配网开关
    List<DevDmsBreaker> devDmsBreakers;

    // 配网熔断器
    List<DevDmsFuse> devDmsFuses;

    // 刀闸 0203
    List<DevDmsDisconnector> devDmsDisconnectors;

    // 配变母线
    List<DevDmsBusbar> devDmsBusbars;

    // 配网分段
    List<DevDmsSegment> devDmsSegments;

    // 配网杆塔
    List<DevDmsPole> devDmsPoles;

    // 配网变压器
    List<DevDmsTr> devDmsTrs;

    // 终端设备
    List<DevDmsJunction> devDmsJunctions;

    // 变压器的绕组
    // List<DevDmsWinding> devDmsWindings = getWindings(devDmsTrs);

    // 中压用户接入点
    List<DevDmsLd> devDmsLds;

    // 配网环网柜表(站房容器)
    List<ConDmsCabinet> conDmsCabinets;
}
