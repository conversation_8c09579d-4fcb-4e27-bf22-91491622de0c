package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网馈线段对象 dev_dms_segment
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("dev_dms_segment")
public class DevDmsSegment{

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private Long terminalId0;
    /**
     * $column.columnComment
     */
    private Long terminalId1;
    /**
     * 节点id
     */
    private Long ind;
    /**
     * 节点id
     */
    private Long jnd;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;
    /**
     * 电缆长度
     */
    private String lenM;
    /**
     * 电阻

     */
    private String rOPerKm;
    /**
     * 电抗
     */
    private String xOPerKm;
    /**
     * 电容
     */
    private String cNfPerKm;
    /**
     * 正序电阻
     */
    private String r1OPerKm;
    /**
     * 正序电抗
     */
    private String x1OPerKm;
    /**
     * 正序电容
     */
    private String c1NfPerKm;
    /**
     * 负序电阻
     */
    private String r2OPerKm;
    /**
     * 负序电抗
     */
    private String x2OPerKm;
    /**
     * 负序电容
     */
    private String c2NfPerKm;
    /**
     * 零序电阻
     */
    private String r0OPerKm;
    /**
     * 零序电抗
     */
    private String x0OPerKm;
    /**
     * 序电容
     */
    private String c0NfPerKm;
}
