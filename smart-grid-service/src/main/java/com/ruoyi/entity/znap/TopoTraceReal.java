package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * topo_trace_real表对象
 */
@Data
@TableName("topo_trace_real")
public class TopoTraceReal {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线路ID
     */
    private String loadMrid;

    /**
     * 线路名称
     */
    private String loadName;

    /**
     * 上级关系ID全路径
     */
    private String allPathMrids;

    /**
     * 上级主变关系ID全路径
     */
    private String trwindingPathMrids;

    /**
     * 历史遥信
     */
    private String historyYx;

    /**
     * 计算时间
     */
    private LocalDateTime computedDatetime;


    /**
     * 线路对应的间隔ID
     */
    private String startBreak;

    /**
     * 线路对应的间隔名称
     */
    private String startBreakName;

    /**
     * 线路对应的母线ID
     */
    private String busbar;

    /**
     * 线路对应的母线名称
     */
    private String busbarName;
}
