package com.ruoyi.entity.znap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 联络线路和开关
 */
@Data
public class ContactFeederKg {
    public ContactFeederKg() {
    }

    public ContactFeederKg(String kgPsrId, String kgPsrType, String kgPsrName) {
        this.kgPsrId = kgPsrId;
        this.kgPsrType = kgPsrType;
        this.kgPsrName = kgPsrName;
    }

    /**
     * 联络开关设备ID
     */
    @JsonProperty("psrId")
    private String kgPsrId;

    /**
     * 联络开关设备类型
     */
    @JsonProperty("psrType")
    private String kgPsrType;

    /**
     * 联络开关设备名称
     */
    private String kgPsrName;

    /**
     * 联络线路ID
     */
    private String feederPsrId;

    /**
     * 联络线路名称
     */
    private String feederPsrName;

    /**
     * 联络变电站
     */
    private String bdzId, bdzName;

    /**
     * 是否原单线图的
     */
    private boolean isSource = true;

}
