package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配网组合开关对象 con_dms_combined
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("con_dms_combined")
public class ConDmsCombined {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String aliasName;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;
    /**
     * 所属馈线，con_dms_feeder表id
     */
    private Long feederId;
    /**
     * 所属环网柜，con_dms_cabinet表id
     */
    private Long cabinetId;

}
