package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 主干路径开关对象 tp_main_path_cb
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("tp_main_path_cb")
public class TpMainPathCb {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 设备类型
     */
    private Long devType;
    /**
     * 开关序号，从出线开关开始
     */
    private Long idx;
    /**
     * 馈线id（联合主键）
     */
   // @TableId(value = "feeder_id" ,type = IdType.ASSIGN_ID)
    private Long feederId;
    /**
     * 路径id（联合主键）
     */
   // @TableId(value = "path_id" ,type = IdType.ASSIGN_ID)
    private Long pathId;
    /**
     * 是否联络开关，0 普通开关，1联络开关
     */
    private Long isLink;

    @TableField(exist = false)
    private final Long linkKg = 1L;
    /**
     * 开关 psrid
     */
    private String psrid;
    /**
     * 馈线 psrid
     */
    private String feederPsrid;

}
