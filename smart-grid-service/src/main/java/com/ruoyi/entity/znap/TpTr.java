package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配变拓扑对象 tp_tr
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("tp_tr")
public class TpTr {

    private static final long serialVersionUID=1L;

    /**
     * 配变id
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 配变psrid
     */
    private String psrid;
    /**
     * 分支开关id
     */
    private Long branchCb;
    /**
     * 分支开关类型
     */
    private Long branchCbType;
    /**
     * 分支开关psrid
     */
    private String branchCbPsrid;
    /**
     * 主干开关id
     */
    private Long mainCb;
    /**
     * 主干开关类型
     */
    private Long mainCbType;
    /**
     * 主干开关psrid
     */
    private String mainCbPsrid;
    /**
     * 所属馈线
     */
    private Long feederId;
    /**
     * 馈线psrid
     */
    private String feederPsrid;

}
