package com.ruoyi.entity.znap;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 主网开关对象 dev_ems_breaker
 *
 * <AUTHOR> developer
 * @date 2025-05-17
 */
@Data
@TableName("dev_ems_breaker")
public class DevEmsBreaker{

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * $column.columnComment
     */
    private String name;
    /**
     * $column.columnComment
     */
    private String aliasName;
    /**
     * $column.columnComment
     */
    private String rdfid;
    /**
     * $column.columnComment
     */
    private String mrid;
    /**
     * 电压等级，enum_basevoltage表id
     */
    private Long bvId;
    /**
     * 所属变电站id，con_substation表id
     */
    private Long subId;
    /**
     * 是否常开   0 否  1 是
     */
    private Long normalOpen;
    /**
     * 开关位置   0 分  1 合
     */
    private Long posValue;
    /**
     * $column.columnComment
     */
    private Long terminalId0;
    /**
     * $column.columnComment
     */
    private Long terminalId1;
    /**
     * 节点id
     */
    private Long ind;
    /**
     * 节点id
     */
    private Long jnd;
    /**
     * $column.columnComment
     */
    private Long feederId;
    /**
     * $column.columnComment
     */
    private String psrid;
    /**
     * $column.columnComment
     */
    private String psrtype;

}
