package com.ruoyi.entity.electricity.bo;

import com.ruoyi.entity.electricity.vo.IndustryUserVo;
import com.ruoyi.entity.problem.Statistics;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户高峰返回实体
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class IndustryPeakAnalysisBo {


    /**
     * 网格编号
     */
    private String code;

    /**
     * 行业编号
     */
    private String indCls;

    /**
     * 日期
     */
    private String time;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 最大数
     */
    private Integer pageSize;
}
