package com.ruoyi.entity.electricity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class LoadMWDimensionAnalysisVo {

    /**
     * 行业编号
     */
    private String indCls;

    /**
     * 行业名称
     */
    private String indClsName;

    /**
     * 用电类别
     */
    private String ecCateg;

    /**
     * 用户重要等级
     */
    private String imptLv;

    /**
     * 电源类型（01单，02双，03多）
     */
    private String isDoublePower;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户编号
     */
    private String userCode;

    /**
     * 负荷数据
     */
    private Double loadMW;

    /**
     * 发生时间
     */
    private String occurTime;


    /**
     * 平均值负荷
     */
    private Double avgLoadMW;


}
