package com.ruoyi.entity.electricity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.entity.electricity.ElectricityAnalysisType;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import com.ruoyi.trans.core.bean.ITransVo;
import lombok.Data;

/**
 *  用点分析——用户详情 analysis_cust
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data

public class AnalysisCustVo  {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /***
     *用电地址
     */
    private String ecAddr;

    /***
     *是否双电源  双电源    01 单电源 02 双电源 03 多电源
     */
    @TransField(type = TransType.EXCEL_DICT, resultHolderField = "isDoublePowerLabel")
    @ExcelDictFormat(readConverterExp = "01=单电源,02=双电源,03=多电源")
    private String isDoublePower;

    private String isDoublePowerLabel;
    /***
     *用电类别
     */

    private String ecCateg;

    private String typeName;

    /***
     *用户编号
     */
    private Long custNo;

    /***
     *所属行业
     */
    private String indCls;

    @TableField(exist = false)
    private String industryName;

    /***
     *合同容量
     */
    private Integer ctrtCap;

    /***
     *运行容量
     */
    private Integer runCap;

    /***
     *用户名称
     */
    private String custName;

    /***
     *电压分类
     */
    private String custCls;

    /***
     *电压等级
     */
    @TransField(type = TransType.DICTIONARY, key = "voltage_level", resultHolderField = "voltageLabel")
    private String voltage;

    private String voltageLabel;

    /***
     *重要性等级
     */
    @TransField(type = TransType.DICTIONARY, key = "user_important_level", resultHolderField = "imptLvLabel")
    private String imptLv;

    private String imptLvLabel;

}
