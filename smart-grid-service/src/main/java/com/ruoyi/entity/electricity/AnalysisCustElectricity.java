package com.ruoyi.entity.electricity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
@TableName("analysis_cust_electricity")
public class AnalysisCustElectricity {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /***
     *用户编号
     */
    private Long custNo;

    /***
     *用户名称
     */
    private String custName;

    /**
     * 时间
     */
    private Date time;

    /**
     * 最大用电量
     */
    private Double maxElectricity;

    /**
     * 最小用电量
     */
    private Double minElectricity;

    /**
     * 平均用电量
     */
    private Double avgElectricity;

    /**
     * 总用电量
     */
    private Double totalElectricity;

    /**
     * 最大用电量发生时间
     */
    private String maxElectricityTime;

    /**
     * 最小用电量
     */
    private String minElectricityTime;


    /**
     * 用电list
     */
    private String electricityList;
}
