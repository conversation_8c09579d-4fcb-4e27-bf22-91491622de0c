package com.ruoyi.entity.electricity.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 *  用点分析——用户详情 analysis_cust
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data

public class AnalysisCustBo {

    /***
     *用电地址
     */
    private String ecAddr;

    /***
     *是否双电源  双电源    01 单电源 02 双电源 03 多电源
     */
    private String isDoublePower;

    /***
     *用电类别
     */
    private String ecCateg;

    /***
     *用户编号
     */
    private Long custNo;

    /***
     *所属行业
     */
    private String indCls;

    /***
     *合同容量
     */
    private Integer ctrtCap;

    /***
     *运行容量
     */
    private Integer runCap;

    /***
     *用户名称
     */
    private String custName;

    /***
     *电压分类
     */
    private String custCls;

    /***
     *电压等级
     */
    private String voltage;

    /***
     *重要性等级
     */
    private String imptLv;

    private Integer pageNum;

    private Integer pageSize;

    private String code;


}
