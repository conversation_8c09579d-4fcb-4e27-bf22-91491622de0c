package com.ruoyi.entity.electricity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class LoadMWDimensionAnalysisBo {

    /**
     * 行业编号
     */
    private String indCls;

    /**
     * 网格编码
     */
    private String code;


    /**
     * 日期
     */
    private String time;

    /**
     * 用电类别
     */
    private String ecCateg;

    /**
     * 用户重要等级
     */
    private String imptLv;

    /**
     * 电源类型（01单，02双，03多）
     */
    private String isDoublePower;

    /**
     * 查看数据类型（0最小，1最大）
     */
    private Integer type;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 最大数
     */
    private Integer pageSize;
}
