package com.ruoyi.entity.simulation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 母线潮流计算结果表实体类
 * 对应表：sim_ret_pf_dms_busbar
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@EqualsAndHashCode(callSuper = true)
@Table(name = "sim_ret_pf_dms_busbar")
public class SimRetPfDmsBusbar extends SimFoundation implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id（对应dev_dms_busbar表id） */
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    /** 计算结果id（sim_ret_pf主键） */
    @Column(name = "ret_id", nullable = false)
    private Long retId;

    /** 时序号（0~95） */
    @Column(name = "idx", nullable = false)
    private Integer idx;

    /** 电压 kV */
    @Column(name = "v_value")
    private Double vValue;

    /** 电压状态，1有效，0无效 */
    @Column(name = "v_status")
    private Integer vStatus;

    /** 电压相角 */
    @Column(name = "va_value")
    private Double vaValue;

    /** 电压相角状态，1有效，0无效 */
    @Column(name = "va_status")
    private Integer vaStatus;

    /** 有功 kW */
    @Column(name = "p_value")
    private Double pValue;

    /** 有功状态，1有效，0无效 */
    @Column(name = "p_status")
    private Integer pStatus;

    /** 无功 kVar */
    @Column(name = "q_value")
    private Double qValue;

    /** 无功状态，1有效，0无效 */
    @Column(name = "q_status")
    private Integer qStatus;

    /** 功率因数*100，0~100 */
    @Column(name = "cos_value")
    private Double cosValue;

    /** 功率因数状态，1有效，0无效 */
    @Column(name = "cos_status")
    private Integer cosStatus;

    /** 注入电流 A */
    @Column(name = "i_value")
    private Double iValue;

    /** 注入电流状态，1有效，0无效 */
    @Column(name = "i_status")
    private Integer iStatus;
}
