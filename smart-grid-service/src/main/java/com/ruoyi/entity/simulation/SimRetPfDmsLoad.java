package com.ruoyi.entity.simulation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 负荷潮流计算结果表实体类
 * 对应表：sim_ret_pf_dms_load
 */
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "sim_ret_pf_dms_load")
public class SimRetPfDmsLoad extends SimFoundation implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id */
    private Long id;

    /** 计算结果id（sim_ret_pf主键） */
    private Long retId;

    /** 时序号（0~95） */
    private Integer idx;

    /** 有功 kW */
    private Double pValue;

    /** 有功状态，1有效，0无效 */
    private Integer pStatus;

    /** 无功 kVar */
    private Double qValue;

    /** 无功状态，1有效，0无效 */
    private Integer qStatus;

    /** 电压 kV */
    private Double vValue;

    /** 电压状态，1有效，0无效 */
    private Integer vStatus;

    /** 注入电流 A */
    private Double iValue;

    /** 注入电流状态，1有效，0无效 */
    private Integer iStatus;

    /** 功率因数*100 */
    private Double cosValue;

    /** 功率因数状态，1有效，0无效 */
    private Integer cosStatus;

    /** 相角 */
    private Double vaValue;

    /** 相角状态，1有效，0无效 */
    private Integer vaStatus;
}
