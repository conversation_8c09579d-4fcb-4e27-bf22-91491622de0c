package com.ruoyi.entity.simulation;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 配网开关时序潮流计算结果表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@EqualsAndHashCode(callSuper = true)
@TableName("sim_ret_pf_dms_breaker")
public class SimRetPfDmsBreaker extends SimFoundation implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id（对应dev_dms_breaker表id） */
    private Long id;

    /** 计算结果id（sim_ret_pf主键） */
    private Long retId;

    /** 时序号（0~95） */
    private Integer idx;

    /** ind有功 kW */
    private Double pIndValue;

    /** ind有功状态，1有效，0无效 */
    private Integer pIndStatus;

    /** ind无功 kVar */
    private Double qIndValue;

    /** ind无功状态，1有效，0无效 */
    private Integer qIndStatus;

    /** jnd有功 kW */
    private Double pJndValue;

    /** jnd有功状态，1有效，0无效 */
    private Integer pJndStatus;

    /** jnd无功 kVar */
    private Double qJndValue;

    /** jnd无功状态，1有效，0无效 */
    private Integer qJndStatus;

    /** 电流 A */
    private Double iValue;

    /** 电流状态，1有效，0无效 */
    private Integer iStatus;

    /** 负载率*100 */
    private Double loadRateValue;

    /** 负载率状态，1有效，0无效 */
    private Integer loadRateStatus;

    /** 电压 kV */
    private Double vValue;

    /** 电压状态，1有效，0无效 */
    private Integer vStatus;

    /** 相角 */
    private Double vaValue;

    /** 相角状态，1有效，0无效 */
    private Integer vaStatus;

    /** 功率因数*100 */
    private Double cosValue;

    /** 功率因数状态，1有效，0无效 */
    private Integer cosStatus;
}
