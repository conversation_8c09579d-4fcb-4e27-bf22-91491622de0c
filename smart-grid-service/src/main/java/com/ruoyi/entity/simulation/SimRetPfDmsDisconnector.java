package com.ruoyi.entity.simulation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 配网刀闸时序潮流计算结果表实体类
 * 对应表：sim_ret_pf_dms_disconnector
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@EqualsAndHashCode(callSuper = true)
@Table(name = "sim_ret_pf_dms_disconnector")
public class SimRetPfDmsDisconnector extends SimFoundation implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id（对应dev_dms_disconnector表id） */
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    /** 计算结果id（sim_ret_pf主键） */
    @Column(name = "ret_id", nullable = false)
    private Long retId;

    /** 时序号（0~95） */
    @Column(name = "idx", nullable = false)
    private Integer idx;

    /** ind有功 kW */
    @Column(name = "p_ind_value")
    private Double pIndValue;

    /** ind有功状态，1有效，0无效 */
    @Column(name = "p_ind_status")
    private Integer pIndStatus;

    /** ind无功 kVar */
    @Column(name = "q_ind_value")
    private Double qIndValue;

    /** ind无功状态，1有效，0无效 */
    @Column(name = "q_ind_status")
    private Integer qIndStatus;

    /** jnd有功 kW */
    @Column(name = "p_jnd_value")
    private Double pJndValue;

    /** jnd有功状态，1有效，0无效 */
    @Column(name = "p_jnd_status")
    private Integer pJndStatus;

    /** jnd无功 kVar */
    @Column(name = "q_jnd_value")
    private Double qJndValue;

    /** jnd无功状态，1有效，0无效 */
    @Column(name = "q_jnd_status")
    private Integer qJndStatus;

    /** 电流 A */
    @Column(name = "i_value")
    private Double iValue;

    /** 电流状态，1有效，0无效 */
    @Column(name = "i_status")
    private Integer iStatus;

    /** 负载率*100 */
    @Column(name = "load_rate_value")
    private Double loadRateValue;

    /** 负载率状态，1有效，0无效 */
    @Column(name = "load_rate_status")
    private Integer loadRateStatus;

    /** 电压 kV */
    @Column(name = "v_value")
    private Double vValue;

    /** 电压状态，1有效，0无效 */
    @Column(name = "v_status")
    private Integer vStatus;

    /** 相角 */
    @Column(name = "va_value")
    private Double vaValue;

    /** 相角状态，1有效，0无效 */
    @Column(name = "va_status")
    private Integer vaStatus;

    /** 功率因数*100 */
    @Column(name = "cos_value")
    private Double cosValue;

    /** 功率因数状态，1有效，0无效 */
    @Column(name = "cos_status")
    private Integer cosStatus;
}
