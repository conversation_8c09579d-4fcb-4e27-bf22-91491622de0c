package com.ruoyi.entity.simulation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 馈线段潮流计算结果表实体类
 * 对应表：sim_ret_pf_segment
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@EqualsAndHashCode(callSuper = true)
@Table(name = "sim_ret_pf_dms_segment")
public class SimRetPfDmsSegment extends SimFoundation implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id（对应dev_dms_segment表id） */
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    /** 计算结果id（sim_ret_pf主键） */
    @Column(name = "ret_id", nullable = false)
    private Long retId;

    /** 时序号（0~95） */
    @Column(name = "idx", nullable = false)
    private Integer idx;

    /** ind有功 kW */
    @Column(name = "p_ind_value")
    private Double pIndValue;

    /** ind有功状态，1有效，0无效 */
    @Column(name = "p_ind_status")
    private Integer pIndStatus;

    /** ind无功 kVar */
    @Column(name = "q_ind_value")
    private Double qIndValue;

    /** ind无功状态，1有效，0无效 */
    @Column(name = "q_ind_status")
    private Integer qIndStatus;

    /** ind电压 kV */
    @Column(name = "v_ind_value")
    private Double vIndValue;

    /** ind电压状态，1有效，0无效 */
    @Column(name = "v_ind_status")
    private Integer vIndStatus;

    /** ind电流 A */
    @Column(name = "i_ind_value")
    private Double iIndValue;

    /** ind电流状态，1有效，0无效 */
    @Column(name = "i_ind_status")
    private Integer iIndStatus;

    /** ind电压相角 */
    @Column(name = "va_ind_value")
    private Double vaIndValue;

    /** ind电压相角状态，1有效，0无效 */
    @Column(name = "va_ind_status")
    private Integer vaIndStatus;

    /** jnd有功 kW */
    @Column(name = "p_jnd_value")
    private Double pJndValue;

    /** jnd有功状态，1有效，0无效 */
    @Column(name = "p_jnd_status")
    private Integer pJndStatus;

    /** jnd无功 kV */
    @Column(name = "q_jnd_value")
    private Double qJndValue;

    /** jnd无功状态，1有效，0无效 */
    @Column(name = "q_jnd_status")
    private Integer qJndStatus;

    /** jnd电压 kV */
    @Column(name = "v_jnd_value")
    private Double vJndValue;

    /** jnd电压状态，1有效，0无效 */
    @Column(name = "v_jnd_status")
    private Integer vJndStatus;

    /** jnd电流 A */
    @Column(name = "i_jnd_value")
    private Double iJndValue;

    /** jnd电流状态，1有效，0无效 */
    @Column(name = "i_jnd_status")
    private Integer iJndStatus;

    /** jnd电压相角 */
    @Column(name = "va_jnd_value")
    private Double vaJndValue;

    /** jnd电压相角状态，1有效，0无效 */
    @Column(name = "va_jnd_status")
    private Integer vaJndStatus;

    /** 有功损耗 kW */
    @Column(name = "ploss_value")
    private Double plossValue;

    /** 有功损耗状态，1有效，0无效 */
    @Column(name = "ploss_status")
    private Integer plossStatus;

    /** 无功损耗 kVar */
    @Column(name = "qloss_value")
    private Double qlossValue;

    /** 无功损耗状态，1有效，0无效 */
    @Column(name = "qloss_status")
    private Integer qlossStatus;

    /** 负载率*100 */
    @Column(name = "load_rate_value")
    private Double loadRateValue;

    /** 负载率状态，1有效，0无效 */
    @Column(name = "load_rate_status")
    private Integer loadRateStatus;

    /** ind端功率因数*100 */
    @Column(name = "cos_ind_value")
    private Double cosIndValue;

    /** ind端功率因数状态，1有效，0无效 */
    @Column(name = "cos_ind_status")
    private Integer cosIndStatus;

    /** jnd端功率因数*100 */
    @Column(name = "cos_jnd_value")
    private Double cosJndValue;

    /** jnd端功率因数状态，1有效，0无效 */
    @Column(name = "cos_jnd_status")
    private Integer cosJndStatus;
}
