package com.ruoyi.entity.simulation;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;

/**
 *仿真基础数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
public class SimFoundation {

    /**
     * 设备的psr_id
     */
    @Column(name = "psrid", nullable = false)
    private String psrId;

    /**
     * 设备的psr_name
     */
    private String name;

    /**
     * 设备的psr_type
     */
    @TableField(exist = false)
    private String psrType;

}
