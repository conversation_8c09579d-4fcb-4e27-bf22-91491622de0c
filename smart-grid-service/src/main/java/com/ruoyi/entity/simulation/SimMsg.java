package com.ruoyi.entity.simulation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 根据MsgId查询所有仿真数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
public class SimMsg {

    /**
     *配网开关时序潮流计算结果
     */
    private List<SimRetPfDmsBreaker> simRetPfDmsBreakerList;

    /**
     *母线潮流计算结果
     */
    private List<SimRetPfDmsBusbar> simRetPfDmsBusbarList;

    /**
     *配网刀闸时序潮流计算结果
     */
    private List<SimRetPfDmsDisconnector> simRetPfDmsDisconnectorList;

    /**
     *配变潮流计算结果
     */
    private List<SimRetPfDmsDt> simRetPfDmsDtList;

    /**
     *荷潮流计算结果
     */
    private List<SimRetPfDmsLoad> simRetPfDmsLoadList;

    /**
     *馈线段潮流计算结果
     */
    private List<SimRetPfDmsSegment> simRetPfDmsSegmentList;

    /**
     *主网出线开关时序潮流计算结果
     */
    private List<SimRetPfEmsBreaker> simRetPfEmsBreakerList;

}
