package com.ruoyi.entity.problem.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.utils.util.BigDecimalSerializer;
import com.ruoyi.common.utils.util.NoNullSerializer;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.Data;

import java.util.Date;

@Data
public class ProblemVo{
    /**
     * 问题id
     */
    @ExcelIgnore
    @TableId(type = IdType.ASSIGN_ID)
    @ExcelProperty(value = "主键id")
    private Long problemId;

    /**
     * 数据来源
     */
    @ExcelIgnore
    @TransField(type = TransType.DICTIONARY, key = "data_source", resultHolderField = "dataSourceLabel")
    private Integer dataSource;

    @ExcelProperty(value = "数据来源")
    @JsonSerialize(using = NoNullSerializer.class)
    private String dataSourceLabel;

    /**
     * 所属地市
     */
    @ExcelProperty(value = "所属地市")
    private String cityComName;

    /**
     * 所属区县
     */
    @ExcelProperty(value = "所属区县")
    private String countyComName;

    /**
     * 运行网格
     */
    @ExcelProperty(value = "运行网格")
    private String runGridName;

    @ExcelIgnore
    private String runGridId;

    /**
     * 运行网格
     */
    @ExcelProperty(value = "规划网格")
    private String inGridName;

    @ExcelIgnore
    private String inGridId;


    /**
     * 线路名称
     */
    @ExcelProperty(value = "线路名称")
    private String feederName;

    /**
     * 线路id
     */
    @ExcelIgnore
    private String feederId;

    /**
     * 所属变电站
     */
    @ExcelProperty(value = "所属变电站")
    private String substationName;

    /**
     * 所属变电站id
     */
    @ExcelIgnore
    private String substationId;
    /**s
     * 严重等级
     */
    @ExcelIgnore
    @TransField(type = TransType.DICTIONARY, key = "problem_grade_name", resultHolderField = "gradeNameLabel")
    private Integer gradeName;

    @ExcelProperty(value = "严重等级")
    private String gradeNameLabel;

    /**
     * 一级分类
     */
    @ExcelProperty(value = "一级分类")
    private String categoryLevel1;

    /**
     * 二级分类
     */
    @ExcelProperty(value = "二级分类")
    private String categoryLevel2;

    /**
     * 一级分类编码
     */
    @ExcelIgnore
    private Integer categoryLevel1Code;

    /**
     * 二级分类编码
     */
    @ExcelIgnore
    private Integer categoryLevel2Code;

    /**
     *问题描述
     */
    @TableField(value = "\"name;\"")
    @ExcelProperty(value = "问题描述")
    private String name;

    /**
     * 问题属性
     */
    @ExcelIgnore
    @TransField(type = TransType.DICTIONARY, key = "attr_name", resultHolderField = "attrNameLabel")
    private Integer attrName;

    @ExcelProperty(value = "问题属性")
    private String attrNameLabel;

    /**
     * 问题状态
     */
    @ExcelIgnore
    @TransField(type = TransType.DICTIONARY, key = "problem_status", resultHolderField = "problemStatusLabel")
    private Integer problemStatus;

    @ExcelProperty(value = "问题状态")
    private String problemStatusLabel;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 发现时间
     */
    @ExcelProperty(value = "发现时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ColumnWidth(25)
//    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date defectTime;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private String deviceId;

    /**
     *  设备类型
     */

    @ExcelIgnore
    @TransField(type = TransType.DICTIONARY, key = "device_type", resultHolderField = "deviceTypeLabel")
    private String deviceType;

    @ExcelProperty(value = "设备类型")
    private String deviceTypeLabel;
    /**
     * 是否符合我们的规则
     */
    @ExcelIgnore
    @TransField(type = TransType.EXCEL_DICT, resultHolderField = "problemMyRuleLabel")
    @ExcelDictFormat(readConverterExp = "0=线路拓扑分析异常,1=不属于规则配置下问题,2=属于规则配置下的问题,3=未匹配")
    private Integer problemMyRule;

    @ExcelProperty(value = "是否符合我们的规则")
    private String problemMyRuleLabel;

    /**
     * 研判状态
     */
    private String existStatus;

    /**
     * 研判描述
     */
    private String judDesc;

//    /**
//     * 一级分类编码
//     */
//    @ExcelProperty(value = "一级分类编码")
//    private Integer categoryLevel1Code;
//
//
//
//    /**
//     * 二级分类编码
//     */
//    @ExcelProperty(value = "二级分类编码")
//    private Integer categoryLevel2Code;
//
//    /**
//     * 主要问题释义
//     */
//    @ExcelProperty(value = "主要问题释义")
//    private String interpretation;
//
//    /**
//     * 重点问题
//     */
//    @ExcelProperty(value = "重点问题")
//    private String keyProblem;
//
//    /**
//     *  一般问题
//     */
//    @ExcelProperty(value = "一般问题")
//    private String generalProblem;
//
//    /**
//     * 所属地市ID
//     */
//    @ExcelProperty(value = "所属地市ID")
//    private String cityComId;
//
//
//
//    /**
//     * 所属地市ID
//     */
//    @ExcelProperty(value = "所属地市ID")
//    private String countyComId;
//
//
//
//    /**
//     * 规划网格 ID
//     */
//    @ExcelProperty(value = "规划网格 ID")
//    private String inGridId;
//
//    /**
//     * 规划网格
//     */
//    @ExcelProperty(value = "规划网格")
//    private String inGridName;
//
//    /**
//     * 运行网格 ID
//     */
//    @ExcelProperty(value = "运行网格 ID")
//    private String runGridId;
//
//
//
//    /**
//     * 所属变电站 ID
//     */
//    @ExcelProperty(value = "所属变电站 ID")
//    private String substationId;
//
//
//
//    /**
//     * 线路 ID
//     */
//    @ExcelProperty(value = "线路 ID")
//    private String feederId;
//
//
//
//    /**
//     * 运检单位 ID
//     */
//    @ExcelProperty(value = "运检单位 ID")
//    private String workGroupId;
//
//    /**
//     * 运检单位
//     */
//    @ExcelProperty(value = "运检单位")
//    private String workGroupName;
//
//
//    /**
//     * 设备ID
//     */
//    @ExcelProperty(value = "设备ID")
//    private String deviceId;
//
//    /**
//     *  设备类型
//     */
//    @ExcelProperty(value = "设备类型")
//    private String deviceType;
//
//
//
//    /**
//     * 电压等级
//     */
//    @ExcelProperty(value = "电压等级")
//    private Integer voltageLevel;
//
//    /**
//     * 需求ID
//     */
//    @ExcelProperty(value = "需求ID")
//    private String sourceNeedId;
//
//    /**
//     *  需求内容
//     */
//    @ExcelProperty(value = "需求内容")
//    private String needName;
//
//    /**
//     *
//     */
//    @ExcelProperty(value = "reserveCode")
//    private String reserveCode;
//
//    /**
//     *
//     */
//    @ExcelProperty(value = "projectName")
//    private String projectName;
//
//    /**
//     *
//     */
//    @ExcelProperty(value = "principal")
//    private String principal;
//
//    /**
//     * 校验结果
//     */
//    @ExcelProperty(value = "results")
//    private String results;
//
//    /**
//     * 合并情况
//     */
//    @TableField(value = "\"merge\"")
//    @ExcelProperty(value = "合并情况")
//    private String merge;



}
