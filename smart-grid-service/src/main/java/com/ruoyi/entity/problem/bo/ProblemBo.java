package com.ruoyi.entity.problem.bo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemBo extends BaseEntity {

    /**
     * id
     */
    private Long problemId;


    /**
     * 数据源
     */

    private Integer dataSource;

    /**
     *问题描述
     */
    @TableField(value = "\"name;\"")
    private String name;

    /**
     * 一级分类编码
     */
    private Integer categoryLevel1Code;

    /**
     * 发现时间
     */
    private String defectTime;


    /**
     * 二级分类编码
     */
    private Integer categoryLevel2Code;

    /**
     * 所属市ID
     */
    private String cityComId;

    /**
     * 所属地市ID
     */
    private String countyComId;

    /**
     * 规划网格
     */
    private String inGridName;

    /**
     * 运行网格
     */
    private String runGridName;

    /**
     * 线路 ID
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 所属变电站 ID
     */
    private String substationId;

    /**
     * 严重等级
     */

    private Integer gradeName;

    /**
     * 问题状态
     */
    private Integer problemStatus;

    /**
     * 合并情况
     */
    private String merge;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 问题属性
     */
    private Integer attrName;

    /**
     * 起始时间
     */
    private String strTime;

    /**
     * 结束时间
     */
    private String endTime;

    private Integer problemMyRule;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     *第几页
     */
    private Integer pageNum;



    private String existStatus;




}
