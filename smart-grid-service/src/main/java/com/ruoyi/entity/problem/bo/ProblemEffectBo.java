package com.ruoyi.entity.problem.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 问题影响的设备情况业务对象 problem_effect
 *
 * <AUTHOR>
 * @date 2025-03-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemEffectBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 影响的设备的json
     */
    @NotBlank(message = "影响的设备的json不能为空", groups = { AddGroup.class, EditGroup.class })
    private String effectDeviceJson;


}
