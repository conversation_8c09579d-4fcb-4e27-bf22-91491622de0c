package com.ruoyi.entity.problem.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.Data;


/**
 * 问题规则视图对象 problem_rule_configuration
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@ExcelIgnoreUnannotated
public class ProblemRuleConfigurationVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 二级分类
     */
    @ExcelProperty(value = "二级分类")
    private String categoryLevel2;

    /**
     * 问题情况
     */
    @ExcelProperty(value = "问题情况")
    private Integer problemSituation;

    /**
     * 条件1
     */
    private String condition1;
    /**
     * 最大数量1
     */
    private Integer conditionMax1;

    /**
     * 最小数量1
     */
    private Integer conditionMin1;

    /**
     * 条件2
     */
    private String condition2;
    /**
     * 最大数量2
     */
    private Integer conditionMax2;

    /**
     * 最小数量2
     */
    private Integer conditionMin2;

    @TransField(type = TransType.DICTIONARY, key = "problem_rule_configuration", resultHolderField = "typeString")
    private Integer type;

    private String typeString;

    private String unit1;
    private String unit2;

}
