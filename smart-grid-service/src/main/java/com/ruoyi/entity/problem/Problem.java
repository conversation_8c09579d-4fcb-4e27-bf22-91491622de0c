package com.ruoyi.entity.problem;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Content;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.Contract;

import java.util.Date;

@Data
@TableName("problem")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class Problem extends BaseEntity {

    /**
     * 问题id
     */
    @TableId(value = "problem_id", type = IdType.AUTO)
    private Long problemId;

    /**
     * 问题状态
     */
    private Integer problemStatus;


    /**
     * 严重等级
     */

    private Integer gradeName;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 发现时间
     */
    private Date defectTime;

    /**
     *问题描述
     */
    @TableField(value = "\"name\"")
    private String name;

    /**
     * 问题属性
     */
    private Integer attrName;


    /**
     * 一级分类
     */
    private String categoryLevel1;

    /**
     * 一级分类编码
     */
    private Integer categoryLevel1Code;

    /**
     * 二级分类
     */
    private String categoryLevel2;

    /**
     * 二级分类编码
     */
    private Integer categoryLevel2Code;

    /**
     * 主要问题释义
     */
    private String interpretation;

    /**
     * 重点问题
     */
    private String keyProblem;

    /**
     *  一般问题
     */
    private String generalProblem;

    /**
     * 所属地市ID
     */
    private String cityComId;

    /**
     * 所属地市
     */
    private String cityComName;

    /**
     * 所属区县ID
     */
    private String countyComId;

    /**
     * 所属区县
     */
    private String countyComName;

    /**
     * 规划网格 ID
     */
    private String inGridId;

    /**
     * 规划网格
     */
    private String inGridName;

    /**
     * 运行网格 ID
     */
    private String runGridId;

    /**
     * 运行网格
     */
    private String runGridName;

    /**
     * 所属变电站 ID
     */
    private String substationId;

    /**
     * 所属变电站
     */
    private String substationName;

    /**
     * 线路 ID
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 运检单位 ID
     */
    private String workGroupId;

    /**
     * 运检单位
     */
    private String workGroupName;


    /**
     * 设备ID
     */
    private String deviceId;

    /**
     *  设备类型
     */

    private String deviceType;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 电压等级
     */
    private Integer voltageLevel;

    /**
     * 需求ID
     */
    private String sourceNeedId;

    /**
     *  需求内容
     */
    private String needName;

    /**
     *
     */
    private String reserveCode;

    /**
     *
     */
    private String projectName;

    /**
     *
     */
    private String principal;

    /**
     * 校验结果
     */
    private String results;

    /**
     * 合并情况
     */
    @TableField(value = "\"merge\"")
    private String merge;

    /**
     * 是否符合我们的规则（0是异常，1是没问题 2是符合问题）
     */
    private Integer problemMyRule;

    /**
     * 判研状态
     */
    private String existStatus;

    /**
     * 研判描述
     */
    private String judDesc;


}
