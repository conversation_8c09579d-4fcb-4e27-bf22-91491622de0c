package com.ruoyi.entity.problem;

import java.util.concurrent.Future;

public   class TaskInfo {
        private Future<?> future;
        private boolean paused = false;
        private boolean cancelled = false;
        private final Object pauseLock = new Object();

        public Future<?> getFuture() {
            return future;
        }

        public void setFuture(Future<?> future) {
            this.future = future;
        }

        public boolean isPaused() {
            return paused;
        }

        public void setPaused(boolean paused) {
            this.paused = paused;
        }

        public boolean isCancelled() {
            return cancelled;
        }

        public void setCancelled(boolean cancelled) {
            this.cancelled = cancelled;
        }

        public Object getPauseLock() {
            return pauseLock;
        }
    }
