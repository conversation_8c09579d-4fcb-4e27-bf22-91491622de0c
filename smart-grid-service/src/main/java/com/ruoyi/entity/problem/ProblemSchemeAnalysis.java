package com.ruoyi.entity.problem;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 方案分析过程对象 problem_scheme_analysis
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
@EqualsAndHashCode()
@Data
@TableName("problem_scheme_analysis")

public class ProblemSchemeAnalysis {
    // 显式添加无参构造函数
    public ProblemSchemeAnalysis() {
    }

    private static final long serialVersionUID = 1L;

    public ProblemSchemeAnalysis(Long problemId, String moduleName, String type, String operateType, String content, String titleBox) {
        this.titleBox = titleBox;
        this.moduleName = moduleName;
        this.type = type;
        this.operateType = operateType;
        this.content = content;
        this.problemId = problemId;
    }

    public ProblemSchemeAnalysis(Long problemId, String moduleName, String type, String operateType, String content) {
        this.problemId = problemId;
        this.moduleName = moduleName;
        this.type = type;
        this.operateType = operateType;
        this.content = content;
    }

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 按钮标题
     */
    private String btnTitle;

    /**
     * 标题
     */
    private String titleBox;

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 类型
     */
    private String type;

    /**
     * 内容
     */
    private String content;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 问题id
     */
    private Long problemId;

    /**
     * 添加时间
     */
    // 插入时自动填充
    private Date addTime;

    /**
     * 模块
     */
    private String moduleName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 步出销毁
     */
    private boolean stepOutDestroy;

    /**
     * 插入的排序序号
     */
    private int index;

    // =========================== 模块 =======================

    public static final String ANALYSIS_MODULE = "problemAnalysis"; // 问题分析

    public static final String NEAR_PROBLEM_MODULE = "nearProblem"; // 附近相关问题

    public static final String ADVANCE_PLAN_MODULE = "advancePlan"; // 预方案生成

    public static final String COMPARE_PLAN_MODULE = "comparePlan"; // 项目方案多维度分析

    // =========================== 推送类型 =======================

    public static final String STRUCTURED = "structured"; // 结构化字符串

    public static final String STRING = "structured"; // 字符串

    public static final String LOADING_TYPE = "loading"; // 只是loading的提示

    public static final String COMPONENT = "component"; //  组件

    public static final String PLANS = "plans"; //  方案列表

    public static final String END_TYPE = "endType"; // 结束

    // =========================== 操作 =======================
    public static final String PLAN_OP_TYPE = "planOpType"; //  方案

    public static final String SEG_IDENTIFY_OP_TYPE = "segIdentifyOpType"; //  分段识别

    public static final String BIG_BRANCH_IDENTIFY_OP_TYPE = "bigBranchOpType"; //  大分子识别

    public static final String LAY_POSITION_OP_TYPE = "layPositionOpType"; //  放置开关和联络开关位置

    public static final String NEAR_FEEDER_OP_TYPE = "nearFeederOpType"; //  附近线路

}
