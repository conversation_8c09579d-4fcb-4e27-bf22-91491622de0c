package com.ruoyi.entity.problem.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 方案分析过程视图对象 problem_scheme_analysis
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
@Data
@ExcelIgnoreUnannotated
public class ProblemSchemeAnalysisVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private Long id;

    /**
     * 按钮标题
     */
    @ExcelProperty(value = "按钮标题")
    private String btnTitle;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String titleBox;

    /**
     * 方案id
     */
    @ExcelProperty(value = "方案id")
    private Long planId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 提示
     */
    @ExcelProperty(value = "提示")
    private String prompt;

    /**
     * 模块
     */
    @ExcelProperty(value = "模块")
    private String moduleName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 步出销毁
     */
    private boolean stepOutDestroy;
}
