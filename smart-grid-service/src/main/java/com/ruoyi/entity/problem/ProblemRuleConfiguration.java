package com.ruoyi.entity.problem;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 问题规则对象 problem_rule_configuration
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("problem_rule_configuration")
public class ProblemRuleConfiguration extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 二级分类
     */
    private String categoryLevel2;

    /**
     * 二级分类
     */
    private Integer categoryLevel2Code;
    /**
     * 问题情况
     */

    public static final int GENERA = 3;    // 一般问题
    public static final int KEY = 5; // 重点问题


    private Integer problemSituation;

    private Integer type;
    /**
     * 条件1
     */
    private String condition1;
    /**
     * 最大数量1
     */
    private Integer conditionMax1;

    /**
     * 最小数量1
     */
    private Integer conditionMin1;

    /**
     * 条件2
     */
    private String condition2;
    /**
     * 最大数量2
     */
    private Integer conditionMax2;

    /**
     * 最小数量2
     */
    private Integer conditionMin2;

    private String unit1;
    private String unit2;



}
