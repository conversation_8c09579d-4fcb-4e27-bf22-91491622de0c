package com.ruoyi.entity.problem.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 故障问题策略业务对象 problem_strategy
 *
 * <AUTHOR>
 * @date 2025-03-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemStrategyBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 二级分类编码
     */
    private String categoryLevel2;

    /**
     * 主要问题释义
     */
    private String problemDefinition;

    /**
     * 重点问题
     */
    private String keyProblem;

    /**
     * 一般问题
     */
    private String generalProblem;

    /**
     * 目标成效描述参考
     */
    private String effectReference;
    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     *第几页
     */
    private Integer pageNum;


}
