package com.ruoyi.entity.problem.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 方案分析过程业务对象 problem_scheme_analysis
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemSchemeAnalysisBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 按钮标题
     */
    @NotBlank(message = "按钮标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String btnTitle;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String titleBox;

    /**
     * 方案id
     */
    @NotNull(message = "方案id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planId;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 提示
     */
    @NotBlank(message = "提示不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prompt;


}
