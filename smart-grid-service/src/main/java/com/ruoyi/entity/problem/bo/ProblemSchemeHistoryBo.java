package com.ruoyi.entity.problem.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 故障解决方案业务对象 problem_scheme
 *
 * <AUTHOR>
 * @date 2025-03-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemSchemeHistoryBo extends BaseEntity {



    /**
     * 主键id
     */
    private Long id;

    /**
     * 负载率
     */
    private String load;

    /**
     * 经济性
     */
    private String economy;

    /**
     * $column.columnComment
     */
    private String reliability;

    /**
     * 实施难度
     */
    private String exe;

    /**
     * 优点
     */
    private String advantage;

    /**
     * 缺点
     */
    private String disadvantage;

    /**
     * 投资预算
     */
    private BigDecimal budgetMin;

    private BigDecimal budgetMax;
    private BigDecimal budget;
    /**
     * N-1校验
     */

    private String n1;

    /**
     * 数据类型
     */
    private String planType;

    /**
     * 数据实体
     */

    private String operateData;

    /**
     * $column.columnComment
     */
    private String operate;

    /**
     * 解决需求数
     */
    private Long requireMents;

    /**
     * 历史时间
     */
    private String historyTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 标题
     */
    private String title;

    /**
     * 起始时间
     */
    private String strTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     *第几页
     */
    private Integer pageNum;


    private Long problemId;

}
