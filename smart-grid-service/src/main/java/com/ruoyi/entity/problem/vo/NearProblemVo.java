package com.ruoyi.entity.problem.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 附近问题
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class NearProblemVo {

    /**
     * 问题id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long problemId;

    /**
     *问题描述
     */
    private String name;

    /**
     * 二级分类
     */
    private String categoryLevel2;

    /**
     * 二级分类编码
     */
    private Integer categoryLevel2Code;

    /**
     * 线路 ID
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     *  设备类型
     */

    private String deviceType;

    /**
     * 设备名称
     */
    private String deviceName;

}
