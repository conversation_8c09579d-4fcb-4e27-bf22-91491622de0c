package com.ruoyi.entity.problem.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 问题规则业务对象 problem_rule_configuration
 *
 * <AUTHOR>
 * @date 2025-04-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ProblemRuleConfigurationBo extends BaseEntity {

    /**
     * 主键id
     */

    private Long id;

    /**
     * 二级分类
     */

    private String categoryLevel2;

    /**
     * 二级分类
     */

    private Integer categoryLevel2Code;

    /**
     * 问题情况
     */

    private Integer problemSituation;

    private Integer type;

    /**
     * 条件1
     */
    private String condition1;
    /**
     * 最大数量1
     */
    private Integer conditionMax1;

    /**
     * 最小数量1
     */
    private Integer conditionMin1;

    /**
     * 条件2
     */
    private String condition2;
    /**
     * 最大数量2
     */
    private Integer conditionMax2;

    /**
     * 最小数量2
     */
    private Integer conditionMin2;

    private String unit1;
    private String unit2;



    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     *第几页
     */
    private Integer pageNum;



}
