package com.ruoyi.entity.psm;

import lombok.Data;

import java.util.Objects;

@Data
public class BusinessCenterApiResult<R> {
    /**
     * 业务响应码
     */
    private String status;

    /**
     * 错误信息
     */
    private String errors;

    /**
     * 结果信息
     */
    private String message;

    /**
     * 响应结果
     */
    private R result;

    public boolean successful() {
        return Objects.equals(status, "000000");
    }

    public boolean wrong() {
        return !successful();
    }
}
