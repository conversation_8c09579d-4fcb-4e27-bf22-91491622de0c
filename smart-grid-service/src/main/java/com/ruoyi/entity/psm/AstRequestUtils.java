package com.ruoyi.entity.psm;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AstRequestUtils {
    private static final Logger logger = LoggerFactory.getLogger(AstRequestUtils.class);

    private static JSONObject baseHandler(Map<String, String> headers, String url, List<Map<String, Object>> param) throws IOException {
        // logger.info("请求资产查询接口，入参：{}", JSONUtil.toJsonStr(param));
        String rspStr = HttpUtils.postBody("http://egw.jn.js.sgcc.com.cn" + url, headers, param);
        JSONObject obj = JSONUtil.parseObj(rspStr);
        if (!"000000".equals(obj.getStr("status"))) {
            String errors = obj.getStr("errors");
            logger.error("请求资产信息失败：{}", errors);
            throw new RuntimeException("请求资产信息失败：" + errors);
        }
        return obj;
    }

    public static List<Map<String, Object>> getTFSAstInfo(Map<String, String> headers, String psrType, String astIds) throws IOException {
        int pageSize = 500, pageNo = 1;
        List<Map<String, Object>> allRecord = new ArrayList<>();
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            FilterBuild filterBuild = new FilterBuild().filter("distribution", 1)
                    .filter("psrType", psrType)
                    .filter("id", "").filter("modelId", "")
                    .filterParam("fields", "").filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .filterParam("astId", "in", astIds);
            JSONObject obj = baseHandler(headers, "/ast-center/astTFService/commonQuery", filterBuild.buildMainNetworkParam());
            BaseRsp baseRsp = obj.getByPath("result." + psrType, BaseRsp.class);
            Integer total = baseRsp.getTotal();
            // 计算还有多少页数，总共循环几次
            pageNo = (total + pageSize - 1) / pageSize;
            allRecord.addAll(baseRsp.getRecords());
        }
        return allRecord;
    }

    public static List<Map<String, Object>> getDSSAstInfo(Map<String, String> headers, String psrType, String astIds, String... field) throws IOException {
        int pageSize = 500, pageNo = 1;
        List<Map<String, Object>> allRecord = new ArrayList<>();
        String fields = "";
        if (field != null && field.length > 0) {
            fields = String.join(",", field);
        }
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            FilterBuild filterBuild = new FilterBuild().filter("distribution", 0)
                    .filter("psrType", psrType)
                    .filter("id", "").filter("modelId", "")
                    .filterParam("fields", fields).filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .filterParam("astId", "in", astIds);
            JSONObject obj = baseHandler(headers, "/ast-center/astDSService/commonQuery", filterBuild.buildMainNetworkParam());
            BaseRsp baseRsp = obj.getByPath("result." + psrType, BaseRsp.class);
            Integer total = baseRsp.getTotal();
            // 计算还有多少页数，总共循环几次
            pageNo = (total + pageSize - 1) / pageSize;
            allRecord.addAll(baseRsp.getRecords());
        }
        return allRecord;
    }

    @Data
    public static class BaseRsp {
        private Integer current;
        private Integer total;
        private Integer pages;
        private Integer size;
        private List<Map<String, Object>> records = new ArrayList<>();
    }
}
