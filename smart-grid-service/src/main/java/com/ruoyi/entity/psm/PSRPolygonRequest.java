package com.ruoyi.entity.psm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 多边形查询PSR设备请求参数
 */
@Data
public class PSRPolygonRequest {
    
    @JsonProperty("polygon")
    private String polygon;
    
    @JsonProperty("srs")
    private String srs = "EPSG:3857";
    
    @JsonProperty("psrQueryInfo")
    private PSRQueryInfo psrQueryInfo;
    
    @Data
    public static class PSRQueryInfo {
        @JsonProperty("psrQueryList")
        private List<PSRQuery> psrQueryList;
        
        @JsonProperty("attrNameList")
        private List<String> attrNameList;
    }
    
    @Data
    public static class PSRQuery {
        @JsonProperty("psrType")
        private String psrType;
        
        @JsonProperty("whereClause")
        private String whereClause = "1=1";
        
        @JsonProperty("attrNameList")
        private Object attrNameList = null;
        
        @JsonProperty("distribution")
        private Integer distribution;
    }
}
