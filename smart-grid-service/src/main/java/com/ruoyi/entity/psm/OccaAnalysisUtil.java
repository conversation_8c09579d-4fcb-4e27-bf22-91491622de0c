package com.ruoyi.entity.psm;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.net.HttpCookie;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 可开放容量登录工具类
 */
public class OccaAnalysisUtil {

    // 接口URL常量
    // 登录接口
    private static final String LOGIN_URL = "http://26.47.29.42:18002/DKY_OCCA/login";
    // 查询线路信息
    private static final String QUERY_LINE_URL = "http://26.47.29.42:18002/DKY_OCCA/line/analysis/queryLineInfo";
    // 站内主变负载及可开放容量信息分析
    private static final String SUBSTATION_TOPO_URL = "http://26.47.29.42:18002/DKY_OCCA/transformer/analysis/substationBasicTopo";

    // Cookie存储
    private static final Map<String, String> COOKIE_STORE = new HashMap<>();
    private static boolean isLoggedIn = false;

    /**
     * 基础响应实体（泛型）
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class BaseResponse<T> {
        private Integer code;
        private T result;
        private String resultMsg;

        public boolean isSuccess() {
            return code != null && code == 200;
        }
    }

    /**
     * 线路信息实体
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class LineInfo {
        private String id;
        private String lineName;
        private Double hisMaxCurrent;
        private Double hisMaxCurrentRaw;
        private String hisMaxDate;
        private String hisMaxDateRaw;
        private Double ratedPower;
        private String volGradeName;
        private Double applyCap;
        private Double ratedCurrentManual;
        private Double ratedCurrentDefault;
        private String ratedCurrentType;
        private Double ratedCurrent;
        private String ratedCurrentD5000;
        private Double lineCapFree;
        private Double allowMaxCur;
        private Double heavyCurrent;
        private Double mainByqCapFree;
        private String stName;
        private String byqVolGradeName;
        private String busbarName;
        private String byqNum;
        private Double byqCap;
        private Double realCapFree;
        private Integer tgNum;
        private String gbNum;
        private String zbNum;
        private String tgCap;
        private String existTgCap;
        private String existTgNum;
        private String notExistTgCap;
        private String notExistTgNum;
        private String pmsPdByqNum;
        private String pmsZsByqNum;
        private String pmsPbTotalNum;
        private String mainByqName;
        private String svgFileUrl;
        private String pbNum;
    }


    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class PhotovoltaicInfo {
        private String lineHisMaxDate;
        private Double gcHisValue;
        private Double weightedCalculation;
        private String gcHisValueDate;
        private Double currentGcValue;
        private Double lineHisMaxCurrent;
    }

    /**
     * 线路查询结果实体
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class LineResult {
        private LineInfo lineInfo;
        private PhotovoltaicInfo photovoltaic;
    }


    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class BusbarInfo {
        private String busbarId;
        private String busbarName;
        private List<LineInfo> lineInfos;
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class TopoInfo {
        private String id;
        private String mainByqName;
        private String windingId;
        private Double loadRate;
        private Integer loadType;
        private Double ratedPower;
        private Double capFree;
        private List<BusbarInfo> busbarInfos;
    }

    /**
     * 变压器信息实体
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class TransformerInfo {
        private String id;
        private String windingId;
        private String name;
        private Double loadRate;
        private Integer loadType;
        private Double ratedPower;
        private Double capFree;
        private Double hisMaxLoad;
        private String occTime;
        private Double hisMaxLoadRaw;
        private String occTimeRaw;
        private Integer windType;
    }

    /**
     * 变电站查询结果实体
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class SubstationResult {
        private String substationId;
        private String substationName;
        private String areaName;
        private String voltageName;
        private List<TopoInfo> topoInfos;
        private List<TransformerInfo> transformerInfoVos;
    }

    /**
     * 执行登录操作
     */
    private static boolean doLogin() {
        JSONObject json = new JSONObject();
        json.set("userName", "njAdmin");
        json.set("password", "bff31697be04f70ce3bc214c68b870f2");

        HttpResponse response = HttpRequest.post(LOGIN_URL)
                .header("Connection", "keep-alive")
                .header("Accept", "application/json, text/plain, */*")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
                .header("Content-Type", "application/json")
                .header("Origin", "http://26.47.29.42:18002")
                .header("Referer", "http://26.47.29.42:18002/DKY_OCCA/")
                .header("Accept-Language", "zh-CN,zh;q=0.9")
                .body(json.toString())
                .execute();

        if (response.isOk()) {
            List<HttpCookie> cookies = response.getCookies();
            for (HttpCookie cookie : cookies) {
                if ("JSESSIONID".equals(cookie.getName())) {
                    COOKIE_STORE.put("Cookie", cookie.getName() + "=" + cookie.getValue());
                    isLoggedIn = true;
                    return true;
                }
            }
        }
        isLoggedIn = false;
        return false;
    }

    /**
     * 检查Token是否失效
     */
    private static boolean isTokenInvalid(String responseBody) {
        try {
            BaseResponse<?> response = JSONUtil.toBean(responseBody, BaseResponse.class);
            return response.getCode() == 401
                    && "您的登录状态已过期，请重新登录!".equals(response.getResultMsg());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 确保登录状态有效
     */
    private static void ensureLogin() {
        if (!isLoggedIn || COOKIE_STORE.isEmpty()) {
            if (!doLogin()) {
                throw new RuntimeException("登录失败，无法获取有效Token");
            }
        }
    }

    /**
     * 通用查询方法
     */
    private static <T> BaseResponse<T> executeQuery(String url, JSONObject param, Class<T> resultType) {
        ensureLogin();

        String resultJson = HttpRequest.post(url)
                .addHeaders(COOKIE_STORE)
                .header("Accept", "application/json, text/plain, */*")
                .header("Content-Type", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
                .header("Referer", "http://26.47.29.42:18002/DKY_OCCA/")
                .body(param.toString())
                .execute()
                .body();

        if (isTokenInvalid(resultJson)) {
            clearCookies();
            ensureLogin();
            resultJson = HttpRequest.post(url)
                    .addHeaders(COOKIE_STORE)
                    .header("Accept", "application/json, text/plain, */*")
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
                    .header("Referer", "http://26.47.29.42:18002/DKY_OCCA/")
                    .body(param.toString())
                    .execute()
                    .body();

            if (isTokenInvalid(resultJson)) {
                throw new RuntimeException("重新登录后仍然无法获取有效Token");
            }
        }
        JSONObject jsonObject = JSONUtil.parseObj(resultJson);
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(jsonObject.getInt("code"));
        response.setResultMsg(jsonObject.getStr("resultMsg"));
        if (jsonObject.get("result") != null) {
            T result = JSONUtil.toBean(jsonObject.getStr("result"), resultType);
            response.setResult(result);
        }
        return response;
    }

    /**
     * 查询线路信息
     */
    public static BaseResponse<LineResult> queryLineInfo(String id) {
        JSONObject param = new JSONObject();
        param.set("id", id);
        return executeQuery(QUERY_LINE_URL, param, LineResult.class);
    }

    /**
     * 站内主变负载及可开放容量信息分析
     */
    public static BaseResponse<SubstationResult> substationBasicTopo(String substationId) {
        JSONObject param = new JSONObject();
        param.set("substationId", substationId);
        return executeQuery(SUBSTATION_TOPO_URL, param, SubstationResult.class);
    }

    /**
     * 清除Cookie
     */
    public static void clearCookies() {
        COOKIE_STORE.clear();
        isLoggedIn = false;
    }
}