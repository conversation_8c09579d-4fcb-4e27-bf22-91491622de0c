package com.ruoyi.entity.psm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.ParameterizedTypeImpl;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ruoyi.common.utils.JsonUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;


public interface MiddleCommon {
    Logger mLog = LoggerFactory.getLogger(MiddleCommon.class);
    String middleUrl = "http://egw.jn.js.sgcc.com.cn";
    Map<String, String> middleHeader = MapUtil.builder(new HashMap<String, String>())
            .put("Authorization", "Bearer 6f0ad858-05a2-4525-b113-d938063d6b88")
            .put("x-system-code", "ztgl-app-06103087-daf9-4690-b7df-6d3a9f63f3e3")
            .put("x-application-code", "app")
            .put("accept-encoding", "").build();

    default String lastUpdateConvert(Object lastUpdate) {
        if (lastUpdate == null) {
            return null;
        }
        Instant instant = Instant.ofEpochMilli(Long.parseLong(lastUpdate.toString()));
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(localDateTime);
    }

    default <T> T commonQuery(String uri, Object param, TypeReference<T> type) throws IOException {
        String requestUrl = middleUrl + "/" + uri;
        String str = HttpUtils.postBody(requestUrl, middleHeader, param);
        RspModel<T> obj = JsonUtils.parseObject(str, new TypeReference<RspModel<T>>() {
            @Override
            public Type getType() {
                ParameterizedType parameterizedType = (ParameterizedTypeImpl) _type;
                Type[] arguments = parameterizedType.getActualTypeArguments();
                arguments[0] = type.getType();
                return new ParameterizedTypeImpl(
                        arguments, parameterizedType.getOwnerType(), parameterizedType.getRawType()
                );
            }
        });
        if (!obj.isSuccessFul()) {
            String errorMsg = StringUtils.isNotBlank(obj.getErrors()) ? obj.getErrors() : obj.getMessage();
            mLog.info("查询失败：{}，入参：{}，响应结果：{}", errorMsg, JSONUtil.toJsonStr(param), str);
            throw new RuntimeException("查询失败");
        }
        return obj.getResult();
    }

    default <T> Map<String, DsResult<T>> middleQuery(Object param, Class<T> type, String... url) throws IOException {
        String requestUrl = middleUrl + "/psr-center/psrDSService/commonQuery";
        if (url != null && url.length > 0) {
            requestUrl = url[0];
        }
        String str = HttpUtils.postBody(requestUrl, middleHeader, param);
        RspModel<Map<String, DsResult<T>>> obj = JsonUtils.parseObject(str, new TypeReference<RspModel<Map<String, DsResult<T>>>>() {
            @Override
            public Type getType() {
                ParameterizedType parameterizedType = (ParameterizedType) _type;
                Type[] arguments = parameterizedType.getActualTypeArguments();
                ParameterizedType typeArgument = (ParameterizedType) arguments[0];
                Type[] actualTypeArguments = typeArgument.getActualTypeArguments();
                ParameterizedType actualTypeArgument = (ParameterizedType) actualTypeArguments[1];
                actualTypeArguments[1] = new ParameterizedTypeImpl(new Type[]{type}, actualTypeArgument.getOwnerType(), actualTypeArgument.getRawType());
                arguments[0] = new ParameterizedTypeImpl(actualTypeArguments, typeArgument.getOwnerType(), typeArgument.getRawType());
                return new ParameterizedTypeImpl(arguments, parameterizedType.getOwnerType(), parameterizedType.getRawType());
            }
        });

        if (!obj.isSuccessFul()) {
            mLog.info("查询失败：{}，入参：{}", obj.getErrors(), JSONUtil.toJsonStr(param));
            throw new RuntimeException("查询设备台账信息失败");
        }
        return obj.getResult();
    }

    default <T> Map<String, DsResult<T>> middleCommonQuery(Object param, TypeReference<T> type, String... url) throws IOException {
        String requestUrl = middleUrl + "/psr-center/psrDSService/commonQuery";
        if (url != null && url.length > 0) {
            requestUrl = url[0];
        }
        String str = HttpUtils.postBody(requestUrl, middleHeader, param);
        RspModel<Map<String, DsResult<T>>> obj = JsonUtils.parseObject(str, new TypeReference<RspModel<Map<String, DsResult<T>>>>() {
            @Override
            public Type getType() {
                ParameterizedType parameterizedType = (ParameterizedType) _type;
                Type[] arguments = parameterizedType.getActualTypeArguments();
                ParameterizedType typeArgument = (ParameterizedType) arguments[0];
                Type[] actualTypeArguments = typeArgument.getActualTypeArguments();
                ParameterizedType actualTypeArgument = (ParameterizedType) actualTypeArguments[1];

                actualTypeArguments[1] = new ParameterizedTypeImpl(
                        new Type[]{type.getType()}, actualTypeArgument.getOwnerType(), actualTypeArgument.getRawType()
                );
                arguments[0] = new ParameterizedTypeImpl(
                        actualTypeArguments, typeArgument.getOwnerType(), typeArgument.getRawType()
                );
                return new ParameterizedTypeImpl(
                        arguments, parameterizedType.getOwnerType(), parameterizedType.getRawType()
                );
            }
        });
        if (!obj.isSuccessFul()) {
            mLog.info("查询失败：{}，入参：{}", obj.getErrors(), JSONUtil.toJsonStr(param));
            throw new RuntimeException("查询设备台账信息失败");
        }
        return obj.getResult();
    }

    default Map<String, DsResult<Map<String, Object>>> middleQuery(Object param, String... url) throws IOException {
        String requestUrl = middleUrl + "/psr-center/psrDSService/commonQuery";
        if (url != null && url.length > 0) {
            requestUrl = url[0];
        }
        String str = HttpUtils.postBody(requestUrl, middleHeader, param);
        RspModel<Map<String, DsResult<Map<String, Object>>>> obj = JsonUtils.parseObject(
                str, new TypeReference<RspModel<Map<String, DsResult<Map<String, Object>>>>>() {
                });
        if (!obj.isSuccessFul()) {
            mLog.info("查询失败：{}，入参：{}", obj.getErrors(), JSONUtil.toJsonStr(param));
            throw new RuntimeException("查询设备台账信息失败");
        }
        return obj.getResult();
    }

    default <T> Map<String, DsResult<T>> middleTFSQuery(Object param, Class<T> type) throws IOException {
        return this.middleQuery(param, type, middleUrl + "/psr-center/psrTFService/commonQuery");
    }

    default <T> Map<String, DsResult<T>> middlePowerPlanQuery(Object param, TypeReference<T> type) throws IOException {
        return this.middleCommonQuery(param, type, middleUrl + "/PSRCenter/queryServices/commonQuery");
    }

    default <T> void middlePowerPlanQueryLoop(FilterBuild baseFilter, TypeReference<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        int pageNo = 1, pageSize = 8000;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            long startTime = System.currentTimeMillis();
            Map<String, DsResult<T>> dsResultMap = this.middlePowerPlanQuery(param, type);
            mLog.info("请求结束耗时：{}秒，当前页：{}，共：{}页", (System.currentTimeMillis() - startTime) / 1000, pageNum, pageNo);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("主网设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            String deviceType = dsResultMap.keySet().iterator().next();
            // 只有一种查询类型的遍历
            mLog.info("查询设备类型：{}，共：{}页，当前页：{}", deviceType, pageNo, pageNum);
            DsResult<T> info = dsResultMap.get(deviceType);
            pageNo = (info.getTotal() + pageSize - 1) / pageSize;
            consumer.accept(info.getRecords());
        }
    }

    default Map<String, DsResult<Map<String, Object>>> middleTFSQuery(Object param) throws IOException {
        return this.middleQuery(param, middleUrl + "/psr-center/psrTFService/commonQuery");
    }

    default <T> void middleQueryLoop(FilterBuild baseFilter, Class<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        this.middleQueryLoop(8000, baseFilter, type, consumer);
    }

    default <T> void middleQueryLoop(Integer pageSize, FilterBuild baseFilter, Class<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        int pageNo = 1;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            Map<String, DsResult<T>> dsResultMap = middleQuery(param, type);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("配网设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            Iterator<String> iterator = dsResultMap.keySet().iterator();
            String typeKey = iterator.next();
            // 只有一种查询类型的遍历
            DsResult<T> info = dsResultMap.get(typeKey);
            pageNo = (info.getTotal() + pageSize - 1) / pageSize;
            mLog.info("查询设备类型：{}，共：{}页，当前页：{}", typeKey, pageNo, pageNum);
            consumer.accept(info.getRecords());
        }
    }

    default void middleQueryLoop(FilterBuild baseFilter, MiddleConsumer<List<Map<String, Object>>> consumer) throws Exception {
        this.middleQueryLoop(8000, baseFilter, consumer);
    }

    default void middleQueryLoop(Integer pageSize, FilterBuild baseFilter, MiddleConsumer<List<Map<String, Object>>> consumer) throws Exception {
        int pageNo = 1;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            Map<String, DsResult<Map<String, Object>>> dsResultMap = middleQuery(param);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("配网设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            for (String deviceType : dsResultMap.keySet()) {
                // 只有一种查询类型的遍历
                DsResult<Map<String, Object>> info = dsResultMap.get(deviceType);
                pageNo = (info.getTotal() + pageSize - 1) / pageSize;
                consumer.accept(info.getRecords());
            }
        }
    }

    default <T> void middleTFSQueryLoop(FilterBuild baseFilter, Class<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        int pageNo = 1, pageSize = 8000;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            long startTime = System.currentTimeMillis();
            Map<String, DsResult<T>> dsResultMap = this.middleTFSQuery(param, type);
            mLog.info("请求结束耗时：{}秒，当前页：{}，共：{}页", (System.currentTimeMillis() - startTime) / 1000, pageNum, pageNo);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("主网设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            String deviceType = dsResultMap.keySet().iterator().next();
            // 只有一种查询类型的遍历
            mLog.info("查询设备类型：{}，共：{}页，当前页：{}", deviceType, pageNo, pageNum);
            DsResult<T> info = dsResultMap.get(deviceType);
            pageNo = (info.getTotal() + pageSize - 1) / pageSize;
            consumer.accept(info.getRecords());
        }
    }

    default void middleTFSQueryLoop(FilterBuild baseFilter, MiddleConsumer<List<Map<String, Object>>> consumer) throws Exception {
        int pageNo = 1, pageSize = 8000;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            long startTime = System.currentTimeMillis();
            Map<String, DsResult<Map<String, Object>>> dsResultMap = this.middleTFSQuery(param);
            mLog.info("请求结束耗时：{}秒，当前页：{}，共：{}页", (System.currentTimeMillis() - startTime) / 1000, pageNum, pageNo);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("主网设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            String deviceType = dsResultMap.keySet().iterator().next();
            // 只有一种查询类型的遍历
            DsResult<Map<String, Object>> info = dsResultMap.get(deviceType);
            pageNo = (info.getTotal() + pageSize - 1) / pageSize;
            consumer.accept(info.getRecords());
        }
    }

    /**
     * 查询线路下的配变信息
     */
    default <T> List<T> listTransformerByFeeder(Object param, Class<T> type) throws IOException {
        String str = HttpUtils.postBody("http://egw.jn.js.sgcc.com.cn/psr-center/accessPointService/listTransformerByFeeder", middleHeader, param);
        RspModel<List<T>> obj = JsonUtils.parseObject(str, new TypeReference<RspModel<List<T>>>() {
            @Override
            public Type getType() {
                ParameterizedType parameterizedType = (ParameterizedType) _type;
                Type[] arguments = parameterizedType.getActualTypeArguments();
                ParameterizedType typeArgument = (ParameterizedType) arguments[0];
                arguments[0] = new ParameterizedTypeImpl(
                        new Type[]{type}, typeArgument.getOwnerType(), typeArgument.getRawType()
                );
                return new ParameterizedTypeImpl(
                        arguments, parameterizedType.getOwnerType(), parameterizedType.getRawType()
                );
            }
        });
        if (!obj.isSuccessFul()) {
            mLog.info("查询失败：{}，入参：{}", obj.getErrors(), JSONUtil.toJsonStr(param));
            throw new RuntimeException("查询设备台账信息失败");
        }
        return obj.getResult();
    }

    /**
     * 低压通用资源查询
     */
    default <T> Map<String, DsResult<T>> lvQuery(Object param, Class<T> type) throws IOException {
        return this.middleQuery(param, type, middleUrl + "/psr-center/psrLVService/commonQuery");
    }

    default <T> void lvQueryLoop(Integer pageSize, FilterBuild baseFilter, Class<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        int pageNo = 1;
        for (int pageNum = 1; pageNum <= pageNo; pageNum++) {
            List<Map<String, Object>> param = baseFilter
                    .filterParam("current", pageNum)
                    .filterParam("size", pageSize)
                    .buildMainNetworkParam();
            Map<String, DsResult<T>> dsResultMap = this.lvQuery(param, type);
            if (CollectionUtil.isEmpty(dsResultMap)) {
                mLog.info("低压设备查询数据为空，入参：{}", JSONUtil.toJsonStr(param));
                break;
            }
            String deviceType = dsResultMap.keySet().iterator().next();
            // 只有一种查询类型的遍历
            DsResult<T> info = dsResultMap.get(deviceType);
            pageNo = (info.getTotal() + pageSize - 1) / pageSize;
            mLog.info("同步设备当前页：{}，共：{}页", pageNum, pageNo);
            consumer.accept(info.getRecords());
        }
    }

    default <T> void lvQueryLoop(FilterBuild baseFilter, Class<T> type, MiddleConsumer<List<T>> consumer) throws Exception {
        this.lvQueryLoop(8000, baseFilter, type, consumer);
    }

    @Data
    class RspModel<T> {
        private String errors;
        private String message;
        private String status;
        private T result;

        public boolean isSuccessFul() {
            return "000000".equals(this.status);
        }
    }

    @Data
    class DsResult<T> {
        private int current;
        private int pages;
        private int size;
        private int total;
        private List<T> records;
    }

    @Data
    class Point {
        private Double lng;
        private Double lat;
    }

    default Point parseGeo(String geoPositon) {
        if (StringUtils.isNotBlank(geoPositon)) {
            String[] geo;
            if (geoPositon.contains(",")) {
                geo = geoPositon.split(",");
            } else {
                geo = geoPositon.split(" ");
            }
            Point point = new Point();
            point.setLng(Double.valueOf(geo[0]));
            point.setLat(Double.valueOf(geo[1]));
            return point;
        }
        return null;
    }

    @Data
    class TelemetryRsp<T> {
        private Integer current;
        private Integer total;
        private Integer pages;
        private Integer size;
        private List<T> records = new ArrayList<>();
    }

    @Data
    class TelemetryValue {
        /**
         * 遥测值
         */
        private Double measValue;
        /**
         * 遥测日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date dataTime;
    }

    /**
     * 查询遥测曲线信息
     */

    default <T> TelemetryRsp<T> queryTelemetry(Map<String, Object> param, Class<T> tClass) {
        try {
            mLog.info("请求遥测接口，入参：{}", JSONUtil.toJsonStr(param));
            String telemetryUrl = "http://egw.jn.js.sgcc.com.cn/realMeasCenter/telemetry/commonQuery";
            String rspBody = HttpUtils.postJson(telemetryUrl, middleHeader, param);
            TypeReference<BusinessCenterApiResult<TelemetryRsp<T>>> typeReference = new TypeReference<BusinessCenterApiResult<TelemetryRsp<T>>>() {
                @Override
                public Type getType() {
                    ParameterizedTypeImpl parameterizedType = (ParameterizedTypeImpl) _type;
                    Type[] arguments = parameterizedType.getActualTypeArguments();
                    ParameterizedTypeImpl typeArgument = (ParameterizedTypeImpl) arguments[0];
                    Type[] modelType = typeArgument.getActualTypeArguments();
                    modelType[0] = new ParameterizedTypeImpl(
                            new Type[]{tClass}, typeArgument.getOwnerType(), typeArgument.getRawType()
                    );
                    return new ParameterizedTypeImpl(
                            modelType, parameterizedType.getOwnerType(), parameterizedType.getRawType()
                    );
                }
            };
            BusinessCenterApiResult<TelemetryRsp<T>> businessResult = JsonUtils.parseObject(rspBody, typeReference);
            if (!businessResult.successful()) {
                mLog.error("查询设备遥测数据失败：{}", businessResult.getErrors());
                throw new RuntimeException("查询设备遥测数据失败，请联系管理员");
            }
            return businessResult.getResult();
        } catch (IOException e) {
            mLog.error("查询设备遥测数据失败：", e);
            throw new RuntimeException("查询设备遥测数据失败", e);
        }
    }
}
