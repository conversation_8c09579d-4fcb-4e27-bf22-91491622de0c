package com.ruoyi.entity.psm;

import java.util.*;

public class FilterBuild {
    private final Map<String, Object> baseParam = new HashMap<>();
    private final Map<String, Object> param = new HashMap<>();
    private final List<Map<String, Object>> params = new ArrayList<>();

    /**
     * 配网查询
     */
    public static FilterBuild distributionNetwork(String psrType) {
        return new FilterBuild().filter("distribution", 0)
                .filter("id", "100001")
                .filter("psrType", psrType);
    }

    /**
     * 主网查询
     */
    public static FilterBuild transmissionNetwork(String psrType) {
        return new FilterBuild().filter("distribution", 1)
                .filter("id", "100001")
                .filter("psrType", psrType);
    }

    public FilterBuild filter(String paramName, Object paramVal) {
        baseParam.put(paramName, paramVal);
        return this;
    }

    public FilterBuild filterParam(String fieldName, String compare, String fieldValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("fieldName", fieldName);
        param.put("compare", compare);
        param.put("fieldValue", fieldValue);
        params.add(param);
        return this;
    }

    public FilterBuild filterParam(Boolean flag, String fieldName, String compare, String fieldValue) {
        if (!flag) {
            return this;
        }
        HashMap<String, Object> param = new HashMap<>();
        param.put("fieldName", fieldName);
        param.put("compare", compare);
        param.put("fieldValue", fieldValue);
        params.add(param);
        return this;
    }

    public FilterBuild filterParam(String paramName, Object paramVal) {
        param.put(paramName, paramVal);
        return this;
    }

    public Map<String, Object> build() {
        baseParam.put("filters", params);
        baseParam.put("params", param);
        return baseParam;
    }

    public List<Map<String, Object>> buildMainNetworkParam() {
        param.put("filters", params);
        baseParam.put("params", param);
        return Collections.singletonList(baseParam);
    }

    public Map<String, Object> buildNestParam() {
        param.put("filters", params);
        baseParam.put("params", param);
        return baseParam;
    }

    public FilterBuild clearParams() {
        params.clear();
        return this;
    }

    public FilterBuild resetParamAndFilter(){
        param.clear();
        params.clear();
        return this;
    }
}
