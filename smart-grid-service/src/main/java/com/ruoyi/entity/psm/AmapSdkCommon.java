package com.ruoyi.entity.psm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.entity.znap.ContactFeederKg;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;

/**
 * 电网一张图接口
 */
public interface AmapSdkCommon {
    Logger amapSdkGridLog = LoggerFactory.getLogger(AmapSdkCommon.class);
    String baseUrl = "http://pms.kjyzt.js.sgcc.com.cn:32080";

    /**
     * 根据设备id查询设备台账信息
     *
     * @param param
     * @return
     * @throws IOException
     */
    static List<DeviceRspModel<DeviceInfo>> queryDeviceById(Object param) throws IOException {
        String requestUrl = baseUrl + "/amap-gateway-service/amap-sdk-service/query/disSearch/queryDeviceById";
        String rspStr = HttpUtils.postBody(requestUrl, Collections.emptyMap(), param);
        TypeReference<AmapRspModel<List<DeviceRspModel<DeviceInfo>>>> typeReference = new TypeReference<AmapRspModel<List<DeviceRspModel<DeviceInfo>>>>() {
        };
        AmapRspModel<List<DeviceRspModel<DeviceInfo>>> rspModel = JsonUtils.parseObject(rspStr, typeReference);
        if (!rspModel.isSuccessFul()) {
            amapSdkGridLog.error("电网一张图设备接口查询设备台账信息失败：{}", rspModel.msg());
            throw new RuntimeException("查询异常：" + rspModel.msg());
        }
        if (CollectionUtil.isEmpty(rspModel.getResult())) {
            return new ArrayList<DeviceRspModel<DeviceInfo>>();
        }
        return rspModel.getResult();
    }

    static String getBaseInfo(Object param) throws IOException {
        String requestUrl = baseUrl + "/amap-gateway-service/pms-amap-components-service/new/devicecard/getBaseInfo";
        String rspStr = HttpUtils.postBody(requestUrl, Collections.emptyMap(), param);
        return rspStr;
    }

    /**
     * 根据多边形查询PSR设备信息
     *
     * @param param 查询参数
     * @return PSR设备查询结果
     * @throws IOException
     */
    static PSRByPolygonRspModel queryPSRByPolygon(Object param) throws IOException {
        String requestUrl = baseUrl + "/erccachesvr/geoCenter/spatialAnalysis/queryPSRByPolygon";

        String rspStr = HttpUtils.postBody(requestUrl, Collections.emptyMap(), param);

        TypeReference<PSRByPolygonRspModel> typeReference = new TypeReference<PSRByPolygonRspModel>() {
        };
        PSRByPolygonRspModel rspModel = JsonUtils.parseObject(rspStr, typeReference);
        if (rspModel == null) {
            amapSdkGridLog.error("多边形查询PSR设备接口返回空结果");
            throw new RuntimeException("查询异常：返回结果为空");
        }
        if (!"000000".equals(rspModel.getStatus())) {
            amapSdkGridLog.error("多边形查询PSR设备接口调用失败：{}", rspModel.getErrors());
            throw new RuntimeException("查询异常：" + rspModel.getErrors());
        }

        return rspModel;
    }

    /**
     * 根据坐标点为原点查询指定半径内设备
     */
    static PSRByPolygonRspModel queryPSRByCircle(Object param) throws IOException {
        String requestUrl = baseUrl + "/erccachesvr/geoCenter/spatialAnalysis/queryPSRByCircle";
        String rspStr = HttpUtils.postBody(requestUrl, Collections.emptyMap(), param);

        TypeReference<PSRByPolygonRspModel> typeReference = new TypeReference<PSRByPolygonRspModel>() {
        };
        PSRByPolygonRspModel rspModel = JsonUtils.parseObject(rspStr, typeReference);
        if (rspModel == null) {
            amapSdkGridLog.error("根据坐标点为原点查询指定半径内设备");
            throw new RuntimeException("查询异常：返回结果为空");
        }
        if (!"000000".equals(rspModel.getStatus())) {
            amapSdkGridLog.error("根据坐标点为原点查询指定半径内设备：{}", rspModel.getErrors());
            throw new RuntimeException("查询异常：" + rspModel.getErrors());
        }
        return rspModel;
    }

    /**
     * 根据馈线id查询联络开关列表
     *
     * @param param
     * @return
     * @throws IOException
     */
    static LiaisonInfo<ContactFeederKg> getLiaisonInfoById(Object param) throws IOException {
        String requestUrl = baseUrl + "/amap-gateway-service/amap-app-service/GetUserInfoByBoxAndPoint/getLiaisonInfoById";
        String rspStr = HttpUtils.postBody(requestUrl, Collections.emptyMap(), param);

        TypeReference<AmapRspModel<LiaisonInfo<ContactFeederKg>>> typeReference = new TypeReference<AmapRspModel<LiaisonInfo<ContactFeederKg>>>() {
        };
        AmapRspModel<LiaisonInfo<ContactFeederKg>> infoAmapRspModel = JsonUtils.parseObject(rspStr, typeReference);
        if (!infoAmapRspModel.isSuccessFul()) {
            amapSdkGridLog.error("电网一张图查询联络接口调用异常：{}", infoAmapRspModel.msg());
            throw new RuntimeException("电网一张图查询联络接口调用异常：" + infoAmapRspModel.msg());
        }
        LiaisonInfo<ContactFeederKg> result = infoAmapRspModel.getResult();
        if (ObjectUtil.isEmpty(result)) {
            return new LiaisonInfo();
        }
        return result;
    }

    @Data
    class AmapRspModel<T> {
        private List<String> logF12List;
        private Reply reply;
        private T result;

        public boolean isSuccessFul() {
            if (this.reply == null) {
                return false;
            }
            return Objects.equals(this.reply.getCode(), 1000);
        }

        public String msg() {
            if (this.reply == null) {
                return "";
            }
            return this.reply.getMsg();
        }
    }

    @Data
    class DeviceRspModel<T> {
        private String distribution;
        private String psrType;
        private Integer count;
        private List<T> psrList;
    }

    @Data
    class DeviceInfo {
        private String cityOrg;
        private String cityOrgName;
        private String coordinate;
        private String crossfeederid;
        private String crossfeederprovince;
        private String direction;
        private String distribution;
        private String feederId;
        private String feederName;
        private String isprovincecontainer;
        private String maintCrew;
        private String maintCrewName;
        private String maintOrg;
        private String maintOrgName;
        private String provinceId;
        private String psrId;
        private String psrName;
        private String psrType;
        private String psrTypeName;
        private String switchStatus;
        private String vlevelCode;
        private String vlevelName;
    }

    @Data
    class LiaisonInfo<T> {
        private String message;
        private List<T> result;
    }

    @Data
    class Reply {
        private Integer code;
        private String msg;
    }

    @Data
    class PowerGridFilter {
        private String polygon;
        private String srs;
        private double radius;
        private String point;
        private Integer maxQueryCount;
        private List<Map<String, Object>> psrUriList;
        private PsrQueryInfo psrQueryInfo = new PsrQueryInfo();

        public static PowerGridFilter construction(Integer maxQueryCount, List<Map<String, Object>> psrUriList) {
            PowerGridFilter powerGridFilterBuild = new PowerGridFilter();
            powerGridFilterBuild.setPsrUriList(psrUriList);
            powerGridFilterBuild.setMaxQueryCount(maxQueryCount);
            return powerGridFilterBuild;
        }

        public PowerGridFilter attrName(List<String> attr) {
            psrQueryInfo.setAttrNameList(attr);
            return this;
        }
    }

    @Data
    class PsrQueryInfo {
        private List<?> psrQueryList;
        private List<String> attrNameList;
    }

    /**
     * 多边形查询PSR设备响应
     */
    @Data
    class PSRByPolygonRspModel {
        private String status;
        private String errors;
        private PSRResult result;
    }

    /**
     * PSR查询结果
     */
    @Data
    class PSRResult {
        private List<PSRDataList> psrDataList;
    }

    /**
     * PSR数据列表
     */
    @Data
    class PSRDataList {
        private String distribution;
        private String psrType;
        private Integer count;
        private List<PSRDevice> psrList;
    }

    /**
     * PSR设备信息
     */
    @Data
    class PSRDevice {
        private String psrId;
        private String psrType;
        private String psrTypeName;
        private String psrName;
        private String zoneId;
        private String zoneName;
        private String vlevelName;
        private String vlevelCode;
        private String coordinate;
        private String feederId;
        private String feederName;
        private String chargedState;
        private String switchStatus;
        private String distribution;
        private String maintCrew;
        private String maintCrewName;
        private String maintOrg;
        private String maintOrgName;
        private String cityOrg;
        private String cityOrgName;
        private String provinceId;
        private String crossfeederid;
        private String crossfeederprovince;
        private String isprovincecontainer;
        private String direction;
        private String siteName;
    }
}
