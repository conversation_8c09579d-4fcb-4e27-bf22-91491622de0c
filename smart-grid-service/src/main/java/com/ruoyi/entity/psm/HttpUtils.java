package com.ruoyi.entity.psm;

import cn.hutool.json.JSONUtil;
import io.opentelemetry.api.internal.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;


import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Http客户端二次封装
 *
 * <AUTHOR> Liu
 */
@Slf4j
public class HttpUtils {

    private static final int HTTP_SUCCESS_CODE = 200;

    private static final OkHttpClient OK_HTTP_CLIENT;
    private static final MediaType APPLICATION_JSON = MediaType.parse("application/json; charset=utf-8");
    private static final String EMPTY_BODY = "";

    static {
        OK_HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(2, TimeUnit.MINUTES)
            .build();
    }

    /**
     * 发送HttpGet请求
     *
     * @param url 请求地址
     * @return 请求响应
     * @throws IOException io异常
     */
    public static String get(String url) throws IOException {
        return get(url, null, null);
    }

    /**
     * 发送路径参数的Get请求
     *
     * @param url      请求地址
     * @param pathPara 路径参数
     * @return 请求响应
     * @throws IOException io异常
     */
    public static String get(String url, Map<String, Object> pathPara) throws IOException {
        return get(url, null, pathPara);
    }

    /**
     * 发送携带Header及路径参数的Get请求
     *
     * @param url      请求地址
     * @param headers  请求头
     * @param pathPara 路径参数，支持value类型为数组、Collection类型
     * @return 请求响应
     * @throws IOException io异常
     */
    public static String get(String url, Map<String, String> headers, Map<String, Object> pathPara) throws IOException {
        Optional<Map<String, Object>> pathParaOptional = Optional.ofNullable(pathPara);
        if (pathParaOptional.isPresent()) {
            String[] originalUrls = url.split("\\?");
            StringBuilder pathParaBuilder = new StringBuilder();
            pathParaBuilder.append(originalUrls[0]);
            pathParaBuilder.append("?");
            if (originalUrls.length > 1) {
                pathParaBuilder.append(originalUrls[1]).append("&");
            }
            pathParaOptional.get().forEach((paraName, paraValue) -> {
                if (null != paraValue) {
                    Class<?> itemClazz = paraValue.getClass();
                    List<Object> values;
                    if (itemClazz.isArray()) {
                        Object[] valueArr = ((Object[]) paraValue);
                        values = Arrays.asList(valueArr);
                    } else if (Collection.class.isAssignableFrom(itemClazz)) {
                        Collection<Object> collection = (Collection<Object>) paraValue;
                        values = new ArrayList<>(collection);
                    } else {
                        values = Collections.singletonList(paraValue);
                    }
                    for (Object value : values) {
                        try {
                            pathParaBuilder.append(paraName)
                                .append("=")
                                .append(URLEncoder.encode(
                                    String.valueOf(value), StandardCharsets.UTF_8.name()
                                )).append("&");
                        } catch (UnsupportedEncodingException e) {
                            // throw new Exception(e.getMessage());
                        }
                    }
                }
            });
            log.debug("original url:{},after combine with path para full url is:{}", url, pathParaBuilder.toString());
            url = pathParaBuilder.toString();
        } else {
            log.debug("url:{} 无 pathPara参数", url);
        }
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.get().url(url);
        Optional<Map<String, String>> headersOptional = Optional.ofNullable(headers);
        headersOptional.ifPresent(headerKeyValueMap -> headerKeyValueMap.forEach(requestBuilder::addHeader));
        Request request = requestBuilder.build();
        return execute(request);
    }

    /**
     * 发送ContentType为application/json的Post请求
     *
     * @param url      请求地址
     * @param headers  头信息
     * @param jsonBody JsonObject格式请求体
     * @return 请求响应
     */
    public static String postJson(String url, Map<String, String> headers, Map<String, Object> jsonBody) throws IOException {
        RequestBody requestBody = RequestBody.create(APPLICATION_JSON,Objects.requireNonNull(JSONUtil.toJsonStr(jsonBody)));
        return post(url, headers, requestBody);
    }

    /**
     * 按照application/json整体Json字符串提交
     *
     * @param url        请求地址
     * @param headers    头信息
     * @param bodyObject http提交本体信息
     * @return 请求响应
     * @throws IOException 操作异常
     */
    public static String postBody(String url, Map<String, String> headers, Object bodyObject) throws IOException {
        String formJson = JsonUtils.toJSONString(bodyObject);
        if (StringUtils.isNullOrEmpty((formJson))) {
            throw new IOException("Form参数转Json失败");
        }
        assert formJson != null;
        RequestBody requestBody = FormBody.create(MediaType.get("application/json"),formJson);
        return post(url, headers, requestBody);
    }

    /**
     * 发送ContentType为application/x-www-form-urlencoded的Post请求
     *
     * @param url     请求地址
     * @param headers 头信息
     * @param form    JsonObject格式Form内容
     * @return 请求响应
     * @throws IOException 操作异常
     */
    public static String postForm(String url, Map<String, String> headers, Map<String, Object> form) throws IOException {
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        Optional<Map<String, Object>> formOptional = Optional.ofNullable(form);
        formOptional.ifPresent(headerKeyValueMap -> headerKeyValueMap.forEach((name, value) -> {
            formBodyBuilder.add(name, String.valueOf(value));
        }));
        RequestBody requestBody = formBodyBuilder.build();
        return post(url, headers, requestBody);
    }


    /**
     * 按照Post发送RequestBody请求
     *
     * @param url         请求地址
     * @param headers     Http请求头
     * @param requestBody 请求响应Body
     * @return 响应内容
     * @throws IOException io异常
     */
    public static String post(String url, Map<String, String> headers, RequestBody requestBody) throws IOException {
        Request.Builder postBuilder = new Request.Builder()
            .post(requestBody)
            .url(url);
        Optional<Map<String, String>> headersOptional = Optional.ofNullable(headers);
        headersOptional.ifPresent(headerKeyValueMap -> headerKeyValueMap.forEach(postBuilder::addHeader));
        Request request = postBuilder.build();
        return execute(request);
    }

    /**
     * 发送ContentType为multipart/form-data的Post请求
     *
     * @param url      请求地址
     * @param headers  头信息
     * @param paramMap 表单内容
     * @return 请求响应
     * @throws IOException 操作异常
     */
    public static String postMultipartForm(String url, Map<String, String> headers, Map<String, String> paramMap) throws IOException {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        paramMap.forEach(builder::addFormDataPart);
        return post(url, headers, builder.build());
    }

    /**
     * 发送ContentType为multipart/form-data的Post请求
     *
     * @param url     请求地址
     * @param headers 头信息
     * @param body    表单内容
     * @return 请求响应
     * @throws IOException 操作异常
     */
    public static String postMultipartForm(String url, Map<String, String> headers, MultipartBody body) throws IOException {
        return post(url, headers, body);
    }

    /**
     * 执行HttpRequest并返回Http请求结果
     *
     * @param request http请求
     * @return 响应内容
     * @throws IOException io异常
     */
    private static String execute(Request request) throws IOException {
        return execute(OK_HTTP_CLIENT, request);
    }

    /**
     * 执行HttpRequest并返回Http请求结果
     *
     * @param request http请求
     * @return 响应内容
     * @throws IOException io异常
     */
    private static String execute(OkHttpClient okHttpClient, Request request) throws IOException {
        try (Response response = okHttpClient.newCall(request).execute()) {
            int responseCode = response.code();
            if (responseCode == HTTP_SUCCESS_CODE) {
                ResponseBody body = response.body();
                if (body == null) {
                    return EMPTY_BODY;
                } else {
                    return body.string();
                }
            }
            throw new IOException(String.format("http request failed,code=%d", responseCode));
        }
    }
}
