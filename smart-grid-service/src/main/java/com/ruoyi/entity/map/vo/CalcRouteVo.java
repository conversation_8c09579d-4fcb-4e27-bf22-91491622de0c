package com.ruoyi.entity.map.vo;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 通过思极或者南瑞生成的路径返回实体
 */
@Data
public class CalcRouteVo {
    public CalcRouteVo(Double totalLength, List<Node> nodeList, List<double[]> lngLatList) {
        this.totalLength = totalLength;
        this.nodeList = nodeList;
        this.lngLatList = lngLatList;
    }

    /**
     * 总长度
     */
    private Double totalLength;

    /**
     * 路径节点集合
     */
    private List<Node> nodeList;


    /**
     * 路径坐标点集合
     */
    private List<double[]> lngLatList;
}
