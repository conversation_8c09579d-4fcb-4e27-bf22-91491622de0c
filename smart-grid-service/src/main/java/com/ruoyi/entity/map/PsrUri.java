package com.ruoyi.entity.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PsrUri {
    @JsonProperty("psrId")
    private String psrId;

    @JsonProperty("psrType")
    private String psrType;

    // 下面这些属性根据实际 JSON 中存在的字段按需添加，示例里第二个元素有 provinceId 和 distribution
    @JsonProperty("provinceId")
    private String provinceId;

    @JsonProperty("distribution")
    private Integer distribution;
}
