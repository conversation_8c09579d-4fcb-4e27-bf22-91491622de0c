package com.ruoyi.entity.map.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 导线段实体
 */
@Data
@EqualsAndHashCode()
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class WireEndVo {
    /**
     * 线路id
     */
    private String psrId;

    /**
     * 线名称
     */
    private String feederName;

    /**
     * 线路类型
     */
    private String feederType;

}
