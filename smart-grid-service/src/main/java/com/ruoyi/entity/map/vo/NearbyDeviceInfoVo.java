package com.ruoyi.entity.map.vo;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.entity.device.DevInfo;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 附近设备信息视图对象
// * 包括物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NearbyDeviceInfoVo {

    /**
     * 设备ID
     */
    private String psrId;

    /**
     * 设备类型
     */
    private String psrType;

    /**
     * 设备类型名称
     */
    private String psrTypeName;

    /**
     * 设备名称
     */
    private String psrName;

    /**
     * 区域ID
     */
    private String zoneId;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 端口列表
     */
    private String portList;

    /**
     * 端口名称列表
     */
    private String portNameList;

    /**
     * 电压等级名称
     */
    private String vlevelName;

    /**
     * 电压等级编码
     */
    private String vlevelCode;

    /**
     * 坐标位置
     */
    private String coordinate;

    /**
     * 所属馈线ID  注意：可能有多个线路拼接的例如：10DKX-481441,10DKX-481313
     */
    private String feederId;

    /**
     * 所属馈线名称 注意：可能有多个线路拼接的例如：10kV金陵村线161,10kV森林湾线262
     */
    private String feederName;

    /**
     * 带电状态
     */
    private String chargedState;

    /**
     * 开关状态
     */
    private String switchStatus;

    /**
     * 配网标识
     */
    private String distribution;

    /**
     * 维护班组ID
     */
    private String maintCrew;

    /**
     * 维护班组名称
     */
    private String maintCrewName;

    /**
     * 维护机构ID
     */
    private String maintOrg;

    /**
     * 维护机构名称
     */
    private String maintOrgName;

    /**
     * 地市机构ID
     */
    private String cityOrg;

    /**
     * 地市机构名称
     */
    private String cityOrgName;

    /**
     * 省份ID
     */
    private String provinceId;

    /**
     * 跨馈线ID
     */
    private String crossFeederId;

    /**
     * 跨馈线省份
     */
    private String crossFeederProvince;

    /**
     * 是否省级容器
     */
    private String isProvinceContainer;

    /**
     * 方向
     */
    private String direction;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 距离（米）
     */
    private Double distance;

    /**
     * 剩余间隔数量（仅对环网柜zf07和开关站zf04有效）
     */
    private Integer remainingBayCount;

    /**
     * 母线开关信息列表（仅对环网柜zf07和开关站zf04有效）
     */
    private List<BusbarSwitchVo> busbarSwitchVoList;

    private double[] lngLat;

    // 是否为具备线路联络的站房类型（环网柜、开关站）
    public boolean isContactStation() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.CONTACT_STATION_TYPES.contains(psrType);
    }

    /**
     * 判断是否杆塔
     */
    public boolean isPole() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.POLE_TYPES.contains(psrType);
    }

    // 是否为具备联络点的设备类型（杆塔、环网柜、开关站）
    public boolean isContact() {
        if (psrType == null) {
            return false;
        }
        return this.isPole() || isContactStation();
    }


    public List<String> getFeederIds() {
        if (StringUtils.isBlank(feederId)) {
            return new ArrayList<>();
        }
        String[] parts = feederId.trim().split(",");
        return Arrays.asList(parts);
    }

    public List<String> getFeederNames() {
        if (StringUtils.isBlank(feederName)) {
            return new ArrayList<>();
        }
        String[] parts = feederName.trim().split(",");
        return Arrays.asList(parts);
    }
}
