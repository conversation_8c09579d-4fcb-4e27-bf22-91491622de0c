package com.ruoyi.entity.map;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 附近节点
 */
@Data
public class NearNode {
    public NearNode() {
    }

    public NearNode(Node node, double[] sourceLngLat) {
        this.node = node;
        this.sourceLngLat = sourceLngLat;
    }

    /**
     * 原和目标节点
     */
    Node node;

    /**
     * 原和目标坐标
     */
    double[] sourceLngLat;

    /**
     * 直线和路径最近的节点
     */
    ClosestNode linearClosestNode, routeClosestNode;

    /**
     * 直线和路径最近的节点
     */
    List<ClosestNode> linearClosestNodes, routeClosestNodes;

    /**
     * 获取最近的节点
     */
    public ClosestNode getClosestNode() {
        return routeClosestNode != null ? routeClosestNode : linearClosestNode;
    }

}
