package com.ruoyi.entity.map;

import lombok.Data;

import java.util.List;

/**
 * 带ID的线段集合
 */
@Data
public class LineString {
    private final String id;
    private final String name;
    private final List<double[]> coordinates;

    public LineString(String id, String name,List<double[]> coordinates) {
        this.id = id;
        this.name = name;
        this.coordinates = coordinates;
    }

    public String getId() {
        return id;
    }
    public String getName() {
        return name;
    }

    public List<double[]> getCoordinates() {
        return coordinates;
    }
}
