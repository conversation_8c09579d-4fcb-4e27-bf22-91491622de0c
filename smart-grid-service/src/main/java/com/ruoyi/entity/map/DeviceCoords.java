package com.ruoyi.entity.map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.locationtech.jts.geom.Geometry;

/**
 * 设备坐标
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceCoords {
    /**
     * 设备id
     */
    private String id;

    /**
     * 设备类型
     */
    private String type;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备距离
     */
    private Double distance;

    /**
     * 坐标
     */
    private String coords;

    /**
     * 所属线路的id
     */

    private String feederId;

    /**
     * 所属线路的id
     */

    private String feederName;

    public DeviceCoords(String id, String type,String name,Double distance,String coords,String feederId) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.distance = distance;
        this.coords = coords;
        this.feederId = feederId;
    }
}
