package com.ruoyi.entity.map.vo;

import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.common.utils.entity.MyCoordinate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class RouteVo {
    /**
     * 路径坐标
     */
    private List<GHPoint> routeList;
    /**
     * 杆塔坐标
     */
    private List<GHPoint> GTList;
}
