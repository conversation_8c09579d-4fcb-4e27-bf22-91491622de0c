package com.ruoyi.entity.map.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 附近线路信息视图对象
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NearbyLineInfoVo {

    /**
     * 线路ID
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 负载率
     */
    private String loadRate;

    /**
     * 负载率数值
     */
    private Double loadRateValue;

    /**
     * 是否重过载
     */
    private Boolean isOverload;

    /**
     * 所属母线ID
     */
    private String busbarId;

    /**
     * 所属母线名称
     */
    private String busbarName;

    /**
     * 所属隔离开关ID
     */
    private String switchId;

    /**
     * 所属隔离开关名称
     */
    private String switchName;

    /**
     * 距离（米）
     */
    private Double supplyRadius;

    /**
     * 是否为备用开关
     */
    private Boolean isSpare;

    /**
     * 所属变电站ID
     */
    private String substationId;

    /**
     * 所属变电站名称
     */
    private String substationName;

}
