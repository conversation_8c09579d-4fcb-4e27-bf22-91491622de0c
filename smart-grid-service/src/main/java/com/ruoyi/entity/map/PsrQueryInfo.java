package com.ruoyi.entity.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class PsrQueryInfo {
    private List<String> attrNameList = Arrays.asList(
            "classId", "psrId", "psrType", "psrTypeName", "psrName",
            "zoneId", "ZoneName", "innerTransId", "srcId", "srcName",
            "portList", "portNameList", "vlevelName", "vlevelCode", "VOLTAGE_LE",
            "coordinate", "feederId", "feederName", "useNature", "chargedState",
            "switchStatus", "distribution", "maintCrew", "maintCrewName", "maintOrg",
            "maintOrgName", "cityOrg", "cityOrgName", "provinceId", "crossFeederId",
            "crossFeederProvince", "isProvinceContainer", "direction", "dataSource", "plantType","pubPrivFlag"
    );


    @JsonProperty("psrQueryList")
    private List<PsrQuery> psrQueryList;  // 若该列表有具体实体结构，可替换为对应类，这里先设为Object

}
