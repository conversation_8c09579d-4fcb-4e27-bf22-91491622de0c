package com.ruoyi.entity.map.vo;

import com.ruoyi.entity.map.DeviceCoords;
import lombok.Data;
import org.locationtech.jts.geom.Coordinate;

import java.util.List;

@Data
public class NeedFeederVo {

    private String psrId;

    private String feedName;

    /**
     * 将数据库String的坐标集合转，List<List<double[]>>
     */
    private List<List<double[]>> geoCoordinateList;

    /**
     * 线找线，线1的坐标
     */
    private Coordinate point1;

    /**
     * 线找线，线2的坐标
     */
    private Coordinate point2;

    /**
     * 点找线，最近的设备坐标
     */
    private double[] recentlyPoint;

    /**
     * 最近设备id
     */
    private String deviceId;

    /**
     * 最近设备类型
     */
    private String deviceType;

    /**
     * 最近设备名称
     */
    private String deviceName;

    /**
     * 线上所有设备
     */
    private List<DeviceCoords> deviceCoordsList;

    /**
     * 供电半径(km)
     */
    private Double supplyRadius;

    /**
     * 装机容量
     */
    private Double feederRateCapacity;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 线路总长度(km)
     */
    private Double length;

    /**
     * 起点电站
     */
    private String startStation;

    /**
     * 供电区域
     */
    private String supplyArea;

}
