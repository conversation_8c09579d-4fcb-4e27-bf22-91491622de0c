package com.ruoyi.entity.map.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备距离信息视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DeviceDistanceInfoVo {

    /**
     * B设备ID
     */
    private String bPsrId;

    /**
     * B设备类型
     */
    private String bPsrType;

    /**
     * B设备名称
     */
    private String bPsrName;

    /**
     * 直线距离（米）
     */
    private Double straightLineDistance;

    /**
     * 规划路径长度（米）
     */
    private Double plannedRouteDistance;

    /**
     * 路径坐标点集合
     */
    private List<double[]> lngLatList;

    /**
     * B设备所属线路ID
     */
    private String feederId;

    /**
     * B设备所属线路名称
     */
    private String feederName;


    /**
     * B设备所属主变名称
     */
    private String transformerName;

    /**
     * B设备所属变电站ID
     */
    private String substationId;

    /**
     * B设备所属变电站名称
     */
    private String substationName;

    /**
     * 装机容量（kVA）
     */
    private Double installedCapacity;

    /**
     * 历史最大电流（A）
     */
    private Double maxHistoryCurrent;

    /**
     * 历史最大负载率
     */
    private Double maxHistoryLoadRate;

    /**
     * 历史最大负载率发生时间
     */
    private String maxHistoryLoadRateDate;

}
