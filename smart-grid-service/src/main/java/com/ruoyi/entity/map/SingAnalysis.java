package com.ruoyi.entity.map;

import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.NodePath;
import lombok.Data;

/**
 * 单线图拓扑分析对象
 */
@Data
public class SingAnalysis {
    public SingAnalysis(ZnapTopology topologyMap, NodePath nodePath) {
        this.topologyMap = topologyMap;
        this.nodePath = nodePath;
    }

    /**
     * znap拓扑节点
     */
    ZnapTopology topologyMap;

    /**
     * 分析之后的节点路径
     */
    NodePath nodePath;
}
