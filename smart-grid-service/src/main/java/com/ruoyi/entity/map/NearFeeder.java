package com.ruoyi.entity.map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附近线路线路实体
 */
@EqualsAndHashCode()
@Data
@TableName("near_feeder")
public class NearFeeder {

    /**
     * 资源ID
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 主线路id
     */
    private String feederId;
    /**
     * 附近线路id
     */
    private String nearFeederId;
}
