package com.ruoyi.entity.map.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 设备距离查询请求参数
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDistanceQueryBo {

    /**
     * A设备经度
     */
    @NotNull(message = "A设备经度不能为空")
    private Double aLng;

    /**
     * A设备纬度
     */
    @NotNull(message = "A设备纬度不能为空")
    private Double aLat;

    /**
     * B设备经度
     */
    @NotNull(message = "B设备经度不能为空")
    private Double bLng;

    /**
     * B设备纬度
     */
    @NotNull(message = "B设备纬度不能为空")
    private Double bLat;


    @NotBlank(message = "B设备feederId不能为空")
    private String bfeederId;

    /**
     * 思极地图
     */
    @NotBlank(message = "token不能为空")
    private String token;
}
