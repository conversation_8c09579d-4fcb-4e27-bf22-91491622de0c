package com.ruoyi.entity.map.vo;

import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import lombok.Data;

import java.util.List;

/**
 * 生产加工联络线实体对象
 */
@Data
public class ProcessContactVo {

    /**
     * 总长度
     */
    private Double totalLength;

    /**
     * 联络节点集合
     */
    List<Node> contactNodeList;

    /**
     * 开始、结束设备ID
     */
    String startPsrId, endPsrId;

    /**
     * 联络线
     */
    String contactFeederId, contactFeederName;

//    /**
//     * 變電站、變電站内母綫、變電站内剩餘開關
//     */
//    String bdzBayKgId, bdzBayKgName;

    /**
     * 站房
     */
    private String endStationPsrId, endStationPsrType, endStationPsrName;

    private String endBusPsrId, endBusPsrName;

    /**
     * 关联的节点
     */
    Node contactNode;

    @Override
    public ProcessContactVo clone() {
        ProcessContactVo result = new ProcessContactVo();

        result.setTotalLength(this.totalLength);
        result.setContactNodeList(NodeUtils.copyNodes(contactNodeList));
        result.setStartPsrId(this.startPsrId);
        result.setEndPsrId(this.endPsrId);
        result.setContactFeederId(this.contactFeederId);
        result.setContactFeederName(this.contactFeederName);
        result.setContactNode(this.contactNode);
        return result;
    }
}
