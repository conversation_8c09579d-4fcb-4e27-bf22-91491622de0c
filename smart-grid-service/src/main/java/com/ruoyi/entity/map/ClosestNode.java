package com.ruoyi.entity.map;

import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 最近距离的节点
 */
@Data
public class ClosestNode {

    public ClosestNode(Node sourceNode, Object closest, double[] sourceLngLat, double[] closestLngLat) {
        this.sourceNode = sourceNode;
        this.closest = closest;
        this.sourceLngLat = sourceLngLat;
        this.closestLngLat = closestLngLat;
    }

    /**
     * 原和目标节点
     */
    Node sourceNode;

    Object closest;

    /**
     * 原和目标最近坐标
     */
    double[] sourceLngLat, closestLngLat;

    /**
     * 长度
     */
    double length;

    /**
     * 当前路径生成对应的节点集合 TODO  生成 参考王贵来写的mapRoute方法
     */
    List<Node> routeNodeList;

//    /**
//     * 路径坐标集合
//     */
//    List<double[]> routeCoords;

  //  String feederId, feederName;

    String bdzId, bdzName;

}
