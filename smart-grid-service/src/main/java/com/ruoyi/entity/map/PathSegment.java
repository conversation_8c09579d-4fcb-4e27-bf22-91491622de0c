package com.ruoyi.entity.map;

import com.graphhopper.util.shapes.GHPoint;
import lombok.Data;

@Data
public  class PathSegment {
        private final GHPoint startTower;
        private final GHPoint endTower;
        private final double length;
       private Boolean gt;

        public PathSegment(GHPoint startTower, GHPoint endTower, double length) {
            this.startTower = startTower;
            this.endTower = endTower;
            this.length = length;
        }

        public GHPoint getStartTower() { return startTower; }
        public GHPoint getEndTower() { return endTower; }
        public double getLength() { return length; }

        @Override
        public String toString() {
            return "PathSegment{" +
                    "startTower=" + startTower +
                    ", endTower=" + endTower +
                    ", length=" + length +
                    '}';
        }
    }
