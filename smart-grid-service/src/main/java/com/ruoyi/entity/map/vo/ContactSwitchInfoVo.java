package com.ruoyi.entity.map.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 联络开关信息视图对象
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ContactSwitchInfoVo {

    /**
     * 联络开关名称
     */
    private String switchName;

    /**
     * 联络开关ID
     */
    private String switchId;

    /**
     * 关联联络线路名称
     */
    private String contactLineName;

    /**
     * 关联联络线路ID
     */
    private String contactLineId;

    /**
     * 线路负载率
     */
    private String loadRate;

    /**
     * 能否转供 - 是否可以转供到其他线路
     */
    private Boolean canTransfer;

    /**
     * 联络开关类型
     */
    private String switchType;

}
