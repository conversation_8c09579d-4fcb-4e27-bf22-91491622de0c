package com.ruoyi.entity.calc;

import lombok.Data;

import java.util.*;

/**
 * 组合式 线路负荷转供
 */
@Data
public class CombFeederTransfer {
    public CombFeederTransfer(double sourceLoad, double sourceChangeLoad, List<FeederTransferCap> feederTransferCaps) {
        this.sourceLoad = sourceLoad;
        this.sourceChangeLoad = sourceChangeLoad;
        this.feederTransferCaps = feederTransferCaps;
    }

    String feederId;

    String feederName;

    /**
     * 原线路负载率
     */
    double sourceLoad;

    /**
     * 专供之后原线路的负载率
     */
    double sourceChangeLoad;

    /**
     * 当前组合线路转供集合
     */
    List<FeederTransferCap> feederTransferCaps;

    /**
     * 联络线线路变化
     */
    List<FeederLoadChange> fTrChangeLoads;

    /**
     * 原线路转供线路 是否通过（低于最大负载率）
     */
    boolean sourceHasPass;

    /**
     * 转供的线路都已经通过（都低于最大负载率）
     */
    boolean fTrHasPass;

    public boolean equals(CombFeederTransfer combFTr) {
        // 负载率不相同直接返回
        if (sourceLoad != combFTr.getSourceLoad() || sourceChangeLoad != combFTr.getSourceChangeLoad()) {
            return false;
        }

        // 比较专供路径是否相同
        List<FeederTransferCap> arr1 = feederTransferCaps;
        List<FeederTransferCap> arr2 = combFTr.getFeederTransferCaps();
        if (arr1 == null || arr2 == null) {
            return arr1 == arr2;
        }

        if (arr1.size() != arr2.size()) {
            return false;
        }

        Set<FeederTransferCap> set1 = new HashSet<>(arr1);
        Set<FeederTransferCap> set2 = new HashSet<>(arr2);

        return set1.equals(set2);
    }
}
