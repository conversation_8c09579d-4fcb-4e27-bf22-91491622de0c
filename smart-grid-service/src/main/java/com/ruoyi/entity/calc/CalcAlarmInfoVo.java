package com.ruoyi.entity.calc;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;



/**
 * 手动计算实例告警视图对象 calc_alarm_info
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class CalcAlarmInfoVo {

    private static final long serialVersionUID = 1L;
    private int id;
    private Integer eventCount;
    private int isAuto;
    /**
     * 设备id
     */
    private String psrId;

    /**
     * 设备类型
     */
    private String psrType;

    /**
     * 设备父id
     */
    private String psrParentId;

    private String feederId;

    /**
     * 设备父类型
     */
    private String psrParentType;

    /**
     * 实例id
     */
    private String instanceId;

    @ExcelProperty(value = "网格名称")
    private String gridName;

    /**
     * 告警时间
     */
    @ExcelProperty(value = "异常时间")
    private Date alarmTime;

    @ExcelProperty(value = "仿真实例号")
    private String simuCaseNo;


    /**
     * 告警类型
     */
    private Long alarmType;
    @ExcelProperty(value = "告警类型")
    private String alarmTypeName;
    /**
     * 告警内容
     */
    @ExcelProperty(value = "告警内容")
    private String alarmContent;


    private Long retId;

    private int state=0;

    private String grid;


    /**
     * 告警设备名称
     */
    private String modelName;
    private Integer accumulative;
    private Integer level = 1;

    /**
     * 是否处理
     */
    private Boolean status;

}
