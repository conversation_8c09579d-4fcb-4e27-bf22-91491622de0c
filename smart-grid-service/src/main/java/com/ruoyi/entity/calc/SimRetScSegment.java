package com.ruoyi.entity.calc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// 短路计算线路段结果
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimRetScSegment {
    private Long id;
    private Integer retId;
    private Long scNd;
    private Double iInitKa;
    private Double iPeakKa;
    private Double iThKa;
    private Double iIndKa;
    private Double iaInd;
    private Double pIndMw;
    private Double qIndMvar;
    private Double vIndValue;
    private Double vaIndValue;
    private Double iJndKa;
    private Double iaJnd;
    private Double pJndMw;
    private Double qJndMvar;
    private Double vJndValue;
    private Double vaJndValue;
}