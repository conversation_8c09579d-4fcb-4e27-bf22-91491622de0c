package com.ruoyi.entity.calc;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class CalcParams {
    /**
     * 消息态 南京固定为：nanjing
     */
    private String groupId;
    /**
     * 馈线id
     */
    private String feeder_id;
    /**
     * 是否潮流计算
     */
    private int is_pf;
    /**
     * 是否断路计算
     */
    private int is_sc;

    /**
     * 添加的光伏设备属性
     */
    private List<CalcVerOneParams> vec_para_gen = new ArrayList<>();
    /**
     * 添加的光伏设备曲线有功无功
     */
    private List<CalcVerTwoParams> vec_run_gen = new ArrayList<>();
    /**
     * 负荷设备信息
     */
    private List<VecRunDomain> vec_run_ld = new ArrayList<>();
    /**
     * 配变设备信息
     */
    private List<VecRunDomain> vec_run_dt = new ArrayList<>();
    /**
     * 电源信息
     */
    private List<Map<Object, Object>> vec_run_source = new ArrayList<>();
}
