package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 网格优化策略对象 grid_optimize_policy
 *
 * <AUTHOR> developer
 * @date 2024-12-16
 */
@Data
@TableName("grid_optimize_policy")
public class GridOptimizePolicy {
    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 分析目标设置
     */
    private String targetSetting;
    /**
     * 分析约束设置
     */
    private String constraintSetting;
}
