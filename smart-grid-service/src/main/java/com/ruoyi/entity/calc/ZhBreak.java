package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 【请填写功能名称】对象 zh_break
 * <AUTHOR> developer
 * @date 2024-08-28
 */
@Data
@TableName("zh_break")
public class ZhBreak {
    /**
     * 设备名称
     */
    private String name;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 投运日期
     */
    private Date startTime;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 设备id
     */
    @TableId
    private String psrId;
    /**
     * 资产id
     */
    private String astId;
    /**
     * 所属电站id
     */
    private String substationId;
    /**
     * 所属电站名称
     */
    private String substationName;
    /**
     * 组合设备类型
     */
    private String combinationType;
    /**
     * 组合设备类型名称
     */
    private String combinationTypeName;
    /**
     * 组合设备id
     */
    private String combinationId;
    /**
     * 组合设备名称
     */
    private String combinationName;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 组合设备所属的线路
     */
    private String parentFeeder;
    /**
     *
     */
    private String feeder;
    /**
     * 开关类型
     */
    private String psrType;
    /**
     * 地区特征
     */
    private String regionalism;

    /**
     * 开闭状态
     */
    private String status;

    /**
     * 所属间隔
     */
    private String bay;

    /**
     * 数据来源
     */
    private String source;
}
