package com.ruoyi.entity.calc;

import com.ruoyi.graph.vo.NodeVo;
import lombok.Data;

import java.util.List;

/**
 * 线路负荷转供
 */
@Data
public class FeederTransferCapVo {
    public FeederTransferCapVo() {
    }

    public FeederTransferCapVo(String sourcePsrId, String sourcePsrName, String tfContactPsrId, String tfContactPsrName) {
        this.sourcePsrId = sourcePsrId;
        this.sourcePsrName = sourcePsrName;
        this.tfContactPsrId = tfContactPsrId;
        this.tfContactPsrName = tfContactPsrName;
    }

    /**
     * 分闸和和闸开关
     */
    NodeVo fenNode, heNode;

    /**
     * 原线路
     */
    String sourcePsrId, sourcePsrName;

    /**
     * 专供线路ID
     */
    String tfContactPsrId, tfContactPsrName;


    /**
     * 原线路负载率
     */
    double sourceLoad;

    /**
     * 专供之后原线路的负载率
     */
    double sourceChangeLoad;

    /**
     * 转供联络线路负载率
     */
    double tfContactLoad;

    /**
     * 专供之后转供联络线路的负载率
     */
    double tfContactChangeLoad;

    /**
     * 原线路的配变数量
     */
    int sourcePbNum;

    /**
     * 转供线路的配变数量
     */
    int tfContactPbNum;

    /**
     * 转供的路径
     */
    List<NodeVo> paths;


}
