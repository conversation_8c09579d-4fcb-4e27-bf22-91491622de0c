package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 供电网格台账数据对象 problem_grid
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Data
@TableName("problem_grid")
public class ZhDmsPowerGrid {
    /**
     * 网格编码
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 网格名称
     */
    private String gridName;
    /**
     * 地市ID
     */
    private String city;
    /**
     * 地市名称
     */
    private String cityName;
    /**
     * 区县ID
     */
    private String maintOrg;
    /**
     * 区县名称
     */
    private String maintName;
    /**
     *
     */
    private String coord;

    /**
     * 网格围栏坐标
     */
    private String wkt;

}
