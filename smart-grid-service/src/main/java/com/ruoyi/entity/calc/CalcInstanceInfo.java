package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 计算实例对象 calc_instance_info
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
@Data
@TableName("calc_instance_info")
public class CalcInstanceInfo {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 实例id
     */

    private Integer isRecycle;

    private String instanceId;
    /**
     * 网格编码
     */
    private String gridCode;
    /**
     * 网格名称
     */
    private String gridName;
    /**
     * 状态0:执行中 1：执行结束 2:执行异常
     */
    private Integer state;


    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 手动计算：0
     * 定时自动计算：1
     */
    private Integer isAuto;

    private Long problemId;
    private String date;


}
