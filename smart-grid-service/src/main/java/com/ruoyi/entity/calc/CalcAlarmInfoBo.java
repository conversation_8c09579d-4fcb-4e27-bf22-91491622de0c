package com.ruoyi.entity.calc;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 手动计算实例告警业务对象 calc_alarm_info
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CalcAlarmInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    //是否消纳
    private Boolean isXn;
    /**
     * 设备id
     */
    @NotBlank(message = "设备id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String psrId;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String psrType;

    /**
     * 设备父id
     */
    @NotBlank(message = "设备父id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String psrParentId;

    /**
     * 设备父类型
     */
    @NotBlank(message = "设备父类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String psrParentType;

    /**
     * 实例id
     */
    @NotBlank(message = "实例id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String instanceId;

    private String gridCode;

    /**
     * 告警时间
     */
    @NotNull(message = "告警时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date alarmTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 告警内容
     */
    @NotBlank(message = "告警内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String alarmContent;

    /**
     * 告警类型
     */
    @NotNull(message = "告警类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long alarmType;

    private Long retId;

    private String feederId;

    /**
     * 告警设备名称
     */
    private String modelName;
    private Integer accumulative;
    private Integer level = 1;
    private String simuCaseNo;
    /**
     * 是否处理
     */
    private Boolean status;
    private int isAuto;

    /**
     * 网格名称
     */
    private String gridName;

}
