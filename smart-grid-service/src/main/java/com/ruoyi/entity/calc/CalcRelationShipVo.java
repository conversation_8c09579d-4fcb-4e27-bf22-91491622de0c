package com.ruoyi.entity.calc;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * 仿真计算关系视图对象 calc_relation_ship
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
@Data
@ExcelIgnoreUnannotated
public class CalcRelationShipVo {

    private static final long serialVersionUID = 1L;

    /**
     * 消息id
     */
    @ExcelProperty(value = "消息id")
    private String msgId;

    /**
     * 实例id
     */
    @ExcelProperty(value = "实例id")
    private String instanceId;


}
