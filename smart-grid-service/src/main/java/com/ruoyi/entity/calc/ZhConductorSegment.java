package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 导线端对象 zh_conductor_segment
 * <AUTHOR> developer
 * @date 2024-09-04
 */
@Data
@TableName("zh_conductor_segment")
public class ZhConductorSegment {
    /**
     * 资源ID
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 所属地市
     */
    private String city;
    /**
     * 所属馈线
     */
    private String feeder;
    /**
     * 所属线路
     */
    private String line;
    /**
     * 参考长度
     */
    private Double referenceLength;
    /**
     * 导线长度
     */
    private Double length;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 起始杆塔
     */
    private String startPole;
    /**
     * 终止杆塔
     */
    private String stopPole;
    /**
     * 所属主干/分支线
     */
    private String branchFeeder;
    /**
     * 资产id
     */
    private String astId;
    /**
     * 所属导线
     */
    private String wire;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 投运日期
     */
    private Date startTime;
    /**
     * 营配标识
     */
    private String pubPrivFlag;
    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
    /**
     * 导线截面积
     */
    private Double sectionalArea;
    /**
     * 调整后面积
     */
    private Double ckSectionalArea;
    /**
     * 每公里电阻值
     */
    private Double rohmPerKm;
    /**
     * 每公里电抗值
     */
    private Double xohmPerKm;
    /**
     * 每公里电容值
     */
    private Double cnfPerKm;
    /**
     * 电阻值
     */
    private Double rohm;
    /**
     * 电抗值
     */
    private Double xohm;
    /**
     * 电容值
     */
    private Double cnf;
    /**
     * 导线型号
     */
    private String model;
}
