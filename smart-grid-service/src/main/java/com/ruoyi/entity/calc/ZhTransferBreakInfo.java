package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 转供开关信息对象 zh_transfer_break_info
 *
 * <AUTHOR> developer
 * @date 2025-03-05
 */
@Data
@TableName("zh_transfer_break_info")
public class ZhTransferBreakInfo {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id" ,type = IdType.NONE)
    private Long id;
    /**
     *索引（开关顺序）
     */
    private Integer indexNum;
    /**
     *联络开关psrId
     */
    private String linkBreakPsrId;

    /**
     * 计算实例id
     * */
    private String calcId;
    /**
     *联络开关名称
     */
    private String linkBreakPsrName;
    /**
     *联络开关类型
     */
    private String linkBreakPsrType;
    /**
     *普通开关psrid
     */
    private String breakPsrId;
    /**
     *普通开关名称
     */
    private String breakPsrName;
    /**
     *普通开关类型电流负载
     */
    private String breakPsrType;
    /**
     *联络线路电流负载
     */
    private Double linkFeederRate;
    /**
     *源线路电流负载
     */
    private Double sourceFeederRate;

}
