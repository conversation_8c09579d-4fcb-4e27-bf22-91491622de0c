package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 实例告警规则对象 calc_alarm_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Data
@TableName("calc_alarm_rule")
public class CalcAlarmRule   {

    private static final long serialVersionUID=1L;

    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 描述
     */
    private String description;
    /**
     * 设定值
     */
    private Double limitValue;

    @TableId(type = IdType.AUTO)
    private Long id;

}
