package com.ruoyi.entity.calc;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;



/**
 * 计算实例视图对象 calc_instance_info
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
@Data
@ExcelIgnoreUnannotated
public class CalcInstanceInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 实例id
     */
    @ExcelProperty(value = "实例id")
    private String instanceId;
    private int id;


    /**
     * 网格编码
     */
    @ExcelProperty(value = "网格编码")
    private String gridCode;
    private Integer isRecycle;

    /**
     * 网格名称
     */
    @ExcelProperty(value = "网格名称")
    private String gridName;

    /**
     * 状态0:执行中 1：执行结束 2:执行异常
     */
    @ExcelProperty(value = "状态0:执行中 1：执行结束 2:执行异常")
    private Integer state;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    @ColumnWidth(20)
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    @ColumnWidth(20)
    private Date endTime;

    private Integer isAuto;

    @ExcelProperty(value = "问题id")
    private Long problemId;

}
