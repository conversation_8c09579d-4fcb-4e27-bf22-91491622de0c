package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ruoyi.util.EpsCommonUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Data
@TableName("zh_conductor_segment_curve")
public class ZhConductorSegmentCurve {
    private String psrId;
    private String totW;
    private String totVar;
    private String phsA;
    private Date day;

    public List<Double> getTotW() {
        if (StringUtils.isNotBlank(totW)) {
            return EpsCommonUtils.readStrToList(totW, new TypeReference<List<Double>>() {});
        }
        return Collections.emptyList();
    }

    public List<Double> getTotVar(){
        if (StringUtils.isNotBlank(totVar)) {
            return EpsCommonUtils.readStrToList(totVar, new TypeReference<List<Double>>() {});
        }
        return Collections.emptyList();
    }

    public List<Double> getPhsA(){
        if (StringUtils.isNotBlank(phsA)) {
            return EpsCommonUtils.readStrToList(phsA, new TypeReference<List<Double>>() {});
        }
        return Collections.emptyList();
    }
}
