package com.ruoyi.entity.calc;

import lombok.Data;

@Data
public class ArtWorkStatistics {
    // 变电站数量
    private int transformerNum;
    // 馈线数量
    private int feederNum;
    // 公变数量
    private int gbNum;
    // 专变数量
    private int zbNum;
    // 光伏数量
    private int genNum;
    // 充电桩数量
    private int chargeNum;
    // 储能数量
    private int energyNum;
    // 用户数量
    private int userNum;
    // 数据日期
    private String time;
    // 总数
    private int total;
}
