package com.ruoyi.entity.calc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimPfRet {
    private Long id;
    private String msgId;
    private Long feederId;
    private Integer pfRet;
    private Integer scRet;
    private Date startDt;
    private Date endDt;
    private Date createDt;
    private Integer retCode;
}
