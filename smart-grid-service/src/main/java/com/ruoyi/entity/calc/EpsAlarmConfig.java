package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 告警配置信息
 * <AUTHOR>
 */
@Data
@TableName("zh_eps_alarm_config")
public class EpsAlarmConfig {
    /**
     * 主键
     */
    @TableId(value = "id" ,type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 警告类型描述
     */
    private String description;
    /**
     * 阈值
     */
    private Double limitValue;
    /**
     * 低中临界值
     */
    private Double lowLimit;
    /**
     * 中高临界值
     */
    private Double highLimit;
    /**
     * 短期临界值
     */
    private Integer lowPeriod;
    /**
     * 长期临界值
     */
    private Integer highPeriod;
}
