package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 告警信息对象 zh_eps_alarm
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Data
@TableName("zh_eps_alarm")
public class ZhEpsAlarm {
    /**
     * 主键
     */
    @TableId(value = "id" ,type = IdType.NONE)
    private Long id;
    /**
     *
     */
    private String modelId;
    private String modelType;
    /**
     * 告警设备名称
     */
    private String modelName;
    /**
     * 网格编号:仿真实例号
     */
    private String simuCaseNo;
    /**
     * 告警时间
     */
    private Date alarmTime;
    /**
     * 告警内容
     */
    private String alarmContext;
    /**
     * 告警类型
     */
    private Integer alarmType;
    /**
     * 严重程度
     */
    @TableField(exist = false)
    private Integer alarmLevel;
    /**
     * 告警阈值
     */
    @TableField(exist = false)
    private Integer limitValue;
    /**
     * 线路id
     */
    private String feederId;
    /**
     * 是否处理
     */
    private Boolean status;
    /**
     * 告警等级
     */
    private Integer level;
}
