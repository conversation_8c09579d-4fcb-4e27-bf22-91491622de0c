package com.ruoyi.entity.calc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// 节点结果
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimRetPfNode {
    private Long id;
    private Integer retId;
    private Integer idx;
    private Double vValue;
    private Integer vStatus;
    private Double vaValue;
    private Integer vaStatus;
    private Double pValue;
    private Integer pStatus;
    private Double qValue;
    private Integer qStatus;
}