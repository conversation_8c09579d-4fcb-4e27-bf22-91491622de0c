package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 计算实例回收规则对象 calc_recycle_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Data
@TableName("calc_recycle_rule")
public class CalcRecycleRule {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id" ,type = IdType.NONE) 
    private Long id;
    /**
     * 实例状态
     */
    private Long state;
    /**
     * 回收天数
     */
    private Long recycleDay;
    /**
     * 回收小时数
     */
    private Long recycleHour;
    /**
     * 回收分钟数
     */
    private Long recycleMin;
    /**
     * 是否启用，0否1是
     */
    private Long isEnable;

}
