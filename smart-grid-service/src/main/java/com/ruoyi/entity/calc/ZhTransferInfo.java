package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 转供方案对象 zh_transfer_info
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
@Data
@TableName("zh_transfer_info")
public class ZhTransferInfo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 源线路
     */
    private String sourceFeeder;
    private String linkFeeder;
    /**
     * 源线路名称
     */
    private String sourceFeederName;
    private String linkFeederName;
    /**
     * 源线路最高电流
     */
    private double sourceFeederCurrent;
    /**
     * 联络馈线额定电流
     */
    private double linkFeederCurrent;
    /**
     * 转供状态
     */
    private String transferStatus;
    /**
     * 源线路联络开关
     */
    private String sourceFeederBreak;
    /**
     * 源线路联络开关名称
     */
    private String sourceFeederBreakName;
    private String sourceFeederBreakType;
    /**
     * 计算id(手动计算的实例id和定时计算的版本id)
     */
    private String calcId;
    /**
     * 计算结果id
     */
    private Long retId;
    /**
     * 计算转供时间
     */
    private Date time;
    /**
     * 联络馈线当前负载
     */
    private double linkFeederRate;
    /**
     * 源馈线当前负载
     */
    private double sourceFeederRate;
    /**
     * 联络馈线原来的负载
     */
    private double linkFeederOriginalRate;

}
