package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zh_telemetry_value")
public class ZhTelemetryValue {
    /**
     * 遥测值
     */
    private String measValue;
    /**
     * 遥测日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dataTime;
    /**
     * 遥测一天的日期点
     */
    private String dateDayPoint;
    /**
     * 遥测点id
     */
    private String measPointId;
}
