package com.ruoyi.entity.calc;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 计算实例业务对象 calc_instance_info
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CalcInstanceInfoBo extends BaseEntity {

    /**
     * 实例id
     */
    @NotBlank(message = "实例id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String instanceId;

    private Integer isRecycle;

    /**
     * 网格编码
     */
    @NotBlank(message = "网格编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String gridCode;

    /**
     * 网格名称
     */
    @NotBlank(message = "网格名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String gridName;

    /**
     * 状态0:执行中 1：执行结束 2:执行异常
     */
    @NotNull(message = "状态0:执行中 1：执行结束 2:执行异常不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer state;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeS;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeE;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeS;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeE;

    private Integer isAuto;

    private Long problemId;


}
