package com.ruoyi.entity.calc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 手动计算实例告警对象 calc_alarm_info
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@Data
@TableName("calc_alarm_info")
public class CalcAlarmInfo {


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;
    /**
     * 设备id
     */
    private String psrId;
    /**
     * 设备类型
     */
    private String psrType;
    /**
     * 设备父id
     */
    private String psrParentId;
    /**
     * 设备父类型
     */
    private String psrParentType;

    private String feederId;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 告警时间
     */
    private Date alarmTime;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 告警类型
     */
    private Long alarmType;

    private Long retId;


    /**
     * 告警设备名称
     */
    private String modelName;
    private Integer accumulative;
    private Integer level = 1;
    private String simuCaseNo;
    /**
     * 是否处理
     */
    private Boolean status;
    private int isAuto;

    private String gridId;
    private Long problemId;


}
