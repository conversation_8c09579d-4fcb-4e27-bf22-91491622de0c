package com.ruoyi.entity.plan.vo;

import com.ruoyi.entity.plan.Plan;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 方案分组视图对象
 * 用于按方案类型对方案进行分组展示
 *
 */
@Data
public class PlanGroupVo {

    /**
     * 分组类型/分类名称
     */
    private String groupType;

    /**
     * 分组类型的中文描述
     */
    private String groupDescription;

    /**
     * 该分组下的方案列表
     */
    private List<Plan> plans;

    /**
     * 该分组下的方案数量
     */
    private Integer planCount;


    public PlanGroupVo(String groupType, String groupDescription) {
        this.groupType = groupType;
        this.groupDescription = groupDescription;
        this.plans = new ArrayList<>();
        this.planCount = 0;
    }

    /**
     * 添加方案到分组
     */
    public void addPlan(Plan plan) {
        if (this.plans == null) {
            this.plans = new ArrayList<>();
        }
        this.plans.add(plan);
        this.planCount = this.plans.size();
    }

    /**
     * 添加多个方案到分组
     */
    public void addPlans(List<Plan> plans) {
        if (this.plans == null) {
            this.plans = new ArrayList<>();
        }
        if (plans != null) {
            this.plans.addAll(plans);
            this.planCount = this.plans.size();
        }
    }

    /**
     * 获取方案数量
     */
    public Integer getPlanCount() {
        return this.plans != null ? this.plans.size() : 0;
    }
}
