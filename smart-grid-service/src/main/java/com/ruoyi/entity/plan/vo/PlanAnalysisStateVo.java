package com.ruoyi.entity.plan.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.trans.core.TransField;
import com.ruoyi.trans.core.TransType;
import lombok.Data;

import java.util.Date;

@Data
public class PlanAnalysisStateVo {
    /**
     * 分析过程状态id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 问题id
     */
    private Long problemId;

    /**
     * 分析过程状态(0分析异常，1是分析结束，2是分析中，3是分析停止)
     */
    @TransField(type = TransType.EXCEL_DICT, resultHolderField = "stateLabel")
    @ExcelDictFormat(readConverterExp = "0=分析异常,1=分析结束,2=分析中,3分析停止")
    private Integer state;
    private String stateLabel;

    /**
     * 分析开始时间
     */
    private Date startTime;

    /**
     * 分析结束时间
     */
    private Date endTime;
}
