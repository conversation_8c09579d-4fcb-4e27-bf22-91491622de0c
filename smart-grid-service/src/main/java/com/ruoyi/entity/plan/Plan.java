package com.ruoyi.entity.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 故障解决方案对象 problem_scheme
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@EqualsAndHashCode
@Data
@TableName("problem_scheme")
public class Plan  {

    private static final long serialVersionUID=1L;

    public Plan(Long id, String operateData, Long problemId) {
        this.id = id;
        this.operateData = operateData;
        this.problemId = problemId;
    }

    public Plan() {
    }

    /**
     * 主键id
     */
    @TableId(value = "id" ,type =  IdType.AUTO)
    private Long id;
    /**
     * 负载率
     */
    private String load;
    /**
     * 经济性
     */
    private String economy;
    /**
     * $column.columnComment
     */
    private String reliability;
    /**
     * 实施难度
     */
    private String exe;
    /**
     * 优点
     */
    private String advantage;
    /**
     * 缺点
     */
    private String disadvantage;
    /**
     * 投资预算
     */
    private BigDecimal budget;
    /**
     * N-1校验
     */
    private String n1;
    /**
     * 数据类型
     */
    private String planType;
    /**
     * 数据实体
     */
    private String operateData;
    /**
     * $column.columnComment
     */
    private String operate;
    /**
     * 解决需求数
     */
    private Long requireMents;

    /**
     * 问题 id
     */
    private Long problemId;

}
