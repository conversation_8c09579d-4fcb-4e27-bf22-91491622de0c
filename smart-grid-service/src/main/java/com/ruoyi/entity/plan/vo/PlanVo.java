package com.ruoyi.entity.plan.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.utils.util.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 故障解决方案视图对象 problem_scheme
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ExcelIgnoreUnannotated
public class PlanVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 负载率
     */
    @ExcelProperty(value = "负载率")
    private String load;

    /**
     * 经济性
     */
    @ExcelProperty(value = "经济性")
    private String economy;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private String reliability;

    /**
     * 实施难度
     */
    @ExcelProperty(value = "实施难度")
    private String exe;

    /**
     * 优点
     */
    @ExcelProperty(value = "优点")
    private String advantage;

    /**
     * 缺点
     */
    @ExcelProperty(value = "缺点")
    private String disadvantage;

    /**
     * 投资预算
     */
    @ExcelProperty(value = "投资预算")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal budget;

    /**
     * N-1校验
     */
    @ExcelProperty(value = "N-1校验")
    private String n1;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型")
    private String planType;

    /**
     * 数据实体
     */
    @ExcelProperty(value = "数据实体")
    private String operateData;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private String operate;

    /**
     * 解决需求数
     */
    @ExcelProperty(value = "解决需求数")
    private Long requireMents;
    private Long problemId;

}
