package com.ruoyi.entity.plan.vo;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BranchMainPathBo {
    public BranchMainPathBo() {
    }

    public BranchMainPathBo(ArrayList<Node> paths, Node startNode, List<BranchNode> otherBranchNodes) {
        this.paths = paths;
        this.startNode = startNode;
        this.otherBranchNodes = otherBranchNodes;
    }

    /**
     * 当前分支路径
     */
    ArrayList<Node> paths;

    // 开始
    Node startNode;

    /**
     * 所有的分支节点集合
     */
    List<List<Node>> branchPaths;

    // 其它分支BranchNode
    List<BranchNode> otherBranchNodes;
}
