package com.ruoyi.entity.plan.enuw;

public enum SupplyAreaEnum {
    A_PLUS("01", "A+"),
    A("02", "A"),
    B("03", "B"),
    C("04", "C"),
    D("05", "C");

    private final String code;
    private final String grade;

    SupplyAreaEnum(String code, String grade) {
        this.code = code;
        this.grade = grade;
    }

    // 根据编码查找枚举
    public static SupplyAreaEnum fromCode(String code) {
        for (SupplyAreaEnum mapping : values()) {
            if (mapping.code.equals(code)) {
                return mapping;
            }
        }
        throw new IllegalArgumentException("未知编码: " + code);
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getGrade() {
        return grade;
    }
}
