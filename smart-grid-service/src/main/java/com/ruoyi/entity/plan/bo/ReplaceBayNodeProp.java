package com.ruoyi.entity.plan.bo;

import lombok.Data;

/**
 * 替换间隔节点的属性对象
 */
@Data
public class ReplaceBayNodeProp {
    public ReplaceBayNodeProp(String sourceKgId, String sourceKgName, String sourceBusId, String sourceBusName, String replaceKgId, String replaceKgName, String replaceBusId, String replaceBusName, String replaceFeederId, String replaceFeederName) {
        this.sourceKgId = sourceKgId;
        this.sourceKgName = sourceKgName;
        this.sourceBusId = sourceBusId;
        this.sourceBusName = sourceBusName;
        this.replaceKgId = replaceKgId;
        this.replaceKgName = replaceKgName;
        this.replaceBusId = replaceBusId;
        this.replaceBusName = replaceBusName;
        this.replaceFeederId = replaceFeederId;
        this.replaceFeederName = replaceFeederName;
    }

    /**
     * 原开关和母线名称
     */
    String sourceKgId, sourceKgName, sourceBusId, sourceBusName;

    String replaceKgId, replaceKgName, replaceBusId, replaceBusName;

    String replaceFeederId;

    String replaceFeederName;

}
