package com.ruoyi.entity.plan.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 故障解决方案业务对象 problem_scheme
 *
 * <AUTHOR>
 * @date 2025-03-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PlanBo extends BaseEntity {


    /**
     * 主键id
     */
    private Long id;

    /**
     * 负载率
     */
    private String load;

    /**
     * 经济性
     */
    private String economy;

    /**
     * $column.columnComment
     */
    private String reliability;

    /**
     * 实施难度
     */
    private String exe;

    /**
     * 优点
     */
    private String advantage;

    /**
     * 缺点
     */
    private String disadvantage;

    /**
     * 投资预算
     */
    private BigDecimal budget;

    private BigDecimal budgetMin;

    private BigDecimal budgetMax;

    /**
     * N-1校验
     */

    private String n1;

    /**
     * 数据类型
     */
    private String planType;

    /**
     * 数据实体
     */

    private String operateData;

    /**
     * $column.columnComment
     */
    private String operate;

    /**
     * 解决需求数
     */
    private Long requireMents;


    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     *第几页
     */
    private Integer pageNum;
    private Long problemId;

}
