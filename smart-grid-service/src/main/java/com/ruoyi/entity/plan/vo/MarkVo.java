package com.ruoyi.entity.plan.vo;

import lombok.Data;

import java.util.List;

@Data
public class MarkVo {
    /**
     * 起始点设备id
     */
    private String startPsrId;

    /**
     * 结束点设备id
     */
    private String endPsrId;

    /**
     * 起始点设备类型
     */
    private String startPsrType;

    /**
     * 结束点设备类型
     */
    private String endPsrType;

    /**
     * 起始点设备名称
     */
    private String startPsrName;

    /**
     * 结束点设备名称
     */
    private String endPsrName;


    /**
     * 设备集合
     */
    private List<PlanDeviceVo> planDeviceVoList;

    /**
     * 路径集合
     */
    private List<PlanDeviceVo> planPathVoList;
}
