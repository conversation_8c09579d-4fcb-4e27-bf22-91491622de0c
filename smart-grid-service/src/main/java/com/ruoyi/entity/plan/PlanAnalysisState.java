package com.ruoyi.entity.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("plan_analysis_state")
public class PlanAnalysisState {
    /**
     * 分析过程状态id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问题id
     */
    private Long problemId;

    /**
     * 分析过程状态常量
     */
    public static final int STATE_ANALYSIS_ERROR = 0;    // 分析异常
    public static final int STATE_ANALYSIS_COMPLETED = 1; // 分析结束
    public static final int STATE_ANALYSIS_IN_PROGRESS = 2; // 分析中
    public static final int STATE_ANALYSIS_STOPPED = 3; // 分析停止

    /**
     * 分析过程状态(0分析异常，1是分析结束，2是分析中，3是分析停止)
     */
    private Integer state;

    /**
     * 分析开始时间
     */
    private Date startTime;

    /**
     * 分析结束时间
     */
    private Date endTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 异常信息
     */
    private String message;
}
