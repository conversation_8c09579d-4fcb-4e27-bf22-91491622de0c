package com.ruoyi.entity.plan.vo;

import lombok.Data;

/**
 * 方案造价
 */
@Data
public class PlanCost {

    /**
     *物理杆塔数量
     */
    private Integer wlgtCount;

    /**
     *运行杆塔数量
     */
    private Integer rungtCount;


    /**
     *环网柜数量
     */
    private Integer hwgCount;

    /**
     *开关站数量
     */
    private Integer kgzCount;

    /**
     *柱上负荷开关
     */
    private Integer zsfhkgCount;

    /**
     *柱上断路器
     */
    private Integer zsdlqCount;

    /**
     *站内负荷开关
     */
    private Integer znfhkgCount;

    /**
     *站内断路器
     */
    private Integer zndlqCount;

    /**
     *站内配电变压器
     */
    private Integer znpdbyqCount;

    /**
     *箱变
     */
    private Integer xbCount;

    /**
     *配电室
     */
    private Integer pdsCount;

    /**
     *柱上变
     */
    private Integer zsbCount;

    /**
     *架空线长度
     */
    private Double jkLength;

    /**
     *电缆线长度
     */
    private Double cableLength;

    /**
     * 总成本
     */
    private Double totalCost;

    /**
     * 采购成本
     */
    private Double purchaseCost;

    /**
     * 建设成本
     */
    private Double buildingCost;

    /**
     * 安装成本
     */
    private Double installCost;

    /**
     * 其他成本
     */
    private Double otherCost;



}

