package com.ruoyi.entity.plan.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 方案分组结果视图对象
 * 包含所有分组的完整结果
 *
 */
@Data
public class PlanGroupResultVo {

    /**
     * 问题ID
     */
    private Long problemId;

    /**
     * 问题分类编码
     */
    private Integer categoryLevel2Code;

    /**
     * 所有分组列表
     */
    private List<PlanGroupVo> groups;

    /**
     * 总方案数量
     */
    private Integer totalPlanCount;

    /**
     * 分组数量
     */
    private Integer groupCount;


    public PlanGroupResultVo(Long problemId, Integer categoryLevel2Code) {
        this.problemId = problemId;
        this.categoryLevel2Code = categoryLevel2Code;
        this.groups = new ArrayList<>();
        this.totalPlanCount = 0;
        this.groupCount = 0;
    }

    /**
     * 添加分组
     */
    public void addGroup(PlanGroupVo group) {
        if (this.groups == null) {
            this.groups = new ArrayList<>();
        }
        this.groups.add(group);
        this.groupCount = this.groups.size();
        this.totalPlanCount += group.getPlanCount();
    }

    /**
     * 获取分组数量
     */
    public Integer getGroupCount() {
        return this.groups != null ? this.groups.size() : 0;
    }

    /**
     * 获取总方案数量
     */
    public Integer getTotalPlanCount() {
        if (this.groups == null) {
            return 0;
        }
        return this.groups.stream()
                .mapToInt(PlanGroupVo::getPlanCount)
                .sum();
    }
}
