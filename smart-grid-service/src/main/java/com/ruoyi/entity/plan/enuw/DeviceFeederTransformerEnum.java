package com.ruoyi.entity.plan.enuw;

public enum DeviceFeederTransformerEnum {
    Public_Transformation("0", "公变"),
    Specific_Transformation("1", "专变");


    private final String code;
    private final String grade;

    DeviceFeederTransformerEnum(String code, String grade) {
        this.code = code;
        this.grade = grade;
    }

    // 根据编码查找枚举
    public static DeviceFeederTransformerEnum fromCode(String code) {
        for (DeviceFeederTransformerEnum mapping : values()) {
            if (mapping.code.equals(code)) {
                return mapping;
            }
        }
        throw new IllegalArgumentException("未知编码: " + code);
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getGrade() {
        return grade;
    }
}
