package com.ruoyi.entity.plan.vo;

import lombok.Data;

import java.util.List;

@Data
public class ProblemFoundationVo {

    /**
     * 线路id
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 线路电压
     */
    private String feederVoltage;

    /**
     * 变电站名称
     */
    private String substationName;

    /**
     * 供电区域
     */
    private String supplyArea;

    /**
     * 线路长度
     */
    private Double length;

    /**
     * 配变数量
     */
    private Integer pbNum;

    /**
     * 装机容量
     */
    private String capacity;

    /**
     * 供电半径
     */
    private Double supplyRadius;


    /**
     * 配变详情
     */
    private List<PBFoundationVo> pbFoundationVoList;

}
