package com.ruoyi.entity.power.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 网格资源统计高压线路基础模块
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class MediumFeederFoundationVo {

    /**
     * 线路条数
     */
    private Integer lineNum;

    /**
     * 线路长度
     */
    private Double lineLength;


    /**
     * 电缆条数
     */
    private Integer cableNum;

    /**
     * 电缆长度
     */
    private Double cableLength;

    /**
     * jk线长度
     */
    private Double jkLength;

    /**
     * jk线条数
     */
    private Integer jkNum;


}
