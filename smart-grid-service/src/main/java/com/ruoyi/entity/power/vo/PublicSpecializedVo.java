package com.ruoyi.entity.power.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.entity.device.DevicePoleTransformer;
import com.ruoyi.entity.device.DeviceStationTransformer;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.device.StationServiceTransformer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.xmlbeans.impl.xb.xsdschema.ListDocument;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PublicSpecializedVo {

    /**
     * 公变容量
     */
    private Double pubRatedCapacity;

    /**
     * 公变台数
     */
    private Integer pubNum;


    /**
     * 专变台数
     */
    private Integer privNum;

    /**
     * 专变容量
     */
    private Double privRatedCapacity;

    //柱上变——公变
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DevicePoleTransformer> pubDevicePoleTransformers;

    //柱上变——专变
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DevicePoleTransformer> prvDevicePoleTransformers;

    //站内变——公变
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DeviceStationTransformer> pubDeviceStationTransformers;

    //站内变——专变
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DeviceStationTransformer> prvDeviceStationTransformers;


}
