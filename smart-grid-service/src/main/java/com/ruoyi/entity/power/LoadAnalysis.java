package com.ruoyi.entity.power;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 负载率数据分析
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class LoadAnalysis {
    /**
     * 设备名称
     */
    private String substationName;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 高峰时间段
     */
    private String timeWindow;

    /**
     * 持续时间
     */
    private Double durationHours;

    /**
     * 频率
     */
    private Integer exceedCount;


    /**
     * 平均数
     */
    private Double averageValue;

   private List<LoadAnalysis> loadAnalysisList;




}
