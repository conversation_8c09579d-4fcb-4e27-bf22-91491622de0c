package com.ruoyi.entity.power.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 网格资源统计高压主变高峰模块
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class TransformerPeakVo {

//    /**
//     * 高峰负荷时段
//     */
//    private String peakTime;
//
//    /**
//     * 低谷负荷时段
//     */
//    private String valleyTime;

    /**
     * 最大负荷
     */
    private Double maxLoadMW;

    /**
     * 最小负荷
     */
    private Double minLoadMW;

    /**
     * 最大负载率
     */
    private Double maxLoad;

    /**
     * 最小负载率
     */
    private Double minLoad;
}
