package com.ruoyi.entity.power.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.entity.device.DeviceZB;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 网格资源统计高压主变基础模块
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class TransformerFoundationVo {

    /**
     * 主变数量
     */
    private Integer totalNum;

    /**
     * 主变总容量
     */
    private Double totalCapacity;

    /**
     * 主变开放总容量
     */
    private Integer openTotalCapacity;

    /**
     * 正常数量
     */
    private Integer normalNum;

    /**
     * 重载数量
     */
    private Integer heavyLoadNum;

    /**
     * 超载数量
     */
    private Integer overLoadNum;

    /**
     * 主变集合
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DeviceZB> zbList;
}
