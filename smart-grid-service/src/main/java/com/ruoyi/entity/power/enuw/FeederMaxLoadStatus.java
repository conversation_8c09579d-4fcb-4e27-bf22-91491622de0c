package com.ruoyi.entity.power.enuw;

public enum FeederMaxLoadStatus {
    UNDERLOAD("轻载", 0, 30, "负载范围在0% - 30%"),
    NORMAL("正常", 30, 70, "负载范围在30% - 70%"),
    OVERLOAD("重载", 70, 90, "负载范围在70% - 90%"),
    CRITICAL("紧急过载", 90, 100, "负载范围在90% - 100%"),
    EMERGENCY("超限", 100, Integer.MAX_VALUE, "负载范围大于100");

    private final String name;
    private final int min;
    private final int max;
    public final String action;

    // 新增 getName() 方法
    public String getName() {
        return this.name;
    }
    FeederMaxLoadStatus(String name, int min, int max, String action) {
        this.name = name;
        this.min = min;
        this.max = max;
        this.action = action;
    }

    public static FeederMaxLoadStatus fromLoadRate(double loadRate) {
        for (FeederMaxLoadStatus status : values()) {
            if (loadRate >= status.min && loadRate < status.max) {
                return status;
            }
        }
        return EMERGENCY;
    }
}
