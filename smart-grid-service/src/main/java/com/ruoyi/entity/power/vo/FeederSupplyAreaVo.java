package com.ruoyi.entity.power.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 网格资源统计高压线路供电区域模块
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class FeederSupplyAreaVo {


    /**
     * 供电区域
     */
    private String supplyArea;

    /**
     * 轻载数量
     */
    private Integer underLoadNum;

    /**
     * 正常数量
     */
    private Integer normalNum;


    /**
     * 重载数量
     */
    private Integer heavyLoadNum;

    /**
     * 超载数量
     */
    private Integer overLoadNum;

    /**
     * 超限
     */
    private Integer seriousOverLoadNum;
}
