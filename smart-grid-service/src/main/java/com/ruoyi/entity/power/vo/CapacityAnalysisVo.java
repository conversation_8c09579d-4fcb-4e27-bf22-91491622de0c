package com.ruoyi.entity.power.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.entity.problem.Statistics;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class CapacityAnalysisVo {
    /**
     * 配变容量分布柱状图
     */
    private List<Statistics> capacityColumnarList;


    /**
     * 配变容量详情
     */
    private Page<TransformerStateVo> capacityList;

    /**
     * 线路容量分布柱状图
     */
    private Page<Statistics> totalFeederCapacityList;

    /**
     * 线路容量详情
     */
    private Page<FeederCapacityVo> feederCapacityVoList;
}
