package com.ruoyi.entity.power.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.utils.util.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PowerGridVo {


    /**
     * 供电区域
     */
    private String supplyArea;


    /**
     * 线路条数
     */
    private Integer lineNum;

    /**
     * 线路长度
     */
    private Double lineLength;


    /**
     * 电缆化率
     */
    private Double cableConversion;

    /**
     * 环网柜数量
     */
    private Integer ringMainUnitNum;

    /**
     * 配电室数量
     */
    private Integer distributionRoomNum;

    /**
     * 柱上开关数量
     */
    private Integer poleSwitches;

    /**
     * 开关站数量
     */
    private Integer switchStations;

    /**
     * 公变容量
     */
    private Double pubRatedCapacity;

    /**
     * 公变台数
     */
    private Integer pubNum;


    /**
     * 专变台数
     */
    private Integer privNum;

    /**
     * 专变容量
     */
    private Double privRatedCapacity;


}
