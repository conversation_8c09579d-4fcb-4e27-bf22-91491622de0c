package com.ruoyi.entity.power.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.entity.device.DeviceFeeder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 网格资源统计高压线路基础模块
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class FeederFoundationVo {

    /**
     * 可开发容量
     */
    private Double openTotalCapacity;

    /**
     * 线路条数
     */
    private Integer lineNum;

    /**
     * 线路长度
     */
    private Double lineLength;


    /**
     * 电缆化率
     */
    private Double cableConversionRate;

    /**
     * 电缆长度
     */
    private Double cableLength;

    /**
     * jk线长度
     */
    private Double jkLength;

    /**
     * 绝缘化率
     */
    private Double isolationConversionRate;

    /**
     * 绝缘线路长度
     */
    private Double isolationLineLength;

    /**
     * 非绝缘长度
     */
    private Double noIsolationLineLength;

    /**
     * 供电区域
     */
    private String supplyArea;

    /**
     * 线路集合
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<DeviceFeeder> deviceFeeders;

    /**
     * 正常数量
     */
    private Integer normalNum;

    /**
     * 重载数量
     */
    private Integer heavyLoadNum;

    /**
     * 超载数量
     */
    private Integer overLoadNum;


}
