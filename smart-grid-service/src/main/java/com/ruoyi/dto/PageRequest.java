package com.ruoyi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest {
    
    /**
     * 页码（从1开始）
     */
    @Builder.Default
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    @Builder.Default
    private Integer pageSize = 20;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向：ASC/DESC
     */
    @Builder.Default
    private String sortOrder = "DESC";
}