package com.ruoyi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分析配置参数
 * 用于自定义分析阈值和参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisConfigDto {
    
    /**
     * 电压上限 (kV)
     */
    @Builder.Default
    private Double voltageUpperLimit = 11.0;
    
    /**
     * 电压下限 (kV)
     */
    @Builder.Default
    private Double voltageLowerLimit = 9.5;
    
    /**
     * 负载率告警阈值
     */
    @Builder.Default
    private Double loadRateWarning = 0.8;
    
    /**
     * 负载率严重阈值
     */
    @Builder.Default
    private Double loadRateCritical = 0.95;
    
    /**
     * 配变告警阈值
     */
    @Builder.Default
    private Double transformerWarning = 0.85;
    
    /**
     * 配变严重阈值
     */
    @Builder.Default
    private Double transformerCritical = 1.0;
    
    /**
     * 短路开断能力 (kA)
     */
    @Builder.Default
    private Double breakingCapacity = 31.5;
}