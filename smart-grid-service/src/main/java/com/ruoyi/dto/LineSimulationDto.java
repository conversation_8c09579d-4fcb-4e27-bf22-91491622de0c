package com.ruoyi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// 线路仿真分析结果
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LineSimulationDto {
    private String psrId;
    private String psrType;
    private Long segmentId;
    private String segmentName;
    private Double pIndValue;
    private Double qIndValue;
    private Double iIndValue;
    private Double vIndValue;
    private Double pJndValue;
    private Double qJndValue;
    private Double iJndValue;
    private Double vJndValue;
    private Double ploss;
    private Double qloss;
    private Double loadRate;
    private String status; // NORMAL, WARNING, CRITICAL
}