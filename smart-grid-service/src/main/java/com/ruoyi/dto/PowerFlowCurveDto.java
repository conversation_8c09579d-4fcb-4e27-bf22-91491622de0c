package com.ruoyi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 潮流曲线数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "潮流曲线数据")
public class PowerFlowCurveDto {

    private Long id;
    private Long retId;
    private Integer idx;
    private Double iValue;
    private Integer iStatus;
    private Double vValue;
    private Integer vStatus;
    private Double pValue;
    private Integer pStatus;
    private Double qValue;
    private Integer qStatus;
    private Double cosValue;
    private Integer cosStatus;
    private Double vaValue;
    private Integer vaStatus;


    // 指标侧（ind）数据
    private Double pIndValue;
    private Integer pIndStatus;
    private Double qIndValue;
    private Integer qIndStatus;
    private Double vIndValue;
    private Integer vIndStatus;
    private Double iIndValue;
    private Integer iIndStatus;
    private Double vaIndValue;
    private Integer vaIndStatus;
    private Double cosIndValue;
    private Integer cosIndStatus;

    // 节点侧（jnd）数据
    private Double pJndValue;
    private Integer pJndStatus;
    private Double qJndValue;
    private Integer qJndStatus;
    private Double vJndValue;
    private Integer vJndStatus;
    private Double iJndValue;
    private Integer iJndStatus;
    private Double vaJndValue;
    private Integer vaJndStatus;
    private Double cosJndValue;
    private Integer cosJndStatus;

    // 损耗数据
    private Double plossValue;
    private Integer plossStatus;
    private Double qlossValue;
    private Integer qlossStatus;

    // 负载率
    private Double loadRateValue;
    private Integer loadRateStatus;

}