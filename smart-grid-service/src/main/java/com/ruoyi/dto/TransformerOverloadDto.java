package com.ruoyi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// 配变超容分析结果
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransformerOverloadDto {
    private Long transformerId;
    private String transformerName;
    private Double loadRate;
    private Double capacity;
    private Double actualLoad;
    private String overloadLevel; // NORMAL, WARNING, CRITICAL
    private Integer severity;
    private String psrId;
    private String psrType;
}