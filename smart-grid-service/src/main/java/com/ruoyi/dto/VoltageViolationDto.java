package com.ruoyi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// 电压越线分析结果
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoltageViolationDto {
    private Long deviceId;
    private String deviceName;
    private String deviceType;
    private Double voltage;
    private Double upperLimit;
    private Double lowerLimit;
    private String violationType; // OVER_UPPER, UNDER_LOWER
    private Double deviation;
    private Integer severity; // 1-低, 2-中, 3-高
    private String date;
    private String psrId;
    private String psrType;

    /**
     计算发生时间
     *
     */
    public VoltageViolationDto getTime(int idx) {
        int hour = idx / 4;
        int minute = (idx % 4) * 15;
        this.date = String.format("%02d:%02d", hour, minute);
        return this;
    }
}