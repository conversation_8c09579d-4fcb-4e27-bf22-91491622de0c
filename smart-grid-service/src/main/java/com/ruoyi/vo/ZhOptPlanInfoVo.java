package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 辅助决策方案详情视图对象 zh_opt_plan_info
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
@Data
@ExcelIgnoreUnannotated
public class ZhOptPlanInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;

    /**
     * 方案id
     */
    @ExcelProperty(value = "方案id")
    private String planId;

    /**
     * 步骤信息
     */
    @ExcelProperty(value = "步骤信息")
    private String stepInfo;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型")
    private String operateType;


}
