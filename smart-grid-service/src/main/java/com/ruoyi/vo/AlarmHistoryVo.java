package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

@Data
public class AlarmHistoryVo {
    /**
     * 网格编号:仿真实例号
     */
    @ExcelProperty("仿真实例号")
    private String simuCaseNo;
    /**
     * 网格名称
     */
    @ExcelProperty("网格名称")
    private String gridName;
    /**
     * 告警时间
     */
    @ExcelProperty("告警时间")
    @ColumnWidth(20)
    private Date alarmTime;
    /**
     * 告警类型
     */
    @ExcelProperty("告警类型")
    private String alarmType;
    /**
     * 告警内容
     */
    @ExcelProperty("告警内容")
    private String alarmContext;
}
