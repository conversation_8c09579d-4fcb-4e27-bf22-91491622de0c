package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 供电网格台账数据视图对象 zh_dms_power_grid
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Accessors(chain = true)
@Data
public class ZhDmsPowerGridVo {
    /**
     * 网格编码
     */
    @ExcelProperty(value = "网格编码" )
    @TableField("psrId")
    private String psrId;

    /**
     * 网格名称
     */
    @ExcelProperty(value = "网格名称" )
    @TableField("gridName")
    private String gridName;

    /**
     * 地市ID
     */
    @ExcelProperty(value = "地市ID" )
    @TableField("city")
    private String city;

    /**
     * 地市名称
     */
    @ExcelProperty(value = "地市名称" )
    @TableField("cityName")
    private String cityName;

    /**
     * 区县ID
     */
    @ExcelProperty(value = "区县ID" )
    @TableField("maintOrg")
    private String maintOrg;

    /**
     * 区县名称
     */
    @ExcelProperty(value = "区县名称" )
    @TableField("maintName")
    private String maintName;

    @ExcelProperty(value = "运维班组" )
    @TableField("coord"
)
    private String coord;

    @ExcelProperty(value = "运维班组" )
    @TableField("zoom"
)
    private String zoom;

    /**
     * 典型日
     */
    @ExcelProperty(value = "典型日" )
    @TableField("typicalDay")
    private String typicalDay;
    /**
     * 负荷最大日
     */
    @ExcelProperty(value = "负荷最大日" )
    @TableField("maxLoadTime")
    private String maxLoadTime;
    /**
     * 电网故障日
     */
    @ExcelProperty(value = "电网故障日" )
    @TableField("faultDay")
    private String faultDay;
    /**
     * 设备退役日
     */
    @ExcelProperty(value = "设备退役日" )
    @TableField("retire")
    private String retire;
    /**
     * 光伏大发
     */
    @ExcelProperty(value = "光伏大发" )
    @TableField("photovoltaicDay")
    private String photovoltaicDay;
    /**
     * 精准切负向
     */
    @ExcelProperty(value = "精准切负向" )
    @TableField("preciseQfx")
    private String preciseQfx;
    /**
     * 网格类型
     */
    @ExcelProperty(value = "网格类型" )
    @TableField("gridType")
    private String gridType;
}
