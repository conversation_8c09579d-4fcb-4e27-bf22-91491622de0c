package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 辅助决策方案视图对象 zh_opt_plan
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
@Data
@ExcelIgnoreUnannotated
public class ZhOptPlanVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private String id;

    /**
     * 实例id
     */
    @ExcelProperty(value = "实例id")
    private String instanceId;

    /**
     * 网格编码
     */
    @ExcelProperty(value = "网格编码")
    private String gridCode;

    /**
     * 决策类型
     */
    @ExcelProperty(value = "决策类型")
    private String policyPlanType;

    /**
     * 内容类型
     */
    @ExcelProperty(value = "内容类型")
    private String contentType;


}
