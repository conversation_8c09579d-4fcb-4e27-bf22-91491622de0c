package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 转供开关信息视图对象 zh_transfer_break_info
 *
 * <AUTHOR> developer
 * @date 2025-03-05
 */
@Data
@ExcelIgnoreUnannotated
public class ZhTransferBreakInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Integer indexNum;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String linkBreakPsrId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String linkBreakPsrName;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String linkBreakPsrType;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String breakPsrId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String breakPsrName;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String breakPsrType;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Double linkFeederRate;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Double sourceFeederRate;


}
