package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * 转供方案视图对象 zh_transfer_info
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
@Data
@ExcelIgnoreUnannotated
public class ZhTransferInfoVo {

    private static final long serialVersionUID = 1L;

    private String sourceFeeder;
    /**
     * 源线路名称
     */
    private String sourceFeederName;

    /**
     * 源线路最高电流
     */
    @ExcelProperty(value = "源线路最高电流")
    private Double sourceFeederCurrent;

    /**
     * 源馈线当前负载
     */
    private double sourceFeederRate;

    private String linkFeeder;

    private String linkFeederName;

    /**
     * 联络馈线原来的负载
     */
    private double linkFeederOriginalRate;

    /**
     * 联络馈线额定电流
     */
    private Double linkFeederCurrent;



    private List<Map<Object, Object>> breakInfos;

}
