package com.ruoyi.vo;

import cn.hutool.core.map.MapUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ZhGridTreeVo {
    private String maintOrg;
    private String maintName;
    private List<Map<String, String>> gridList = new ArrayList<>();

    public void addGrindInfo(String grid, String gridName) {
        this.gridList.add(MapUtil.builder(new HashMap<String, String>()).put("grid", grid).put("gridName", gridName).build());
    }
}
