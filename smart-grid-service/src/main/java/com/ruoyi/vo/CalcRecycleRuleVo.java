package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 计算实例回收规则视图对象 calc_recycle_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Data
@ExcelIgnoreUnannotated
public class CalcRecycleRuleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 实例状态
     */
    @ExcelProperty(value = "实例状态")
    private Long state;

    /**
     * 回收天数
     */
    @ExcelProperty(value = "回收天数")
    private Long recycleDay;

    /**
     * 回收小时数
     */
    @ExcelProperty(value = "回收小时数")
    private Long recycleHour;

    /**
     * 回收分钟数
     */
    @ExcelProperty(value = "回收分钟数")
    private Long recycleMin;

    /**
     * 是否启用，0否1是
     */
    @ExcelProperty(value = "是否启用，0否1是")
    private Long isEnable;


}
