package com.ruoyi.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;


/**
 * 导线端视图对象 zh_conductor_segment
 * <AUTHOR> developer
 * @date 2024-09-04
 */
@Data
public class ZhConductorSegmentVo {
    /**
     * 资源ID
     */
    @ExcelProperty(value = "资源ID")
    @TableField("psrId")
    private String psrId;
    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    @TableField("name")
    private String name;
    /**
     * 运行状态
     */
    @ExcelProperty(value = "运行状态")
    @TableField("psrState")
    private String psrState;
    /**
     * 导线型号
     */
    @ExcelProperty(value = "导线型号")
    @TableField("model")
    private String model;
    /**
     * 导线长度
     */
    @ExcelProperty(value = "导线长度")
    @TableField("length")
    private Double length;
    /**
     * 参考长度
     */
    @ExcelProperty(value = "参考长度")
    @TableField("referenceLength")
    private Double referenceLength;
    /**
     * 导线截面积
     */
    @ExcelProperty(value = "导线截面积")
    @TableField("sectionalArea")
    private Double sectionalArea;
    /**
     * 调整截面积
     */
    @ExcelProperty(value = "调整截面积")
    @TableField("ckSectionalArea")
    private Double ckSectionalArea;
    /**
     * 每公里电阻值
     */
    @ExcelProperty(value = "每公里电阻值")
    @TableField("rohmPerKm")
    private Double rohmPerKm;

    /**
     * 每公里电抗值
     */
    @ExcelProperty(value = "每公里电抗值")
    @TableField("xohmPerKm")
    private Double xohmPerKm;
    /**
     * 每公里电容值
     */
    @ExcelProperty(value = "每公里电容值")
    @TableField("cnfPerKm")
    private Double cnfPerKm;

    /**
     * 电阻值
     */
    @ExcelProperty(value = "电阻值")
    @TableField("rohm")
    private Double rohm;

    /**
     * 电抗值
     */
    @ExcelProperty(value = "电抗值")
    @TableField("xohm")
    private Double xohm;

    /**
     * 电容值
     */
    @ExcelProperty(value = "电容值")
    @TableField("cnf")
    private Double cnf;
    /**
     * 所属馈线
     */
    @ExcelProperty(value = "所属馈线")
    @TableField("feeder")
    private String feeder;
    /**
     * 所属馈线名称
     */
    @ExcelProperty(value = "所属馈线名称")
    @TableField("feederName")
    private String feederName;
    /**
     * 维护班组
     */
    @ExcelProperty(value = "维护班组")
    @TableField("maintGroup")
    private String maintGroup;
    /**
     * 所属主干/分支线
     */
    @ExcelProperty(value = "所属主干")
    @TableField("branchFeeder")
    private String branchFeeder;

    /**
     * 投运日期
     */
    @ExcelProperty(value = "投运日期")
    @TableField("startTime")
    @ColumnWidth(20)
    private Date startTime;
}
