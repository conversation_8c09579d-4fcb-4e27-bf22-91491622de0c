package com.ruoyi.vo;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 母线开关信息
 */
@Data
public class BusbarSwitchVo {

    /**
     * 线路ID
     */
    private String feederId;

    /**
     * 线路名称
     */
    private String feederName;

    /**
     * 母线ID
     */
    private String busbarId;

    /**
     * 母线名称
     */
    private String busbarName;

    /**
     * 是否为备用开关
     */
    private Boolean isSpare;

    /**
     * 开关ID
     */
    private String switchId;

    // TODO 需要加开关类型

    /**
     * 开关名称
     */
    private String switchName;

    /**
     * 开关类型
     */
    private String switchType;

    /**
     * 站房
     */
    private String stationPsrId, stationPsrType, stationPsrName;

    /**
     * 经纬度
     */
    double[] lngLat;



}
