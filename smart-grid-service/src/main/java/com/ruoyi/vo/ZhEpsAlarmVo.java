package com.ruoyi.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;


/**
 * 告警信息视图对象 zh_eps_alarm
 *
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@Data
public class ZhEpsAlarmVo {
    /**
     * 主键
     */
    private Long id;
    private String modelId;
    private String modelType;
    /**
     * 告警设备名称
     */
    private String modelName;
    /**
     * 网格编号:仿真实例号
     */
    private String simuCaseNo;
    /**
     * 网格id
     */
    private String grid;
    /**
     * 网格名称
     */
    private String gridName;
    /**
     * 告警时间
     */
    private Date alarmTime;
    /**
     * 告警内容
     */
    private String alarmContext;
    /**
     * 告警类型
     */
    private Integer alarmType;
    /**
     * 告警数量
     */
    private Integer eventCount;
    /**
     * 线路id
     */
    private String feederId;
    /**
     * 告警等级
     */
    private Integer level;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long versionId;
    private Long retId;
    /**
     * 是否处理
     */
    private Boolean status;
    private int state=0;
}
