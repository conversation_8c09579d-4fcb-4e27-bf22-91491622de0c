package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;


/**
 * 实例告警规则视图对象 calc_alarm_rule
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@Data
@ExcelIgnoreUnannotated
public class CalcAlarmRuleVo {

    private Long id;

    private static final long serialVersionUID = 1L;

    /**
     * 实例id
     */
    @ExcelProperty(value = "实例id")
    @JsonIgnore
    private String instanceId;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 设定值
     */
    @ExcelProperty(value = "设定值")
    private Double limitValue;


}
