package com.ruoyi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Accessors(chain = true)
@Data
public class AlarmStatisticsVo {
    /**
     * 类型统计数据
     */
    private List<Map<String, Object>> typeStatistics;
    /**
     * 优先级统计
     */
    private List<Map<String, Object>> levelStatistics;
    /**
     * 上次计算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCalcTime;
    private Integer lowPeriod;
    private Integer highPeriod;
}
