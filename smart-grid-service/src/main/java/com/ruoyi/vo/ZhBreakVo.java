package com.ruoyi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】视图对象 zh_break
 *
 * <AUTHOR> developer
 * @date 2024-08-28
 */
@Data
public class ZhBreakVo {
    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    @TableField("name")
    private String name;

    /**
     * 电压等级
     */
    @ExcelProperty(value = "电压等级")
    @TableField("voltageLevel")
    private String voltageLevel;

    /**
     * 投运日期
     */
    @ExcelProperty(value = "投运日期")
    @TableField("startTime")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date startTime;

    /**
     * 运行状态
     */
    @ExcelProperty(value = "运行状态")
    @TableField("psrState")
    private String psrState;

    /**
     * 维护班组
     */
    @ExcelProperty(value = "维护班组")
    @TableField("maintGroup")
    private String maintGroup;

    /**
     * 运维单位
     */
    @ExcelProperty(value = "运维单位")
    @TableField("maintOrg")
    private String maintOrg;

    /**
     * 所属地市
     */
    @ExcelProperty(value = "所属地市")
    @TableField("city")
    private String city;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    @TableField("psrId")
    private String psrId;

    /**
     * 资产id
     */
    @ExcelProperty(value = "资产id")
    @TableField("astId")
    private String astId;

    /**
     * 所属电站id
     */
    @ExcelProperty(value = "所属电站id")
    @TableField("substationId")
    private String substationId;

    /**
     * 所属电站名称
     */
    @ExcelProperty(value = "所属电站名称")
    @TableField("substationName")
    private String substationName;

    /**
     * 组合设备类型
     */
    @ExcelProperty(value = "组合设备类型")
    @TableField("combinationType")
    private String combinationType;

    /**
     * 组合设备类型名称
     */
    @ExcelProperty(value = "组合设备类型名称")
    @TableField("combinationTypeName")
    private String combinationTypeName;

    /**
     * 组合设备id
     */
    @ExcelProperty(value = "组合设备id")
    @TableField("combinationId")
    private String combinationId;

    /**
     * 组合设备名称
     */
    @ExcelProperty(value = "组合设备名称")
    @TableField("combinationName")
    private String combinationName;

    /**
     * 最后更新时间
     */
    @ExcelProperty(value = "最后更新时间")
    @TableField("lastUpdateTime")
    @ColumnWidth(20)
    private Date lastUpdateTime;

    /**
     * 组合设备所属的线路
     */
    @ExcelProperty(value = "组合设备所属的线路")
    @TableField("parentFeeder")
    private String parentFeeder;

    /**
     *
     */
    @ExcelProperty(value = "")
    @TableField("feeder")
    private String feeder;

    /**
     * 开关类型
     */
    @ExcelProperty(value = "开关类型")
    @TableField("psrType")
    private String psrType;

    /**
     * 地区特征
     */
    @ExcelProperty(value = "地区特征")
    @TableField("regionalism")
    private String regionalism;

    /**
     * 额定电压
     */
    @ExcelProperty(value = "额定电压")
    @TableField("ratedVoltage")
    private String ratedVoltage;

    /**
     * 额定电流
     */
    @ExcelProperty(value = "额定电流")
    @TableField("ratedCurrent")
    private String ratedCurrent;

    /**
     * 资产性质
     */
    @ExcelProperty(value = "资产性质")
    @TableField("astNature")
    private String astNature;
    /**
     * 开闭状态
     */
    @ExcelProperty(value = "开闭状态")
    @TableField("status")
    private String status;
    /**
     * 所属间隔
     */
    @ExcelProperty(value = "所属间隔")
    @TableField("bay")
    private String bay;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "所属间隔")
    @TableField("source")
    private String source;
}
