package com.ruoyi.mapper.problem;


import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 方案分析过程Mapper接口
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
public interface ProblemSchemeAnalysisMapper extends BaseMapperPlus<ProblemSchemeAnalysisMapper, ProblemSchemeAnalysis, ProblemSchemeAnalysisVo> {

    @Select("SELECT * FROM problem_scheme_analysis WHERE problem_id = #{problemId} ORDER BY  index ASC , add_time ASC")
    List<ProblemSchemeAnalysisVo> selectByProblemId(Long problemId);

    @Select("SELECT * FROM problem_scheme_analysis WHERE problem_id = #{problemId} ORDER BY  index ASC , add_time ASC")
    List<ProblemSchemeAnalysis> queryProblemId(Long problemId);

    @Select("SELECT MAX(index) FROM problem_scheme_analysis WHERE problem_id = #{problemId}")
    Integer getMaxIndex(Long problemId);

    @Delete("DELETE FROM problem_scheme_analysis WHERE problem_id = #{problemId}")
    int deleteByProblemId(Long problemId);
}
