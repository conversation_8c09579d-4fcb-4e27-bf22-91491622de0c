package com.ruoyi.mapper.problem;


import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.sysDict.SysDictData;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 故障Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface ProblemMapper extends BaseMapperPlus<ProblemMapper, Problem, ProblemVo> {

    List<SysDictData> selectDateSource();

    List<SysDictData> selectAttrName();

    List<SysDictData> selectGradeName();

    List<SysDictData> selectProblemStatus();

    List<SysDictData> selectLevel1();

    List<SysDictData> selectLevel2();

    @Select("SELECT psr_id FROM problem_grid ")
    List<String> selectGrid();

    @Select("SELECT wkt FROM problem_grid where psr_id =#{psrId} ")
    String selectGridCoords(String psrId);
}
