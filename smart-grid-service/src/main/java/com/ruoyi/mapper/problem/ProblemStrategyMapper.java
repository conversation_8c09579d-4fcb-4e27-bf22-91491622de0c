package com.ruoyi.mapper.problem;


import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.problem.ProblemStrategy;
import com.ruoyi.entity.problem.vo.ProblemStrategyVo;
import org.apache.ibatis.annotations.Select;

/**
 * 故障问题策略Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface ProblemStrategyMapper extends BaseMapperPlus<ProblemStrategyMapper, ProblemStrategy, ProblemStrategyVo> {

    @Select("SELECT dict_label FROM sys_dict_data where dict_type = 'category_level2' AND dict_value = #{code}")
    String selectDateSource(String code);
}
