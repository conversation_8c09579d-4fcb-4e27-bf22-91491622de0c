package com.ruoyi.mapper.calc;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.ZhConductorSegment;
import com.ruoyi.entity.calc.ZhConductorSegmentCurve;
import com.ruoyi.vo.ZhConductorSegmentVo;
import org.apache.ibatis.annotations.Select;

/**
 * 导线端Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-09-04
 */
public interface ZhConductorSegmentMapper extends BaseMapperPlus<ZhConductorSegmentMapper, ZhConductorSegment, ZhConductorSegmentVo> {

    @Select("select * from zh_conductor_segment_curve where psr_id=#{psrId}")
    ZhConductorSegmentCurve queryCurve(String psrId);

    Integer queryRateValueById(String modelId);

}
