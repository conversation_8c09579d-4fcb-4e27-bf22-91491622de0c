package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.ZhEpsAlarmBo;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.AlarmCurve;
import com.ruoyi.entity.calc.EpsEvent;
import com.ruoyi.entity.calc.ZhEpsAlarm;
import com.ruoyi.vo.AlarmHistoryVo;
import com.ruoyi.vo.ZhEpsAlarmVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 告警信息Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-08-27
 */
public interface ZhEpsAlarmMapper extends BaseMapperPlus<ZhEpsAlarmMapper, ZhEpsAlarm, ZhEpsAlarmVo> {

    List<Map<String, Object>> statistics(ZhEpsAlarmBo bo);

    List<EpsEvent> detailList(Long alarmId);

    Date lastCalcTime();

    @Select("select * from zh_eps_event where alarm_id = #{alarmId}")
    List<EpsEvent> listEvent(Long alarmId);


    Page<ZhEpsAlarmVo> queryAlarmPageList(Page<Object> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<ZhEpsAlarm> lqw, @Param("bo") ZhEpsAlarmBo bo);

    List<String> queryAlarmFeederList(@Param("gridCode") String gridCode);

    List<Map<String, Object>> equipmentList(String grid);

    Page<ZhEpsAlarmVo> queryHistoryPageList(Page<Object> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<ZhEpsAlarm> lqw, @Param("bo") ZhEpsAlarmBo bo);

    Map<String, Integer> periodAlarm(ZhEpsAlarmBo bo);

    Page<ZhEpsAlarmVo> queryLowPeriodList(Page<Object> page, @Param("grid") String grid, @Param("gridName") String gridName,
                                          @Param("status") Boolean status, @Param("lowPeriod") Integer lowPeriod,
                                          @Param("highPeriod") Integer highPeriod);

    Page<ZhEpsAlarmVo> queryHightPeriodList(Page<Object> page, @Param("grid") String grid, @Param("gridName") String gridName,
                                            @Param("status") Boolean status, @Param("highPeriod") Integer highPeriod);

    List<Map<String, Object>> historyStatisticsByLevel(Integer dimensionality);

    List<Map<String, Object>> historyStatisticsByGrid();

    List<Map<String, Object>> historyStatisticsByType();

    List<Map<String, Object>> levelAlarmStatistics(ZhEpsAlarmBo bo);

    List<AlarmHistoryVo> queryHistoryList(@Param("start") LocalDate startTime, @Param("end") LocalDate endTime);

    @Slave
    @InterceptorIgnore
    List<AlarmCurve> queryDmsBreakCurveList(@Param("modelId") String modelId, @Param("retId")  Integer retId);

    @Slave
    @InterceptorIgnore
    List<AlarmCurve> queryEmsBreakCurveList(@Param("modelId") String modelId, @Param("retId") Integer retId);

    @Slave
    @InterceptorIgnore
    List<AlarmCurve> querySegmentCurveList(@Param("modelId") String modelId,@Param("retId")  Integer retId);
}
