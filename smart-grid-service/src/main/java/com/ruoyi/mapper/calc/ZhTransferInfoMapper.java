package com.ruoyi.mapper.calc;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.ZhTransferInfo;
import com.ruoyi.vo.NReduceOneVo;
import com.ruoyi.vo.ZhTransferInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 转供方案Mapper接口
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
@InterceptorIgnore
public interface ZhTransferInfoMapper extends BaseMapperPlus<ZhTransferInfoMapper, ZhTransferInfo, ZhTransferInfoVo> {

    List<NReduceOneVo> statistics(@Param("gridCode") String gridCode, @Param("instanceId") String instanceId);

    String queryMaxCalcId();


    Map<String, Object> statisticsByType(@Param("type") String type);

    Map<String, Object> statisticsByInstanceId(@Param("type") String type, @Param("instanceId") String instanceId);

    Map<String, Object> statisticsByGridCode(@Param("type") String type, @Param("instanceId") String instanceId);

    List<ZhTransferInfo> selectListById(String calcId);
}
