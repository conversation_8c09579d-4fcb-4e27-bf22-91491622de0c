package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.simulation.SimRetPfDmsBreaker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetPfDmsBreakerMapper {
    
    @Select("SELECT DISTINCT ON (A.id) A.*, B.name, B.psrid\n" +
            "FROM dms.sim_ret_pf_dms_breaker A\n" +
            "LEFT JOIN dms.dev_dms_breaker B ON A.id = B.id\n" +
            "WHERE A.ret_id = #{retId}\n" +
            "ORDER BY A.id;")
    List<SimRetPfDmsBreaker> findByRetId(Long retId);
    
    @Select("SELECT * FROM dms.sim_ret_pf_dms_breaker WHERE ret_id = #{retId} AND load_rate_value > #{threshold}")
    List<SimRetPfDmsBreaker> findOverloadedBreakers(@Param("retId") Long retId, @Param("threshold") Double threshold);
}