package com.ruoyi.mapper.calc;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.CalcAlarmInfo;
import com.ruoyi.entity.calc.CalcAlarmInfoBo;
import com.ruoyi.entity.calc.CalcAlarmInfoVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 手动计算实例告警Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@InterceptorIgnore
public interface CalcAlarmInfoMapper extends BaseMapperPlus<CalcAlarmInfoMapper, CalcAlarmInfo, CalcAlarmInfoVo> {

    List<String> queryFeederIdsByInstanceId(String instanceId);

    @DS("slave")
    List<Map<String, Object>> queryContactBreakAndFeederByFeeder(String feederId);

    @DS("slave")
    String queryMainBreakByFeeder(String feederId);

    List<Map<String, Object>> statistics(String instanceId);

    Date lastCalcTime(String instanceId);



    int queryEventCount(Integer ids);


    @Select("select max(alarm_time) from calc_alarm_info")
    Date lastVersionAlarmTime();

    Page<CalcAlarmInfoVo> historyList(CalcAlarmInfoBo bo, PageQuery pageQuery);

    Map<String, Object> getInstanceIdByGridCode(String gridCode);

    List<Map<String, Object>> historyStatistics(Integer dimensionality);

    List<CalcAlarmInfoVo> historyExportList(@Param("startTime") Date startTime,@Param("endTime")  Date endTime);

    Page<CalcAlarmInfoVo> selectAlarmInfoPage(Page<Object> build, @Param(Constants.WRAPPER) LambdaQueryWrapper<CalcAlarmInfo> lqw);

    Page<CalcAlarmInfoVo> selectAlarmInfoWithGridPage(Page<Object> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<CalcAlarmInfo> lqw, @Param("gridName") String gridName);
}
