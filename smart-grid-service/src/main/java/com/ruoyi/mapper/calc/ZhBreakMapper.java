package com.ruoyi.mapper.calc;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.ZhBreak;
import com.ruoyi.vo.ZhBreakVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-08-28
 */
public interface ZhBreakMapper extends BaseMapperPlus<ZhBreakMapper, ZhBreak, ZhBreakVo> {

    List<Map<String, Object>> selectAstInfo(Set<String> strings);

    List<Map<String, Object>> queryFeederCurve(String psrId);

    Integer queryRateValueById(String modelId);
}
