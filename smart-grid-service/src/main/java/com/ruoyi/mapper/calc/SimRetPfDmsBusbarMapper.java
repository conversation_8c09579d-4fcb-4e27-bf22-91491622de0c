package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.simulation.SimRetPfDmsBusbar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetPfDmsBusbarMapper {
    
    @Select("SELECT * FROM dms.sim_ret_pf_dms_busbar WHERE ret_id = #{retId}")
    List<SimRetPfDmsBusbar> findByRetId(Long retId);
}
