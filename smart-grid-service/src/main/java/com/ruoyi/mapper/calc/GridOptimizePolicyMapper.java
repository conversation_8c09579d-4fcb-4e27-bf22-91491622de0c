package com.ruoyi.mapper.calc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.GridOptimizePolicy;
import com.ruoyi.vo.GridOptimizePolicyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网格优化策略Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-12-16
 */
public interface GridOptimizePolicyMapper extends BaseMapperPlus<GridOptimizePolicyMapper, GridOptimizePolicy, GridOptimizePolicyVo> {

    List<GridOptimizePolicyVo> listVo(@Param(Constants.WRAPPER) LambdaQueryWrapper<GridOptimizePolicy> lqw);
}
