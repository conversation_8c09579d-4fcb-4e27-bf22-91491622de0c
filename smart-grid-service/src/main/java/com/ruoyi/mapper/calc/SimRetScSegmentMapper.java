package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.calc.SimRetScSegment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetScSegmentMapper {
    
    @Select("SELECT * FROM dms.sim_ret_sc_segment WHERE ret_id = #{retId}")
    List<SimRetScSegment> findByRetId(Integer retId);
}
