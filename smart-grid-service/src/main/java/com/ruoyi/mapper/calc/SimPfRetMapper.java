package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.calc.SimPfRet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimPfRetMapper {
    
    @Select("SELECT * FROM dms.sim_pf_ret WHERE id = #{id}")
    SimPfRet findById(Integer id);

    @Select("SELECT * FROM dms.sim_pf_ret WHERE msg_id = #{msgId}")
    SimPfRet findByMsgId(String msgId);

    @Select("SELECT * FROM dms.sim_pf_ret WHERE feeder_id = #{feederId} ORDER BY create_dt DESC LIMIT #{limit}")
    List<SimPfRet> findByFeederId(@Param("feederId") Long feederId, @Param("limit") Integer limit);
    
    @Select("SELECT * FROM dms.sim_pf_ret WHERE ret_code = 0 ORDER BY create_dt DESC LIMIT #{limit}")
    List<SimPfRet> findSuccessfulResults(@Param("limit") Integer limit);
}