package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.simulation.SimRetPfDmsSegment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetPfDmsSegmentMapper {
    
    @Select("SELECT * FROM dms.sim_ret_pf_dms_segment WHERE ret_id = #{retId}")
    List<SimRetPfDmsSegment> findByRetId(Long retId);
    
    @Select("SELECT * FROM dms.sim_ret_pf_dms_segment WHERE ret_id = #{retId} AND load_rate_value > #{threshold}")
    List<SimRetPfDmsSegment> findOverloadedSegments(@Param("retId") Long retId, @Param("threshold") Double threshold);

    @Select("SELECT A.*,B.name,B.psrid\n" +
            "        FROM sim_ret_pf_dms_segment A\n" +
            "        LEFT JOIN dev_dms_segment B on A.id=B.id\n" +
            "        WHERE ret_id = #{retId} and load_rate_value>#{loadRateWarning}")
    List<SimRetPfDmsSegment> findByRetIdAndRateValue(@Param("retId")Long retId,@Param("loadRateWarning") double loadRateWarning);
}