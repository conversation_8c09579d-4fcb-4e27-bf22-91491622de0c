package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.calc.SimRetPfNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetPfNodeMapper {
    
    @Select("SELECT * FROM dms.sim_ret_pf_node WHERE ret_id = #{retId}")
    List<SimRetPfNode> findByRetId(Long retId);
    
    @Select("SELECT * FROM dms.sim_ret_pf_node WHERE ret_id = #{retId} AND (v_value < #{lowerLimit} OR v_value > #{upperLimit})")
    List<SimRetPfNode> findVoltageViolations(@Param("retId") Long retId,
                                            @Param("lowerLimit") Double lowerLimit, 
                                            @Param("upperLimit") Double upperLimit);
}