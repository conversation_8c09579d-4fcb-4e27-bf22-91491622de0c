package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
@Slave
public interface SimRetPfDmsDtMapper {
    
//    @Select("SELECT * FROM dms.sim_ret_pf_dms_dt WHERE ret_id = #{retId}")
//    List<SimRetPfDmsDt> findByRetId(Integer retId);
    
    @Select("SELECT dt.*, dev.name as device_name,dev.psrid FROM dms.sim_ret_pf_dms_dt dt\n" +
            "            LEFT JOIN dms.dev_dms_tr dev ON dt.id = dev.id\n" +
            "            WHERE dt.ret_id = #{retId}")
    List<Map<String, Object>> findTransformersWithNames(Long retId);
}