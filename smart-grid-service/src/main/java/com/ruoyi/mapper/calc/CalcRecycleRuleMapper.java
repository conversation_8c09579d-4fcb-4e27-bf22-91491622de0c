package com.ruoyi.mapper.calc;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.CalcRecycleRule;
import com.ruoyi.vo.CalcRecycleRuleVo;

/**
 * 计算实例回收规则Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@InterceptorIgnore
public interface CalcRecycleRuleMapper extends BaseMapperPlus<CalcRecycleRuleMapper, CalcRecycleRule, CalcRecycleRuleVo> {

}
