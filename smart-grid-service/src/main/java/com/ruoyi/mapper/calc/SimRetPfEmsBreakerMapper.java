package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.simulation.SimRetPfEmsBreaker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetPfEmsBreakerMapper {
    
    @Select("SELECT * FROM dms.sim_ret_pf_ems_breaker WHERE ret_id = #{retId}")
    List<SimRetPfEmsBreaker> findByRetId(Long retId);
}