package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.entity.calc.SimRetScNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@Slave
public interface SimRetScNodeMapper {
    
    @Select("SELECT * FROM dms.sim_ret_sc_node WHERE ret_id = #{retId}")
    List<SimRetScNode> findByRetId(Long retId);
    
    @Select("SELECT * FROM dms.sim_ret_sc_node WHERE ret_id = #{retId} AND i_peak_ka > #{threshold}")
    List<SimRetScNode> findShortCircuitViolations(@Param("retId") Long retId, @Param("threshold") Double threshold);
}
