package com.ruoyi.mapper.calc;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.calc.VecRunDomain;
import com.ruoyi.entity.calc.ZhTelemetryValue;
import com.ruoyi.vo.ZhTelemetryValueVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专变信息Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-10-29
 */
public interface PmsZhTelemetryValueMapper extends BaseMapperPlus<PmsZhTelemetryValueMapper, ZhTelemetryValue, ZhTelemetryValueVo> {

    @Insert("insert into pb_curve_template(psr_id,psr_type,meas_type_code,value) values (#{psrId},#{psrType},#{measTypeCode},#{value})")
    void insertCurveTemplate(@Param("psrId") String psrId, @Param("psrType") String psrType, @Param("measTypeCode") String measTypeCode, @Param("value") String value);

    @InterceptorIgnore
    List<VecRunDomain> selectPbValueByPsrIds(@Param("feederIds") List<String> feederId);

    @InterceptorIgnore
    List<VecRunDomain> selectLdValueByPsrIds(@Param("feederIds") List<String> feederId);

}
