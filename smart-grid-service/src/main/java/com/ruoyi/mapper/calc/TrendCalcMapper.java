package com.ruoyi.mapper.calc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ruoyi.entity.calc.*;
import com.ruoyi.vo.MainPathVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR> developer
 * @date 2024-09-20
 */
@Mapper
@InterceptorIgnore
public interface TrendCalcMapper {
    @DS("slave")
    @Select("select * from sim_pf_ret where msg_id = #{msgId}")
    Map<String, Object> queryRetInfoByMsgId(@Param("msgId") String msgId);


    @DS("slave")
    List<AlarmDoMain> queryBreakByRetId(int retId);

    List<ZhDmsGridFeeder> queryFeederByGridCode(String gridCode);

    @DS("slave")
    List<SimPfRet> querySimPfRetByMsgIds(@Param("msgIds") List<String> msgIds);

    @DS("slave")
    List<SimRetPfDmsSegment> querySegmentListByRetId(Long retId);

    Map<String, Object> querySegmentAstInfoByPsrId(String psrId);

    @DS("slave")
    List<SimRetPfDmsBreak> queryBreakListByRetId(Long id);

    @DS("slave")
    Map<String, Object> queryBreakListById(Long id);

    Map<String, Object> queryBreakAstInfoByPsrId(String psrId);

    @DS("slave")
    List<SimRetPfEmsBreak> queryEmsBreakList(Long id);


    @Select("select  distinct  feeder_id,ret_id   from calc_alarm_info where instance_id = #{instanceId}")
    List<Map<String, Object>> queryQuestionFeeders(String instanceId);

    @DS("slave")
    List<Map<String, Object>> queryToFeeder(String questionFeeder);

    @DS("slave")
    List<Long> queryTrByFeeder(String questionFeeders);

    @DS("slave")
    List<Long> queryLdByFeeder(String questionFeeder);

    @DS("slave")
    Map<String, Double> queryMaxPValueAndQValueByTr(@Param("trId") Long trId, @Param("retId") Long retId);

    @DS("slave")
    Map<String, Double> queryMaxPValueAndQValueByLd(@Param("trId") Long trId, @Param("retId") Long retId);

    @Select("select max_allowable_current  from device_feeder mdfd where psr_id = #{toFeederPsrId}")
    double queryMaxmaxCurrent(String toFeederPsrId);


    @DS("slave")
    @Select("select id,msg_id,pf_ret,sc_ret,start_dt,create_dt,end_dt from sim_pf_ret where end_dt is not null and id=#{id}")
    Map<String, Object> queryOneByRetId(Long id);

    @Select("select psr_id from device_feeder where  grid_code =#{gridCode} ")
    List<Map<String, Object>> queryFeederByGrid(String gridCode);

    List<Map<String, Object>> queryCurveListByPsrId(@Param("psrIds")List<String> psrIds);

    @Select("select distinct grid_code from device_feeder  group by grid_code")
    List<String> queryGrid();

    @Select("select DISTINCT  power_grid_name  from zh_dms_grid_feeder zdgf where power_grid_code =#{grid}")
    String queryGridNameByGird(String grid);

    @Select("select distinct power_grid_code from zh_dms_grid_feeder where feeder=#{feederId}")
    Map<String, Object> queryGridCodeByFeederId(String feederId);

    @Select("select max(id) max from calc_alarm_info")
    Long selectMaxEventId();

    @Select("select *  from calc_alarm_info cai where psr_id =#{psrId} and alarm_type =#{alarmType} ")
    CalcAlarmInfo queryAccumulative(@Param("psrId") String psrId,@Param("alarmType")  Long alarmType);

    @Slave
    List<MainPathVO> queryMainPath(@Param("feederId") String feederId, @Param("breakId") String breakId);
}
