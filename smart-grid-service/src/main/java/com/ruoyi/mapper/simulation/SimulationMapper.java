package com.ruoyi.mapper.simulation;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.entity.simulation.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS("slave")
public interface SimulationMapper {
    /**
     * 根据仿真主表id查询配网开关时序潮流计算结果
     */
    List<SimRetPfDmsBreaker> selectSimRetPfDmsBreaker(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询配网开关时序潮流计算结果
     */
    List<SimRetPfEmsBreaker> selectSimRetPfEmsBreaker(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询——配网刀闸时序潮流计算结果
     */
    List<SimRetPfDmsDisconnector> selectSimRetPfDmsDisconnector(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询——馈线段潮流计算结果
     */
    List<SimRetPfDmsSegment> selectSimRetPfSegment(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询——母线潮流计算结果
     */
    List<SimRetPfDmsBusbar> selectSimRetPfDmsBusbar(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询——配变潮流计算结果
     */
    List<SimRetPfDmsDt> selectSimRetPfDmsDt(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    List<SimRetPfDmsLoad> selectSimRetPfDmsLoad(@Param("retId") Long retId, @Param("id") Long id);

    /**
     * 根据msgId查询出 retId
     * @param msgId
     * @return
     */
    Long selectRetId(String msgId);
}