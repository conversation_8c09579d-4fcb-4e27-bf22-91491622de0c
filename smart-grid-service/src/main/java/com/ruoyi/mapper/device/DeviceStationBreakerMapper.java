package com.ruoyi.mapper.device;

import com.ruoyi.entity.device.DeviceStationBreaker;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 变电站内断路器(0305)Mapper接口
 *
 */
public interface DeviceStationBreakerMapper extends BaseMapperPlus<DeviceStationBreakerMapper, DeviceStationBreaker, DeviceStationBreaker> {

    @Select("SELECT * FROM device_station_breaker WHERE feeder = #{feederId}")
    List<DeviceStationBreaker> selectByFeederId(String feederId);


    @Select("SELECT * FROM device_station_breaker WHERE psr_id = #{psrId}")
    DeviceStationBreaker selectByPsrId(String psrId);

    List<DeviceStationBreaker> selectByStationId(String stationId);
}
