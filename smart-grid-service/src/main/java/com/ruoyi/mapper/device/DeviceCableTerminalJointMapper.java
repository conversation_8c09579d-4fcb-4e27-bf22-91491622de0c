package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceCableTerminalJoint;
import com.ruoyi.entity.device.DeviceFeederJk;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 配电电缆终端接头（0202）Mapper接口
 *
 */
public interface DeviceCableTerminalJointMapper extends BaseMapperPlus<DeviceCableTerminalJointMapper, DeviceCableTerminalJoint, DeviceCableTerminalJoint> {

    @Select("SELECT * FROM device_cable_terminal_joint WHERE feeder = #{feederId}")
    List<DeviceCableTerminalJoint> selectByFeederId(String feederId);

    @Update({
            "<script>",
            "UPDATE device_cable_terminal_joint",
            "SET geo_position = CASE psr_id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.psrId} THEN #{item.geoPosition}",
            "  </foreach>",
            "  ELSE geo_position",
            "END,",
            "state = CASE psr_id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.psrId} THEN #{item.state}",
            "  </foreach>",
            "  ELSE state",
            "END",
            "WHERE psr_id IN",
            "  <foreach collection='list' item='item' open='(' separator=',' close=')'>",
            "    #{item.psrId}",
            "  </foreach>",
            "</script>"
    })
    void batchUpdateGeoAndState(List<DeviceCableTerminalJoint> updateList);
}
