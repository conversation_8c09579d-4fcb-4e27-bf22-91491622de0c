package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.NtFeeder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 线路一些最大值的
 *
 */
public interface NtFeederMapper extends BaseMapperPlus<NtFeederMapper, NtFeeder, NtFeeder> {

    List<NtFeeder> selectByFeederIds(@Param("feederIds") List<String> feederIds);

    @Select("select yc_id from nt_feeder where psr_id = #{feederId} limit 1")
    String selectByFeederId(String feederId);

}
