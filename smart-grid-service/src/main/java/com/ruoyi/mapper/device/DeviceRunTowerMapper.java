package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceRunTower;
import com.ruoyi.entity.device.vo.RunTowerPosition;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 变电站内断路器(0305)Mapper接口
 *
 */
public interface DeviceRunTowerMapper extends BaseMapperPlus<DeviceRunTowerMapper, DeviceRunTower, DeviceRunTower> {

    @Select("SELECT * FROM device_run_tower WHERE feeder = #{feederId}")
    List<DeviceRunTower> selectByFeederId(String feederId);

    List<RunTowerPosition> selectPosition(@Param("psrIds") List<String> psrIds);

}
