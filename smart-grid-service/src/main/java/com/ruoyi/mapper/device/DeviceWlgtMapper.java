package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceCableTerminalJoint;
import com.ruoyi.entity.device.DeviceWlgt;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 物联杆塔设备信息Mapper接口
 *
 */
public interface DeviceWlgtMapper extends BaseMapperPlus<DeviceWlgtMapper, DeviceWlgt, DeviceWlgt> {
    @Update({
            "<script>",
            "UPDATE device_wlgt",
            "SET geo_positon = CASE ast_id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.astId} THEN #{item.geoPositon}",
            "  </foreach>",
            "  ELSE geo_positon",
            "END,",
            "state = CASE ast_id",
            "  <foreach collection='list' item='item'>",
            "    WHEN #{item.astId} THEN #{item.state}",
            "  </foreach>",
            "  ELSE state",
            "END",
            "WHERE ast_id IN",
            "  <foreach collection='list' item='item' open='(' separator=',' close=')'>",
            "    #{item.astId}",
            "  </foreach>",
            "</script>"
    })
    void batchUpdateGeoAndState(List<DeviceWlgt> updateList);

    /**
     * 计算物理杆塔总数
     * @return
     */
    @Select("SELECT COUNT(*) FROM device_wlgt WHERE geo_positon IS NOT NULL AND  state IS NUll AND  deploy_state != '20'")
    int getWlgtCount();

    /**
     * 分页查询范围内的 DeviceWlgt 设备
     * @param offset 偏移量
     * @param limit  每页数量
     * @return 符合条件的设备列表
     */
    @Select("SELECT ast_id, geo_positon " +
            "FROM device_wlgt " +
            "WHERE geo_positon IS NOT NULL " +
            "  AND state IS NULL " +
            "  AND deploy_state != '30' " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<DeviceWlgt> queryRangeWlgt(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 计算终端总数
     * @return
     */
    @Select("SELECT COUNT(*) FROM device_cable_terminal_joint WHERE geo_position IS NOT NULL AND  state IS NUll ")
    int getCableTerminalJointCount();

    /**
     * 分页查询范围内的 DeviceCableTerminalJointCount 设备
     * @param offset 偏移量
     * @param limit  每页数量
     * @return 符合条件的设备列表
     */
    @Select("SELECT psr_id, geo_position " +
            "FROM device_cable_terminal_joint " +
            "WHERE geo_position IS NOT NULL " +
            "  AND state IS NULL " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<DeviceCableTerminalJoint> queryRangeTerminalJoint(@Param("offset") int offset, @Param("limit") int limit);
}
