package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceFeederJk;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配电架空线（dxd）Mapper接口
 *
 */
public interface DeviceFeederJkMapper extends BaseMapperPlus<DeviceFeederJkMapper, DeviceFeederJk, DeviceFeederJk> {

    @Select("SELECT * FROM device_feeder_jk WHERE feeder = #{feederId}")
    List<DeviceFeederJk> selectByFeederId( String psrId);
    @Select("<script>" +
            "SELECT * FROM device_feeder_jk " +

            "<if test='psrId != null and psrId.size() > 0'>" +
            " WHERE feeder IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<DeviceFeederJk> selectJKList( @Param("psrId") List<String> psrId);

    @Select("SELECT * FROM device_feeder WHERE grid_code = #{psrId}")
    List<DeviceFeeder> selectDeviceFeeder(String psrId);
}
