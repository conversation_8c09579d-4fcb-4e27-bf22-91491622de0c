package com.ruoyi.mapper.device;

import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.StationPb;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PbMapper {

    /**
     * 查询中压用户接入点下的配变
     */
    List<StationPb> selectMiddleUserPb(@Param("feeder") String feeder, @Param("idList") List<String> idList);

    /**
     * 线路下的配变总数
     */
    Integer selectFeederPbCount(@Param("feederId") String feederId);

    /**
     * 线路下的配变总数
     */
    List<PBEntity> selectTransformersById(@Param("psrIdList") List<String> psrIdList);
}
