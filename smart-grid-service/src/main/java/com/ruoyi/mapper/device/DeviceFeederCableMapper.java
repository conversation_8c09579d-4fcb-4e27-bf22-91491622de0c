package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceFeederCable;
import com.ruoyi.entity.device.DeviceFeederJk;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配电电缆线段（0201）Mapper接口
 *
 */
public interface DeviceFeederCableMapper extends BaseMapperPlus<DeviceFeederCableMapper, DeviceFeederCable, DeviceFeederCable> {

    @Select("SELECT * FROM device_feeder_cable WHERE feeder = #{feederId}")
    List<DeviceFeederCable> selectByFeederId(String feederId);

    @Select("<script>" +
            "SELECT * FROM device_feeder_cable " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            " WHERE feeder IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<DeviceFeederCable> selectCableList(@Param("psrId") List<String> feederId);
}
