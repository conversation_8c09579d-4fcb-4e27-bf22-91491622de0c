package com.ruoyi.mapper.device;

import com.ruoyi.entity.device.StationKg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KgMapper {

    /**
     * 查询站房下的的开关
     */
    List<StationKg> selectStationKg(@Param("feeder") String feeder, @Param("idList") List<String> idList);


    List<StationKg> selectStationKgByStationId(@Param("stationId") String stationId);

    /**
     * 查询的开关集合 根据设备ID集合
     */
    List<StationKg> selectStationKgByPsrIds(@Param("psrIdList") List<String> psrIdList);


    /**
     * 查询站内开关集合 根据设备ID集合
     */
    List<StationKg> selectFeederKg(@Param("idList") List<String> psrIdList);

    /**
     * 查询站外开关集合 根据设备ID集合
     */
    List<StationKg> selectOutsideFeederKg(@Param("idList") List<String> psrIdList);
}
