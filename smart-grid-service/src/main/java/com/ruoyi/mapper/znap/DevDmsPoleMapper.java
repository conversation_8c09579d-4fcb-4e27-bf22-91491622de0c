package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsPole;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网杆塔Mapper接口
 *
 */
@DS("slave")
public interface DevDmsPoleMapper extends BaseMapperPlus<DevDmsPoleMapper, DevDmsPole, DevDmsPole> {
    @Select("SELECT * FROM dev_dms_pole WHERE feeder_id = #{id}")
    List<DevDmsPole> selectByFeederId(Long id);
}
