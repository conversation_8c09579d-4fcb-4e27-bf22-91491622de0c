package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsLd;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网负荷Mapper接口
 *
 */
@DS("slave")
public interface DevDmsLdMapper extends BaseMapperPlus<DevDmsLdMapper, DevDmsLd, DevDmsLd> {
    @Select("SELECT * FROM dev_dms_ld WHERE feeder_id = #{id}")
    List<DevDmsLd> selectByFeederId(Long id);
}
