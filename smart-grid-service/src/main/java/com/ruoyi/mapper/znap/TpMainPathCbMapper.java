package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.TpMainPathCb;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 主干路径开关Mapper接口
 *
 */
@DS("slave")
public interface TpMainPathCbMapper extends BaseMapperPlus<TpMainPathCbMapper, TpMainPathCb, TpMainPathCb> {
    @Select("SELECT * FROM tp_main_path_cb WHERE feeder_id = #{id}")
    List<TpMainPathCb> selectByFeederId(Long id);
}
