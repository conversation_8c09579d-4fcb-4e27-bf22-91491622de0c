package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsBusbar;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网母线Mapper接口
 */
@DS("slave")
public interface DevDmsBusbarMapper extends BaseMapperPlus<DevDmsBusbarMapper, DevDmsBusbar, DevDmsBusbar> {
    @Select("SELECT * FROM dev_dms_busbar WHERE feeder_id = #{id}")
    List<DevDmsBusbar> selectByFeederId(Long id);
}
