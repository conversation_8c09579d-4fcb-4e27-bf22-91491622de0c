package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.entity.znap.ConDmsCabinet;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网环网柜Mapper接口
 *
 */
@DS("slave")
public interface ConDmsCabinetMapper extends BaseMapperPlus<ConDmsCabinetMapper, ConDmsCabinet, ConDmsCabinet> {
    @Select("SELECT * FROM con_dms_cabinet WHERE feeder_id = #{id}")
    List<ConDmsCabinet> selectByFeederId(Long id);
}
