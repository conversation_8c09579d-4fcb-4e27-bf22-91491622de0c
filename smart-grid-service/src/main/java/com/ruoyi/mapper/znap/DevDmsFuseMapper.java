package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsBreaker;
import com.ruoyi.entity.znap.DevDmsFuse;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网熔断器Mapper接口
 */
@DS("slave")
public interface DevDmsFuseMapper extends BaseMapperPlus<DevDmsFuseMapper, DevDmsFuse, DevDmsFuse> {

    @Select("SELECT * FROM dev_dms_fuse WHERE feeder_id = #{id}")
    List<DevDmsFuse> selectByFeederId(Long id);
}
