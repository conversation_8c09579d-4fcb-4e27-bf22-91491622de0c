package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsSegment;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网馈线段Mapper接口
 *
 */
@DS("slave")
public interface DevDmsSegmentMapper extends BaseMapperPlus<DevDmsSegmentMapper, DevDmsSegment, DevDmsSegment> {
    @Select("SELECT * FROM dev_dms_segment WHERE feeder_id = #{id}")
    List<DevDmsSegment> selectByFeederId(Long id);
}
