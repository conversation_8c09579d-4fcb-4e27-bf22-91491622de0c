package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.TpLinkCb;
import com.ruoyi.entity.znap.vo.LinkCbFeederVo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 联络开关Mapper接口
 */
@DS("slave")
public interface TpLinkCbMapper extends BaseMapperPlus<TpLinkCbMapper, TpLinkCb, TpLinkCb> {
    @Select("SELECT * FROM tp_link_cb WHERE  feeder_id1 = #{id} OR  feeder_id0 = #{id} ")
    List<TpLinkCb> selectByFeederId(Long id);


    /**
     * 根据线路ID查询关联的开关 和 对应的线路
     */
    List<LinkCbFeederVo> selectLinkCbFeeder(String feederId);
}
