package com.ruoyi.mapper.znap;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.graph.TopoToNodes;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

public interface TopoToNodesMapper extends BaseMapperPlus<TopoToNodesMapper, TopoToNodes, TopoToNodes> {


    /**
     * 查询指定线路ID
     *
     * @param feederId 线路ID
     * @return 符合条件的数据列表
     */
    @Select("SELECT * FROM topo_to_nodes WHERE feeder_id = #{feederId} ")
    TopoToNodes findByFeederIdAndTimeRange(@Param("feederId") String feederId);


    /**
     * 插入拓扑节点数据
     *
     * @param feederId  线路ID
     * @param nodesJson JSON格式的节点数据
     * @param addTime   添加时间
     * @return 插入成功的记录数
     */
    @Insert({
            "INSERT INTO topo_to_nodes (feeder_id, nodes_json, add_time) ",
            "VALUES (#{feederId}, #{nodesJson}::jsonb, #{addTime})"
    })
    int insertTopoNode(@Param("feederId") String feederId, @Param("nodesJson") String nodesJson, @Param("addTime") Date addTime);


    /**
     * 根据线路ID删除
     */
    @Delete("DELETE FROM topo_to_nodes WHERE feeder_id = #{feederId}")
    void deleteByFeederId(String feederId);
}
