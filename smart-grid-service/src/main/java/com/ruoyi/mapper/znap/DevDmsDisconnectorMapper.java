package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsDisconnector;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网刀闸Mapper接口
 */
@DS("slave")
public interface DevDmsDisconnectorMapper extends BaseMapperPlus<DevDmsDisconnectorMapper, DevDmsDisconnector, DevDmsDisconnector> {

    @Select("SELECT * FROM dev_dms_disconnector WHERE  feeder_id = #{id}")
    List<DevDmsDisconnector> selectByFeederId(Long id);

    /**
     * 根据jnd或者ind查询
     */
    @Select("SELECT * FROM dev_dms_disconnector WHERE  jnd=#{id} OR ind = #{id}")
    DevDmsDisconnector selectByIJnd(Long id);
}
