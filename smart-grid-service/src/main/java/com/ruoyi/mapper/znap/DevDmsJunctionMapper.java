package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsJunction;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR> developer
 * @date 2025-06-18
 */
@DS("slave")
public interface DevDmsJunctionMapper extends BaseMapperPlus<DevDmsJunctionMapper, DevDmsJunction, DevDmsJunction> {

    @Select("SELECT * FROM dev_dms_junction WHERE feeder_id = #{id}")
    List<DevDmsJunction> selectByFeederId(Long id);
}
