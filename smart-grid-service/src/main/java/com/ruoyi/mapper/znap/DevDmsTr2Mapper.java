package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsTr;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网变压器Mapper接口
 *
 */
@DS("slave")
public interface DevDmsTr2Mapper extends BaseMapperPlus<DevDmsTr2Mapper, DevDmsTr, DevDmsTr> {

    @Select("SELECT * FROM dev_dms_tr WHERE feeder_id = #{id}")
    List<DevDmsTr> selectByFeederId(Long id);
}
