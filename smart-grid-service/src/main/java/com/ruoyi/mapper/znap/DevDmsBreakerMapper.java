package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.DevDmsBreaker;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 配网开关Mapper接口
 */
@DS("slave")
public interface DevDmsBreakerMapper extends BaseMapperPlus<DevDmsBreakerMapper, DevDmsBreaker, DevDmsBreaker> {

    @Select("SELECT * FROM dev_dms_breaker WHERE feeder_id = #{id}")
    List<DevDmsBreaker> selectByFeederId(Long id);

    /**
     * 根据jnd或者ind查询
     */
    @Select("SELECT * FROM dev_dms_breaker WHERE jnd=#{id} OR ind = #{id}")
    DevDmsBreaker selectByIJnd(Long id);
}
