package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.entity.znap.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 拓扑查询Mapper接口
 */
public interface BayQueryMapper {

    /**
     * 根据psrId查询ems_ld_link中的线路信息
     */
    EmsLdLink selectLineByPsrId(@Param("psrId") String psrId);

    /**
     * 根据psrId查询ems_ld_link中的线路信息
     */
    EmsLdLink selectLineById(@Param("id") Long id);

    /**
     * 根据线路ID查询线路拓扑信息
     */
    TopoLoadNj selectLineTopoInfo(@Param("lineId") String lineId);

    /**
     * 根据线路ID查询拓扑关系
     */
    TopoTraceReal selectLineTopoTrace(@Param("lineId") String lineId);


    /**
     * 根据变电站ID查询变电站信息
     */
    TopoSubstationNj selectSubstationInfo(@Param("substationId") String substationId);


    /**
     * 根据母线ID查询同母线下的所有线路和间隔
     */
    List<TopoTraceReal> selectLinesByBusbar(@Param("busbarId") String busbarId);


    /**
     * 根据变电站下的线路和间隔获取所有母线ID（去重）
     */
    List<TopoTraceReal> selectBusbarsBySubstation(@Param("substationId") String substationId);

    /**
     * 根据变电站查询所有开关
     */
    List<TopoTraceReal> selectSwitchBySubstation(@Param("substationId") String substationId);

}
