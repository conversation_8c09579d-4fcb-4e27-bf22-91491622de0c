package com.ruoyi.mapper.znap;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.znap.ConDmsFeeder;
import org.apache.ibatis.annotations.Select;


/**
 * 配网馈线容器Mapper接口
 */
@DS("slave")
public interface ConDmsFeederMapper extends BaseMapperPlus<ConDmsFeederMapper, ConDmsFeeder, ConDmsFeeder> {

    @Select("SELECT * FROM con_dms_feeder WHERE psrid = CONCAT('PD_dkx_', #{feederId})")
    ConDmsFeeder selectByPmsFeederId(String feederId);


}
