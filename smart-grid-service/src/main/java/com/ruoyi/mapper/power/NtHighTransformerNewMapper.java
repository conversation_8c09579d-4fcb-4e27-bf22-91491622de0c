package com.ruoyi.mapper.power;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.device.DeviceSubstation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface NtHighTransformerNewMapper extends BaseMapperPlus<NtHighTransformerNewMapper, DeviceNtHighTransformer, DeviceNtHighTransformer> {
    @Update({ "<script>",
            "<foreach collection='list' item='item' separator=';'>",
            "UPDATE nt_high_transformer_new SET capacity = #{item.capacity} WHERE psr_id = #{item.psrId}",
            "</foreach>",
            "</script>"})
    void updateBatch(@Param("list") List<DeviceNtHighTransformer> updateNtHighTransformerList);

    @Select("SELECT * FROM device_substation where psr_id = #{station}")
    DeviceSubstation selectDeviceSubstation(String station);
}
