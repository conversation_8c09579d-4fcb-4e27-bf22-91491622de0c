package com.ruoyi.mapper.power;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.vo.DeviceApiCollector;
import com.ruoyi.entity.power.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface PowerMapper {
    List<DeviceFeeder> selectMiddleDmsFeederDevice(String code);

    Integer selectSubstationHWG(String code);

    Integer selectSubstationPDS(String code);

    Integer selectStationIsolateKg(@Param("list") List<String> lineIdList);

    Integer selectStationLoadKg(@Param("list") List<String> lineIdList);

    Integer selectSubstationKGZ(@Param("list") List<String> lineIdList);

    List<PBEntity> selectTransformersByFeedId(@Param("feedIdList") List<String> lineIdList);

    PowerGrid selectPowerGrid(String code);


    @Select("<script>" +
            "SELECT * FROM transformer_load " +
            "WHERE recording_time BETWEEN #{strTime} AND #{endTime} " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            "AND psr_Id IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")

    List<TransformerLoad> selectTransformerLoad(@Param("strTime") Date strTime,
                                                @Param("endTime") Date endTime,
                                                @Param("psrId") List<String> psrId);

    @Select("<script>" +
            "SELECT * FROM transformer_load_mw " +
            "WHERE recording_time BETWEEN #{strTime} AND #{endTime} " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            "AND psr_Id IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<TransformerLoadMW> selectTransformerLoadMW(@Param("strTime") Date strTime,
                                                  @Param("endTime") Date endTime,
                                                  @Param("psrId") List<String> psrId);

    @Select("SELECT * FROM transformer_load WHERE psr_Id = #{psrId}")
    List<TransformerLoad> selectAllTransformerLoad(String psrIdList);

    @Select("SELECT * FROM transformer_load_mw WHERE psr_Id = #{psrId}")
    List<TransformerLoadMW> selectAllTransformerLoadMW(String psrId);

    @Select("<script>" +
            "SELECT * FROM feeder_load " +
            "WHERE recording_time BETWEEN #{strTime} AND #{endTime} " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            "AND psr_Id IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")

    List<FeederLoad> selectFeeDerLoad(@Param("strTime") Date strTime,
                                      @Param("endTime") Date endTime,
                                      @Param("psrId") List<String> psrId);

    @Select("<script>" +
            "SELECT * FROM feeder_load_mw " +
            "WHERE recording_time BETWEEN #{strTime} AND #{endTime} " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            "AND psr_Id IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<FeederLoadMW> selectFeeDerLoadMW(@Param("strTime") Date strTime,
                                          @Param("endTime") Date endTime,
                                          @Param("psrId") List<String> psrId);


    @Select("SELECT COUNT(DISTINCT bay) AS unique_bay_count\n" +
            "FROM (\n" +
            "    SELECT bay FROM \"device_station_isolate_kg\" WHERE station = #{psrId}\n" +
            "    UNION ALL\n" +
            "    SELECT bay FROM \"device_station_breaker\" WHERE station = #{psrId}\n" +
            "    UNION ALL\n" +
            "    SELECT bay FROM \"device_station_load_kg\" WHERE station = #{psrId}\n" +
            ") AS all_bays;\n")
    Integer selectBayNum(String psrId);

    @Select("SELECT COUNT(DISTINCT bay) AS unique_bay_count\n" +
            "FROM (\n" +
            "    SELECT bay FROM \"device_station_isolate_kg\" \n" +
            "    WHERE station = #{psrId} AND \"name\" LIKE '%备用%'\n" +
            "    \n" +
            "    UNION ALL\n" +
            "    \n" +
            "    SELECT bay FROM \"device_station_breaker\" \n" +
            "    WHERE station = #{psrId} AND \"name\" LIKE '%备用%'\n" +
            "    \n" +
            "    UNION ALL\n" +
            "    \n" +
            "    SELECT bay FROM \"device_station_load_kg\" \n" +
            "    WHERE station = #{psrId} AND \"name\" LIKE '%备用%'\n" +
            ") AS all_filtered_bays;\n")
    Integer selectremainingNum(String psrId);


    @Select("<script>" +
            "SELECT * FROM device_feeder  " +
            "WHERE " +
            "<if test='psrId != null and psrId.size() > 0'>" +
            "grid_code IN " +
            "<foreach item='item' collection='psrId' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<DeviceFeeder> selectList(@Param("psrId")List<String> list);
    @Select("SELECT MAX(recording_time) AS max_recording_time FROM feeder_load")
    Date selectTime();

    @Select("select json from device_api_json where grid_code = #{gridCode} ")
    String  selectDeviceApiJson(String gridCode);

    @Select("select json from device_api_json} ")
    String  selectDeviceApiJsonAll();
}
