package com.ruoyi.mapper.plan;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.PlanVo;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 故障解决方案Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface PlanMapper extends BaseMapperPlus<PlanMapper, Plan, PlanVo> {
    @Select("SELECT *\n" +
            "FROM problem_scheme_analysis\n" +
            "WHERE problem_id = #{id}\n" +
            "ORDER BY add_time DESC \n")
    List<ProblemSchemeAnalysis> selectNew(Long id);

    @Select("SELECT * FROM problem_scheme WHERE problem_id = #{problemId}")
    List<Plan> queryByProblemId(Long problemId);

//    @Select("SELECT * FROM problem_scheme WHERE problem_id = #{problemId}")
//    List<Map<String,Object>> queryByProblemId(Long problemId);

    @Delete("DELETE FROM problem_scheme WHERE problem_id = #{problemId}")
    int deleteByProblemId(Long problemId);

    int insertBatchPlan(List<Plan> list);
}
