package com.ruoyi.mapper.plan;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.problem.Problem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PlanProblemDescribeMapper {
    @Select("select * from problem where problem_id = #{id}")
    Problem selectProblem(Long id);

    @Select("select * from device_feeder where psr_id = #{feederId}")
    DeviceFeeder selectArea(String feederId);

    @Select("select * from device_substation where psr_id = #{startStation}")
    DeviceSubstation SubstationName(String startStation);
    @Select("select dict_label from sys_dict_data where dict_type = 'voltage_level' AND dict_value = #{voltageLevel}")
    String FeederVoltage(String voltageLevel);
}
