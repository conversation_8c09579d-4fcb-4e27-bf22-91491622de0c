package com.ruoyi.mapper.plan;


import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.plan.PlanAnalysisState;
import com.ruoyi.entity.plan.vo.PlanAnalysisStateVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 故障Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface PlanAnalysisStateMapper extends BaseMapperPlus<PlanAnalysisStateMapper, PlanAnalysisState, PlanAnalysisStateVo> {
    @Delete("DELETE FROM plan_analysis_state WHERE problem_id = #{problemId}")
    void deleteByProblemId(Long problemId);

    @Select("SELECT COUNT(*) FROM plan_analysis_state " +
            "WHERE state = 2 " +
            "AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - start_time)) / 60 > 5 " +
            "AND id = #{id}")
    int countOverdueRecords(@Param("id") String id);
}
