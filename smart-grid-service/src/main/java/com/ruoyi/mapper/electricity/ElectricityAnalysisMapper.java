package com.ruoyi.mapper.electricity;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.DeviceAccessPoint;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.electricity.ElectricityAnalysisIndustry;
import com.ruoyi.entity.electricity.ElectricityAnalysisType;
import com.ruoyi.entity.electricity.bo.AnalysisCustBo;
import com.ruoyi.entity.electricity.vo.AnalysisCustVo;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.sysDict.SysDictData;
import com.ruoyi.mapper.problem.ProblemMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface ElectricityAnalysisMapper extends BaseMapperPlus<ElectricityAnalysisMapper, AnalysisCust, AnalysisCustVo> {
    /**
     *查询所有主行业类被
     * @return
     */
    @Select("SELECT * FROM electricity_analysis_industry WHERE prnt_code_cls_val = 'A000'")
    List<ElectricityAnalysisIndustry> selectPullDownMenu();


    /**
     *根据用户编码查用信息
     * @return
     */
    @Select("SELECT * FROM analysis_cust WHERE cust_no = #{userCode}")
   AnalysisCust selectById(Long userCode);

    /**
     *根据行业编号查询所有用户
     * @return
     */
    @Select("SELECT * FROM analysis_cust WHERE ind_cls = #{indCls}")
    List<AnalysisCust> selectByIndustry(String indCls);

    @Select("SELECT * FROM analysis_cust ")
    List<AnalysisCust> selectAll();

    /**
     *根据行业编号查询行业名称
     * @return
     */
    @Select("SELECT code_cls_val_name FROM electricity_analysis_industry  WHERE code_cls_val = #{indCls}")
    String selectIndustryName(String indCls);

    @Select("SELECT * FROM electricity_analysis_industry ")
    List<ElectricityAnalysisIndustry> selectIndustryAll();

    @Select("SELECT * FROM electricity_analysis_type  ")
    List<ElectricityAnalysisType>  selectTypeAll();

    @Select("<script>" +
            "SELECT * FROM device_access_point " +
            "WHERE " +
            "<if test='list != null and list.size() > 0'>" +
            " user_id IN " +
            "<foreach item='item' collection='list' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    List<DeviceAccessPoint> selectDeviceAccessPoint(@Param("list")List<String> collect);

    List<AnalysisCustVo> selectVolListWithTypeAndIndustry(@Param("bo")AnalysisCustBo bo);
    @Select("SELECT * FROM sys_dict_data where dict_type = 'voltage_level'  ")
    List<SysDictData> selectVoltage();

    @Select("SELECT * FROM sys_dict_data where dict_type = 'user_important_level'  ")
    List<SysDictData> selectUserImp();
}
