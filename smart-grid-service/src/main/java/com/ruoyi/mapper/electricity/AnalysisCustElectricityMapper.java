package com.ruoyi.mapper.electricity;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.electricity.AnalysisCustElectricity;
import com.ruoyi.entity.electricity.vo.AnalysisCustVo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AnalysisCustElectricityMapper extends BaseMapperPlus<AnalysisCustElectricityMapper, AnalysisCustElectricity, AnalysisCustElectricity> {

    /**
     * 查询今天之前7天（不含今天）的用户用电数据
     * @return 用户用电数据列表
     */
    @Select("SELECT * " +
            "FROM analysis_cust_electricity " +
            "WHERE DATE(time) BETWEEN CURRENT_DATE - INTERVAL '7 days' AND CURRENT_DATE - INTERVAL '1 day' AND cust_no = #{id}")
    List<AnalysisCustElectricity> getLast7DaysData(Long id);



}
