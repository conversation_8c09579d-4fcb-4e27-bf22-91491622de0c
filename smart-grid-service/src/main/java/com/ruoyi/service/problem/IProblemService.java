package com.ruoyi.service.problem;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.PullDownMenuIntSon;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.PullDownMenuTree;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.*;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 故障Service接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface IProblemService {

    /**
     * 查询故障
     */
    ProblemVo queryById(Long problemId);

    /**
     * 查询故障列表
     */
    TableDataInfo<ProblemVo> queryPageList(ProblemBo bo) throws ParseException;

    /**
     * 查询故障列表
     */
    List<ProblemVo> queryList(ProblemBo bo) throws ParseException;

    /**
     * 新增故障
     */
    Boolean insertByBo(ProblemBo bo);

    /**
     * 修改故障
     */
    Boolean updateByBo(ProblemBo bo) throws ParseException;

    /**
     * 校验并批量删除故障信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据坐标，已经半径参数，查询范围内所有的故障设备信息
     */
    CoordsAndProblem byDevices(Long problemId, Double radius) throws JsonProcessingException;

    /**
     * 查询故障列表的所有下拉菜单
     */
    ProblemPullDownMenu pullDownMenu() throws ParseException;

    /**
     * 统计故障列表的各类型数量
     */
    ProblemStatistics statistics();

    /**
     * 查询故障列表的问题来源下拉菜单
     *
     * @return
     */
    List<PullDownMenuIntSon> pullDownMenuDateSource();

    /**
     * 查询故障列表的问题分类下拉菜单
     *
     * @return
     */
    List<PullDownMenuTree> pullDownMenuAttrNameTree();


    /**
     * 查询故障列表的地市下拉菜单
     *
     * @return
     */
    List<PullDownMenuStringSon> pullDownMenuCity();

    /**
     * 查询故障列表的区县下拉菜单
     *
     * @return
     */
    List<PullDownMenuStringSon> pullDownMenuCounty();


    /**
     * 查询故障列表的问题状态下拉菜单
     *
     * @return
     */
    List<PullDownMenuIntSon> pullDownMenuProblemStatus();

    /**
     * 查询故障列表的严重等级下拉菜单
     *
     * @return
     */
    List<PullDownMenuIntSon> pullDownMenuGradeName();

    List<PullDownMenuIntSon> pullDownMenuAttrName();

    List<PullDownMenuIntSon> pullDownMenuLevel1(Integer problemId);

    List<PullDownMenuIntSon> pullDownMenuLevel2(Integer problemId);
    /**
     * 查找附近线的相关问题
     */
    List<ProblemVo> nearFeederProblem(String feederId,Double radius);

    List<ProblemVo> getOtherProblem(Long problemId);

    FeederNtVo getMaxLoadTime(Long problemId);
}
