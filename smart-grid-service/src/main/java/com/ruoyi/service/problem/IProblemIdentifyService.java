package com.ruoyi.service.problem;

import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.ProblemVo;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface IProblemIdentifyService {
    /**
     * 传入问题判断，其是否符合我们规则
     */
    Integer checkProblem(Long problemId) throws IOException;
    /**
     * 根据线路id分析其是否是分段配变不合理的问题
     */
    Integer unreasonablePBSegmentation(String feederId,String deviceId);


    /**
     * 根据线路id分析其是否是挂架配变过多识别
     */
    Integer pylonsPBSegmentation(String feederId);

    /**
     * 根据线路id分析其是否是单辐射无联络问题
     */
    Integer singleRadiationSegmentation(String feederId);

    /**
     * 根据线路id分析其是否是大分支无联络问题
     */
    Integer bigBranchSegmentation(String feederId,String deviceId);

    /**
     * 根据线路id分析其是否是线路重过载问题
     */
    Integer lineOverload(String feederId) throws Exception;

    /**
     * 根据线路id分析其是否是同母联络问题
     */
    Integer sameContact(String feederId)  throws IOException;
}
