package com.ruoyi.service.problem;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.bo.ProblemSchemeAnalysisBo;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;

import java.util.Collection;
import java.util.List;

/**
 * 方案分析过程Service接口
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
public interface IProblemSchemeAnalysisService {

    /**
     * 查询方案分析过程
     */
    ProblemSchemeAnalysisVo queryById(Long id);

    /**
     * 查询方案分析过程列表
     */
    TableDataInfo<ProblemSchemeAnalysisVo> queryPageList(ProblemSchemeAnalysisBo bo, PageQuery pageQuery);

    /**
     * 查询方案分析过程列表
     */
    List<ProblemSchemeAnalysisVo> queryList(ProblemSchemeAnalysisBo bo);

    /**
     * 新增方案分析过程
     */
    Boolean insertByBo(ProblemSchemeAnalysisBo bo);

    /**
     * 修改方案分析过程
     */
    Boolean updateByBo(ProblemSchemeAnalysisBo bo);

    /**
     * 校验并批量删除方案分析过程信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
