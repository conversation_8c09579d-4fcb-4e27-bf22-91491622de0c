package com.ruoyi.service.problem;


import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.problem.bo.ProblemRuleConfigurationBo;
import com.ruoyi.entity.problem.vo.ProblemRuleConfigurationVo;
import com.ruoyi.entity.problem.vo.SelectRuleVo;

import java.util.Collection;
import java.util.List;

/**
 * 问题规则Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IProblemRuleConfigurationService {

    /**
     * 查询问题规则
     */
    ProblemRuleConfigurationVo queryById(Long id);

    /**
     * 查询问题规则列表
     */
    TableDataInfo<ProblemRuleConfigurationVo> queryPageList(ProblemRuleConfigurationBo bo);

    /**
     * 查询问题规则列表
     */
    List<ProblemRuleConfigurationVo> queryList(ProblemRuleConfigurationBo bo);

    /**
     * 新增问题规则
     */
    Boolean insertByBo(ProblemRuleConfigurationBo bo);

    /**
     * 修改问题规则
     */
    Boolean updateByBo(List<ProblemRuleConfigurationBo> boList);

    /**
     * 校验并批量删除问题规则信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<String> level2();

    SelectRuleVo selectRule(Integer code);

//    ProblemConditionStrategyVo selectConditionStrategy(String condition, Integer num);
}
