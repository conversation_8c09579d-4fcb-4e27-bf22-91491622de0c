package com.ruoyi.service.problem.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.bo.ProblemSchemeAnalysisBo;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;
import com.ruoyi.mapper.problem.ProblemSchemeAnalysisMapper;
import com.ruoyi.service.problem.IProblemSchemeAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 方案分析过程Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2025-05-21
 */
@RequiredArgsConstructor
@Service
public class ProblemSchemeAnalysisServiceImpl implements IProblemSchemeAnalysisService {

    private final ProblemSchemeAnalysisMapper baseMapper;

    /**
     * 查询方案分析过程
     */
    @Override
    public ProblemSchemeAnalysisVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询方案分析过程列表
     */
    @Override
    public TableDataInfo<ProblemSchemeAnalysisVo> queryPageList(ProblemSchemeAnalysisBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProblemSchemeAnalysis> lqw = buildQueryWrapper(bo);
        Page<ProblemSchemeAnalysisVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询方案分析过程列表
     */
    @Override
    public List<ProblemSchemeAnalysisVo> queryList(ProblemSchemeAnalysisBo bo) {
        LambdaQueryWrapper<ProblemSchemeAnalysis> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProblemSchemeAnalysis> buildQueryWrapper(ProblemSchemeAnalysisBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProblemSchemeAnalysis> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBtnTitle()), ProblemSchemeAnalysis::getBtnTitle, bo.getBtnTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getTitleBox()), ProblemSchemeAnalysis::getTitleBox, bo.getTitleBox());
        lqw.eq(bo.getPlanId() != null, ProblemSchemeAnalysis::getPlanId, bo.getPlanId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ProblemSchemeAnalysis::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ProblemSchemeAnalysis::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getPrompt()), ProblemSchemeAnalysis::getPrompt, bo.getPrompt());
        return lqw;
    }

    /**
     * 新增方案分析过程
     */
    @Override
    public Boolean insertByBo(ProblemSchemeAnalysisBo bo) {
        ProblemSchemeAnalysis add = BeanUtil.toBean(bo, ProblemSchemeAnalysis.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改方案分析过程
     */
    @Override
    public Boolean updateByBo(ProblemSchemeAnalysisBo bo) {
        ProblemSchemeAnalysis update = BeanUtil.toBean(bo, ProblemSchemeAnalysis.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProblemSchemeAnalysis entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除方案分析过程
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }





}
