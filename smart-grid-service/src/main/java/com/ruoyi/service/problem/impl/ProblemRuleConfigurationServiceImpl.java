package com.ruoyi.service.problem.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.problem.HBUnit;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.ProblemRuleConfiguration;
import com.ruoyi.entity.problem.ProblemStrategy;
import com.ruoyi.entity.problem.bo.ProblemRuleConfigurationBo;
import com.ruoyi.entity.problem.vo.ProblemRuleConfigurationVo;
import com.ruoyi.entity.problem.vo.ProblemStrategyVo;
import com.ruoyi.entity.problem.vo.SelectRuleVo;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.mapper.problem.ProblemRuleMapper;
import com.ruoyi.mapper.problem.ProblemStrategyMapper;
import com.ruoyi.service.electricity.impl.SynchronousGridServiceImpl;
import com.ruoyi.service.problem.IProblemRuleConfigurationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RequiredArgsConstructor
@Service
public class ProblemRuleConfigurationServiceImpl implements IProblemRuleConfigurationService {

    private final ProblemRuleMapper baseMapper;

    @Autowired
    ProblemStrategyMapper problemStrategyMapper;

    @Autowired
    ProblemMapper problemMapper;

    @Autowired
    SynchronousGridServiceImpl synchronousGridService;

    /**
     * 查询问题规则
     */
    @Override
    public ProblemRuleConfigurationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询问题规则列表
     */
    @Override
    public TableDataInfo<ProblemRuleConfigurationVo> queryPageList(ProblemRuleConfigurationBo bo) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(bo.getPageSize());
        pageQuery.setPageNum(bo.getPageNum());
        LambdaQueryWrapper<ProblemRuleConfiguration> lqw = buildQueryWrapper(bo);
        Page<ProblemRuleConfigurationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询问题规则列表
     */
    @Override
    public List<ProblemRuleConfigurationVo> queryList(ProblemRuleConfigurationBo bo) {
        LambdaQueryWrapper<ProblemRuleConfiguration> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProblemRuleConfiguration> buildQueryWrapper(ProblemRuleConfigurationBo bo) {
        LambdaQueryWrapper<ProblemRuleConfiguration> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCategoryLevel2Code() != null, ProblemRuleConfiguration::getCategoryLevel2Code, bo.getCategoryLevel2Code());
        lqw.eq(bo.getProblemSituation() != null, ProblemRuleConfiguration::getProblemSituation, bo.getProblemSituation());
        lqw.eq(StringUtils.isNotBlank(bo.getCondition1()), ProblemRuleConfiguration::getCondition1, bo.getCondition1());
        lqw.eq(StringUtils.isNotBlank(bo.getCondition2()), ProblemRuleConfiguration::getCondition2, bo.getCondition2());
        return lqw;
    }

    /**
     * 新增问题规则
     */
    @Override
    public Boolean insertByBo(ProblemRuleConfigurationBo bo) {
        ProblemRuleConfiguration add = BeanUtil.toBean(bo, ProblemRuleConfiguration.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改问题规则
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(List<ProblemRuleConfigurationBo> boList) {

        //更新规则配置
        List<ProblemRuleConfiguration> updateList = BeanUtil.copyToList(boList, ProblemRuleConfiguration.class);
        try {
            baseMapper.updateBatchById(updateList);
        } catch (Exception e) {
            return false;
        }

        // TODO 这里有问题 注释掉
        //更新问题
//        LambdaQueryWrapper<Problem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(Problem::getCategoryLevel2Code, bo.getCategoryLevel2());
//        List<Problem> problemList = problemMapper.selectList();
//
        //      synchronousGridService.processProblemsInBatches(problemList);
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProblemRuleConfiguration entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除问题规则
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<String> level2() {
        List<String> list = baseMapper.selectList().stream().map(ProblemRuleConfiguration::getCategoryLevel2) // 提取 name 字段
                .distinct()         // 去重
                .collect(Collectors.toList());
        return list;

    }

    @Override
    public SelectRuleVo selectRule(Integer code) {

        String categoryLevel2 = problemStrategyMapper.selectDateSource(code.toString());

        LambdaQueryWrapper<ProblemStrategy> problemStrategyVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        problemStrategyVoLambdaQueryWrapper.eq(ProblemStrategy::getCategoryLevel2, categoryLevel2);
        ProblemStrategyVo problemStrategyVo = problemStrategyMapper.selectVoOne(problemStrategyVoLambdaQueryWrapper);
        if (problemStrategyVo == null) {
            return null;
        }
        LambdaQueryWrapper<ProblemRuleConfiguration> problemRuleConfigurationVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        problemRuleConfigurationVoLambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, code);
        problemRuleConfigurationVoLambdaQueryWrapper.orderByAsc(ProblemRuleConfiguration::getCondition1);
        List<ProblemRuleConfigurationVo> problemRuleConfigurationVoList = baseMapper.selectVoList(problemRuleConfigurationVoLambdaQueryWrapper);
        SelectRuleVo selectRuleVo = new SelectRuleVo();
        selectRuleVo.setProblemDefinition(problemStrategyVo.getProblemDefinition());

        List<ProblemRuleConfigurationVo> keyList = problemRuleConfigurationVoList.stream().filter(e -> e.getProblemSituation().equals(ProblemRuleConfiguration.KEY)).collect(Collectors.toList());
        List<ProblemRuleConfigurationVo> generalList = problemRuleConfigurationVoList.stream().filter(e -> e.getProblemSituation().equals(ProblemRuleConfiguration.GENERA)).collect(Collectors.toList());

        if (code.equals(PlanConstants.SEG_PB_MUSH_LEVEL) || code.equals(PlanConstants.SEG_ZFMX_MUSH_LEVEL) || code.equals(PlanConstants.SEG_DFZWLN_MUSH_LEVEL) || code.equals(PlanConstants.SEG_XLKFNLBZ_MUSH_LEVEL)) {
            Map<String, HBUnit> keyMap = new HashMap<>();
            Map<String, HBUnit> generalMap = new HashMap<>();
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                if (keyMap.get(hbUnit.getInterval()) != null) {
                    HBUnit hbUnitMap = keyMap.get(hbUnit.getInterval());
                    hbUnitMap.setCondition(hbUnitMap.getCondition() + "、" + hbUnit.getCondition());
                } else {
                    keyMap.put(hbUnit.getInterval(), hbUnit);
                }
            }
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                if (generalMap.get(hbUnit.getInterval()) != null) {
                    HBUnit hbUnitMap = generalMap.get(hbUnit.getInterval());
                    hbUnitMap.setCondition(hbUnitMap.getCondition() + "、" + hbUnit.getCondition());
                } else {
                    generalMap.put(hbUnit.getInterval(), hbUnit);
                }
            }
            Map<String, HBUnit> orderKeyMap = keyMap.entrySet().stream()
                    .sorted(Comparator.comparing(
                            entry -> entry.getValue().getCondition().substring(0, 1).toUpperCase()
                    ))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing, // 冲突策略（不会发生）
                            LinkedHashMap::new // 使用LinkedHashMap保留顺序
                    ));

            Map<String, HBUnit> sortedMap = generalMap.entrySet().stream()
                    .sorted(Comparator.comparing(
                            entry -> entry.getValue().getCondition().substring(0, 1).toUpperCase()
                    ))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing, // 冲突策略（不会发生）
                            LinkedHashMap::new // 使用LinkedHashMap保留顺序
                    ));

            String keyProblem = orderKeyMap.values().stream()
                    .map(v -> v.getCondition() + v.getMin() + v.getUnit())
                    .collect(Collectors.joining(";"));

            String generalProblem = sortedMap.values().stream()
                    .map(v -> v.getCondition() + v.getMin())
                    .collect(Collectors.joining(";"));

            selectRuleVo.setGeneralProblem(generalProblem);
            selectRuleVo.setKeyProblem(keyProblem);
        }
        if (code.equals(PlanConstants.SEG_DFSXL_MUSH_LEVEL)) {
            Map<String, String> keyMap = new HashMap<>();
            Map<String, String> generalMap = new HashMap<>();
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                keyMap.put(problemRuleConfigurationVo.getCondition1(), hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit());

            }
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                generalMap.put(problemRuleConfigurationVo.getCondition1(), hbUnit.getCondition());

            }

            // 按Key的首字母排序（忽略大小写）
            Map<String, String> sortedMap = keyMap.entrySet().stream()
                    .sorted(Comparator.comparing(
                            entry -> entry.getKey().substring(0, 1).toUpperCase()
                    ))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing, // 冲突策略
                            LinkedHashMap::new // 保持排序顺序
                    ));


            Map<String, String> keyResultMap = keyMap.entrySet().stream()
                    .collect(Collectors.groupingBy(
                            Map.Entry::getValue,  // 按value分组
                            Collectors.mapping(Map.Entry::getKey, Collectors.joining(","))
                    ));

            // 输出结果（格式：value=key1,key2;value=key3,key4;...）
            String keyResult = keyResultMap.entrySet().stream()
                    .map(e -> e.getValue() + "且" + e.getKey())
                    .collect(Collectors.joining(";"));


            // 按Key的首字母排序（忽略大小写）
            Map<String, String> sorteMap = generalMap.entrySet().stream()
                    .sorted(Comparator.comparing(
                            entry -> entry.getKey().substring(0, 1).toUpperCase()
                    ))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> existing, // 冲突策略
                            LinkedHashMap::new // 保持排序顺序
                    ));


            Map<String, String> generalResultMap = sorteMap.entrySet().stream()
                    .collect(Collectors.groupingBy(
                            Map.Entry::getValue,  // 按value分组
                            Collectors.mapping(Map.Entry::getKey, Collectors.joining(","))
                    ));

            // 转换为最终字符串
            String generalResult = generalResultMap.entrySet().stream()
                    .map(e -> e.getKey())
                    .collect(Collectors.joining(";"));

            selectRuleVo.setGeneralProblem(generalResult);
            selectRuleVo.setKeyProblem(keyResult);

        }
        if (code.equals(PlanConstants.SEG_TMLN_MUSH_LEVEL)) {
            String keyResult = "";
            String generalResult = "";
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                keyResult = keyResult + problemRuleConfigurationVo.getCondition1() + "、";
            }
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                generalResult = generalResult + problemRuleConfigurationVo.getCondition1() + "、";
            }
            selectRuleVo.setGeneralProblem(generalResult.substring(0, generalResult.length() - 1));
            selectRuleVo.setKeyProblem(keyResult.substring(0, keyResult.length() - 1));
        }
        if (code.equals(PlanConstants.SEG_XLGDBJ_MUSH_LEVEL)) {
            String keyResult = "";
            String generalResult = "";
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                HBUnit hbUnit1 = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                keyResult = keyResult + hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit() + "且"
                        + hbUnit1.getCondition() + hbUnit1.getMin() + hbUnit1.getUnit() + "、";

            }

            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                HBUnit hbUnit1 = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                generalResult = generalResult + hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit() + "且"
                        + hbUnit1.getCondition() + hbUnit1.getMax() + hbUnit1.getUnit() + "、";
                selectRuleVo.setGeneralProblem(generalResult.substring(0, generalResult.length() - 1));
                selectRuleVo.setKeyProblem(keyResult.substring(0, keyResult.length() - 1));
            }
        }
        if (code.equals(PlanConstants.SEG_XLGJPB_MUSH_LEVEL)) {
            String keyResult = "";
            String generalResult = "";
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                HBUnit hbUnit1 = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                keyResult = keyResult + hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit() + "且"
                        + hbUnit1.getCondition() + hbUnit1.getMin() + hbUnit1.getUnit() + "、";

            }

            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                HBUnit hbUnit1 = intervalConvert(problemRuleConfigurationVo.getConditionMax2(),
                        problemRuleConfigurationVo.getConditionMin2(),
                        problemRuleConfigurationVo.getCondition2(),
                        problemRuleConfigurationVo.getUnit2());
                generalResult = generalResult + hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit();
                selectRuleVo.setGeneralProblem(generalResult.substring(0, generalResult.length() - 1));
                selectRuleVo.setKeyProblem(keyResult.substring(0, keyResult.length() - 1));
            }
        }
        if (code.equals(PlanConstants.SEG_DHJKX_MUSH_LEVEL)) {
            String keyResult = "";
            String generalResult = "";
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                keyResult = hbUnit.getMin() + hbUnit.getUnit() + "及以上" + hbUnit.getCondition();

            }

            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                generalResult = hbUnit.getMin() + hbUnit.getCondition();
                selectRuleVo.setGeneralProblem(generalResult.substring(1, generalResult.length()));
                selectRuleVo.setKeyProblem(keyResult.substring(1, keyResult.length()));
            }
        }
        if (code.equals(PlanConstants.SEG_XLZGZ_MUSH_LEVEL)) {
            String keyResult = "";
            String generalResult = "";
            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : keyList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                keyResult = hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit() + "且持续" + problemRuleConfigurationVo.getConditionMin2() + "小时";
            }

            for (ProblemRuleConfigurationVo problemRuleConfigurationVo : generalList) {
                HBUnit hbUnit = intervalConvert(problemRuleConfigurationVo.getConditionMax1(),
                        problemRuleConfigurationVo.getConditionMin1(),
                        problemRuleConfigurationVo.getCondition1(),
                        problemRuleConfigurationVo.getUnit1());
                generalResult = hbUnit.getCondition() + hbUnit.getMin() + hbUnit.getUnit();
                selectRuleVo.setGeneralProblem(generalResult);
                selectRuleVo.setKeyProblem(keyResult);
            }
        }

        return selectRuleVo;
    }

    private HBUnit intervalConvert(Integer max, Integer min, String condition, String unit) {
        if (max == null && min != null) {
            HBUnit hbUnit = new HBUnit();
            hbUnit.setCondition(condition);
            hbUnit.setMin("≥" + min);
            hbUnit.setUnit(unit);
            hbUnit.setInterval("≥" + min);
            return hbUnit;
        } else if (max != null && min != null) {
            HBUnit hbUnit = new HBUnit();
            hbUnit.setCondition(condition);
            hbUnit.setMin("≥" + min + unit);
            hbUnit.setUnit(unit);
            hbUnit.setInterval(max + unit + "≥" + "," + "≥" + min + unit);
            return hbUnit;
        } else if (max != null && min == null) {
            HBUnit hbUnit = new HBUnit();
            hbUnit.setCondition(condition);
            hbUnit.setMax("≤" + max);
            hbUnit.setUnit(unit);
            hbUnit.setInterval(max + unit + "≥" + "," + "≥" + min + unit);
            return hbUnit;
        } else {
            HBUnit hbUnit = new HBUnit();
            hbUnit.setCondition("其余");
            return hbUnit;
        }

    }

    //    @Override
    public ProblemRuleConfigurationVo selectConditionStrategy(String condition, Integer num) {

        ProblemRuleConfigurationVo problemConditionStrategyVo = baseMapper.selectConditionStrategy(condition, num);

        return problemConditionStrategyVo;
    }
}
