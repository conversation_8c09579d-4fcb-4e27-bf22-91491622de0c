package com.ruoyi.service.simulation;

import com.ruoyi.entity.simulation.*;

import java.util.List;

public interface ISimulationService {
    /**
     * 根据仿真主表id查询配网开关时序潮流计算结果
     */
    List<SimRetPfDmsBreaker> selectSimRetPfDmsBreaker(Long retId,Long id);

    /**
     * 根据仿真主表id查询主网出线开关时序潮流计算结果
     */
    List<SimRetPfEmsBreaker> selectSimRetPfEmsBreaker(Long retId,Long id);

    /**
     * 根据仿真主表id查询——配网刀闸时序潮流计算结果
     */
    List<SimRetPfDmsDisconnector> selectSimRetPfDmsDisconnector(Long retId,Long id);

    /**
     * 根据仿真主表id查询——馈线段潮流计算结果
     */
    List<SimRetPfDmsSegment> selectSimRetPfSegment(Long retId,Long id);

    /**
     * 根据仿真主表id查询——母线潮流计算结果
     */
    List<SimRetPfDmsBusbar> selectSimRetPfDmsBusbar(Long retId,Long id);

    /**
     * 根据仿真主表id查询——配变潮流计算结果
     */
    List<SimRetPfDmsDt> selectSimRetPfDmsDt(Long retId,Long id);

    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    List<SimRetPfDmsLoad> selectSimRetPfDmsLoad(Long retId,Long id);
    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    SimMsg selectMsgBySim(String msgId);
}
