package com.ruoyi.service.simulation.impl;

import com.ruoyi.entity.simulation.*;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.mapper.simulation.SimulationMapper;
import com.ruoyi.service.simulation.ISimulationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service

public class SimulationServiceImpl implements ISimulationService {
    @Autowired
    SimulationMapper simulationMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ExecutorService executorService; // 注入自定义线程池

    /**
     * 根据仿真主表id查询配网开关时序潮流计算结果
     */
    @Override
    public List<SimRetPfDmsBreaker> selectSimRetPfDmsBreaker(Long retId, Long id) {
        List<SimRetPfDmsBreaker> simRetPfDmsBreakerList = simulationMapper.selectSimRetPfDmsBreaker(retId, id);
        for (SimRetPfDmsBreaker simRetPfDmsBreaker : simRetPfDmsBreakerList) {
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfDmsBreaker.getPsrId());
                simRetPfDmsBreaker.setPsrId(idAndType[1]);
                simRetPfDmsBreaker.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfDmsBreakerList;
    }

    /**
     * 根据仿真主表id查询配网开关时序潮流计算结果
     */
    @Override
    public List<SimRetPfEmsBreaker> selectSimRetPfEmsBreaker(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfEmsBreaker> simRetPfEmsBreakerList = simulationMapper.selectSimRetPfEmsBreaker(retId, id);
        for (SimRetPfEmsBreaker simRetPfEmsBreaker : simRetPfEmsBreakerList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfEmsBreaker.getPsrId());
                simRetPfEmsBreaker.setPsrId(idAndType[1]);
                simRetPfEmsBreaker.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfEmsBreakerList;
    }

    /**
     * 根据仿真主表id查询——配网刀闸时序潮流计算结果
     */
    @Override
    public List<SimRetPfDmsDisconnector> selectSimRetPfDmsDisconnector(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfDmsDisconnector> simRetPfDmsDisconnectorList = simulationMapper.selectSimRetPfDmsDisconnector(retId, id);
        for (SimRetPfDmsDisconnector simRetPfDmsDisconnector : simRetPfDmsDisconnectorList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfDmsDisconnector.getPsrId());
                simRetPfDmsDisconnector.setPsrId(idAndType[1]);
                simRetPfDmsDisconnector.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfDmsDisconnectorList;

    }

    /**
     * 根据仿真主表id查询——馈线段潮流计算结果
     */
    @Override
    public List<SimRetPfDmsSegment> selectSimRetPfSegment(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfDmsSegment> simRetPfSegmentList = simulationMapper.selectSimRetPfSegment(retId, id);
        for (SimRetPfDmsSegment simRetPfSegment : simRetPfSegmentList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfSegment.getPsrId());
                simRetPfSegment.setPsrId(idAndType[1]);
                simRetPfSegment.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfSegmentList;
    }

    /**
     * 根据仿真主表id查询——母线潮流计算结果
     */
    @Override
    public List<SimRetPfDmsBusbar> selectSimRetPfDmsBusbar(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfDmsBusbar> simRetPfDmsBusbarList = simulationMapper.selectSimRetPfDmsBusbar(retId, id);
        for (SimRetPfDmsBusbar simRetPfDmsBusbar : simRetPfDmsBusbarList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfDmsBusbar.getPsrId());
                simRetPfDmsBusbar.setPsrId(idAndType[1]);
                simRetPfDmsBusbar.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfDmsBusbarList;
    }

    /**
     * 根据仿真主表id查询——配变潮流计算结果
     */
    @Override
    public List<SimRetPfDmsDt> selectSimRetPfDmsDt(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfDmsDt> simRetPfDmsDtList = simulationMapper.selectSimRetPfDmsDt(retId, id);
        for (SimRetPfDmsDt simRetPfDmsDt : simRetPfDmsDtList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfDmsDt.getPsrId());
                simRetPfDmsDt.setPsrId(idAndType[1]);
                simRetPfDmsDt.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfDmsDtList;
    }

    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    @Override
    public List<SimRetPfDmsLoad> selectSimRetPfDmsLoad(Long retId, Long id) {
        //查询到对应数据
        List<SimRetPfDmsLoad> simRetPfDmsLoadList = simulationMapper.selectSimRetPfDmsLoad(retId, id);
        for (SimRetPfDmsLoad simRetPfDmsLoad : simRetPfDmsLoadList) {
            //判断psrId是否是空
            try {
                String[] idAndType = ZnapUtils.parsePsrStr(simRetPfDmsLoad.getPsrId());
                simRetPfDmsLoad.setPsrId(idAndType[1]);
                simRetPfDmsLoad.setPsrType(idAndType[0]);
            } catch (Exception e) {
                continue;
            }
        }
        return simRetPfDmsLoadList;
    }

    /**
     * 根据仿真主表id查询——负荷潮流计算结果
     */
    @Override
    public SimMsg selectMsgBySim(String msgId) {
        // 1. 查询 retId
        Long retId = simulationMapper.selectRetId(msgId);
        if (retId == null) {
            return null;
        }

//        CompletableFuture.supplyAsync(() -> selectSimRetPfDmsBreaker(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfDmsBusbar(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfDmsDisconnector(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfDmsDt(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfDmsLoad(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfSegment(retId));
//        CompletableFuture.supplyAsync(() ->selectSimRetPfEmsBreaker(retId));
        // 提交所有异步任务
        ExecutorService executor = Executors.newFixedThreadPool(7);
        CompletableFuture<List<SimRetPfDmsBreaker>> breakerFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfDmsBreaker(retId, null), executor);

        CompletableFuture<List<SimRetPfDmsBusbar>> busbarFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfDmsBusbar(retId, null), executor);

        CompletableFuture<List<SimRetPfDmsDisconnector>> disconnectorFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfDmsDisconnector(retId, null), executor);

        CompletableFuture<List<SimRetPfDmsDt>> dtFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfDmsDt(retId, null), executor);

        CompletableFuture<List<SimRetPfDmsLoad>> loadFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfDmsLoad(retId, null), executor);

        CompletableFuture<List<SimRetPfDmsSegment>> segmentFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfSegment(retId, null), executor);

        CompletableFuture<List<SimRetPfEmsBreaker>> emsBreakerFuture = CompletableFuture.supplyAsync(
                () -> selectSimRetPfEmsBreaker(retId, null), executor);

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                breakerFuture, busbarFuture, disconnectorFuture,
                dtFuture, loadFuture, segmentFuture, emsBreakerFuture
        );
        // 创建 SimMsg 并设置结果
        return allFutures.thenApply(v -> {
            SimMsg simMsg = new SimMsg();
            try {

                simMsg.setSimRetPfDmsBreakerList(breakerFuture.get());
                simMsg.setSimRetPfDmsBusbarList(busbarFuture.get());
                simMsg.setSimRetPfDmsDisconnectorList(disconnectorFuture.get());
                simMsg.setSimRetPfDmsDtList(dtFuture.get());
                simMsg.setSimRetPfDmsLoadList(loadFuture.get());
                simMsg.setSimRetPfDmsSegmentList(segmentFuture.get());
                simMsg.setSimRetPfEmsBreakerList(emsBreakerFuture.get());

                // 其他设置...
            } catch (Exception e) {
                throw new RuntimeException("获取结果失败", e);
            }
            return simMsg;
        }).exceptionally(ex -> {
            throw new RuntimeException("异步任务执行失败", ex);
        }).join();

    }


}
