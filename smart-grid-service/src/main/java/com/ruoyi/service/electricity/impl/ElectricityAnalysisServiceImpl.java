package com.ruoyi.service.electricity.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.device.DeviceAccessPoint;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.electricity.AnalysisCustElectricity;
import com.ruoyi.entity.electricity.ElectricityAnalysisIndustry;
import com.ruoyi.entity.electricity.bo.AnalysisCustBo;
import com.ruoyi.entity.electricity.bo.IndustryPeakAnalysisBo;
import com.ruoyi.entity.electricity.bo.LoadMWDimensionAnalysisBo;
import com.ruoyi.entity.electricity.vo.*;
import com.ruoyi.entity.power.MaxAndIndex;
import com.ruoyi.entity.power.enuw.TimeEnum96;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.Statistics;
import com.ruoyi.mapper.electricity.AnalysisCustElectricityMapper;
import com.ruoyi.mapper.electricity.ElectricityAnalysisMapper;
import com.ruoyi.mapper.power.PowerMapper;
import com.ruoyi.service.electricity.ElectricityAnalysisService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 故障Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@RequiredArgsConstructor
@Service
public class ElectricityAnalysisServiceImpl implements ElectricityAnalysisService {

    @Autowired
    PowerMapper powerMapper;

    private final ElectricityAnalysisMapper electricityAnalysisMapper;

    private final AnalysisCustElectricityMapper analysisCustElectricityMapper;
    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final int DAYS_IN_HALF_YEAR = 90;

    /**
     * 查询行业类型的菜单
     *
     * @return
     */
    @Override
    public List<PullDownMenuStringSon> pullDownMenuIndustry() {
        List<ElectricityAnalysisIndustry> industryList = electricityAnalysisMapper.selectPullDownMenu();
        List<PullDownMenuStringSon> pullDownMenuSonList = new ArrayList<>(industryList.stream()
                .filter(Objects::nonNull)
                .filter(o -> o.getCodeClsValName() != null)
                .collect(Collectors.toMap(
                        ElectricityAnalysisIndustry::getCodeClsValName,
                        o -> new PullDownMenuStringSon(o.getCodeClsVal(), o.getCodeClsValName()),
                        (o1, o2) -> o1
                ))
                .values());
        return pullDownMenuSonList;
    }

    /**
     * 查询单用户的用电分析
     */
    @Override
    public SingleUserAnalysisVo singleUserElectricityAnalysis(Long userCode) {
        SingleUserAnalysisVo singleUserAnalysisVo = new SingleUserAnalysisVo();
        //用户基本信息
        AnalysisCust analysisCust = electricityAnalysisMapper.selectById(userCode);
        if (analysisCust == null) {
            return null;
        }
        singleUserAnalysisVo.setAnalysisCust(analysisCust);

        List<AnalysisCustElectricity> analysisCustElectricityList =analysisCustElectricityMapper.getLast7DaysData(userCode);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        List<String> timeList = new ArrayList<>();
        List<List<Double>> dataList = new ArrayList<>();
        List<Double> totalList = new ArrayList<>();
        for (AnalysisCustElectricity analysisCustElectricity : analysisCustElectricityList) {
            timeList.add(formatter.format(analysisCustElectricity.getTime()));
            dataList.add(Arrays.stream(analysisCustElectricity.getElectricityList().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Double::parseDouble)
                    .collect(Collectors.toList()));
            totalList.add(analysisCustElectricity.getTotalElectricity());
        }

        List<Statistics> electricityTrendList = new ArrayList<>();
        electricityTrendList.add(new Statistics(timeList.get(0), totalList.get(0).toString()));
        electricityTrendList.add(new Statistics(timeList.get(1), totalList.get(1).toString()));
        electricityTrendList.add(new Statistics(timeList.get(2), totalList.get(2).toString()));
        electricityTrendList.add(new Statistics(timeList.get(3), totalList.get(3).toString()));
        electricityTrendList.add(new Statistics(timeList.get(4), totalList.get(4).toString()));
        electricityTrendList.add(new Statistics(timeList.get(5), totalList.get(5).toString()));
        electricityTrendList.add(new Statistics(timeList.get(6), totalList.get(6).toString()));
        singleUserAnalysisVo.setElectricityTrendList(electricityTrendList);

        List<Double> morning = getDoubleList(analysisCustElectricityList,6,10);
        List<Double> noon = getDoubleList(analysisCustElectricityList,11,14);
        List<Double> afternoon = getDoubleList(analysisCustElectricityList,15,18);
        List<Double> evening = getDoubleList(analysisCustElectricityList,19,22);
        List<Double> beforeDawn = getDoubleList(analysisCustElectricityList,0,5);
        List<Double> lateNight = getDoubleList(analysisCustElectricityList,23,24);



        List<Statistics> electricityHabitList = new ArrayList<>();
        electricityHabitList.add(new Statistics("凌晨(0-5点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(beforeDawn)))));
        electricityHabitList.add(new Statistics("早上(6-10点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(morning)))));
        electricityHabitList.add(new Statistics("中午(11-14点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(noon)))));
        electricityHabitList.add(new Statistics("下午(15-18点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(afternoon)))));
        electricityHabitList.add(new Statistics("晚上(19-22点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(evening)))));
        electricityHabitList.add(new Statistics("深夜(23-24点)", String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateMean(lateNight)))));
        singleUserAnalysisVo.setElectricityHabitList(electricityHabitList);

        List<Statistics> electricityVolatileList = new ArrayList<>();
        electricityVolatileList.add(new Statistics(timeList.get(0), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(0))))));
        electricityVolatileList.add(new Statistics(timeList.get(1), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(1))))));
        electricityVolatileList.add(new Statistics(timeList.get(2), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(2))))));
        electricityVolatileList.add(new Statistics(timeList.get(3), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(3))))));
        electricityVolatileList.add(new Statistics(timeList.get(4), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(4))))));
        electricityVolatileList.add(new Statistics(timeList.get(5), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(5))))));
        electricityVolatileList.add(new Statistics(timeList.get(6), String.valueOf(DoubleFormatter.formatToThreeDecimals2(calculateCoefficientOfVariation(dataList.get(6))))));
        singleUserAnalysisVo.setElectricityVolatileList(electricityVolatileList);

        List<Statistics> usuallyAndWeekendList = new ArrayList<>();
        List<Double> weekendTotal = new ArrayList<>();
        List<Double> usuallyTotal = new ArrayList<>();

        for (AnalysisCustElectricity analysisCustElectricity : analysisCustElectricityList) {
            if (isWeekend(formatter.format(analysisCustElectricity.getTime()))) {
                weekendTotal.add(analysisCustElectricity.getTotalElectricity());
            } else {
                usuallyTotal.add(analysisCustElectricity.getTotalElectricity());
            }
        }
        usuallyAndWeekendList.add(new Statistics("工作日", String.valueOf(calculateMean(usuallyTotal))));
        usuallyAndWeekendList.add(new Statistics("周末", String.valueOf(calculateMean(weekendTotal))));
        singleUserAnalysisVo.setUsuallyAndWeekendList(usuallyAndWeekendList);
        singleUserAnalysisVo.setUsuallyAvg(calculateMean(usuallyTotal));
        singleUserAnalysisVo.setWeekendAvg(calculateMean(weekendTotal));

        List<Double> dailyData = Arrays.asList(
                calculateSum(dataList.get(0), 0, 96),
                calculateSum(dataList.get(1), 0, 96),
                calculateSum(dataList.get(2), 0, 96),
                calculateSum(dataList.get(3), 0, 96),
                calculateSum(dataList.get(4), 0, 96),
                calculateSum(dataList.get(5), 0, 96),
                calculateSum(dataList.get(6), 0, 96)
        );

        // 1. 去趋势（线性拟合）
        List<Double> trend = fitLinearTrend(dailyData);
        List<Double> residual = new ArrayList<>();
        for (int i = 0; i < dailyData.size(); i++) {
            residual.add(dailyData.get(i) - trend.get(i));
        }

        // 2. 计算周期性强度
        double totalVariance = calculateVariance(dailyData);
        double residualVariance = calculateVariance(residual);
        double strength = (totalVariance > 0) ? residualVariance / totalVariance : 0;
        singleUserAnalysisVo.setPeriodicity(DoubleFormatter.formatToThreeDecimals2(strength*100));

        return singleUserAnalysisVo;
    }

    /**
     * 取两个时间点之间的数据
     * @param analysisCustElectricityList
     * @param str
     * @param end
     * @return
     */

    private static List<Double> getDoubleList(List<AnalysisCustElectricity> analysisCustElectricityList,int str ,int end) {
        List<Double> morning = Arrays.asList(
                calculateSum(Arrays.stream(analysisCustElectricityList.get(0).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(1).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(2).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(3).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(4).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(5).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4),
                calculateSum(Arrays.stream(analysisCustElectricityList.get(6).getElectricityList().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Double::parseDouble)
                        .collect(Collectors.toList()), str * 4, end * 4)
        );
        return morning;
    }

    /**
     * 行业用户用电高峰时段分析
     */
    @Override
    public IndustryPeakAnalysisVo industryElectricityPeakAnalysis(IndustryPeakAnalysisBo bo) {

        List<AnalysisCust> analysisCustList = selectAnalysisCust(bo.getCode(),bo.getIndCls());

        String name = electricityAnalysisMapper.selectIndustryName(bo.getIndCls());
        List<AnalysisCustElectricity> analysisCustElectricityList = new ArrayList<>();
        for (AnalysisCust analysisCust : analysisCustList) {
            LambdaQueryWrapper<AnalysisCustElectricity> lambdaQueryWrapper =new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AnalysisCustElectricity::getTime,bo.getTime());
            lambdaQueryWrapper.eq(AnalysisCustElectricity::getCustNo,analysisCust.getCustNo());
            analysisCustElectricityList.add(analysisCustElectricityMapper.selectOne(lambdaQueryWrapper));
        }

        List<IndustryUserVo> industryUserVoList = new ArrayList<>();

        List<Statistics> industryPeakColumnarList = new ArrayList<>();


        for (AnalysisCust analysisCust : analysisCustList) {
            for (AnalysisCustElectricity analysisCustElectricity : analysisCustElectricityList) {
                if (analysisCust.getCustName().equals(analysisCustElectricity.getCustName())) {
                    List<Double> list = Arrays.stream(analysisCustElectricityList.get(4).getElectricityList().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Double::parseDouble)
                            .collect(Collectors.toList());

                    IndustryUserVo industryUserVo = new IndustryUserVo();
                    industryUserVo.setElectricityQuantity(analysisCustElectricity.getTotalElectricity());
                    industryUserVo.setUserName(analysisCust.getCustName());
                    industryUserVo.setCustNo(analysisCust.getCustNo());
                    industryUserVo.setIndustryName(name);
                    industryUserVo.setType(analysisCust.getIndCls());
                    industryUserVoList.add(industryUserVo);
                    if (CollectionUtils.isEmpty(industryPeakColumnarList)) {
                        industryPeakColumnarList.add(new Statistics("0点-2点", String.valueOf(calculateSum(list, 0, 2*4))));
                        industryPeakColumnarList.add(new Statistics("3点-4点", String.valueOf(calculateSum(list, 3 * 4, 4 * 4))));
                        industryPeakColumnarList.add(new Statistics("5点-6点", String.valueOf(calculateSum(list, 5 * 4, 6 * 4))));
                        industryPeakColumnarList.add(new Statistics("7点-8点", String.valueOf(calculateSum(list, 7 * 4, 8 * 4))));
                        industryPeakColumnarList.add(new Statistics("9点-10点", String.valueOf(calculateSum(list, 9 * 4, 10 * 4))));
                        industryPeakColumnarList.add(new Statistics("11点-12点", String.valueOf(calculateSum(list, 11 * 4, 12 * 4))));
                        industryPeakColumnarList.add(new Statistics("13点-14点", String.valueOf(calculateSum(list, 13 * 4, 14 * 4))));
                        industryPeakColumnarList.add(new Statistics("15点-16点", String.valueOf(calculateSum(list, 15 * 4, 16 * 4))));
                        industryPeakColumnarList.add(new Statistics("17点-18点", String.valueOf(calculateSum(list, 17 * 4, 18 * 4))));
                        industryPeakColumnarList.add(new Statistics("19点-20点", String.valueOf(calculateSum(list, 19 * 4, 20 * 4))));
                        industryPeakColumnarList.add(new Statistics("21点-22点", String.valueOf(calculateSum(list, 21* 4, 22 * 4))));
                        industryPeakColumnarList.add(new Statistics("23点-24点", String.valueOf(calculateSum(list, 23 * 4, 24 * 4))));
                    } else {
                        industryPeakColumnarList.get(0).setTypeNum(BigDecimal.valueOf(calculateSum(list, 0, 2 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(0).getTypeNum()))).toString());
                        industryPeakColumnarList.get(1).setTypeNum(BigDecimal.valueOf(calculateSum(list, 3*4, 4 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(1).getTypeNum()))).toString());
                        industryPeakColumnarList.get(2).setTypeNum(BigDecimal.valueOf(calculateSum(list, 5*4, 6 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(2).getTypeNum()))).toString());
                        industryPeakColumnarList.get(3).setTypeNum(BigDecimal.valueOf(calculateSum(list, 7*4, 8 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(3).getTypeNum()))).toString());
                        industryPeakColumnarList.get(4).setTypeNum(BigDecimal.valueOf(calculateSum(list, 9*4, 10 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(4).getTypeNum()))).toString());
                        industryPeakColumnarList.get(5).setTypeNum(BigDecimal.valueOf(calculateSum(list, 11*4, 12 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(5).getTypeNum()))).toString());
                        industryPeakColumnarList.get(6).setTypeNum(BigDecimal.valueOf(calculateSum(list, 13*4, 14 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(6).getTypeNum()))).toString());
                        industryPeakColumnarList.get(7).setTypeNum(BigDecimal.valueOf(calculateSum(list, 15*4, 16 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(7).getTypeNum()))).toString());
                        industryPeakColumnarList.get(8).setTypeNum(BigDecimal.valueOf(calculateSum(list, 17*4, 18 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(8).getTypeNum()))).toString());
                        industryPeakColumnarList.get(9).setTypeNum(BigDecimal.valueOf(calculateSum(list, 19*4, 20 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(9).getTypeNum()))).toString());
                        industryPeakColumnarList.get(10).setTypeNum(BigDecimal.valueOf(calculateSum(list, 21*4, 22 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(10).getTypeNum()))).toString());
                        industryPeakColumnarList.get(11).setTypeNum(BigDecimal.valueOf(calculateSum(list, 23*4, 24 * 4)).add(BigDecimal.valueOf(Double.parseDouble(industryPeakColumnarList.get(11).getTypeNum()))).toString());

                    }

                }
            }
        }

        IndustryPeakAnalysisVo industryPeakAnalysisVo = new IndustryPeakAnalysisVo();
        industryPeakAnalysisVo.setIndustryPeakColumnarList(industryPeakColumnarList);


        Page<IndustryUserVo> page = new Page<>(bo.getPageNum(), bo.getPageSize(), industryUserVoList.size());

// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), industryUserVoList.size());
        page.setRecords(industryUserVoList.subList(fromIndex, toIndex));
        industryPeakAnalysisVo.setIndustryUserVoList(page);

        return industryPeakAnalysisVo;
    }

    /**
     * 负荷多维度聚合
     */
    @Override
    public Page<LoadMWDimensionAnalysisVo> LoadMWDimensionAnalysis(LoadMWDimensionAnalysisBo bo) {

        List<AnalysisCust> selectAnalysisCust = selectAnalysisCust(bo.getCode(),bo.getIndCls());

        if(StringUtils.isNotBlank(bo.getEcCateg())){
            List<AnalysisCust> list = selectAnalysisCust.stream().filter(e->e.getEcCateg().equals(bo.getEcCateg())).collect(Collectors.toList());
            selectAnalysisCust.clear();
            selectAnalysisCust.addAll(list);
        }
        if(StringUtils.isNotBlank(bo.getIndCls())){
            List<AnalysisCust> list = selectAnalysisCust.stream().filter(e->e.getIndCls().equals(bo.getIndCls())).collect(Collectors.toList());
            selectAnalysisCust.clear();
            selectAnalysisCust.addAll(list);
        }
        if(StringUtils.isNotBlank(bo.getImptLv())){
            List<AnalysisCust> list = selectAnalysisCust.stream().filter(e->e.getImptLv().equals(bo.getImptLv())).collect(Collectors.toList());
            selectAnalysisCust.clear();
            selectAnalysisCust.addAll(list); }
        if(StringUtils.isNotBlank(bo.getIsDoublePower())){
            List<AnalysisCust> list = selectAnalysisCust.stream().filter(e->e.getIsDoublePower().equals(bo.getIsDoublePower())).collect(Collectors.toList());
            selectAnalysisCust.clear();
            selectAnalysisCust.addAll(list);
        }


        return null;
    }

    /**
     * 网格下所有用户，加上筛选条件
     */
    @Override
    public TableDataInfo<AnalysisCustVo> selectGridByAnalysisCust(AnalysisCustBo bo) {


        List<AnalysisCustVo> analysisCustList = electricityAnalysisMapper.selectVolListWithTypeAndIndustry(bo);



        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(bo.getCode());

        List<DeviceAccessPoint>  deviceAccessPointList = electricityAnalysisMapper.selectDeviceAccessPoint(analysisCustList.stream().map(e->String.valueOf(e.getCustNo())).collect(Collectors.toList()));
// 筛选出 list1 中满足条件的元素
        List<DeviceAccessPoint> resultDeviceAccessPointList = deviceAccessPointList.stream()
                .filter(entity1 ->
                        dmsFeederDeviceList.stream()
                                .anyMatch(entity2 ->
                                        entity1.getFeeder().equals(entity2.getPsrId())  // 比较 A 和 B
                                )
                )
                .collect(Collectors.toList());

        List<AnalysisCustVo> resultAnalysisCustList = analysisCustList.stream()
                .filter(entity1 ->
                        resultDeviceAccessPointList.stream()
                                .anyMatch(entity2 ->
                                        entity1.getCustNo().toString().equals(entity2.getUserId())  // 比较 A 和 B
                                )
                )
                .collect(Collectors.toList());






        Page<AnalysisCustVo> page = new Page<>(bo.getPageNum(), bo.getPageSize(), resultAnalysisCustList.size());

// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), resultAnalysisCustList.size());
        page.setRecords(resultAnalysisCustList.subList(fromIndex, toIndex));

        return TableDataInfo.build(page);
    }

    @Override
    public List<Statistics> selectElectricityAnalysisIndustry() {
        return electricityAnalysisMapper.selectIndustryAll().stream().
                map(entry -> new Statistics(
                        entry.getCodeClsValName(),
                        entry.getCodeClsVal()
                ))
                .collect(Collectors.toList());
    }

    @Override
    public  List<Statistics> selectElectricityAnalysisType() {
        return  electricityAnalysisMapper.selectTypeAll().stream().
                map(entry -> new Statistics(
                        entry.getCodeClsValName(),
                        entry.getCodeClsVal()
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<Statistics> selectVoltage() {

        return  electricityAnalysisMapper.selectVoltage().stream().
                map(entry -> new Statistics(
                        entry.getDictLabel(),
                        entry.getDictValue()

                ))
                .collect(Collectors.toList());

    }

    @Override
    public List<Statistics> selectUserImp() {
        return  electricityAnalysisMapper.selectUserImp().stream().
                map(entry -> new Statistics(
                        entry.getDictLabel(),
                        entry.getDictValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     *配电网——添加假的用电记录
     */
    @Override
    public boolean testElectricity() throws Exception {
        List<AnalysisCust> list = electricityAnalysisMapper.selectAll();

        insertElectricity(list,1000);



        return true;
    }


    /**
     * 线路功率分批策略
     *
     * @param analysisCustList
     * @param batchSize
     * @throws Exception
     */
    public void insertElectricity(List<AnalysisCust> analysisCustList,
                                   int batchSize) throws Exception {

        // 使用工作窃取线程池提高效率
        ExecutorService executor = Executors.newWorkStealingPool();

        // 分批处理
        analysisCustList.forEach(e -> {
            CompletableFuture.runAsync(() -> {
                List<AnalysisCustElectricity> batch = new ArrayList<>(batchSize);





                for(int a = 0;a<DAYS_IN_HALF_YEAR;a++){
                    List<Double> data = generateRandomDoubles(96, 0.0, 0.5);
                    AnalysisCustElectricity analysisCustElectricity = new AnalysisCustElectricity();
                    analysisCustElectricity.setCustNo(e.getCustNo());
                    analysisCustElectricity.setCustName(e.getCustName());

                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_YEAR, -a);
                    Date beforeDate = calendar.getTime();

                    // 格式化日期为 yyyy-MM-dd
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    String time =  formatter.format(beforeDate);

                    analysisCustElectricity.setTime( beforeDate);
                    MaxAndIndex maxAndIndex = maxAndIndex(data,time);
                    analysisCustElectricity.setMaxElectricity(maxAndIndex.getMaxNum());
                    analysisCustElectricity.setMaxElectricityTime(maxAndIndex.getMaxTime());
                    analysisCustElectricity.setMinElectricity(maxAndIndex.getMinNum());
                    analysisCustElectricity.setMinElectricityTime(maxAndIndex.getMinTime());
                    analysisCustElectricity.setAvgElectricity(DoubleFormatter.formatToThreeDecimals2(data.stream().mapToDouble(Double::doubleValue).average().getAsDouble()));
                    String dataString = data.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    analysisCustElectricity.setElectricityList(dataString);

                    analysisCustElectricity.setTotalElectricity(DoubleFormatter.formatToThreeDecimals2(data.stream().mapToDouble(Double::doubleValue).sum()));
                    batch.add(analysisCustElectricity);
                }


                if (batch.size() >= batchSize) {
                   //生成1000条假数据
                    System.out.println(batch);
                    batch.clear();
                }else {
                    // 插入剩余记录
                    if (!batch.isEmpty()) {
                        //添加剩下的数据
//                        feederLoadMWMapper.insertBatch(batch);
                        System.out.println(batch);
                    }
                }

            }, executor);
        });

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);
    }
    private static MaxAndIndex maxAndIndex(List<Double> doubles, String time) {
        MaxAndIndex maxAndIndex = new MaxAndIndex();
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, doubles.size())
                .reduce((a, b) -> doubles.get(a) >= doubles.get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            Double maxValue = doubles.get(maxIndex);
            maxAndIndex.setMaxNum(maxValue);
            String timeStr = TimeEnum96.getTime(maxIndex);  // 直接使用枚举常量
            maxAndIndex.setMaxTime(time + " " + timeStr);
        }


        OptionalInt minIndexOpt = IntStream.range(0, doubles.size())
                .reduce((a, b) -> doubles.get(a) <= doubles.get(b) ? a : b);

        if (minIndexOpt.isPresent()) {
            int minIndex = minIndexOpt.getAsInt();
            Double maxValue = doubles.get(minIndex);
            maxAndIndex.setMinNum(maxValue);
            String timeStr = TimeEnum96.getTime(minIndex);
            maxAndIndex.setMinTime(time + " " + timeStr);
        }
        return maxAndIndex;
    }

    public static List<Double> generateRandomDoubles(int count, double min, double max) {
        List<Double> result = new ArrayList<>(count);
        Random random = new Random();

        for (int i = 0; i < count; i++) {
            // 生成 [min, max) 范围内的随机 double
            double value = min + random.nextDouble() * (max - min);
            double roundedValue = Math.round(value * 100) / 100.0;
            result.add(roundedValue);
        }

        return result;
    }


    /**
     * 查询网格下的某个行业用户
     */
    public List<AnalysisCust> selectAnalysisCust(String code,String indCls) {

        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(code);

        List<AnalysisCust> analysisCustList =electricityAnalysisMapper.selectByIndustry(indCls) ;

        List<DeviceAccessPoint>  deviceAccessPointList = electricityAnalysisMapper.selectDeviceAccessPoint(analysisCustList.stream().map(e->String.valueOf(e.getCustNo())).collect(Collectors.toList()));


// 筛选出 list1 中满足条件的元素
        List<DeviceAccessPoint> resultDeviceAccessPointList = deviceAccessPointList.stream()
                .filter(entity1 ->
                        dmsFeederDeviceList.stream()
                                .anyMatch(entity2 ->
                                        entity1.getFeeder().equals(entity2.getPsrId())  // 比较 A 和 B
                                )
                )
                .collect(Collectors.toList());

        List<AnalysisCust> resultAnalysisCustList = analysisCustList.stream()
                .filter(entity1 ->
                        resultDeviceAccessPointList.stream()
                                .anyMatch(entity2 ->
                                        entity1.getCustNo().toString().equals(entity2.getUserId())  // 比较 A 和 B
                                )
                )
                .collect(Collectors.toList());


        return resultAnalysisCustList;
    }



    /**
     * 判断字符串格式的日期是否是周末
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return true-是周末，false-不是周末或格式错误
     */
    public static boolean isWeekend(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
            DayOfWeek day = date.getDayOfWeek();
            return day == DayOfWeek.SATURDAY || day == DayOfWeek.SUNDAY;
        } catch (Exception e) {
            System.err.println("日期格式无效: " + dateStr);
            return false;
        }
    }

    /**
     * 计算指定时间范围内的用电量总和
     *
     * @param data       用电量数据列表
     * @param startIndex 开始索引（包含）
     * @param endIndex   结束索引（不包含）
     * @return 时间段内的用电量总和
     */
    public static double calculateSum(List<Double> data, int startIndex, int endIndex) {
        Double sum = 0.0;

        for (int i = startIndex; i < endIndex && i < data.size(); i++) {
            BigDecimal sumBigDecimal = BigDecimal.valueOf(sum);
            BigDecimal bigDecimal = BigDecimal.valueOf(data.get(i));
            BigDecimal newBigDecimal = sumBigDecimal.add(bigDecimal);
            sum = newBigDecimal.doubleValue();

        }
        return sum;
    }

    /**
     * 计算平均值（Mean）
     *
     * @param data
     * @return
     */
    public static double calculateMean(List<Double> data) {
        return data.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0); // 如果数据为空，返回0
    }

    public static double calculateCoefficientOfVariation(List<Double> dailyData) {
        // 计算平均值
        double mean = dailyData.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);

        // 计算标准差
        double variance = dailyData.stream()
                .mapToDouble(x -> Math.pow(x - mean, 2))
                .average()
                .orElse(0.0);
        double stdDev = Math.sqrt(variance);

        // 波动系数 = 标准差 / 平均值
        return (mean != 0) ? stdDev / mean : 0.0;
    }


    /**
     * 线性拟合去趋势（返回趋势线数据）
     *
     * @param data
     * @return
     */
    public static List<Double> fitLinearTrend(List<Double> data) {
        int n = data.size();
        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
        for (int i = 0; i < n; i++) {
            sumX += i;
            sumY += data.get(i);
            sumXY += i * data.get(i);
            sumXX += i * i;
        }
        double slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX); // 斜率b
        double intercept = (sumY - slope * sumX) / n;                          // 截距a

        /**
         * 生成趋势线数据
         * @param data
         * @return
         */
        List<Double> trend = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            trend.add(intercept + slope * i);
        }
        return trend;
    }

    // 计算方差
    public static double calculateVariance(List<Double> data) {
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        return data.stream()
                .mapToDouble(x -> Math.pow(x - mean, 2))
                .average()
                .orElse(0);
    }


}
