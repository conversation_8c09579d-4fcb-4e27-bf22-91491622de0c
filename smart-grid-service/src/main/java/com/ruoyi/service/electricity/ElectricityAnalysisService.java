package com.ruoyi.service.electricity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.electricity.AnalysisCust;
import com.ruoyi.entity.electricity.bo.AnalysisCustBo;
import com.ruoyi.entity.electricity.bo.IndustryPeakAnalysisBo;
import com.ruoyi.entity.electricity.bo.LoadMWDimensionAnalysisBo;
import com.ruoyi.entity.electricity.vo.AnalysisCustVo;
import com.ruoyi.entity.electricity.vo.IndustryPeakAnalysisVo;
import com.ruoyi.entity.electricity.vo.LoadMWDimensionAnalysisVo;
import com.ruoyi.entity.electricity.vo.SingleUserAnalysisVo;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.Statistics;

import java.util.List;

/**
 * 用电分析Service接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface ElectricityAnalysisService {
    /**
     * 查询行业类别的列表
     * @return
     */
    List<PullDownMenuStringSon> pullDownMenuIndustry();

    /**
     * 查询单用户的用电分析
     */
    SingleUserAnalysisVo singleUserElectricityAnalysis(Long userCode);
    /**
     * 行业用户用电高峰时段分析
     */
    IndustryPeakAnalysisVo industryElectricityPeakAnalysis(IndustryPeakAnalysisBo bo);

    /**
     * 负荷多维度聚合
     */
    Page<LoadMWDimensionAnalysisVo> LoadMWDimensionAnalysis(LoadMWDimensionAnalysisBo bo);

    /**
     * 网格下所有用户，加上筛选条件
     */
    TableDataInfo<AnalysisCustVo> selectGridByAnalysisCust(AnalysisCustBo bo);

    List<Statistics> selectElectricityAnalysisIndustry();

    List<Statistics> selectElectricityAnalysisType();

    List<Statistics> selectVoltage();

    List<Statistics> selectUserImp();

    /**
     *配电网——添加假的用电记录
     */
    boolean testElectricity() throws Exception;
}
