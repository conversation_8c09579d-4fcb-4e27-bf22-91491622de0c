package com.ruoyi.service.electricity;

import com.ruoyi.entity.map.vo.RouteVo;

public interface ISynchronousGridService {

    /**
     * 同步行业用户的网格
     */
    boolean synchronousCustAsGrid();



    void nearFeeder(String feederId);


    void test();
    /**
     * 再此确认导入的问题是否是我们配置的规则下的问题
     */
    void problemMyRule();
    /**
     * 物理杆塔的坐标换成国网坐标
     */
    void wlgtCoordinates() throws Exception;
    /**
     * 终端的坐标换成国网坐标
     */
    void cableTerminalJointcoordinates() throws Exception;
}
