package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceCableTerminalJoint;
import com.ruoyi.mapper.device.DeviceCableTerminalJointMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

// 开关相关策略
@Component
public class DeviceCableTerminalJointStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceCableTerminalJointMapper deviceCableTerminalJointMapper;

    public DeviceCableTerminalJointStrategy() {
        super("0202", "device_cable_terminal_joint");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceCableTerminalJoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCableTerminalJoint::getPsrId, psrId);
        return deviceCableTerminalJointMapper.selectOne(queryWrapper);
    }

}