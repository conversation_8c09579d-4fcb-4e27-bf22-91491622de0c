package com.ruoyi.service.device.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.bo.DeviceAccessPointBo;
import com.ruoyi.entity.device.vo.*;
import com.ruoyi.mapper.device.*;
import com.ruoyi.util.coordinates.CoordinateConverter;
import net.objecthunter.exp4j.Expression;
import net.objecthunter.exp4j.ExpressionBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.constant.DeviceConstants.JUNCTION_TYPES;
import static com.ruoyi.constant.DeviceConstants.POLE_TYPES;

@Service
public class QueryDeviceInfoImpl {

    //  配电柱上负荷开关（0112）、配电柱上断  路器（0111）
    @Autowired
    DevicePoleBreakMapper devicePoleBreakMapper;

    // device_station_isolate_kg 变电站内隔离开关(0306)
    @Autowired
    DeviceStationIsolateKgMapper deviceStationIsolateKgMapper;

    // device_station_load_kg 变电站内负荷开关(0307)
    @Autowired
    DeviceStationLoadKgMapper deviceStationLoadKgMapper;

    // device_station_breaker 变电站内断路器(0305)
    @Autowired
    DeviceStationBreakerMapper deviceStationBreakerMapper;

    // (配电站内变压器(0302)
    @Autowired
    DeviceStationTransformerMapper deviceStationTransformerMapper;

    @Autowired
    DeviceMapper deviceMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PbMapper pbMapper;


    /**
     * 根据设备id和设备type 获取设备名称
     *
     * @param psrId
     * @param psrType
     * @return
     */
    public DevInfo getKg(String psrId, String psrType) {

        // 配电柱上负荷开关（0112）、配电柱上断  路器（0111）
        if (StringUtils.equals(psrType, "0112") || StringUtils.equals(psrType, "0112")) {
            LambdaQueryWrapper<DevicePoleBreak> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DevicePoleBreak::getPsrId, psrId).eq(DevicePoleBreak::getPsrType, psrType);
            DevicePoleBreak devicePoleBreak = devicePoleBreakMapper.selectOne(queryWrapper);
            return new DevInfo(devicePoleBreak.getPsrId(), devicePoleBreak.getPsrType(), devicePoleBreak.getName());
        } else if (StringUtils.equals(psrType, "0306")) {
            DeviceStationIsolateKg deviceStationIsolateKg = deviceStationIsolateKgMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationIsolateKg.getPsrId(), "0306", deviceStationIsolateKg.getName());
        } else if (StringUtils.equals(psrType, "0307")) {
            DeviceStationLoadKg deviceStationLoadKg = deviceStationLoadKgMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationLoadKg.getPsrId(), "0307", deviceStationLoadKg.getName());
        } else if (StringUtils.equals(psrType, "0305")) {
            DeviceStationBreaker deviceStationBreaker = deviceStationBreakerMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationBreaker.getPsrId(), "0305", deviceStationBreaker.getName());
        }
        return null;
    }


    /**
     * 根据线路id查询母线等信息
     *
     * @param feederId
     * @return
     */
    public ByFeederBusBarAndBreaker selectBusBarAndBreaker(String feederId) {
        ByFeederBusBarAndBreaker byFeederBusBarAndBreaker = new ByFeederBusBarAndBreaker();
        //第一步 根据feederId查询设备线路mrid和变电站id
        Middle feederMrIdAndSubstationIdMiddle = deviceMapper.selectFeederMrIdAndSubstationIdInfo(feederId);
        if (feederMrIdAndSubstationIdMiddle != null) {
            byFeederBusBarAndBreaker.setSubstationId(feederMrIdAndSubstationIdMiddle.getId2());
            //查询变电站信息
            String substationName = deviceMapper.selectSubstationName(feederMrIdAndSubstationIdMiddle.getId2());
            if (StringUtils.isNotBlank(substationName)) {
                byFeederBusBarAndBreaker.setSubstationName(substationName);
                byFeederBusBarAndBreaker.setSubstationName(substationName);
            }

        }

        //第二步查询根据feederId查询母线id和间隔id
        Middle busBarIdAndBreakerIdMiddle = deviceMapper.selectBusBarIdAndBreakerIdInfo(feederId);
        if (busBarIdAndBreakerIdMiddle != null) {
            //查询间隔信息
            Breaker breaker = deviceMapper.seletBreaker(busBarIdAndBreakerIdMiddle.getId1());
            if (breaker != null) {
                byFeederBusBarAndBreaker.setBreakerId(breaker.getBreakerId());
                byFeederBusBarAndBreaker.setBreakerName(breaker.getBreakerName());
                byFeederBusBarAndBreaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
            BusBar busBar = deviceMapper.seletBusBar(busBarIdAndBreakerIdMiddle.getId2());
            //查询母线信息
            if (busBar != null) {
                byFeederBusBarAndBreaker.setBusBarId(busBar.getBusBarId());
                byFeederBusBarAndBreaker.setBusBarName(busBar.getBusBarName());
            }
        }


        return byFeederBusBarAndBreaker;

    }


    /**
     * 根据变电站名称查询所有的母线等信息
     *
     * @param substationName
     * @return
     */
    public BySubstationBusBarAndBreaker selectSubstationBusBarAndBreaker(String substationName) {
        BySubstationBusBarAndBreaker bySubstationBusBarAndBreaker = new BySubstationBusBarAndBreaker();
        //第一步根据变电站名字查询ID
        String substationId = deviceMapper.selectSubstationId(substationName);
        if (StringUtils.isBlank(substationId)) {
            return null;
        }
        List<BusBar> returnBusBarList = new ArrayList<>();
        //查询变电站下的间隔
        List<Breaker> breakerList = deviceMapper.selectBreakerList(substationId);
        if (CollectionUtils.isNotEmpty(breakerList)) {
            for (Breaker breaker : breakerList) {
                breaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
            bySubstationBusBarAndBreaker.setBreakerList(breakerList);
            List<String> idList = breakerList.stream().map(Breaker::getBreakerId).collect(Collectors.toList());
            List<BusBar> busBarList = deviceMapper.selectBusBarListByBreak(idList);
            returnBusBarList.addAll(busBarList);
        }

        //去重
        if (CollectionUtils.isNotEmpty(returnBusBarList)) {
            bySubstationBusBarAndBreaker.setBusBarList(new ArrayList<>(returnBusBarList.stream()
                    .filter(busBar -> busBar != null && busBar.getBusBarId() != null) // 过滤 null
                    .collect(Collectors.toMap(
                            BusBar::getBusBarId,  // 以 busBarId 作为 Key
                            busBar -> busBar,     // 保留 BusBar 本身
                            (existing, replacement) -> existing  // 重复时保留已存在的
                    ))
                    .values()));

        }

        return bySubstationBusBarAndBreaker;
    }

    /**
     * 根据设备ID和类型列表查询设备的额定容量
     *
     * @return 所有设备的额定容量列表（无效或空值转为0.0），保持原始ID顺序
     */
    public List<Double> selectDeviceRatedCapacity(List<String> idList) {
        List<PBEntity> specializedTransformers = pbMapper.selectTransformersById(idList);

        // 创建ID到transformer的映射（保持原始顺序）
        Map<String, PBEntity> idToTransformer = new LinkedHashMap<>();
        for (PBEntity transformer : specializedTransformers) {
            idToTransformer.put(transformer.getPsrId(), transformer);
        }

        // 按原始ID顺序收集容量值，并将String转换为Double
        List<Double> doubleList = idList.stream()
                .map(id -> {
                    PBEntity transformer = idToTransformer.get(id);
                    // 处理可能的空值和转换异常
                    if (transformer != null && transformer.getRatedCapacity() != null) {
                        try {
                            return Double.parseDouble(transformer.getRatedCapacity());
                        } catch (NumberFormatException e) {
                            // 处理格式错误，返回默认值或记录日志
                            System.err.println("Invalid capacity format for ID " + id + ": " + transformer.getRatedCapacity());
                            return 0.0;
                        }
                    }
                    return 0.0;
                })
                .collect(Collectors.toList());

        return doubleList;
    }


    /**
     * 根据设备ID和类型列表查询设备信息
     *
     * @return 设备信息列表（保持原始顺序）
     */
    public List<PBEntity> selectDevice(List<String> idList) {
        List<PBEntity> specializedTransformers = pbMapper.selectTransformersById(
                idList
        );
        // 创建ID到transformer的映射（保持原始顺序）
        Map<String, PBEntity> idToTransformer = new LinkedHashMap<>();
        for (PBEntity transformer : specializedTransformers) {
            idToTransformer.put(transformer.getPsrId(), transformer);
        }

        // 按原始ID顺序重新构建列表
        return idList.stream()
                .map(id -> idToTransformer.getOrDefault(id, null)) // 直接使用id作为键
                .filter(Objects::nonNull) // 过滤可能不存在的ID对应的null值
                .collect(Collectors.toList());
    }


    /**
     * 根据设备id和设备类型查找设备坐标(目前是查抄wlgt和0202、0203)
     *
     * @param
     * @return 容量
     */
    public List<Double> selectDeviceCoords(String psrId, String psrType) {
        List<Double> coordsList = new ArrayList<>();

        //wlgt坐标
        if (POLE_TYPES.contains(psrType)) {
            String coords = deviceMapper.selectWLGTCoords(psrId);
            if (StringUtils.isNotBlank(coords)) {
                coordsList.addAll(CoordinateConverter.parseCommaCoordinates(coords, ","));
            }
        }
        //0202、0303的坐标
        if (JUNCTION_TYPES.contains(psrType)) {
            String coords = deviceMapper.selectSDDLCoords(psrId);
            if (StringUtils.isNotBlank(coords)) {
                coordsList.addAll(CoordinateConverter.parseCommaCoordinates(coords, ","));
            }
        }


        return coordsList;
    }

    /**
     * 根据中压用户接入点id查询其对应容量
     *
     * @param joinEcList
     * @return 容量
     */
    public List<DeviceAccessPointBo> selectJoinEcRatedCapacity(List<String> joinEcList) {
        List<DeviceAccessPointBo> deviceAccessPointList = deviceMapper.selectJoinEcCapacity(joinEcList);
        for (DeviceAccessPointBo accessPoint : deviceAccessPointList) {
            //如果是空则返回0
            if (StringUtils.isBlank(accessPoint.getCapacity())) {
                accessPoint.setCapacity("0");
            }
            //如果是双容量只取第一个容量
            accessPoint.setCapacity(isTwoCapacity(accessPoint.getCapacity()));

        }
        return deviceAccessPointList;
    }

    /**
     * 根据间隔id查询所在母线下所有的间隔id
     *
     * @param breakId
     * @return 容量
     */
    public List<Breaker> selectBreakIds(String breakId) {
        List<Breaker> breakIds = deviceMapper.selectBreakIds(breakId);
        if (CollectionUtils.isNotEmpty(breakIds)) {
            for (Breaker breaker : breakIds) {
                breaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
        }
        return breakIds;
    }


    /**
     * 根据间隔名称判断是否是在用的
     *
     * @param input
     * @return
     */
    public static boolean isValidString(String input) {
        if (input == null) {
            return true; // 或根据需求返回 false
        }
        return !input.contains("预留") && !input.contains("备用");
    }

    /**
     * 判断容量是否是2个
     *
     * @param input
     * @return
     */
    public static String isTwoCapacity(String input) {
        if (input == null) {
            return "0"; // 或根据需求返回 false
        }
        if (input.contains("/")) {
            String[] s = input.split("/");
            return calculation(s[0]);
        }

        return calculation(input);
    }

    //计算容量
    public static String calculation(String input) {
        //带单位就先去掉单位
        if (input.contains("kVA")) {
            String result = input.replaceAll("kVA", "");
            Expression expression = new ExpressionBuilder(result).build();
            return String.valueOf(expression.evaluate());
        }
        //不带单位直接计算
        Expression expression = new ExpressionBuilder(input).build();
        return String.valueOf(expression.evaluate());
    }

}
