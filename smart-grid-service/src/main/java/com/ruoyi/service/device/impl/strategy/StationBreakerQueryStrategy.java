package com.ruoyi.service.device.impl.strategy;

import com.ruoyi.mapper.device.DeviceStationBreakerMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class StationBreakerQueryStrategy extends BaseDeviceQueryStrategy {
    
    @Resource
    private DeviceStationBreakerMapper deviceStationBreakerMapper;
    
    public StationBreakerQueryStrategy() {
        super("0305", "device_station_breaker");
    }
    
    @Override
    public Object queryDevice(String psrId) {
        return deviceStationBreakerMapper.selectByPsrId(psrId);
    }

}