package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceRunTower;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

// 杆塔相关策略
@Component
public class TransmissionTowerQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceRunTowerMapper deviceRunTowerMapper;

    public TransmissionTowerQueryStrategy() {
        super("0103", "device_run_tower");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceRunTower> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceRunTower::getPsrId, psrId);
        return deviceRunTowerMapper.selectOne(queryWrapper);
    }
}