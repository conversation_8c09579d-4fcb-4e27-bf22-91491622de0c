package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceWlgt;
import com.ruoyi.mapper.device.DeviceWlgtMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class IoTTowerQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceWlgtMapper deviceWlgtMapper;

    public IoTTowerQueryStrategy() {
        super("wlgt", "device_wlgt");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceWlgt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceWlgt::getAstId, psrId);
        return deviceWlgtMapper.selectOne(queryWrapper);
    }
}