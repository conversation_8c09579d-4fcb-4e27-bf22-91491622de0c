package com.ruoyi.service.device.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.*;
import com.ruoyi.mapper.device.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 查询站内设备
 * 开关：
 * 1、device_station_load_kg(变电站内负荷开关(0307))
 * 2、device_station_breaker(变电站内断路器(0305)
 * 3、device_station_isolate_kg(变电站内隔离开关(0306)
 * 配变
 * 1、device_station_transformer(配电站内变压器(0302)
 * 2、station_service_transformer(0303)变电站内站用变
 * 母线 1、device_station_generatrix
 */
@Service
public class QueryStationDevService {

    @Autowired
    DeviceStationGeneratrixMapper deviceStationGeneratrixMapper;

    @Autowired
    DeviceStationLoadKgMapper deviceStationLoadKgMapper;

    @Autowired
    DeviceStationBreakerMapper deviceStationBreakerMapper;

    @Autowired
    DeviceStationIsolateKgMapper deviceStationIsolateKgMapper;

    @Autowired
    DeviceUserStationMapper deviceUserStationMapper;

    @Autowired
    KgMapper kgMapper;

    /**
     * 获取用户站房
     */
    public List<DeviceUserStation> getUserStations(List<String> stationIds) {
        if (CollectionUtils.isEmpty(stationIds)) {
            return null;
        }
        LambdaQueryWrapper<DeviceUserStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceUserStation::getPsrId, stationIds);

        return deviceUserStationMapper.selectList(queryWrapper);
    }

    /**
     * 获取站房下的开关
     */
    public List<StationKg> getStationKgs(String feeder, List<String> stationIds) {
        return kgMapper.selectStationKg(feeder, stationIds);
    }

    /**
     * 获取站房下的母线
     */
    public List<DeviceStationGeneratrix> getStationBus(String feeder, List<String> stationIds) {
        LambdaQueryWrapper<DeviceStationGeneratrix> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceStationGeneratrix::getStation, stationIds);
        queryWrapper.eq(DeviceStationGeneratrix::getFeeder, feeder);

        return deviceStationGeneratrixMapper.selectList(queryWrapper);
    }

    /**
     * 获取站房下的变电站内负荷开关(0307)
     */
    public List<DeviceStationLoadKg> getStationLoadKgs(String feeder, List<String> stationIds) {
        LambdaQueryWrapper<DeviceStationLoadKg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceStationLoadKg::getStation, stationIds);
        queryWrapper.eq(DeviceStationLoadKg::getFeeder, feeder);

        return deviceStationLoadKgMapper.selectList(queryWrapper);
    }

    /**
     * 获取站房下的变 变电站内断路器(0305)
     */
    public List<DeviceStationBreaker> getStationBreakers(String feeder, List<String> stationIds) {
        LambdaQueryWrapper<DeviceStationBreaker> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceStationBreaker::getStation, stationIds);
        queryWrapper.eq(DeviceStationBreaker::getFeeder, feeder);

        return deviceStationBreakerMapper.selectList(queryWrapper);
    }

    /**
     * 获取站房下的变 变电站内断路器(0305)
     */
    public List<DeviceStationIsolateKg> getStationIsolateKgs(String feeder, List<String> stationIds) {
        LambdaQueryWrapper<DeviceStationIsolateKg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceStationIsolateKg::getStation, stationIds);
        queryWrapper.eq(DeviceStationIsolateKg::getFeeder, feeder);

        return deviceStationIsolateKgMapper.selectList(queryWrapper);
    }

}
