package com.ruoyi.service.device.impl.strategy;

import org.springframework.stereotype.Component;

import java.util.*;

//策略工厂类
@Component
public class DeviceQueryStrategyFactory {

    private final Map<String, DeviceQueryStrategy> strategyMap = new HashMap<>();

    public DeviceQueryStrategyFactory(List<DeviceQueryStrategy> strategies) {
        // 多个psrType对应一个查询策略
        for (DeviceQueryStrategy strategy : strategies) {
            if (strategy.getSupportedPsrType().contains(",")) {
                String[] psrTypes = strategy.getSupportedPsrType().split(",");
                for (String psrType : psrTypes) {
                    strategyMap.put(psrType, strategy);
                }
            } else {
                strategyMap.put(strategy.getSupportedPsrType(), strategy);
            }
        }
    }

    /**
     * 根据PSR类型获取对应的查询策略
     * @param psrType PSR类型
     * @return 查询策略
     */
    public DeviceQueryStrategy getStrategy(String psrType) {
        DeviceQueryStrategy strategy = strategyMap.get(psrType);
        if (strategy == null) {
            throw new UnsupportedOperationException("不支持的设备类型: " + psrType);
        }
        return strategy;
    }

    /**
     * 获取所有支持的PSR类型
     * @return PSR类型列表
     */
    public Set<String> getSupportedPsrTypes() {
        return strategyMap.keySet();
    }
}