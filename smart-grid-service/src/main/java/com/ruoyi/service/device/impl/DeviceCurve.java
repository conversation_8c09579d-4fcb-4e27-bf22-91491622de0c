package com.ruoyi.service.device.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.constant.DeviceCurveType;
import com.ruoyi.entity.device.vo.DeviceCurveVo;
import com.ruoyi.entity.device.vo.DeviceLoadMaxVo;
import com.ruoyi.entity.power.enuw.TimeEnum288;
import com.ruoyi.entity.problem.Statistics;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Component
public class DeviceCurve {
    private final RestTemplate restTemplate;

    public DeviceCurve(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${selectDeviceCurve.path}")
    private String baseUrl;


    /**
     * 返回时间和数值的对应方法
     *
     * @param defectTime 时间
     * @param psrId      设备id
     * @param psrType    设备类型
     * @param type       查询的条件类型（负载率，电流，功率）
     * @return
     * @throws Exception
     */
    public List<DeviceCurveVo> selectTimeAndElectricCurrent(Date defectTime, String psrId, String psrType, String type) throws Exception {
        Map<String, Object> params = getStringStringMap(defectTime, psrId, type, psrType);

        String result = queryEquipPsrData(params);
        HashMap<String, List<Double>> doubleList = dataList(result);
        if (CollectionUtils.isEmpty(doubleList)) {
            return null;
        }

        List<DeviceCurveVo> deviceCurveVos = new ArrayList<>();
        doubleList.forEach((key, value) -> {
            List<Statistics> statisticsList = new ArrayList<>();
            DeviceCurveVo deviceCurveVo = new DeviceCurveVo();
            value = value.stream().map(d -> d == null ? 0.0 : d).collect(Collectors.toList());
            for (int i = 0; i < value.size(); i++) {
                statisticsList.add(new Statistics(TimeEnum288.fromIndex(i).getTimeString(), value.get(i).toString()));
            }
            deviceCurveVo.setStatisticsList(statisticsList);
            deviceCurveVo.setType(DeviceCurveType.getDeviceCurveType(key));
            deviceCurveVo.setName(key);
            deviceCurveVos.add(deviceCurveVo);

        });
        return deviceCurveVos;
    }


    /**
     * 查询设备PSR数据
     *
     * @param params 查询参数Map
     * @return 响应结果字符串
     */
    public String queryEquipPsrData(Map<String, Object> params) {
        HttpRequest request = HttpRequest.get(baseUrl)
                .form("equip_id", params.get("equip_id"))
                .form("grid_type", params.get("grid_type"))
                .form("distribution", "0")
                .form("equip_type",  params.get("equip_type"))
                .form("psrType", params.get("psr_type"))
                .form("meas_codes", params.get("meas_codes"))
                .form("startTime", params.get("startTime"))
                .form("endTime", params.get("endTime"))
                // 设置请求头
                .header("Connection", "keep-alive")
                .header("Accept", "application/json, text/plain, */*")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
                .header("X-Requested-With", "XMLHttpRequest")
                .header("Referer", "http://pms.kjyzt.js.sgcc.com.cn:32080/pms-amap-ui/?F3321F30=c0h8e2n0g2l5h0174284e191a1c8fc0c&73E5D5EC=901932607295902792646111e1a890b7&20C927FF=A6CCF15C6C28E728C5E01F0074C2E00F&4AEAA0EB=D330A5A170A43FF81455FADF556CD003&jsIdsDomain=")
                .header("Accept-Language", "zh-CN,zh;q=0.9")
                .header("Cookie", "auth_token=96dcf9b3d5a4098713055f0544e1998da06c33c5d5111b8689f5ff23a972121f23a983df7b8b4de80448f43f34895d2c1a297c04c0da12b5dab0ba14734d3d601a413a6e2d6836bd4f2581e0e8ae43c0a156e73e9519e10771ee2cbc41ec38907285eaff0b34151ae35c2a5adc4273f5; JSESSIONID=3599E7B75AAF7DD191B4B49F25904E9E; _at=eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI5MTM2Nzk5MjkiLCJpYXQiOjE3NTMzMjcxMzgsInN1YiI6ImNoZW5nbGgxIiwiZXhwIjoxNzUzNDEzNTM4fQ.SDNthNTLPCYCl7N4_5PjWlEAP5I9i444oKSGN7I2BNI; SESSION=a0fd19e6-a595-40c6-a2f3-519f8afe416f");


        HttpResponse response = request.execute();
//        HttpResponse response = HttpUtil.createGet(baseUrl).execute();
        String body = response.body();
        // 获取结果集
        if (response.isOk()) {
            // 获取data的结果集
            return body;
        }
        return "";

    }


    /**
     * 将输入的日期字符串转换为当天的开始和结束时间
     *
     * @return 包含开始时间和结束时间的字符串数组
     * @throws IllegalArgumentException 如果输入日期格式不正确
     */
    public static String[] getDayTimeRange(Date date) {
        // 将Date转换为LocalDate
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 生成当天的开始时间（00:00:00）
        String startTime = localDate + "+00:00:00";

        // 生成当天的结束时间（23:59:59）
        String endTime = localDate + "+23:59:59";

        return new String[]{startTime, endTime};
    }


    /**
     * 解析json数据，获取负载率、电流、功率数据
     *
     * @param jsonString
     * @return
     * @throws Exception
     */

    public HashMap<String, List<Double>> dataList(String jsonString) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(jsonString);
        HashMap<String, List<Double>> resultMap = new HashMap<>();

        // 获取series数组
        JsonNode seriesArray = rootNode.path("result").path("series");

        for (JsonNode seriesNode : seriesArray) {
            // 获取name和data
            String name = seriesNode.path("name").asText();
            JsonNode dataNode = seriesNode.path("data");

            // 将data节点转换为List<Double>
            List<Double> dataList = mapper.convertValue(dataNode, mapper.getTypeFactory().constructCollectionType(List.class, Double.class));

            resultMap.put(name, dataList);
        }

        return resultMap;
    }

    /**
     * 查询实体
     *
     * @param problem
     * @param feeder
     * @param LoadRate
     * @param psrType
     * @return
     */
    @NotNull
    public Map<String, Object> getStringStringMap(Date problem, String feeder, String LoadRate, String psrType) {
        String[] timeRange = getDayTimeRange(problem);
        //计算负载率
        Map<String, Object> params = new HashMap<>();
        params.put("equip_id", feeder);
        params.put("grid_type", psrType);
        params.put("distribution", "0");
        params.put("equip_type", psrType);
        params.put("psr_type", psrType);
        params.put("meas_codes", LoadRate);
        params.put("startTime", timeRange[0]);
        params.put("endTime", timeRange[1]);
        return params;
    }

    /**
     * 根据时间范围查询设备负载率每天的最大值
     *
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param deviceId   设备ID
     * @param deviceType 设备类型
     * @return 时间范围内每天的负载率最大值列表，找不到数据时返回0
     * @throws Exception
     */
    public List<DeviceLoadMaxVo> selectDeviceLoadMaxValuesByDateRange(Date startDate, Date endDate, String deviceId, String deviceType) throws Exception {
        List<DeviceLoadMaxVo> resultList = new ArrayList<>();
        // 查询负载率指标
        String queryTypes = DeviceCurveType.Load;

        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 遍历日期范围内的每一天
        for (LocalDate currentDate = start; !currentDate.isAfter(end); currentDate = currentDate.plusDays(1)) {
            String dateStr = currentDate.toString();
            Double maxValue = 0.0;
            try {
                Date currentDateAsDate = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                // 查询当天的设备曲线数据
                List<DeviceCurveVo> dailyData = selectTimeAndElectricCurrent(currentDateAsDate, deviceId, deviceType, queryTypes);
                if (dailyData != null && !dailyData.isEmpty()) {
                    // 计算当天负载率的最大值
                    maxValue = calculateDailyLoadMaxValue(dailyData);
                }
            } catch (Exception e) {
                maxValue = 0.0;
            }
            resultList.add(new DeviceLoadMaxVo(dateStr, maxValue));
        }

        return resultList;
    }

    /**
     * 计算每日负载率数据的最大值
     *
     * @param dailyData 当日的设备曲线数据
     * @return 当日负载率的最大值，如果没有数据返回0.0
     */
    private Double calculateDailyLoadMaxValue(List<DeviceCurveVo> dailyData) {
        if (dailyData == null || dailyData.isEmpty()) {
            return 0.0;
        }

        // 查找负载率数据
        for (DeviceCurveVo curveVo : dailyData) {
            if (curveVo.getStatisticsList() != null && !curveVo.getStatisticsList().isEmpty()) {
                // 找出负载率的最大值
                double maxValue = curveVo.getStatisticsList().stream().mapToDouble(stat -> {
                    try {
                        return Double.parseDouble(stat.getTypeNum());
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                }).max().orElse(0.0);

                return maxValue;
            }
        }
        return 0.0;
    }

}
