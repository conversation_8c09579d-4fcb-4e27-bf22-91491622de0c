package com.ruoyi.service.device.impl;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.utils.NodeUtils;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用于拓扑分段服务
 */
@Service
public class SegBranchServiceImpl {

    /**
     * 获取主干路径的第一个分段
     *
     * @param nodePath       路径分析对象
     * @param deviceId       主干路径的开始节点
     * @param kgContactNodes 联络开关节点集合
     */
    public SegBetween getMainSegBetween(NodePath nodePath, String deviceId, List<Node> kgContactNodes) {
        return getMainSegBetweenList(nodePath, deviceId, kgContactNodes).get(0);
    }

    /**
     * 获取主干路径的所有分段
     *
     * @param nodePath       路径分析对象
     * @param deviceId       主干路径的开始节点
     * @param kgContactNodes 联络开关节点集合
     */
    public List<SegBetween> getMainSegBetweenList(NodePath nodePath, String deviceId, List<Node> kgContactNodes) {
        if (StringUtil.isBlank(deviceId)) {
            return null;
        }
        HashMap<String, List<SegBetween>> segBetweenGroupMap = nodePath.getSegBetweenGroupMap();
        // TODO 这里我们暂时取第0个后续 后续应该要先校核问题  并且明确确定那一段
        List<SegBetween> segBetweens = segBetweenGroupMap.get(deviceId);
        if (segBetweens == null || segBetweens.isEmpty()) {
            return null;
        }
        if (!CollectionUtils.isEmpty(kgContactNodes)) {
            // 有联络开关的放在后面
            segBetweens.sort((a, b) -> {
                int num1 = kgContactNodes.stream().anyMatch(n -> n.equals(a.getStartNodeId()) || n.equals(a.getEndNodeId())) ? 1 : 3;
                int num2 = kgContactNodes.stream().anyMatch(n -> n.equals(b.getStartNodeId()) || n.equals(b.getEndNodeId())) ? 1 : 3;
                return num2 - num1;
            });
        }

        return segBetweens;
    }

    /**
     * 获取主分支路径的分支（由于只有设备ID没有确定的方向，我们这里按照自己逻辑确定）
     *
     * @param nodePath 路径分析对象
     * @param deviceId 主干路径的开始节点
     */
    public BranchNode getMainBranch(NodePath nodePath, String deviceId) {
        if (StringUtil.isBlank(deviceId)) {
            return null;
        }
        Collection<BranchNode> allBranchNodes = nodePath.getBranchNodeMap().values();
        // 先通过起始节点过滤查询
        List<BranchNode> branchNodes = allBranchNodes.stream().filter(n -> StringUtils.equals(deviceId, n.getPsrId())).collect(Collectors.toList());

        // 说明大分子的问题在节点里面我们得深度查找
        if (CollectionUtils.isEmpty(branchNodes)) {
            branchNodes = allBranchNodes.stream().filter(n ->
                    n.getNodes().stream().anyMatch(a -> StringUtils.equals(deviceId, a.getPsrId()))
            ).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(branchNodes)) {
            return null;
        }

        // 由于pms3.0问题管理库的数据只给一个设备ID 没法确定是那一段  我们这里通过排序去做处理
        // 主干路径开始的优先 其次配变数量多（TODO 按道理应该总容量和负荷大小，这个后续再说）
        branchNodes.sort((branch1, branch2) -> getBigSortTotal(branch1) - getBigSortTotal(branch2));

        return branchNodes.get(0);
    }


    private int getBigSortTotal(BranchNode branchNode) {
        int total = branchNode.getPbNodes().size();
        if (branchNode.isMain()) {
            total = total + 10000;
        }
        return total;
    }
}
