package com.ruoyi.service.device.impl.strategy;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

// 设备查询服务类
@Service
public class DeviceQueryService {
    
    @Resource
    private DeviceQueryStrategyFactory strategyFactory;
    
    /**
     * 根据PSR类型和设备ID查询设备信息
     * @param psrType PSR类型
     * @param psrId 设备ID
     * @return 设备信息
     */
    public Object queryDevice(String psrType, String psrId) {
        try {
            DeviceQueryStrategy strategy = strategyFactory.getStrategy(psrType);
            return strategy.queryDevice(psrId);
        } catch (Exception e) {
            throw new RuntimeException("查询设备信息失败: psrType=" + psrType + ", psrId=" + psrId, e);
        }
    }

}