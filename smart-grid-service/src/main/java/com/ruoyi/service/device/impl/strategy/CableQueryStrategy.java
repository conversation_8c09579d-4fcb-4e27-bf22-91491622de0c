package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeederCable;
import com.ruoyi.mapper.device.DeviceFeederCableMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

// 具体策略实现类 - 导线段相关
@Component
public class CableQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceFeederCableMapper deviceFeederCableMapper;

    public CableQueryStrategy() {
        super("0201", "device_feeder_cable");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceFeederCable> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFeederCable::getPsrId, psrId);
        return deviceFeederCableMapper.selectOne(queryWrapper);
    }

}