package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeederJk;
import com.ruoyi.mapper.device.DeviceFeederJkMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OverheadLineQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceFeederJkMapper deviceFeederJkMapper;

    public OverheadLineQueryStrategy() {
        super("dxd", "device_feeder_jk");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceFeederJk> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFeederJk::getPsrId, psrId);
        return deviceFeederJkMapper.selectOne(queryWrapper);
    }

}