package com.ruoyi.service.device.impl.strategy;

// 抽象基础查询策略类
public abstract class BaseDeviceQueryStrategy implements DeviceQueryStrategy {
    protected final String tableName;
    protected final String psrType;
    
    public BaseDeviceQueryStrategy(String psrType, String tableName) {
        this.psrType = psrType;
        this.tableName = tableName;
    }
    
    @Override
    public String getSupportedPsrType() {
        return this.psrType;
    }

}