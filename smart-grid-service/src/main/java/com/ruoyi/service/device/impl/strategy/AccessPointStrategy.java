package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceAccessPoint;
import com.ruoyi.mapper.device.DeviceAccessPointMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AccessPointStrategy extends BaseDeviceQueryStrategy {


    @Resource
    private DeviceAccessPointMapper deviceAccessPointMapper;

    public AccessPointStrategy() {
        super("370000", "device_access_point");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceAccessPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceAccessPoint::getPsrId, psrId);
        return deviceAccessPointMapper.selectOne(queryWrapper);
    }
}