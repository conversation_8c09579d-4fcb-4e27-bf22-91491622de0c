package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceStationTransformer;
import com.ruoyi.mapper.device.DeviceStationTransformerMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

// 5. 配变相关策略
@Component
public class StationTransformerQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DeviceStationTransformerMapper deviceStationTransformerMapper;

    public StationTransformerQueryStrategy() {
        super("0302", "device_station_transformer");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DeviceStationTransformer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceStationTransformer::getPsrId, psrId);
        return deviceStationTransformerMapper.selectOne(queryWrapper);
    }

}