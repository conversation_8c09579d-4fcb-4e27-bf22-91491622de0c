package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DevicePoleBreak;
import com.ruoyi.mapper.device.DevicePoleBreakMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DevicePoleBreakStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DevicePoleBreakMapper devicePoleBreakMapper;

    public DevicePoleBreakStrategy() {
        super("0112,0111", "device_pole_break");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DevicePoleBreak> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevicePoleBreak::getPsrId, psrId);
        return devicePoleBreakMapper.selectOne(queryWrapper);
    }
}