package com.ruoyi.service.device.impl.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DevicePoleTransformer;
import com.ruoyi.mapper.device.DevicePoleTransformerMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PoleTransformerQueryStrategy extends BaseDeviceQueryStrategy {

    @Resource
    private DevicePoleTransformerMapper devicePoleTransformerMapper;

    public PoleTransformerQueryStrategy() {
        super("0110", "device_pole_transformer");
    }

    @Override
    public Object queryDevice(String psrId) {
        LambdaQueryWrapper<DevicePoleTransformer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DevicePoleTransformer::getPsrId, psrId);
        return devicePoleTransformerMapper.selectOne(queryWrapper);
    }

}