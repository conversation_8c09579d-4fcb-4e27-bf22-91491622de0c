package com.ruoyi.service.calc;

import com.ruoyi.bo.CalcRecycleRuleBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.vo.CalcRecycleRuleVo;

import java.util.Collection;
import java.util.List;

/**
 * 计算实例回收规则Service接口
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
public interface ICalcRecycleRuleService {

    /**
     * 查询计算实例回收规则
     */
    CalcRecycleRuleVo queryById(Long id);

    /**
     * 查询计算实例回收规则列表
     */
    TableDataInfo<CalcRecycleRuleVo> queryPageList(CalcRecycleRuleBo bo, PageQuery pageQuery);

    /**
     * 查询计算实例回收规则列表
     */
    List<CalcRecycleRuleVo> queryList(CalcRecycleRuleBo bo);

    /**
     * 新增计算实例回收规则
     */
    Boolean insertByBo(List<CalcRecycleRuleBo> bo);

    /**
     * 修改计算实例回收规则
     */
    Boolean updateByBo(CalcRecycleRuleBo bo);

    /**
     * 校验并批量删除计算实例回收规则信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
