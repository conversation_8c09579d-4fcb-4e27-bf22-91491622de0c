package com.ruoyi.service.calc;

import com.ruoyi.bo.ZhEpsAlarmBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.EpsEvent;
import com.ruoyi.vo.AlarmHistoryVo;
import com.ruoyi.vo.AlarmStatisticsVo;
import com.ruoyi.vo.ZhEpsAlarmVo;
import vo.ResultVO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AlarmService {
    /**
     * 网格告警事件列表
     */
    ResultVO listEvent(Long alarmId);
    /**
     * 网格告警配置列表
     */
    ResultVO listConfig();

    TableDataInfo<ZhEpsAlarmVo> queryPageList(ZhEpsAlarmBo bo, PageQuery pageQuery);

    AlarmStatisticsVo statistics(ZhEpsAlarmBo bo);

    List<EpsEvent> detailList(Long alarmId);

    Date lastCalcTime();

    List<Map<String, Object>> equipmentList(String grid);

    int processed(Long alarmId);

    TableDataInfo<ZhEpsAlarmVo> historyList(ZhEpsAlarmBo bo, PageQuery pageQuery);

    boolean deleteWithValidById(Integer id);

    TableDataInfo<ZhEpsAlarmVo> periodList(ZhEpsAlarmBo bo, PageQuery pageQuery);

    List<Map<String, Object>> historyStatistics(Integer dimensionality);

    List<AlarmHistoryVo> queryHistoryList(LocalDate startTime, LocalDate endTime);

    R curveListById(String id);

    R curveListByCaId(String caId);
}
