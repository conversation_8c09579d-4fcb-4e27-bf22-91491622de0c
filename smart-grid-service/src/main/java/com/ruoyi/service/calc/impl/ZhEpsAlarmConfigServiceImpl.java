package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.ZhEpsAlarmConfigBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.EpsAlarmConfig;
import com.ruoyi.mapper.calc.ZhEpsAlarmConfigMapper;
import com.ruoyi.service.calc.IZhEpsAlarmConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警信息Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-09-27
 */
@RequiredArgsConstructor
@Service
public class ZhEpsAlarmConfigServiceImpl implements IZhEpsAlarmConfigService {
    private final ZhEpsAlarmConfigMapper baseMapper;

    /**
     * 查询告警信息
     */
    @Override
    public EpsAlarmConfig queryById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询告警信息列表
     */
    @Override
    public TableDataInfo<EpsAlarmConfig> queryPageList(ZhEpsAlarmConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EpsAlarmConfig> lqw = buildQueryWrapper(bo);
        Page<EpsAlarmConfig> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询告警信息列表
     */
    @Override
    public List<EpsAlarmConfig> queryList(ZhEpsAlarmConfigBo bo) {
        LambdaQueryWrapper<EpsAlarmConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    @Override
    public boolean updateByBo(ZhEpsAlarmConfigBo bo) {
        EpsAlarmConfig update = BeanUtil.toBean(bo, EpsAlarmConfig.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateBatchByBoList(List<ZhEpsAlarmConfigBo> bo) {
        if (CollectionUtil.isEmpty(bo)) {
            return true;
        }
        List<EpsAlarmConfig> batchUpdateList = bo.stream().map(item -> BeanUtil.toBean(item, EpsAlarmConfig.class)).collect(Collectors.toList());
        return baseMapper.updateBatchById(batchUpdateList);
    }



    private LambdaQueryWrapper<EpsAlarmConfig> buildQueryWrapper(ZhEpsAlarmConfigBo bo) {
        LambdaQueryWrapper<EpsAlarmConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), EpsAlarmConfig::getDescription, bo.getDescription());
        lqw.eq(bo.getLimitValue() != null, EpsAlarmConfig::getLimitValue, bo.getLimitValue());
        return lqw;
    }
}
