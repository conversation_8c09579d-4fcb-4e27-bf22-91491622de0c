package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.entity.calc.*;
import com.ruoyi.mapper.calc.*;
import com.ruoyi.service.calc.TrendCalcService;
import com.ruoyi.service.electricity.impl.ElectricityAnalysisServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrendCalcServiceImpl implements TrendCalcService {

    @Value("${znap-service.powerRangeUrl}")
    private String powerRangeUrl = "";

    @Value("${znap-service.calcUrl}")
    private String calcUrl = "";


    private static final String groupId = "nanjing";

    private final PmsZhTelemetryValueMapper pmsZhTelemetryValueMapper;

    private final TrendCalcMapper trendCalcMapper;

    private final CalcInstanceInfoMapper calcInstanceInfoMapper;

    private final CalcRelationShipMapper calcRelationShipMapper;

    private final CalcAlarmRuleMapper calcAlarmRuleMapper;

    private final CalcAlarmInfoMapper calcAlarmInfoMapper;
    private final ZhTransferInfoMapper zhTransferInfoMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    private final PbCurveTemplateMapper pbCurveTemplateMapper;
    ObjectMapper objectMapper = new ObjectMapper();


    @Override
    @Transactional
    public R<?> feederCalc(Map<String, Object> params) {
        // 1. 参数验证
        String feederId = validateAndGetStringParam(params, "feederId", "馈线id不能为空");
        String date = validateAndGetStringParam(params, "date", "时间不能为空");
        Long problemId = validateAndGetLongParam(params, "problemId", "问题id不能为空");

        try {
            // 2. 构建计算参数
            CalcParams calcParams = buildCalcParams(feederId);

            // 3. 获取设备数据
            Map<String, List<String>> loadEquipment = buildMapPsrIds(feederId, 209);
            Map<String, List<String>> distributionEquipment = buildMapPsrIds(feederId, 208);

            // 4. 验证是否存在曲线数据
            if (!hasCurveData(loadEquipment, distributionEquipment, date)) {
                return R.fail("当前日期没有找到曲线数据");
            }

            // 5. 处理负荷设备数据
            processLoadEquipment(calcParams, loadEquipment, date, feederId);

            // 6. 处理配变设备数据
            processDistributionEquipment(calcParams, distributionEquipment, date, feederId);

            // 7. 验证设备数据完整性
            if (CollectionUtil.isEmpty(loadEquipment) && CollectionUtil.isEmpty(distributionEquipment)) {
                log.info("当前馈线：{}，查询不到对应配变设备和负荷设备跳过计算", feederId);
                return R.fail("当前馈线查询不到对应配变设备和负荷设备");
            }

            // 8. 设置电源源数据
            setCalcSource(calcParams, feederId);

            // 9. 执行计算处理结果
            return executeCalculationAndSaveResult(calcParams, params, problemId, date, feederId);

        } catch (Exception e) {
            log.error("馈线计算异常，feederId: {}, date: {}", feederId, date, e);
            return R.fail("计算执行失败: " + e.getMessage());
        }
    }

    /**
     * 验证并获取字符串参数
     */
    private String validateAndGetStringParam(Map<String, Object> params, String key, String errorMsg) {
        Object value = params.get(key);
        if (value == null || StringUtils.isEmpty(value.toString())) {
            throw new RuntimeException(errorMsg);
        }
        return value.toString();
    }

    /**
     * 验证并获取Long参数
     */
    private Long validateAndGetLongParam(Map<String, Object> params, String key, String errorMsg) {
        Object value = params.get(key);
        if (value == null) {
            throw new RuntimeException(errorMsg);
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            throw new RuntimeException(errorMsg + "，格式不正确");
        }
    }

    /**
     * 构建计算参数
     */
    private CalcParams buildCalcParams(String feederId) {
        CalcParams calcParams = new CalcParams();
        calcParams.setGroupId(groupId);
        calcParams.setVec_para_gen(new ArrayList<>());
        calcParams.setVec_run_gen(new ArrayList<>());
        calcParams.setVec_run_ld(new ArrayList<>());
        calcParams.setVec_run_dt(new ArrayList<>());
        calcParams.setIs_pf(1);
        calcParams.setIs_sc(1);
        calcParams.setFeeder_id(feederId);
        return calcParams;
    }

    /**
     * 检查是否存在曲线数据
     */
    private boolean hasCurveData(Map<String, List<String>> loadEquipment, Map<String, List<String>> distributionEquipment, String date) {
        // 检查负荷设备曲线数据
        if (CollectionUtil.isNotEmpty(loadEquipment)) {
            for (List<String> psrIds : loadEquipment.values()) {
                if (hasCurveDataForPsrIds(psrIds, date)) {
                    return true;
                }
            }
        }

        // 检查配变设备曲线数据
        if (CollectionUtil.isNotEmpty(distributionEquipment)) {
            for (List<String> psrIds : distributionEquipment.values()) {
                if (hasCurveDataForPsrIds(psrIds, date)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查指定PSR IDs是否存在曲线数据
     */
    private boolean hasCurveDataForPsrIds(List<String> psrIds, String date) {
        if (CollectionUtil.isEmpty(psrIds)) {
            return false;
        }
        LambdaQueryWrapper<PbCurveTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PbCurveTemplate::getPsrId, psrIds).eq(PbCurveTemplate::getDate, date).select(PbCurveTemplate::getDate);

        List<PbCurveTemplate> templates = pbCurveTemplateMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(templates);
    }

    /**
     * 处理负荷设备数据
     */
    private void processLoadEquipment(CalcParams calcParams, Map<String, List<String>> loadEquipment, String date, String feederId) {
        if (CollectionUtil.isNotEmpty(loadEquipment)) {
            for (Map.Entry<String, List<String>> entry : loadEquipment.entrySet()) {
                List<VecRunDomain> vecRunDomains = queryPbCurve(entry.getValue(), entry.getKey(), date);
                calcParams.getVec_run_ld().addAll(vecRunDomains);
            }
        } else {
            log.info("当前馈线：{}，查询不到对应负荷设备", feederId);
        }
    }

    /**
     * 处理配变设备数据
     */
    private void processDistributionEquipment(CalcParams calcParams, Map<String, List<String>> distributionEquipment, String date, String feederId) {
        if (CollectionUtil.isNotEmpty(distributionEquipment)) {
            for (Map.Entry<String, List<String>> entry : distributionEquipment.entrySet()) {
                List<VecRunDomain> vecRunDomains = queryPbCurve(entry.getValue(), entry.getKey(), date);
                calcParams.getVec_run_dt().addAll(vecRunDomains);
            }
        } else {
            log.info("当前馈线：{}，查询不到对应配变设备", feederId);
        }
    }

    /**
     * 设置电源源数据
     */
    private void setCalcSource(CalcParams calcParams, String feederId) {
        Map<Object, Object> source = MapUtil.builder().put("id", feederId).put("vec_v_kv", mrKvValue()).put("vec_va", mrVaValue()).build();
        calcParams.setVec_run_source(Collections.singletonList(source));
    }

    /**
     * 执行计算并保存结果
     */
    private R<?> executeCalculationAndSaveResult(CalcParams calcParams, Map<String, Object> params, Long problemId, String date, String feederId) {
        try {
            // 执行计算
            String result = HttpUtils.postBody(calcUrl, null, calcParams);
            JSONObject resultJson = JSONUtil.parseObj(result);

            String msgId = Optional.ofNullable(resultJson.get("msgId")).map(Object::toString).orElse(null);

            if (StringUtils.isEmpty(msgId)) {
                log.error("计算失败，未返回msgId，feederId: {}, result: {}", feederId, result);
                return R.fail("计算失败，未返回消息ID");
            }

            log.info("当前馈线：{}，计算成功，消息id：{}", feederId, msgId);

            // 保存计算实例信息
            CalcInstanceInfo calcInstanceInfo = saveCalcInstanceInfo(problemId, date);

            // 保存告警规则
            saveAlarmRules(params, calcInstanceInfo.getInstanceId());

            // 保存实例关系
            saveCalcRelationShip(calcInstanceInfo.getInstanceId(), msgId);

            return R.ok("消息发送成功", calcInstanceInfo);

        } catch (IOException e) {
            log.error("计算请求异常，feederId: {}", feederId, e);
            return R.fail("计算请求失败");
        }
    }

    /**
     * 保存计算实例信息
     */
    private CalcInstanceInfo saveCalcInstanceInfo(Long problemId, String date) {
        CalcInstanceInfo calcInstanceInfo = new CalcInstanceInfo();
        calcInstanceInfo.setState(0);
        calcInstanceInfo.setStartTime(new Date());
        calcInstanceInfo.setEndTime(null);
        calcInstanceInfo.setIsAuto(0);
        calcInstanceInfo.setIsRecycle(0);
        calcInstanceInfo.setInstanceId(buildInstanceId("TEST"));
        calcInstanceInfo.setProblemId(problemId);
        calcInstanceInfo.setDate(date);

        calcInstanceInfoMapper.insert(calcInstanceInfo);
        return calcInstanceInfo;
    }

    /**
     * 保存告警规则
     */
    private void saveAlarmRules(Map<String, Object> params, String instanceId) {
        Object calcWarnRuleObj = params.get("calcWarnRule");
        if (calcWarnRuleObj != null) {
            try {
                List<CalcAlarmRule> calcWarnRules = JsonUtils.parseArray(JSONUtil.parseArray(calcWarnRuleObj).toString(), CalcAlarmRule.class);

                List<CalcAlarmRule> updatedRules = calcWarnRules.stream().map(item -> {
                    item.setInstanceId(instanceId);
                    return item;
                }).collect(Collectors.toList());

                calcAlarmRuleMapper.insertBatch(updatedRules);
            } catch (Exception e) {
                log.error("保存告警规则失败，instanceId: {}", instanceId, e);
            }
        }
    }

    /**
     * 保存计算实例关系
     */
    private void saveCalcRelationShip(String instanceId, String msgId) {
        CalcRelationShip calcRelationShip = new CalcRelationShip();
        calcRelationShip.setInstanceId(instanceId);
        calcRelationShip.setMsgId(msgId);

        calcRelationShipMapper.insertBatch(Collections.singletonList(calcRelationShip));
    }

    @Override
    public R<?> calcSimulation(Map<String, Object> params) {

        if (params.get("instanceId") != null) {
            String instanceId = params.get("instanceId").toString();
            // 重新计算
            // 删除之前的实例关系数据
            LambdaQueryWrapper<CalcRelationShip> calcRelationShipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcRelationShipLambdaQueryWrapper.eq(CalcRelationShip::getInstanceId, instanceId);
            calcRelationShipMapper.delete(calcRelationShipLambdaQueryWrapper);
            // 删除之前的实例主表
            LambdaQueryWrapper<CalcInstanceInfo> calcInstanceInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcInstanceInfoLambdaQueryWrapper.eq(CalcInstanceInfo::getInstanceId, instanceId);
            calcInstanceInfoMapper.delete(calcInstanceInfoLambdaQueryWrapper);
            // 删除之前的告警信息
            LambdaQueryWrapper<CalcAlarmInfo> calcAlarmInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcAlarmInfoLambdaQueryWrapper.eq(CalcAlarmInfo::getInstanceId, instanceId);
            calcAlarmInfoMapper.delete(calcAlarmInfoLambdaQueryWrapper);
            // 删除之前的转供信息
            LambdaQueryWrapper<ZhTransferInfo> zhTransferInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            zhTransferInfoLambdaQueryWrapper.eq(ZhTransferInfo::getCalcId, instanceId);
            zhTransferInfoMapper.delete(zhTransferInfoLambdaQueryWrapper);
            // 删除之前的规则信息
            LambdaQueryWrapper<CalcAlarmRule> calcAlarmRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcAlarmRuleLambdaQueryWrapper.eq(CalcAlarmRule::getInstanceId, instanceId);
            calcAlarmRuleMapper.delete(calcAlarmRuleLambdaQueryWrapper);
        }
        if (params.get("gridCode") == null) {
            throw new RuntimeException("网格编码不能为空");
        }
        String date = params.get("date").toString();
        if (StringUtils.isEmpty(date)) {
            throw new RuntimeException("时间不能为空");
        }
        if (params.get("problemId") == null) {
            throw new RuntimeException("问题id不能为空");
        }
        // 查询网格所属线路
        List<ZhDmsGridFeeder> zhDmsGridFeeders = trendCalcMapper.queryFeederByGridCode(params.get("gridCode").toString());
        if (CollectionUtil.isEmpty(zhDmsGridFeeders)) {
            throw new RuntimeException("该网格所属馈线为空无法计算");
        }
        long problemId = Long.parseLong(params.get("problemId").toString());

        // 构建计算实例
        CalcInstanceInfo calcInstanceInfo = new CalcInstanceInfo();
        calcInstanceInfo.setGridCode(zhDmsGridFeeders.get(0).getPowerGridCode());
        calcInstanceInfo.setGridName(zhDmsGridFeeders.get(0).getPowerGridName());
        calcInstanceInfo.setState(0);
        calcInstanceInfo.setStartTime(new Date());
        calcInstanceInfo.setEndTime(null);
        calcInstanceInfo.setIsAuto(0);
        calcInstanceInfo.setIsRecycle(0);
        calcInstanceInfo.setInstanceId(buildInstanceId(zhDmsGridFeeders.get(0).getPowerGridCode()));
        calcInstanceInfo.setProblemId(problemId);
        // 新增实例记录
        calcInstanceInfoMapper.insert(calcInstanceInfo);

        if (params.get("calcWarnRule") != null) {
            List<CalcAlarmRule> calcWarnRules = JsonUtils.parseArray(JSONUtil.parseArray(params.get("calcWarnRule")).toString(), CalcAlarmRule.class);
            calcWarnRules = calcWarnRules.stream().map(item -> {
                item.setInstanceId(calcInstanceInfo.getInstanceId());
                return item;
            }).collect(Collectors.toList());
            calcAlarmRuleMapper.insertBatch(calcWarnRules);
        }

        // 构建计算参数
        CalcParams calcParams = new CalcParams();
        calcParams.setGroupId(groupId);
        calcParams.setVec_para_gen(new ArrayList<>());
        calcParams.setVec_run_gen(new ArrayList<>());
        calcParams.setIs_pf(1);
        calcParams.setIs_sc(1);

        // 存放实例关系
        List<CalcRelationShip> calcRelationShips = new ArrayList<>();
        List<String> feederIds = zhDmsGridFeeders.stream().map(ZhDmsGridFeeder::getFeeder).collect(Collectors.toList());

        for (String feederId : feederIds) {
            calcParams.setFeeder_id(feederId);
            // 循环计算馈线
            // 查询馈线负荷以及其特征曲线
            List<String> loadPsrIds = buildPsrIds(feederId, 209);
            List<VecRunDomain> ldVecRunDomains = new ArrayList<>();
            if (CollectionUtil.isEmpty(loadPsrIds)) {
                log.info("当前馈线：{}，查询不到对应负荷设备", feederId);
            } else {
                ldVecRunDomains = queryPbCurve(loadPsrIds, "370000", date);
            }
            calcParams.setVec_run_ld(ldVecRunDomains);
            List<String> pbPsrIds = buildPsrIds(feederId, 208);

            List<VecRunDomain> pbVecRunDomains = new ArrayList<>();
            if (CollectionUtil.isEmpty(pbPsrIds)) {
                log.info("当前馈线：{}，查询不到对应配变设备", feederId);
            } else {
                pbVecRunDomains = queryPbCurve(pbPsrIds, "0110", date);

            }
            calcParams.setVec_run_dt(pbVecRunDomains);
            if (CollectionUtil.isEmpty(pbPsrIds) && CollectionUtil.isEmpty(loadPsrIds)) {
                log.info("当前馈线：{}，查询不到对应配变设备和负荷设备跳过计算", feederId);
                continue;
            }

            // 电源参数
            // 默认将馈线的开始作为电源
            Map<Object, Object> source = MapUtil.builder().put("id", feederId).put("vec_v_kv", mrKvValue()).put("vec_va", mrVaValue()).build();
            calcParams.setVec_run_source(Collections.singletonList(source));
            String result = null;
            try {
                result = HttpUtils.postBody(calcUrl, null, calcParams);
            } catch (IOException e) {
                log.error(e.getMessage());
            }
            JSONObject resultJson = JSONUtil.parseObj(result);
            if (resultJson.get("msgId") != null) {
                String msgId = resultJson.get("msgId").toString();
                log.info("当前馈线：{}，计算成功，消息id：{}", feederId, msgId);
                CalcRelationShip calcRelationShip = new CalcRelationShip();
                calcRelationShip.setInstanceId(calcInstanceInfo.getInstanceId());
                calcRelationShip.setMsgId(msgId);
                calcRelationShips.add(calcRelationShip);
            }
        }
        // 新增实例关系
        calcRelationShipMapper.insertBatch(calcRelationShips);

        return R.ok("消息发送成功", calcInstanceInfo);
    }


    /**
     * 查询负荷、配变曲线
     *
     * @param pbPsrIds psrId列表
     * @param psrType  设备类型(37000、0110、0302、0303等)
     * @return 曲线数据列表
     */
    private List<VecRunDomain> queryPbCurve(List<String> pbPsrIds, String psrType, String date) {
        List<VecRunDomain> vecRunDomains = new ArrayList<>();

        for (String pbPsrId : pbPsrIds) {
            VecRunDomain vecRunDomain = buildVecRunDomain(pbPsrId, psrType, date);
            vecRunDomains.add(vecRunDomain);
        }

        return vecRunDomains;
    }

    /**
     * 构建单个VecRunDomain对象
     */
    private VecRunDomain buildVecRunDomain(String pbPsrId, String psrType, String date) {
        VecRunDomain vecRunDomain = new VecRunDomain();
        vecRunDomain.setId(pbPsrId);

        // 处理特殊类型(370000和0303)的随机数据
        if (isSpecialPsrType(psrType)) {
            return handleSpecialType(vecRunDomain, pbPsrId, psrType);
        }

        // 查询数据库获取曲线数据
        return handleDbQuery(vecRunDomain, pbPsrId, psrType, date);
    }

    /**
     * 判断是否为需要生成随机数据的特殊类型
     */
    private boolean isSpecialPsrType(String psrType) {
        return StringUtils.isNotBlank(psrType) && ("370000".equals(psrType) || "0303".equals(psrType));
    }

    /**
     * 处理查询不到曲线的数据(生成随机数据并缓存)
     */
    private VecRunDomain handleSpecialType(VecRunDomain vecRunDomain, String pbPsrId, String psrType) {
        String pKey = "vecP_" + psrType + "_" + pbPsrId;
        String qKey = "vecQ_" + psrType + "_" + pbPsrId;

        // 尝试从缓存获取
        if (redisTemplate.hasKey(pKey) && redisTemplate.hasKey(qKey)) {
            List<BigDecimal> vecP = (List<BigDecimal>) redisTemplate.opsForValue().get(pKey);
            List<BigDecimal> vecQ = (List<BigDecimal>) redisTemplate.opsForValue().get(qKey);
            return populateVecRunDomain(vecRunDomain, vecP, vecQ, psrType, pbPsrId);
        }

        // 生成随机数据
        List<BigDecimal> vecP = ElectricityAnalysisServiceImpl.generateRandomDoubles(96, 15, 50).stream().map(BigDecimal::new).collect(Collectors.toList());
        List<BigDecimal> vecQ = ElectricityAnalysisServiceImpl.generateRandomDoubles(96, 5, 7).stream().map(BigDecimal::new).collect(Collectors.toList());

        // 缓存数据
        redisTemplate.opsForValue().set(pKey, vecP, 1, TimeUnit.DAYS);
        redisTemplate.opsForValue().set(qKey, vecQ, 1, TimeUnit.DAYS);

        return populateVecRunDomain(vecRunDomain, vecP, vecQ, psrType, pbPsrId);
    }


    /**
     * 处理数据库查询
     */
    private VecRunDomain handleDbQuery(VecRunDomain vecRunDomain, String pbPsrId, String psrType, String date) {
        LambdaQueryWrapper<PbCurveTemplate> queryWrapper = new LambdaQueryWrapper<>();
        // 查询有功、无功
        queryWrapper.eq(PbCurveTemplate::getPsrId, pbPsrId).eq(PbCurveTemplate::getPsrType, psrType).in(PbCurveTemplate::getMeasTypeCode, "TotW", "TotVar").eq(PbCurveTemplate::getDate, date);
        log.info("开始查询曲线数据:pbPsrId:{},psrType:{}", pbPsrId, psrType);
        List<PbCurveTemplate> pbCurveTemplates = pbCurveTemplateMapper.selectList(queryWrapper);

        if (CollectionUtil.isNotEmpty(pbCurveTemplates)) {
            log.info("pbPsrId:{},psrType:{},查询曲线成功", pbPsrId, psrType);
            List<BigDecimal> vecP = new ArrayList<>();
            List<BigDecimal> vecQ = new ArrayList<>();

            for (PbCurveTemplate template : pbCurveTemplates) {
                if ("TotW".equals(template.getMeasTypeCode())) {
                    vecP = JSONUtil.toList(JSONUtil.parseArray(template.getValue()), BigDecimal.class);
                } else if ("TotVar".equals(template.getMeasTypeCode())) {
                    vecQ = JSONUtil.toList(JSONUtil.parseArray(template.getValue()), BigDecimal.class);
                }
            }
            return populateVecRunDomain(vecRunDomain, vecP, vecQ, psrType, pbPsrId);
        } else {
            log.info("pbPsrId:{},psrType:{},未查询到曲线，模拟生成", pbPsrId, psrType);
            vecRunDomain = handleSpecialType(vecRunDomain, pbPsrId, psrType);
        }

        return vecRunDomain;
    }

    /**
     * 填充VecRunDomain对象
     */
    private VecRunDomain populateVecRunDomain(VecRunDomain vecRunDomain, List<BigDecimal> vecP, List<BigDecimal> vecQ, String psrType, String pbPsrId) {

        vecP = handleNullVecList(vecP);
        vecQ = handleNullVecList(vecQ);

        //保留四位小数
        vecP = vecP.stream().map(item -> new BigDecimal(item.toPlainString()).setScale(4, RoundingMode.HALF_UP)).collect(Collectors.toList());
        vecQ = vecQ.stream().map(item -> new BigDecimal(item.toPlainString()).setScale(4, RoundingMode.HALF_UP)).collect(Collectors.toList());

        vecRunDomain.setVec_p(vecP);
        vecRunDomain.setVec_q(vecQ);
        vecRunDomain.setId("PD_" + psrType + "_" + pbPsrId);
        return vecRunDomain;
    }

    /**
     * 处理null值
     */
    List<BigDecimal> handleNullVecList(List<BigDecimal> values) {
        return values.stream().map(d -> d == null ? new BigDecimal(0.0): d).collect(Collectors.toList());
    }

    public String buildInstanceId(String gridCode) {
        StringBuilder instanceId = new StringBuilder();
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        String formatDate = dateFormat.format(date);
        instanceId.append(gridCode);
        instanceId.append("_");
        instanceId.append(formatDate);
        instanceId.append("_");
        List<CalcInstanceInfo> calcInstanceInfos = calcInstanceInfoMapper.selectList();
        int count = calcInstanceInfos.stream().mapToInt(CalcInstanceInfo::getId).max().orElse(0);
        count++;
        instanceId.append(count);

        return instanceId.toString();
    }


    @Override
    public R<?> findPowerRangeByFeeder(String feederId) {
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(new ArrayList<>());
        String result = null;
        try {
            result = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.ok(JSONUtil.parseArray(result));
    }


    public List<String> buildPsrIds(String feederId, int type) {
        // 查询负荷id
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(Collections.singletonList(type));
        String loadPsrInfo = null;
        try {
            loadPsrInfo = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray jsonArray = JSONUtil.parseArray(loadPsrInfo);
        if (CollectionUtil.isNotEmpty(jsonArray)) {
            PowerRangeResult bean = BeanUtil.toBean(jsonArray.get(0), PowerRangeResult.class);
            if (CollectionUtil.isNotEmpty(bean.getDevList())) {
                List<DevInfo> devList = bean.getDevList();
                return devList.get(0).getPsrId();
            }
        }
        return null;
    }

    /**
     * psrType和对应的设备列表
     *
     * @param feederId
     * @param type
     * @return
     */
    public Map<String, List<String>> buildMapPsrIds(String feederId, int type) {
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(Collections.singletonList(type));
        String loadPsrInfo = null;
        try {
            loadPsrInfo = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray jsonArray = JSONUtil.parseArray(loadPsrInfo);
        if (CollectionUtil.isNotEmpty(jsonArray)) {
            PowerRangeResult bean = BeanUtil.toBean(jsonArray.get(0), PowerRangeResult.class);
            if (CollectionUtil.isNotEmpty(bean.getDevList())) {
                List<DevInfo> devList = bean.getDevList();
                return devList.stream().collect(Collectors.toMap(DevInfo::getPsrType, DevInfo::getPsrId));
            }
        }
        return null;
    }

    @SneakyThrows
    @Override
    public R<?> calc(CalcParams param) {
        param.setGroupId(groupId);
        String result;
        try {
            // 查询馈线负荷以及其特征曲线
            List<String> loadPsrIds = buildPsrIds(param.getFeeder_id(), 209);
            if (CollectionUtil.isEmpty(loadPsrIds)) {
                throw new RuntimeException("负荷数据为空");
            }
            List<VecRunDomain> ldVecRunDomains = pmsZhTelemetryValueMapper.selectLdValueByPsrIds(loadPsrIds);
            for (VecRunDomain domain : ldVecRunDomains) {
                List<BigDecimal> bigDecimals = objectMapper.readValue(domain.getVecP(), new TypeReference<List<BigDecimal>>() {
                });
                domain.setVec_p(bigDecimals.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList()));
                List<BigDecimal> bigDecimals1 = objectMapper.readValue(domain.getVecQ(), new TypeReference<List<BigDecimal>>() {
                });
                domain.setVec_q(bigDecimals1.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList()));
            }
            param.setVec_run_ld(ldVecRunDomains);

            List<String> pbPsrIds = buildPsrIds(param.getFeeder_id(), 208);
            if (CollectionUtil.isEmpty(pbPsrIds)) {
                throw new RuntimeException("配变数据为空");
            }
            // 查询馈线配变以及其特征曲线
            List<VecRunDomain> pbVecRunDomains = pmsZhTelemetryValueMapper.selectPbValueByPsrIds(pbPsrIds);
            for (VecRunDomain domain : pbVecRunDomains) {
                List<BigDecimal> bigDecimals = objectMapper.readValue(domain.getVecP(), new TypeReference<List<BigDecimal>>() {
                });
                domain.setVec_p(bigDecimals.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList()));

                List<BigDecimal> bigDecimals1 = objectMapper.readValue(domain.getVecQ(), new TypeReference<List<BigDecimal>>() {
                });
                domain.setVec_q(bigDecimals1.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList()));

            }
            param.setVec_run_dt(pbVecRunDomains);

            List<CalcVerTwoParams> vecRunGen = param.getVec_run_gen();
            for (CalcVerTwoParams calcVerTwoParams : vecRunGen) {
                List<Double> collect = calcVerTwoParams.getVec_q().stream().map(Math::abs).collect(Collectors.toList());
                calcVerTwoParams.setVec_q(collect);
            }
            // 电源参数
            Map<Object, Object> source = MapUtil.builder().put("id", param.getFeeder_id()).put("vec_v_kv", mrKvValue()).put("vec_va", mrVaValue()).build();

            param.setVec_run_source(Collections.singletonList(source));
            result = HttpUtils.postBody(calcUrl, null, param);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
        return R.ok(JSONUtil.parseObj(result));
    }

    @Override
    public TableDataInfo<AlarmDoMain> alarmByMsgId(PageQuery pageQuery, String msgId) {
        TableDataInfo<AlarmDoMain> build = new TableDataInfo<>();
        try {
            if (StringUtils.isBlank(msgId) && msgId == null) {
                throw new RuntimeException("消息msgId不能为空");
            }
            // 根据msgId查询记录表
            Map<String, Object> simPfRetInfo = trendCalcMapper.queryRetInfoByMsgId(msgId);
            if (simPfRetInfo == null) {
                build.setCode(500);
                throw new RuntimeException("消息不存在");
            }
            if (simPfRetInfo.get("endDt") == null) {
                build.setCode(200);
                build.setTotal(-1);
                throw new RuntimeException("潮流计算还没有结束");
            }
            if (simPfRetInfo.get("pfRet") == null) {
                build.setCode(500);
                throw new RuntimeException("潮流计算失败");
            }
            int retId = Integer.parseInt(simPfRetInfo.get("id").toString());
            // 查询告警
            List<AlarmDoMain> breakList = trendCalcMapper.queryBreakByRetId(retId);
            if (CollectionUtil.isEmpty(breakList)) {
                // 没有告警约定好code设置为1
                build.setMsg("暂无告警信息");
                build.setCode(-1);
                return build;
            }
            List<AlarmDoMain> alarmDoMains = breakList.stream().skip((long) (pageQuery.getPageNum() - 1) * pageQuery.getPageSize()).limit(pageQuery.getPageSize()).collect(Collectors.toList());
            build.setPageNum(pageQuery.getPageNum());
            build.setPageSize(pageQuery.getPageSize());
            build.setMsg("查询成功");
            build.setCode(200);
            build.setTotal(breakList.size());
            build.setRows(alarmDoMains);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return build;
    }

    @Override
    public R querySegmentByPsrIdAndPsrType(String psrId, String psrType) {
        List<String> poleTypes = Arrays.asList("startPole", "stopPole");
        List<String> segmentPsrIdList = new ArrayList<>();
        if (psrType.equals("wlgt")) {
            // 转换psrId
            psrId = handlerPsr(psrId);
        }
        for (String poleType : poleTypes) {
            // 构建查询参数
            List<Map<Object, Object>> psrParams = genPsrParams(psrId, poleType);
            // 通用资源查询
            String post = null;
            try {
                post = HttpUtils.postBody(BusinessCenterConstant.BASE_URL + BusinessCenterConstant.TELEMETRY_URL, BusinessCenterConstant.COMMON_HEADER, psrParams);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String message = JSONUtil.parseObj(post).get("message").toString();
            if ("成功".equals(message)) {
                String psrTypeResult = JSONUtil.parseObj(JSONUtil.parseObj(post).get("result").toString()).get("dxd").toString();
                String records = JSONUtil.parseObj(psrTypeResult).get("records").toString();
                JSONArray jsonArray = JSONUtil.parseArray(records);
                if (CollectionUtil.isNotEmpty(jsonArray)) {
                    String segmentPsrId = JSONUtil.parseObj(jsonArray.get(0)).get("psrId").toString();
                    segmentPsrIdList.add(segmentPsrId);
                }
            }
        }
        return R.ok(segmentPsrIdList);
    }


    private String handlerPsr(String psrId) {
        List<Map<Object, Object>> param = new ArrayList<>();
        List<Map<Object, Object>> filters = new ArrayList<>();
        Map<Object, Object> filterOne = MapUtil.builder().put("compare", "in").put("fieldName", "psrState").put("fieldValue", "10,20").build();
        Map<Object, Object> filterTwo = MapUtil.builder().put("compare", "in").put("fieldName", "astId").put("fieldValue", psrId).build();
        filters.add(filterOne);
        filters.add(filterTwo);

        Map<Object, Object> build = MapUtil.builder().put("id", "100001").put("psrType", "0103").put("distribution", 0).put("params", MapUtil.builder().put("filters", filters).build()).build();
        param.add(build);
        HttpResponse stationStr = HttpUtil.createPost("http://egw.jn.js.sgcc.com.cn/psr-center/psrDSService/commonQuery").headerMap(BusinessCenterConstant.COMMON_HEADER, true).body(JSONUtil.toJsonStr(param)).execute();
        if (stationStr.isOk()) {
            JSONObject jsonObjectPsr = JSONUtil.parseObj(stationStr.body());
            if ("成功".equals(jsonObjectPsr.get("message"))) {
                JSONArray jsonArray = JSONUtil.parseArray(JSONUtil.parseObj(JSONUtil.parseObj(jsonObjectPsr.get("result")).get("0103")).get("records"));
                for (Object item : jsonArray) {
                    if (JSONUtil.parseObj(item).get("psrId") != null) {
                        return JSONUtil.parseObj(item).get("psrId").toString();
                    }
                }
            }
        } else {
            throw new RuntimeException("astId转化psrId失败");
        }
        return null;
    }

    public static String getSubStr(String feeder) {
        int startIndex = feeder.indexOf("_");
        int endIndex = feeder.indexOf("_", startIndex + 1);
        if (endIndex != -1) {
            return feeder.substring(endIndex + 1);
        } else {
            return "";
        }
    }

    public static List<Double> mrVaValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            doubleList.add(0.0);
        }
        return doubleList;
    }

    public static List<Double> mrKvValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            double randomDouble = RandomUtil.randomDouble(10, 12);
            doubleList.add(new BigDecimal(randomDouble).setScale(4, RoundingMode.HALF_UP).doubleValue());
        }
        return doubleList;
    }


    public static List<Map<Object, Object>> genPsrParams(String psrId, String type) {
        List<Map<Object, Object>> param = new ArrayList<>();
        List<Map<Object, Object>> filters = new ArrayList<>();

        Map<Object, Object> filterOne = MapUtil.builder().put("compare", "=").put("fieldName", type).put("fieldValue", psrId).build();
        filters.add(filterOne);

        Map<Object, Object> filterTwo = MapUtil.builder().put("compare", "in").put("fieldName", "psrState").put("fieldValue", "10,20").build();
        filters.add(filterTwo);

        Map<Object, Object> build = MapUtil.builder().put("id", "100001").put("psrType", "dxd").put("distribution", 0).put("params", MapUtil.builder().put("filters", filters).put("page", 1).put("size", 10).build()).build();
        param.add(build);
        return param;
    }

    public interface BusinessCenterConstant {

        String BASE_URL = "http://egw.jn.js.sgcc.com.cn";
        String TELEMETRY_URL = "/psr-center/psrDSService/commonQuery";
        Map<String, String> COMMON_HEADER = Collections.unmodifiableMap(new HashMap<String, String>() {
            {
                this.put("x-system-code", "ztgl-app-06103087-daf9-4690-b7df-6d3a9f63f3e3");
                this.put("Authorization", "Bearer 6f0ad858-05a2-4525-b113-d938063d6b88");
                this.put("x-application-code", "app");
                this.put("Content-Type", "application/json");
                this.put(Header.ACCEPT_ENCODING.getValue(), "identity");
            }
        });
    }

}
