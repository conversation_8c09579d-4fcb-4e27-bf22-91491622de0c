package com.ruoyi.service.calc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.bo.CalcAlarmRuleBo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.CalcAlarmRule;
import com.ruoyi.mapper.calc.CalcAlarmRuleMapper;
import com.ruoyi.service.calc.ICalcAlarmRuleService;
import com.ruoyi.vo.CalcAlarmRuleVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实例告警规则Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@RequiredArgsConstructor
@Service
public class CalcAlarmRuleServiceImpl implements ICalcAlarmRuleService {

    private final CalcAlarmRuleMapper baseMapper;

    /**
     * 查询实例告警规则列表
     */
    @Override
    public List<CalcAlarmRuleVo> queryList(CalcAlarmRuleBo bo) {
        LambdaQueryWrapper<CalcAlarmRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CalcAlarmRule> buildQueryWrapper(CalcAlarmRuleBo bo) {
        LambdaQueryWrapper<CalcAlarmRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getInstanceId()), CalcAlarmRule::getInstanceId, bo.getInstanceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), CalcAlarmRule::getDescription, bo.getDescription());
        lqw.eq(bo.getLimitValue() != null, CalcAlarmRule::getLimitValue, bo.getLimitValue());
        return lqw;
    }

}
