package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsSegment;
import com.ruoyi.entity.znap.DevDmsSegment;
import com.ruoyi.mapper.znap.DevDmsSegmentMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线路段潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SegmentPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsSegment> {

    private final ISimulationService iSimulationService;
    private final DevDmsSegmentMapper devDmsSegmentMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.SEGMENT.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询线路段潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevDmsSegment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsSegment::getPsrid, psrId);
        DevDmsSegment devDmsSegment = devDmsSegmentMapper.selectOne(queryWrapper);
        if (devDmsSegment == null) {
            return null;
        }
        // 查询线路段的潮流数据
        List<SimRetPfDmsSegment> segments = iSimulationService.selectSimRetPfSegment(retId, devDmsSegment.getId());
        // 转换为DTO
        return segments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
}