package com.ruoyi.service.calc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.GridOptimizePolicyBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.GridOptimizePolicy;
import com.ruoyi.mapper.calc.GridOptimizePolicyMapper;
import com.ruoyi.service.calc.IGridOptimizePolicyService;
import com.ruoyi.vo.GridOptimizePolicyVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * 网格优化策略Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-16
 */
@RequiredArgsConstructor
@Service
public class GridOptimizePolicyServiceImpl implements IGridOptimizePolicyService {
    private final GridOptimizePolicyMapper baseMapper;

    /**
     * 查询网格优化策略
     */
    @Override
    public GridOptimizePolicyVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询网格优化策略列表
     */
    @Override
    public TableDataInfo<GridOptimizePolicyVo> queryPageList(GridOptimizePolicyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GridOptimizePolicy> lqw = buildQueryWrapper(bo);
        Page<GridOptimizePolicyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询网格优化策略列表
     */
    @Override
    public List<GridOptimizePolicyVo> queryList(GridOptimizePolicyBo bo) {
        LambdaQueryWrapper<GridOptimizePolicy> lqw = buildQueryWrapper(bo);
        return baseMapper.listVo(lqw);
    }

    private LambdaQueryWrapper<GridOptimizePolicy> buildQueryWrapper(GridOptimizePolicyBo bo) {
        return Wrappers.lambdaQuery();
    }

    /**
     * 新增网格优化策略
     */
    @Override
    public Boolean insertByBo(GridOptimizePolicyBo bo) {
        List<GridOptimizePolicy> gridOptimizePolicies = baseMapper.selectList();
        if (CollectionUtil.isNotEmpty(gridOptimizePolicies)) {
            Iterator<GridOptimizePolicy> iterator = gridOptimizePolicies.iterator();
            GridOptimizePolicy gridOptimizePolicy = iterator.next();
            gridOptimizePolicy.setConstraintSetting(JSONUtil.toJsonStr(bo.getConstraintSetting()));
            gridOptimizePolicy.setTargetSetting(JSONUtil.toJsonStr(bo.getTargetSetting()));
            baseMapper.updateById(gridOptimizePolicy);
            return true;
        }
        GridOptimizePolicy gridOptimizePolicy = new GridOptimizePolicy();
        gridOptimizePolicy.setConstraintSetting(JSONUtil.toJsonStr(bo.getConstraintSetting()));
        gridOptimizePolicy.setTargetSetting(JSONUtil.toJsonStr(bo.getTargetSetting()));
        return baseMapper.insert(gridOptimizePolicy) > 0;
    }
}
