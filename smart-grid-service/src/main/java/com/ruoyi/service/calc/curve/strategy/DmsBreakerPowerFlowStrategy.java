package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsBreaker;
import com.ruoyi.entity.znap.DevDmsBreaker;
import com.ruoyi.mapper.znap.DevDmsBreakerMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配网开关潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DmsBreakerPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsBreaker> {


    private final ISimulationService iSimulationService;
    private final DevDmsBreakerMapper devDmsBreakerMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.DMS_BREAKER.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询配网开关潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevDmsBreaker> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsBreaker::getPsrid, psrId);
        DevDmsBreaker devDmsBreaker = devDmsBreakerMapper.selectOne(queryWrapper);
        if (devDmsBreaker == null) {
            return null;
        }
        // 查询开关的潮流数据
        List<SimRetPfDmsBreaker> breakers = iSimulationService.selectSimRetPfDmsBreaker(retId, devDmsBreaker.getId());
        // 转换为DTO
        return breakers.stream().map(this::convertToDto).collect(Collectors.toList());
    }
}
