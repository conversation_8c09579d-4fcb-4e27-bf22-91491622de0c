package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.bo.ZhOptPlanBo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.ZhOptPlan;
import com.ruoyi.entity.calc.ZhOptPlanInfo;
import com.ruoyi.mapper.calc.ZhOptPlanInfoMapper;
import com.ruoyi.mapper.calc.ZhOptPlanMapper;
import com.ruoyi.service.calc.IZhOptPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 辅助决策方案Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
@RequiredArgsConstructor
@Service
public class ZhOptPlanServiceImpl implements IZhOptPlanService {

    private final ZhOptPlanMapper baseMapper;
    private final ZhOptPlanInfoMapper zhOptPlanInfoMapper;


    @Override
    public R queryList(ZhOptPlanBo bo) {
        List<Map<Object, Object>> result = new ArrayList<>();
        try {
            if (StringUtils.isBlank(bo.getDecisionId())) {
                throw new RuntimeException("网格编码或实例id不能为空");
            }
            LambdaQueryWrapper<ZhOptPlan> zhOptPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(bo.getPolicyPlanType())) {
                zhOptPlanLambdaQueryWrapper.eq(ZhOptPlan::getPolicyPlanType, bo.getPolicyPlanType());
            }
            if (StringUtils.isNotBlank(bo.getContentType())) {
                zhOptPlanLambdaQueryWrapper.eq(ZhOptPlan::getContentType, bo.getContentType());
            }
            zhOptPlanLambdaQueryWrapper.eq(ZhOptPlan::getDecisionId, bo.getDecisionId());
            List<ZhOptPlan> zhOptPlans = baseMapper.selectList(zhOptPlanLambdaQueryWrapper);
            if (CollectionUtil.isEmpty(zhOptPlans)) {
                return R.ok(result);
            }
            for (ZhOptPlan zhOptPlan : zhOptPlans) {
                Map<Object, Object> build = MapUtil.builder().build();
                build.put("id", zhOptPlan.getId());
                build.put("policyPlanType", zhOptPlan.getPolicyPlanType());
                build.put("contentType", zhOptPlan.getContentType());
                Integer id = zhOptPlan.getId();
                LambdaQueryWrapper<ZhOptPlanInfo> zhOptPlanInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                zhOptPlanInfoLambdaQueryWrapper.eq(ZhOptPlanInfo::getId, id);
                List<ZhOptPlanInfo> zhOptPlanInfos = zhOptPlanInfoMapper.selectList(zhOptPlanInfoLambdaQueryWrapper);
                if (CollectionUtil.isEmpty(zhOptPlanInfos)) {
                    continue;
                }
                List<Map<Object, Object>> programmeList = new ArrayList<>();
                for (ZhOptPlanInfo zhOptPlanInfo : zhOptPlanInfos) {
                    String planId = zhOptPlanInfo.getPlanId();
                    String data = zhOptPlanInfo.getData();
                    Map<Object, Object> programme = MapUtil.builder()
                            .put("id", planId)
                            .put("data", data)
                            .build();
                    programme.put("data", data);
                    /*if (zhOptPlan.getContentType().equals("import")) {
                        //设备引入是整个对象
                        programme.put("data", JSONUtil.parseObj(data));
                    }
                    if (zhOptPlan.getContentType().equals("adjust")) {
                        //调整是数组
                        programme.put("data", JSONUtil.parseArray(data));
                    }*/
                    programmeList.add(programme);
                }
                build.put("programmeList", programmeList);
                result.add(build);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("操作失败，原因：" + e.getMessage());
        }
        return R.ok(result);
    }

    /**
     * 新增辅助决策方案
     */
    @Override
    public R insertByBo(ZhOptPlanBo bo) {
        try {
            if (StringUtils.isBlank(bo.getDecisionId())) {
                throw new RuntimeException("网格编码或实例id不能为空");
            }
            ZhOptPlan zhOptPlan = BeanUtil.toBean(bo, ZhOptPlan.class);
            // 新增决策主表 先删除后新增
            LambdaQueryWrapper<ZhOptPlan> zhOptPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
            zhOptPlanLambdaQueryWrapper.eq(ZhOptPlan::getId, bo.getId());
            ZhOptPlan zhOptPlanOld = baseMapper.selectOne(zhOptPlanLambdaQueryWrapper);
            if (zhOptPlanOld != null) {
                baseMapper.deleteById(zhOptPlanOld.getId());
                LambdaQueryWrapper<ZhOptPlanInfo> zhOptPlanInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                zhOptPlanInfoLambdaQueryWrapper.eq(ZhOptPlanInfo::getId, zhOptPlanOld.getId());
                zhOptPlanInfoMapper.delete(zhOptPlanInfoLambdaQueryWrapper);
            }
            baseMapper.insert(zhOptPlan);
            Integer optPlanId = zhOptPlan.getId();
            List<ZhOptPlanInfo> zhOptPlanInfos = new ArrayList<>();
            List<Map<String, Object>> programmeList = bo.getProgrammeList();
            for (Map<String, Object> programme : programmeList) {
                String planId = programme.get("id").toString();
                ZhOptPlanInfo zhOptPlanInfo = new ZhOptPlanInfo();
                zhOptPlanInfo.setId(optPlanId);
                zhOptPlanInfo.setPlanId(planId);
                zhOptPlanInfo.setData(JSONUtil.toJsonStr(programme.get("data")));
                zhOptPlanInfos.add(zhOptPlanInfo);
            }
            // 新增方案详情
            zhOptPlanInfoMapper.insertBatch(zhOptPlanInfos);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("操作失败，原因：" + e.getMessage());
        }
        return R.ok("操作成功");
    }
}
