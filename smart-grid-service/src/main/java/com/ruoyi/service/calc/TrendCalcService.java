package com.ruoyi.service.calc;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.AlarmDoMain;
import com.ruoyi.entity.calc.CalcParams;

import java.util.Map;

public interface TrendCalcService {
    R<?>  calc(CalcParams param);

    TableDataInfo<AlarmDoMain> alarmByMsgId(PageQuery pageQuery, String msgId);

    R querySegmentByPsrIdAndPsrType(String psrId, String psrType);

    R<?>  findPowerRangeByFeeder(String feederId);

    R<?> calcSimulation(Map<String, Object> params);
    R<?> feederCalc(Map<String, Object> params);

}
