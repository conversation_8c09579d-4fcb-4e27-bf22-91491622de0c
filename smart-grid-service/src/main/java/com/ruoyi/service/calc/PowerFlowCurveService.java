package com.ruoyi.service.calc;

import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.service.calc.curve.strategy.PowerFlowCurveStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 潮流曲线服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PowerFlowCurveService {

    private final List<PowerFlowCurveStrategy> strategies;

    // 策略映射，用于快速查找
    private Map<String, PowerFlowCurveStrategy> strategyMap;

    /**
     * 初始化策略映射
     */
    private void initStrategyMap() {
        if (strategyMap == null) {
            strategyMap = strategies.stream().collect(Collectors.toMap(PowerFlowCurveStrategy::getSupportedDeviceType, Function.identity()));
        }
    }

    /**
     * 查询设备的潮流曲线
     *
     * @param retId 潮流计算结果ID
     * @param psrType 设备类型
     * @param psrId 设备PSR ID
     * @return 潮流曲线数据
     */
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrType, String psrId) {
        log.info("查询设备潮流曲线, retId: {}, deviceType: {}, psrId: {}", retId, psrType, psrId);
        // 初始化策略映射
        initStrategyMap();
        // 获取对应的策略
        PowerFlowCurveStrategy strategy = strategyMap.get(psrType);
        if (strategy == null) {
            log.warn("不支持的设备类型: {}", psrType);
            return Collections.emptyList();
        }
        // 使用策略查询潮流曲线
        return strategy.queryPowerFlowCurve(retId, psrId);
    }


}