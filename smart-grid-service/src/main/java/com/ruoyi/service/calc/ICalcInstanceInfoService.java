package com.ruoyi.service.calc;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.CalcInstanceInfoBo;
import com.ruoyi.entity.calc.CalcInstanceInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 计算实例Service接口
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
public interface ICalcInstanceInfoService {

    /**
     * 查询计算实例
     */
    CalcInstanceInfoVo queryById(String instanceId);

    /**
     * 查询计算实例列表
     */
    TableDataInfo<CalcInstanceInfoVo> queryPageList(CalcInstanceInfoBo bo, PageQuery pageQuery);

    /**
     * 查询计算实例列表
     */
    List<Map<String, Object>> queryList(CalcInstanceInfoBo bo);

    /**
     * 新增计算实例
     */
    Boolean insertByBo(CalcInstanceInfoBo bo);

    /**
     * 修改计算实例
     */
    Boolean updateByBo(CalcInstanceInfoBo bo);

    /**
     * 校验并批量删除计算实例信息
     */
    Boolean deleteWithValidByIds(String id, Boolean isValid);
}
