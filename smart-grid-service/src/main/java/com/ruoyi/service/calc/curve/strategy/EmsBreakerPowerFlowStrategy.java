package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfEmsBreaker;
import com.ruoyi.entity.znap.DevEmsBreaker;
import com.ruoyi.mapper.znap.DevEmsBreakerMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 主网开关潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmsBreakerPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfEmsBreaker> {

    private final ISimulationService iSimulationService;
    private final DevEmsBreakerMapper devEmsBreakerMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.EMS_BREAKER.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询主网开关潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevEmsBreaker> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevEmsBreaker::getPsrid, psrId);
        DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectOne(queryWrapper);
        if (devEmsBreaker == null) {
            return null;
        }
        // 查询开关的潮流数据
        List<SimRetPfEmsBreaker> breakers = iSimulationService.selectSimRetPfEmsBreaker(retId, devEmsBreaker.getId());
        // 转换为DTO
        return breakers.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

}
