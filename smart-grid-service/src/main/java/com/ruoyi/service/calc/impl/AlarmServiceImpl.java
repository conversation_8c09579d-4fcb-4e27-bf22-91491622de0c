package com.ruoyi.service.calc.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.ZhEpsAlarmBo;
import com.ruoyi.bo.ZhEpsAlarmConfigBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.AlarmType;
import com.ruoyi.entity.calc.*;
import com.ruoyi.mapper.calc.CalcAlarmInfoMapper;
import com.ruoyi.mapper.calc.ZhBreakMapper;
import com.ruoyi.mapper.calc.ZhConductorSegmentMapper;
import com.ruoyi.mapper.calc.ZhEpsAlarmMapper;
import com.ruoyi.service.calc.AlarmService;
import com.ruoyi.service.calc.IZhDmsPowerGridService;
import com.ruoyi.service.calc.IZhEpsAlarmConfigService;
import com.ruoyi.vo.AlarmHistoryVo;
import com.ruoyi.vo.AlarmStatisticsVo;
import com.ruoyi.vo.ZhEpsAlarmVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vo.ResultVO;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AlarmServiceImpl implements AlarmService {
    private final ZhEpsAlarmMapper baseMapper;
    private final IZhDmsPowerGridService zhDmsPowerGridService;
    private final IZhEpsAlarmConfigService epsAlarmConfigService;
    private final ZhBreakMapper zhBreakMapper;
    private final ZhConductorSegmentMapper zhConductorSegmentMapper;
    private final CalcAlarmInfoMapper calcAlarmInfoMapper;

    @Override
    public R curveListById(String id) {
        Map<Object, Object> rt = new HashMap<>();
        try {
            ZhEpsAlarm zhEpsAlarm = baseMapper.selectById(id);
            if (zhEpsAlarm == null) {
                throw new RuntimeException("数据不存在");
            }
            rt.put("status", zhEpsAlarm.getStatus());
            rt.put("alarmContext", zhEpsAlarm.getAlarmContext());
            String modelId = zhEpsAlarm.getModelId();
            String modelType = zhEpsAlarm.getModelType();
            String simuCaseNo = zhEpsAlarm.getSimuCaseNo();
            Integer alarmType = zhEpsAlarm.getAlarmType();
            String[] split = simuCaseNo.split(":");
            Integer retId = Integer.parseInt(split[1]);
            // 查询网格数据，填充网格名称
            extracted(split, rt, alarmType, modelType, modelId, retId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process curve list by id: " + id, e);
        }
        return R.ok(rt);
    }

    @Override
    public R curveListByCaId(String id) {
        Map<Object, Object> rt = new HashMap<>();
        try {
            CalcAlarmInfo calcAlarmInfo = null;
            if (StringUtils.isNotBlank(id)) {
                calcAlarmInfo = calcAlarmInfoMapper.selectById(Integer.parseInt(id));
            } else {
                throw new RuntimeException("id不能为空");
            }
            if (calcAlarmInfo == null) {
                throw new RuntimeException("数据不存在");
            }
            rt.put("status", 0);
            rt.put("alarmContext", calcAlarmInfo.getAlarmContent());
            String modelId = calcAlarmInfo.getPsrId();
            String modelType = calcAlarmInfo.getPsrType();
            Integer retId = Math.toIntExact(calcAlarmInfo.getRetId());
            int alarmType = 11;
            if (calcAlarmInfo.getAlarmType() != null) {
                alarmType = Math.toIntExact(calcAlarmInfo.getAlarmType());
            }
            String[] split = calcAlarmInfo.getInstanceId().split("_");
            extracted(split, rt, alarmType, modelType, modelId, retId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process curve list by id: " + id, e);
        }
        return R.ok(rt);
    }

    private void extracted(String[] split, Map<Object, Object> rt, int alarmType, String modelType, String modelId, Integer retId) {
        // 查询网格数据，填充网格名称
        List<ZhDmsPowerGrid> zhDmsPowerGrids = zhDmsPowerGridService.queryByIds(Collections.singleton(split[0]));
        if (!zhDmsPowerGrids.isEmpty()) {
            rt.put("gridName", zhDmsPowerGrids.get(0).getGridName());
        }

        List<EpsAlarmConfig> epsAlarmConfigs = epsAlarmConfigService.queryList(new ZhEpsAlarmConfigBo());
        Map<Long, List<EpsAlarmConfig>> epsAlarmConfigMap = epsAlarmConfigs.stream()
                .collect(Collectors.groupingBy(EpsAlarmConfig::getId));

        List<AlarmCurve> alarmCurves = null;
        if (alarmType == 11) {
            if ((modelType.equals("0111") || modelType.equals("0112") || modelType.equals("0305") || modelType.equals("0307")) && alarmType != 12) {
                alarmCurves = baseMapper.queryDmsBreakCurveList(modelId, retId);
                if (CollectionUtil.isNotEmpty(alarmCurves)) {
                    processAlarmCurves(rt, alarmCurves, 11L, epsAlarmConfigMap, zhBreakMapper, modelId, alarmType);
                }
                rt.put("rateValue", (zhBreakMapper.queryRateValueById(modelId)));
            } else if (modelType.equals("0201") || modelType.equals("dxd")) {
                alarmCurves = baseMapper.querySegmentCurveList(modelId, retId);
                if (CollectionUtil.isNotEmpty(alarmCurves)) {
                    processAlarmCurves(rt, alarmCurves, 11L, epsAlarmConfigMap, zhConductorSegmentMapper, modelId, alarmType);
                }
                rt.put("rateValue", (zhConductorSegmentMapper.queryRateValueById(modelId)));
            }
        } else if (alarmType == 12) {
            alarmCurves = baseMapper.queryEmsBreakCurveList(modelId, retId);
            if (CollectionUtil.isNotEmpty(alarmCurves)) {
                processAlarmCurves(rt, alarmCurves, 12L, epsAlarmConfigMap, null, modelId, alarmType);
            }
            rt.put("rateValue", 0);
        } else if (alarmType == 13) {
            alarmCurves = baseMapper.queryDmsBreakCurveList(modelId, retId);
            if (CollectionUtil.isNotEmpty(alarmCurves)) {
                processAlarmCurves(rt, alarmCurves, 13L, epsAlarmConfigMap, null, modelId, alarmType);
            }
            rt.put("rateValue", 10);
        } else if (alarmType == 14) {
            alarmCurves = baseMapper.querySegmentCurveList(modelId, retId);
            if (CollectionUtil.isNotEmpty(alarmCurves)) {
                processAlarmCurves(rt, alarmCurves, 14L, epsAlarmConfigMap, null, modelId, alarmType);
            }
        } else {
            rt.put("rateValue", 0);
        }
    }

    private void processAlarmCurves(Map<Object, Object> rt, List<AlarmCurve> alarmCurves, Long configId, Map<Long, List<EpsAlarmConfig>> epsAlarmConfigMap, Object mapper, String modelId, int alarmType) {
        if (alarmCurves == null || alarmCurves.isEmpty()) {
            throw new RuntimeException("相关数据不存在");
        }
        List<String> time = new ArrayList<>();
        List<Double> values = new ArrayList<>();
        Map<String, List<Double>> valueMap = alarmCurves.stream().collect(Collectors.toMap(
                curve -> null,
                curve -> {
                    Double value = null;
                    switch (alarmType) {
                        case 11:
                            value = curve.getIValue();
                            break;
                        case 12:
                            value = curve.getPValue();
                            break;
                        case 13:
                            value = curve.getVValue();
                            break;
                        case 14:
                            value = curve.getPLossValue();
                            break;
                        default:
                            break;
                    }
                    if (value != null) {
                        values.add(value);
                    }
                    if (curve.getTime() != null) {
                        time.add(curve.getTime());
                    }
                    return values;
                },
                (left, right) -> left
        ));
        valueMap.put("values", values);

        rt.put("curve", new HashMap<String, Object>() {{
            put("value", valueMap.get("values"));
            put("time", time);
        }});
        rt.put("date", alarmCurves.get(0).getDate());

        // 告警设定的值
        Double limitValue = epsAlarmConfigMap.getOrDefault(configId, Collections.emptyList()).stream()
                .findFirst()
                .map(EpsAlarmConfig::getLimitValue)
                .orElse(null);
        rt.put("limitValue", limitValue);

        // 超过设定值的最大值
        OptionalDouble result = values.stream()
                .mapToDouble(Double::doubleValue)
                .max();
        rt.put("maxValue", result.isPresent() ? result.getAsDouble() : 0);

        rt.put("alarmType", alarmType);
    }


    @Override
    public ResultVO listEvent(Long alarmId) {
        List<EpsEvent> entityList = baseMapper.listEvent(alarmId);
        return new ResultVO() {{
            this.setCode(200);
            this.setTooltip("操作成功");
            this.setData(entityList);
        }};
    }

    @Override
    public ResultVO listConfig() {
        List<EpsAlarmConfig> epsAlarmConfigs = epsAlarmConfigService.queryList(new ZhEpsAlarmConfigBo());
        return new ResultVO() {{
            this.setCode(200);
            this.setTooltip("操作成功");
            this.setData(epsAlarmConfigs);
        }};
    }


    @Override
    public TableDataInfo<ZhEpsAlarmVo> historyList(ZhEpsAlarmBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZhEpsAlarm> lqw = buildQueryWrapper(bo);
        Page<ZhEpsAlarmVo> result = baseMapper.queryHistoryPageList(pageQuery.build(), lqw, bo);
        List<ZhEpsAlarmVo> records = result.getRecords();
        Map<String, List<ZhEpsAlarmVo>> listMap = records.stream()
                .peek(item -> item.setGrid(item.getSimuCaseNo().split(":")[0]))
                .filter(item -> StringUtils.isNotBlank(item.getSimuCaseNo()))
                .collect(Collectors.groupingBy(o -> o.getSimuCaseNo().split(":")[0]));
        // 查询网格数据，填充网格名称
        List<ZhDmsPowerGrid> zhDmsPowerGrids = zhDmsPowerGridService.queryByIds(listMap.keySet());
        for (ZhDmsPowerGrid zhDmsPowerGrid : zhDmsPowerGrids) {
            String gridName = zhDmsPowerGrid.getGridName();
            listMap.get(zhDmsPowerGrid.getPsrId()).forEach(item -> item.setGridName(gridName));
        }
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<ZhEpsAlarmVo> queryPageList(ZhEpsAlarmBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZhEpsAlarm> lqw = buildQueryWrapper(bo);
        Page<ZhEpsAlarmVo> result = baseMapper.queryAlarmPageList(pageQuery.build(), lqw, bo);
        List<ZhEpsAlarmVo> records = result.getRecords();
        if (bo.getIsXn() != null && bo.getIsXn()) {
            if (CollectionUtil.isNotEmpty(records)) {
                Random random = new Random();
                int count = random.nextInt(3) + 1;
                for (int i = 0; i < count; i++) {
                    if (records.get(i) != null) {
                        records.get(i).setState(1);
                    }
                }
                records = records.stream().sorted(Comparator.comparing(ZhEpsAlarmVo::getState).reversed()).collect(Collectors.toList());
                result.setRecords(records);
            }
        }
        Map<String, List<ZhEpsAlarmVo>> listMap = records.stream()
                .peek(item -> item.setGrid(item.getSimuCaseNo().split(":")[0]))
                .filter(item -> StringUtils.isNotBlank(item.getSimuCaseNo()))
                .collect(Collectors.groupingBy(o -> o.getSimuCaseNo().split(":")[0]));
        // 查询网格数据，填充网格名称
        List<ZhDmsPowerGrid> zhDmsPowerGrids = zhDmsPowerGridService.queryByIds(listMap.keySet());
        for (ZhDmsPowerGrid zhDmsPowerGrid : zhDmsPowerGrids) {
            String gridName = zhDmsPowerGrid.getGridName();
            listMap.get(zhDmsPowerGrid.getPsrId()).forEach(item -> item.setGridName(gridName));
        }

        return TableDataInfo.build(result);
    }

    @Override
    public AlarmStatisticsVo statistics(ZhEpsAlarmBo bo) {
        List<Map<String, Object>> statistics = baseMapper.statistics(bo);
        Map<String, Integer> periodInfo = baseMapper.periodAlarm(bo);
        List<Map<String, Object>> levelStatistics = baseMapper.levelAlarmStatistics(bo);
        Date localDateTime = calcAlarmInfoMapper.lastVersionAlarmTime();
        AlarmStatisticsVo alarmStatisticsVo = new AlarmStatisticsVo()
                .setTypeStatistics(statistics)
                .setLastCalcTime(localDateTime)
                .setLevelStatistics(levelStatistics);
        if (periodInfo != null) {
            alarmStatisticsVo.setLowPeriod(periodInfo.get("lowPeriod")).setHighPeriod(periodInfo.get("highPeriod"));
        }
        return alarmStatisticsVo;
    }

    @Override
    public List<EpsEvent> detailList(Long alarmId) {
        return baseMapper.detailList(alarmId);
    }

    @Override
    public Date lastCalcTime() {
        return calcAlarmInfoMapper.lastVersionAlarmTime();
    }

    @Override
    public List<Map<String, Object>> equipmentList(String grid) {
        return baseMapper.equipmentList(grid);
    }

    @Override
    public int processed(Long alarmId) {
        CalcAlarmInfo calcAlarmInfo = calcAlarmInfoMapper.selectById(alarmId);
        if (calcAlarmInfo == null) {
            return 1;
        }
        calcAlarmInfo.setStatus(true);
        return calcAlarmInfoMapper.updateById(calcAlarmInfo);
    }


    @Override
    public boolean deleteWithValidById(Integer id) {
        return calcAlarmInfoMapper.deleteById(id) > 0;
    }

    @Override
    public TableDataInfo<ZhEpsAlarmVo> periodList(ZhEpsAlarmBo bo, PageQuery pageQuery) {
        Integer period = bo.getPeriod();
        if (period == null || (period != 1 && period != 2)) {
            throw new ServiceException("查询类型异常");
        }
        EpsAlarmConfig epsAlarmConfig = epsAlarmConfigService.queryById(AlarmType.OVERLOAD.getId());
        Integer lowPeriod = epsAlarmConfig.getLowPeriod();
        Integer highPeriod = epsAlarmConfig.getHighPeriod();
        Page<ZhEpsAlarmVo> page = null;
        if (period == 1) {
            page = baseMapper.queryLowPeriodList(pageQuery.build(), bo.getGrid(), bo.getGridName(), bo.getStatus(), lowPeriod, highPeriod);
        }
        if (period == 2) {
            page = baseMapper.queryHightPeriodList(pageQuery.build(), bo.getGrid(), bo.getGridName(), bo.getStatus(), highPeriod);
        }
        List<ZhEpsAlarmVo> records = page.getRecords();
        Map<String, List<ZhEpsAlarmVo>> listMap = records.stream()
                .peek(item -> item.setGrid(item.getSimuCaseNo().split(":")[0]))
                .filter(item -> StringUtils.isNotBlank(item.getSimuCaseNo()))
                .collect(Collectors.groupingBy(o -> o.getSimuCaseNo().split(":")[0]));
        // 查询网格数据，填充网格名称
        List<ZhDmsPowerGrid> zhDmsPowerGrids = zhDmsPowerGridService.queryByIds(listMap.keySet());
        for (ZhDmsPowerGrid zhDmsPowerGrid : zhDmsPowerGrids) {
            String gridName = zhDmsPowerGrid.getGridName();
            listMap.get(zhDmsPowerGrid.getPsrId()).forEach(item -> item.setGridName(gridName));
        }
        return TableDataInfo.build(page);
    }

    @Override
    public List<Map<String, Object>> historyStatistics(Integer dimensionality) {
        if (dimensionality == null || Stream.of(1, 2, 3).noneMatch(item -> item.equals(dimensionality))) {
            return Collections.emptyList();
        }
        if (dimensionality == 1) {
            return baseMapper.historyStatisticsByGrid();
        }
        if (dimensionality == 2) {
            return baseMapper.historyStatisticsByType();
        }
        return baseMapper.historyStatisticsByLevel(dimensionality);
    }

    @Override
    public List<AlarmHistoryVo> queryHistoryList(LocalDate startTime, LocalDate endTime) {
        return baseMapper.queryHistoryList(startTime, endTime);
    }


    private LambdaQueryWrapper<ZhEpsAlarm> buildQueryWrapper(ZhEpsAlarmBo bo) {
        LambdaQueryWrapper<ZhEpsAlarm> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getModelId()), ZhEpsAlarm::getModelId, bo.getModelId());
        lqw.eq(StringUtils.isNotBlank(bo.getSimuCaseNo()), ZhEpsAlarm::getSimuCaseNo, bo.getSimuCaseNo());
        lqw.eq(bo.getAlarmTime() != null, ZhEpsAlarm::getAlarmTime, bo.getAlarmTime());
        lqw.like(StringUtils.isNotBlank(bo.getAlarmContext()), ZhEpsAlarm::getAlarmContext, bo.getAlarmContext());
        lqw.eq(bo.getAlarmType() != null, ZhEpsAlarm::getAlarmType, bo.getAlarmType());
        lqw.likeRight(StringUtils.isNotBlank(bo.getGrid()), ZhEpsAlarm::getSimuCaseNo, bo.getGrid());
        lqw.ge(bo.getStartTime() != null, ZhEpsAlarm::getAlarmTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, ZhEpsAlarm::getAlarmTime, bo.getEndTime());
        return lqw;
    }
}
