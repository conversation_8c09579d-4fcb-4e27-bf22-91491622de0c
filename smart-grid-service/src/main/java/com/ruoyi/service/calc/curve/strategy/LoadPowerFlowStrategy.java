package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsLoad;
import com.ruoyi.entity.znap.DevDmsLd;
import com.ruoyi.mapper.znap.DevDmsLdMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 负荷潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoadPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsLoad> {

    private final ISimulationService iSimulationService;
    private final DevDmsLdMapper devDmsLdMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.LOAD.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询负荷潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevDmsLd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsLd::getPsrid, psrId);
        DevDmsLd devDmsLd = devDmsLdMapper.selectOne(queryWrapper);
        if (devDmsLd == null) {
            return null;
        }
        // 查询负荷的潮流数据
        List<SimRetPfDmsLoad> loads = iSimulationService.selectSimRetPfDmsLoad(retId, devDmsLd.getId());
        // 转换为DTO
        return loads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

}