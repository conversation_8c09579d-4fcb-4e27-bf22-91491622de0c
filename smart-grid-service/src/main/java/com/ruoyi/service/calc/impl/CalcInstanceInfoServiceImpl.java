package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.*;
import com.ruoyi.mapper.calc.CalcAlarmRuleMapper;
import com.ruoyi.mapper.calc.CalcInstanceInfoMapper;
import com.ruoyi.mapper.calc.CalcRelationShipMapper;
import com.ruoyi.service.calc.ICalcInstanceInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 计算实例Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-10
 */
@RequiredArgsConstructor
@Service
public class CalcInstanceInfoServiceImpl implements ICalcInstanceInfoService {

    private final CalcInstanceInfoMapper baseMapper;
    private final CalcRelationShipMapper calcRelationShipMapper;
    private final CalcAlarmRuleMapper calcAlarmRuleMapper;

    /**
     * 查询计算实例
     */
    @Override
    public CalcInstanceInfoVo queryById(String instanceId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<CalcInstanceInfo>().eq(CalcInstanceInfo::getInstanceId, instanceId));
    }

    /**
     * 查询计算实例列表
     */
    @Override
    public TableDataInfo<CalcInstanceInfoVo> queryPageList(CalcInstanceInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CalcInstanceInfo> lqw = buildQueryWrapper(bo);
        Page<CalcInstanceInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询计算实例列表
     */
    @Override
    public List<Map<String, Object>> queryList(CalcInstanceInfoBo bo) {
        LambdaQueryWrapper<CalcInstanceInfo> lqw = buildQueryWrapper(bo);
        List<Map<String, Object>> mapList = baseMapper.selectMaps(lqw);
        return mapList;
    }

    private LambdaQueryWrapper<CalcInstanceInfo> buildQueryWrapper(CalcInstanceInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CalcInstanceInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getInstanceId()), CalcInstanceInfo::getInstanceId, bo.getInstanceId());
        lqw.eq(StringUtils.isNotBlank(bo.getGridCode()), CalcInstanceInfo::getGridCode, bo.getGridCode());
        lqw.like(StringUtils.isNotBlank(bo.getGridName()), CalcInstanceInfo::getGridName, bo.getGridName());
        lqw.eq(bo.getState() != null, CalcInstanceInfo::getState, bo.getState());
        lqw.eq(bo.getStartTime() != null, CalcInstanceInfo::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, CalcInstanceInfo::getEndTime, bo.getEndTime());
        lqw.eq(bo.getIsRecycle() != null, CalcInstanceInfo::getIsRecycle, bo.getIsRecycle());
        if (bo.getStartTimeS() != null && bo.getStartTimeE() != null) {
            lqw.between(CalcInstanceInfo::getStartTime, bo.getStartTimeS(), bo.getStartTimeE());
        }
        if (bo.getEndTimeS() != null && bo.getEndTimeE() != null) {
            lqw.between(CalcInstanceInfo::getEndTime, bo.getEndTimeS(), bo.getEndTimeE());
        }
        if (bo.getProblemId()!=null){
            lqw.eq(CalcInstanceInfo::getProblemId, bo.getProblemId());
        }
        lqw.eq(CalcInstanceInfo::getIsRecycle, 0);
        lqw.eq(bo.getIsAuto() != null, CalcInstanceInfo::getIsAuto, bo.getIsAuto());
        lqw.orderByDesc(CalcInstanceInfo::getStartTime);
        return lqw;
    }

    /**
     * 新增计算实例
     */
    @Override
    public Boolean insertByBo(CalcInstanceInfoBo bo) {
        CalcInstanceInfo add = BeanUtil.toBean(bo, CalcInstanceInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setInstanceId(add.getInstanceId());
        }
        return flag;
    }

    /**
     * 修改计算实例
     */
    @Override
    public Boolean updateByBo(CalcInstanceInfoBo bo) {
        CalcInstanceInfo update = BeanUtil.toBean(bo, CalcInstanceInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CalcInstanceInfo entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除计算实例
     */
    @Override
    public Boolean deleteWithValidByIds(String id, Boolean isValid) {
        try {
            LambdaQueryWrapper<CalcInstanceInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CalcInstanceInfo::getInstanceId, id);
            baseMapper.delete(queryWrapper);

            LambdaQueryWrapper<CalcRelationShip> calcRelationShipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcRelationShipLambdaQueryWrapper.eq(CalcRelationShip::getInstanceId, id);
            calcRelationShipMapper.delete(calcRelationShipLambdaQueryWrapper);

            LambdaQueryWrapper<CalcAlarmRule> calcAlarmRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcAlarmRuleLambdaQueryWrapper.eq(CalcAlarmRule::getInstanceId, id);
            calcAlarmRuleMapper.delete(calcAlarmRuleLambdaQueryWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
