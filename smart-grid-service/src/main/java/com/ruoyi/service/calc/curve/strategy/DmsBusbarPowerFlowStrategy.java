package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsBusbar;
import com.ruoyi.entity.znap.DevDmsBusbar;
import com.ruoyi.mapper.znap.DevDmsBusbarMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配网母线潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DmsBusbarPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsBusbar> {

    private final ISimulationService iSimulationService;
    private final DevDmsBusbarMapper devDmsBusbarMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.DMS_BUSBAR.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询配网母线潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevDmsBusbar> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsBusbar::getPsrid, psrId);

        DevDmsBusbar devDmsBusbar = devDmsBusbarMapper.selectOne(queryWrapper);
        if (devDmsBusbar == null) {
            return null;
        }
        // 查询母线的潮流数据
        List<SimRetPfDmsBusbar> busbars = iSimulationService.selectSimRetPfDmsBusbar(retId, devDmsBusbar.getId());
        // 转换为DTO
        return busbars.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

}
