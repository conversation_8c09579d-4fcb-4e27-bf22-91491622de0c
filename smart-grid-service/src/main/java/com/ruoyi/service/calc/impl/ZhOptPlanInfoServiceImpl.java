package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.ZhOptPlanInfoBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.ZhOptPlanInfo;
import com.ruoyi.mapper.calc.ZhOptPlanInfoMapper;
import com.ruoyi.service.calc.IZhOptPlanInfoService;
import com.ruoyi.vo.ZhOptPlanInfoVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 辅助决策方案详情Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
@RequiredArgsConstructor
@Service
public class ZhOptPlanInfoServiceImpl implements IZhOptPlanInfoService {

    private final ZhOptPlanInfoMapper baseMapper;

    /**
     * 查询辅助决策方案详情
     */
    @Override
    public ZhOptPlanInfoVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询辅助决策方案详情列表
     */
    @Override
    public TableDataInfo<ZhOptPlanInfoVo> queryPageList(ZhOptPlanInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZhOptPlanInfo> lqw = buildQueryWrapper(bo);
        Page<ZhOptPlanInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询辅助决策方案详情列表
     */
    @Override
    public List<ZhOptPlanInfoVo> queryList(ZhOptPlanInfoBo bo) {
        LambdaQueryWrapper<ZhOptPlanInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZhOptPlanInfo> buildQueryWrapper(ZhOptPlanInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZhOptPlanInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlanId()), ZhOptPlanInfo::getPlanId, bo.getPlanId());
        return lqw;
    }


    /**
     * 修改辅助决策方案详情
     */
    @Override
    public Boolean updateByBo(ZhOptPlanInfoBo bo) {
        ZhOptPlanInfo update = BeanUtil.toBean(bo, ZhOptPlanInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZhOptPlanInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除辅助决策方案详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
