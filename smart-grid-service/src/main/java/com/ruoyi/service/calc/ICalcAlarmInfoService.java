package com.ruoyi.service.calc;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.CalcAlarmInfoBo;
import com.ruoyi.entity.calc.CalcAlarmInfoVo;
import com.ruoyi.vo.AlarmStatisticsVo;


import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 手动计算实例告警Service接口
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
public interface ICalcAlarmInfoService {

    /**
     * 查询手动计算实例告警
     */
    CalcAlarmInfoVo queryById(Long id);

    /**
     * 查询手动计算实例告警列表
     */
    TableDataInfo<CalcAlarmInfoVo> queryPageList(CalcAlarmInfoBo bo, PageQuery pageQuery);

    /**
     * 查询手动计算实例告警列表
     */
    List<Map<String,Object>> queryList(CalcAlarmInfoBo bo);

    R plan(CalcAlarmInfoBo bo);

    AlarmStatisticsVo statistics(CalcAlarmInfoBo bo);

    TableDataInfo<CalcAlarmInfoVo> historyList(CalcAlarmInfoBo bo, PageQuery pageQuery);

    R getInstanceIdByGridCode(String gridCode);

    List<Map<String, Object>> historyStatistics(Integer dimensionality);

    List<CalcAlarmInfoVo> historyExportList(Date startTime, Date endTime);
}
