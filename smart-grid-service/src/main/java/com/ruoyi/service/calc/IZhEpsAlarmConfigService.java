package com.ruoyi.service.calc;

import com.ruoyi.bo.ZhEpsAlarmConfigBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.EpsAlarmConfig;

import java.util.List;

/**
 * 告警信息Service接口
 *
 * <AUTHOR> developer
 * @date 2024-09-27
 */
public interface IZhEpsAlarmConfigService {
    /**
     * 查询告警信息
     */
    EpsAlarmConfig queryById(Long id);

    /**
     * 查询告警信息列表
     */
    TableDataInfo<EpsAlarmConfig> queryPageList(ZhEpsAlarmConfigBo bo, PageQuery pageQuery);

    /**
     * 查询告警信息列表
     */
    List<EpsAlarmConfig> queryList(ZhEpsAlarmConfigBo bo);

    boolean updateByBo(ZhEpsAlarmConfigBo bo);

    boolean updateBatchByBoList(List<ZhEpsAlarmConfigBo> bo);


}
