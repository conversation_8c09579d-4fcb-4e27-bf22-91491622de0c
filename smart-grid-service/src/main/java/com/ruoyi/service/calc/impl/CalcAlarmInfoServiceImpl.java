package com.ruoyi.service.calc.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.ZhEpsAlarmBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.*;
import com.ruoyi.mapper.calc.CalcAlarmInfoMapper;
import com.ruoyi.mapper.calc.ZhEpsAlarmMapper;
import com.ruoyi.service.calc.ICalcAlarmInfoService;
import com.ruoyi.service.calc.IZhDmsPowerGridService;
import com.ruoyi.vo.AlarmStatisticsVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 手动计算实例告警Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-11
 */
@RequiredArgsConstructor
@Service
public class CalcAlarmInfoServiceImpl implements ICalcAlarmInfoService {

    private final CalcAlarmInfoMapper baseMapper;
    private final ZhEpsAlarmMapper zhEpsAlarmMapper;
    private final IZhDmsPowerGridService zhDmsPowerGridService;

    /**
     * 查询手动计算实例告警
     */
    @Override
    public CalcAlarmInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询手动计算实例告警列表
     */
    @Override
    public TableDataInfo<CalcAlarmInfoVo> queryPageList(CalcAlarmInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CalcAlarmInfo> lqw = buildQueryWrapper(bo);
        if (StringUtils.isEmpty(bo.getInstanceId())) {
            // 查询全省最新日期的数据的instanceId
            Date maxDate = baseMapper.lastVersionAlarmTime();
            if (maxDate == null) {
                maxDate = DateUtils.getNowDate();
            }
            String targetDate = new SimpleDateFormat("yyyy-MM-dd").format(maxDate);
//            lqw.eq(CalcAlarmInfo::getIsAuto, 1);
//            lqw.apply("DATE(alarm_time) <= STR_TO_DATE({0}, '%Y-%m-%d')", targetDate);
            lqw.apply("DATE(alarm_time) <= TO_DATE({0}, 'YYYY-MM-DD')", targetDate);
        }
        Page<CalcAlarmInfoVo> result = baseMapper.selectAlarmInfoPage(pageQuery.build(), lqw);
        List<CalcAlarmInfoVo> records = result.getRecords();
        Map<String, List<CalcAlarmInfoVo>> listMap = records.stream().peek(item -> item.setGrid(item.getSimuCaseNo().split(":")[0])).filter(item -> StringUtils.isNotBlank(item.getSimuCaseNo())).collect(Collectors.groupingBy(o -> o.getSimuCaseNo().split(":")[0]));
        // 查询网格数据，填充网格名称
        List<ZhDmsPowerGrid> zhDmsPowerGrids = zhDmsPowerGridService.queryByIds(listMap.keySet());
        for (ZhDmsPowerGrid zhDmsPowerGrid : zhDmsPowerGrids) {
            String gridName = zhDmsPowerGrid.getGridName();
            listMap.get(zhDmsPowerGrid.getPsrId()).forEach(item -> item.setGridName(gridName));
        }
        return TableDataInfo.build(result);
    }


    @Override
    public TableDataInfo<CalcAlarmInfoVo> historyList(CalcAlarmInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CalcAlarmInfo> lqw = buildQueryWrapper(bo);
        if (StringUtils.isEmpty(bo.getInstanceId())) {
            // 查询全省最新日期的数据的instanceId
            Date maxDate = baseMapper.lastVersionAlarmTime();
            if (maxDate == null) {
                maxDate = DateUtils.getNowDate();
            }
            String targetDate = new SimpleDateFormat("yyyy-MM-dd").format(maxDate);
            lqw.apply("DATE(alarm_time) <= TO_DATE({0}, 'YYYY-MM-DD')", targetDate);
        }
        if (bo.getStartTime() != null && bo.getEndTime() != null) {
            lqw.between(CalcAlarmInfo::getAlarmTime, bo.getStartTime(), bo.getEndTime());
        }

        Page<CalcAlarmInfoVo> result = baseMapper.selectAlarmInfoWithGridPage(pageQuery.build(), lqw, bo.getGridName());

        return TableDataInfo.build(result);
    }

    @Override
    public R getInstanceIdByGridCode(String gridCode) {
        // 查询全省最新日期的数据的instanceId
        Map<String, Object> rt = baseMapper.getInstanceIdByGridCode(gridCode);
        if (rt == null) {
            return R.ok(null);
        }
        return R.ok(rt);
    }

    @Override
    public List<Map<String, Object>> historyStatistics(Integer dimensionality) {
        if (dimensionality == null || Stream.of(1, 2, 3).noneMatch(item -> item.equals(dimensionality))) {
            return Collections.emptyList();
        }
        return baseMapper.historyStatistics(dimensionality);
    }

    @Override
    public List<CalcAlarmInfoVo> historyExportList(Date startTime, Date endTime) {
        return baseMapper.historyExportList(startTime, endTime);
    }


    /**
     * 查询手动计算实例告警列表
     */
    @Override
    public List<Map<String, Object>> queryList(CalcAlarmInfoBo bo) {
        LambdaQueryWrapper<CalcAlarmInfo> lqw = buildQueryWrapper(bo);
        lqw.select(CalcAlarmInfo::getPsrId, CalcAlarmInfo::getPsrType, CalcAlarmInfo::getPsrParentId, CalcAlarmInfo::getPsrParentType, CalcAlarmInfo::getAlarmType, CalcAlarmInfo::getFeederId, CalcAlarmInfo::getId, CalcAlarmInfo::getAlarmContent);
        return baseMapper.selectMaps(lqw);
    }


    // 辅助决策
    @Override
    public R plan(CalcAlarmInfoBo bo) {
        Map<Object, Object> result = MapUtil.builder().build();
        try {
            List<String> feederIds = new ArrayList<>();
            if (StringUtils.isNotBlank(bo.getInstanceId())) {
                // 根据实例id查线路
                feederIds = baseMapper.queryFeederIdsByInstanceId(bo.getInstanceId());
            }
            if (StringUtils.isNotBlank(bo.getGridCode())) {
                // 根据网格编码查线路
                feederIds = zhEpsAlarmMapper.queryAlarmFeederList(bo.getGridCode());
            }
            if (CollectionUtil.isEmpty(feederIds)) {
                throw new RuntimeException("暂无告警线路");
            }
            // 一个线路一个方案
            for (String feederId : feederIds) {
                feederId = "PD_dkx_" + feederId;
                // 查询联络开关和线路
                List<Map<String, Object>> breakAndFeederList = baseMapper.queryContactBreakAndFeederByFeeder(feederId);
                // 查询线路主网开关
                String mainBreakName = baseMapper.queryMainBreakByFeeder(feederId);
                List<AlarmPlan> alarmPlans = new ArrayList<>();
                for (Map<String, Object> stringObjectMap : breakAndFeederList) {
                    // 原始线路
                    String feederName = stringObjectMap.get("feedername").toString();
                    // 联络线路
                    String toFeederName = stringObjectMap.get("tofeedername").toString();
                    // 联络开关
                    String contactBreak = stringObjectMap.get("breakname").toString();

                    List<String> stepOneContent = Collections.singletonList(String.format("1.原线路：%s向线路：%s转供进行电力输送。", feederName, toFeederName));
                    AlarmPlan stepOne = new AlarmPlan();
                    stepOne.setStepName("步骤一：确定转供路径");
                    stepOne.setStepInfo(stepOneContent);

                    List<String> stepTwoContent = new ArrayList<>();
                    stepTwoContent.add(String.format("1.断开%s的断路器或者隔离开关：%s", feederName, mainBreakName));
                    stepTwoContent.add(String.format("2.合上%s的联络开关：%s", toFeederName, contactBreak));
                    AlarmPlan stepTwo = new AlarmPlan();
                    stepTwo.setStepName("步骤二：调整开关状态");
                    stepTwo.setStepInfo(stepTwoContent);

                    List<String> stepThreeContent = Collections.singletonList("1.操作开关的过程中监控电力潮流的变化。");
                    AlarmPlan stepThree = new AlarmPlan();
                    stepThree.setStepName("步骤三：监控电力潮流");
                    stepThree.setStepInfo(stepThreeContent);
                    alarmPlans.add(stepOne);
                    alarmPlans.add(stepTwo);
                    alarmPlans.add(stepThree);
                }
                result.put(feederId, alarmPlans);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
        return R.ok(result);
    }

    @Override
    public AlarmStatisticsVo statistics(CalcAlarmInfoBo bo) {
        List<Map<String, Object>> statistics = baseMapper.statistics(bo.getInstanceId());
        ZhEpsAlarmBo zhEpsAlarmBo = new ZhEpsAlarmBo();
        zhEpsAlarmBo.setGrid(bo.getGridCode());
        Map<String, Integer> periodInfo = zhEpsAlarmMapper.periodAlarm(zhEpsAlarmBo);
        Date localDateTime = baseMapper.lastCalcTime(bo.getInstanceId());
        AlarmStatisticsVo alarmStatisticsVo = new AlarmStatisticsVo().setTypeStatistics(statistics).setLastCalcTime(localDateTime);
        if (periodInfo != null) {
            alarmStatisticsVo.setLowPeriod(periodInfo.get("lowPeriod")).setHighPeriod(periodInfo.get("highPeriod"));
        }
        return alarmStatisticsVo;
    }


    private LambdaQueryWrapper<CalcAlarmInfo> buildQueryWrapper(CalcAlarmInfoBo bo) {
        LambdaQueryWrapper<CalcAlarmInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPsrId()), CalcAlarmInfo::getPsrId, bo.getPsrId());
        lqw.eq(StringUtils.isNotBlank(bo.getPsrType()), CalcAlarmInfo::getPsrType, bo.getPsrType());
        lqw.eq(StringUtils.isNotBlank(bo.getPsrParentId()), CalcAlarmInfo::getPsrParentId, bo.getPsrParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getPsrParentType()), CalcAlarmInfo::getPsrParentType, bo.getPsrParentType());
        lqw.eq(StringUtils.isNotBlank(bo.getInstanceId()), CalcAlarmInfo::getInstanceId, bo.getInstanceId());
        lqw.like(StringUtils.isNotBlank(bo.getSimuCaseNo()), CalcAlarmInfo::getSimuCaseNo, bo.getSimuCaseNo());
        lqw.eq(bo.getAlarmTime() != null, CalcAlarmInfo::getAlarmTime, bo.getAlarmTime());
        lqw.like(StringUtils.isNotBlank(bo.getAlarmContent()), CalcAlarmInfo::getAlarmContent, bo.getAlarmContent());
        lqw.eq(bo.getAlarmType() != null, CalcAlarmInfo::getAlarmType, bo.getAlarmType());
        lqw.eq(bo.getStatus() != null, CalcAlarmInfo::getStatus, bo.getStatus());
        lqw.eq(bo.getFeederId() != null, CalcAlarmInfo::getFeederId, bo.getFeederId());
        return lqw;
    }

}
