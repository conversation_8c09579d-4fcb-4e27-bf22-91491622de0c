package com.ruoyi.service.calc;

import com.ruoyi.bo.ZhTransferInfoBo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.vo.ZhTransferInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 转供方案Service接口
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
public interface IZhTransferInfoService {

    /**
     * 查询转供方案
     */
    ZhTransferInfoVo queryById(Long id);

    /**
     * 查询转供方案列表
     */
    R list(String calcId);

    /**
     * 查询转供方案列表
     */
    List<ZhTransferInfoVo> queryList(ZhTransferInfoBo bo);

    /**
     * 新增转供方案
     */
    Boolean insertByBo(ZhTransferInfoBo bo);

    /**
     * 修改转供方案
     */
    Boolean updateByBo(ZhTransferInfoBo bo);

    /**
     * 校验并批量删除转供方案信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R statistics(String instanceId,String gridCode);

    R statisticsByType(String type, String instanceId, String gridCode);

}
