package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.bo.ZhTransferInfoBo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.ZhTransferBreakInfo;
import com.ruoyi.entity.calc.ZhTransferInfo;
import com.ruoyi.mapper.calc.ZhTransferBreakInfoMapper;
import com.ruoyi.mapper.calc.ZhTransferInfoMapper;
import com.ruoyi.service.calc.IZhTransferInfoService;
import com.ruoyi.vo.NReduceOneVo;
import com.ruoyi.vo.ZhTransferInfoVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 转供方案Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2025-02-07
 */
@RequiredArgsConstructor
@Service
public class ZhTransferInfoServiceImpl implements IZhTransferInfoService {

    private final ZhTransferInfoMapper baseMapper;
    private final ZhTransferBreakInfoMapper zhTransferBreakInfoMapper;

    /**
     * 查询转供方案
     */
    @Override
    public ZhTransferInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询转供方案列表
     */
    @Override
    public R list(String calcId) {

        List<ZhTransferInfo> zhTransferInfos = baseMapper.selectListById(calcId);
        // 按线路分组可能又多个联络开关
        if (CollectionUtil.isEmpty(zhTransferInfos)) {
            return R.fail("暂无转供方案");
        }

        Map<String, List<ZhTransferInfo>> collect = zhTransferInfos
                .stream()
                .collect(Collectors.groupingBy(ZhTransferInfo::getSourceFeeder));
        List<ZhTransferInfoVo> zhTransferInfoVos = new ArrayList<>();
        Set<Map.Entry<String, List<ZhTransferInfo>>> entries = collect.entrySet();
        for (Map.Entry<String, List<ZhTransferInfo>> entry : entries) {
            ZhTransferInfoVo zhTransferInfoVo = new ZhTransferInfoVo();
            List<ZhTransferInfo> zhTransferInfoList = entry.getValue();
            if (CollectionUtil.isNotEmpty(zhTransferInfoList)) {
                String sourceFeeder = zhTransferInfoList.get(0).getSourceFeeder();
                String sourceFeederName = zhTransferInfoList.get(0).getSourceFeederName();
                // 源线路最高电流
                Double sourceFeederCurrent = zhTransferInfoList.get(0).getSourceFeederCurrent();
                // 源馈线当前负载
                double sourceFeederRate = zhTransferInfoList.get(0).getSourceFeederRate();
                double linkFeederCurrent = zhTransferInfoList.get(0).getLinkFeederCurrent();
                double linkFeederOriginalRate = zhTransferInfoList.get(0).getLinkFeederOriginalRate();
                String linkFeeder = zhTransferInfoList.get(0).getLinkFeeder();
                String linkFeederName = zhTransferInfoList.get(0).getLinkFeederName();
                zhTransferInfoVo.setSourceFeeder(sourceFeeder);
                zhTransferInfoVo.setSourceFeederName(sourceFeederName);
                zhTransferInfoVo.setSourceFeederCurrent(sourceFeederCurrent);
                zhTransferInfoVo.setSourceFeederRate(sourceFeederRate);
                zhTransferInfoVo.setLinkFeeder(linkFeeder);
                zhTransferInfoVo.setLinkFeederName(linkFeederName);
                zhTransferInfoVo.setLinkFeederCurrent(linkFeederCurrent);
                zhTransferInfoVo.setLinkFeederOriginalRate(linkFeederOriginalRate);

                List<Map<Object, Object>> breakInfos = new ArrayList<>();
                for (ZhTransferInfo zhTransferInfo : zhTransferInfoList) {
                    Map<Object, Object> linkInfo = MapUtil
                            .builder()
                            .put("linkBreakPsrId", zhTransferInfo.getSourceFeederBreak())
                            .put("linkBreakPsrName", zhTransferInfo.getSourceFeederBreakName())
                            .put("linkBreakPsrType", zhTransferInfo.getSourceFeederBreakType())
                            .build();
                    LambdaQueryWrapper<ZhTransferBreakInfo> zhTransferBreakInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    zhTransferBreakInfoLambdaQueryWrapper.eq(ZhTransferBreakInfo::getCalcId, calcId);
                    zhTransferBreakInfoLambdaQueryWrapper.eq(ZhTransferBreakInfo::getLinkBreakPsrId, zhTransferInfo.getSourceFeederBreak());
                    List<ZhTransferBreakInfo> zhTransferBreakInfos = zhTransferBreakInfoMapper.selectList(zhTransferBreakInfoLambdaQueryWrapper);
                    List<Map<Object, Object>> breakInfosMaps = zhTransferBreakInfos.stream().map(item -> {
                        Map<Object, Object> breakInfo = MapUtil
                                .builder()
                                .put("breakPsrId", item.getBreakPsrId())
                                .put("breakPsrName", item.getBreakPsrName())
                                .put("breakPsrType", item.getBreakPsrType())
                                .put("linkFeederRate", item.getLinkFeederRate())
                                .put("sourceFeederRate", item.getSourceFeederRate())
                                .build();
                        return breakInfo;
                    }).collect(Collectors.toList());
                    linkInfo.put("breakInfo", breakInfosMaps);
                    breakInfos.add(linkInfo);
                }
                zhTransferInfoVo.setBreakInfos(breakInfos);
            }
            zhTransferInfoVos.add(zhTransferInfoVo);
        }
        return R.ok(zhTransferInfoVos);
    }

    /**
     * 查询转供方案列表
     */
    @Override
    public List<ZhTransferInfoVo> queryList(ZhTransferInfoBo bo) {
        LambdaQueryWrapper<ZhTransferInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZhTransferInfo> buildQueryWrapper(ZhTransferInfoBo bo) {
        LambdaQueryWrapper<ZhTransferInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSourceFeeder()), ZhTransferInfo::getSourceFeeder, bo.getSourceFeeder());
        lqw.like(StringUtils.isNotBlank(bo.getSourceFeederName()), ZhTransferInfo::getSourceFeederName, bo.getSourceFeederName());
        lqw.eq(bo.getSourceFeederCurrent() != null, ZhTransferInfo::getSourceFeederCurrent, bo.getSourceFeederCurrent());
        lqw.eq(bo.getLinkFeederCurrent() != null, ZhTransferInfo::getLinkFeederCurrent, bo.getLinkFeederCurrent());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferStatus()), ZhTransferInfo::getTransferStatus, bo.getTransferStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceFeederBreak()), ZhTransferInfo::getSourceFeederBreak, bo.getSourceFeederBreak());
        lqw.like(StringUtils.isNotBlank(bo.getSourceFeederBreakName()), ZhTransferInfo::getSourceFeederBreakName, bo.getSourceFeederBreakName());
        lqw.eq(bo.getRetId() != null, ZhTransferInfo::getRetId, bo.getRetId());
        lqw.eq(bo.getTime() != null, ZhTransferInfo::getTime, bo.getTime());
        lqw.eq(bo.getCalcId() != null, ZhTransferInfo::getCalcId, bo.getCalcId());
        return lqw;
    }

    /**
     * 新增转供方案
     */
    @Override
    public Boolean insertByBo(ZhTransferInfoBo bo) {
        ZhTransferInfo add = BeanUtil.toBean(bo, ZhTransferInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改转供方案
     */
    @Override
    public Boolean updateByBo(ZhTransferInfoBo bo) {
        ZhTransferInfo update = BeanUtil.toBean(bo, ZhTransferInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZhTransferInfo entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除转供方案
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public R statistics(String instanceId, String gridCode) {
        List<NReduceOneVo> statistics = baseMapper.statistics(instanceId, gridCode);
        return R.ok(statistics);
    }

    @Override
    public R statisticsByType(String type, String instanceId, String gridCode) {

        if (StringUtils.isNotEmpty(instanceId) && StringUtils.isEmpty(gridCode)) {
            // 实例不为空，网格为空
            // 点击实例进去的
            return R.ok(baseMapper.statisticsByInstanceId(type, instanceId));
        } else if (StringUtils.isEmpty(instanceId) && StringUtils.isNotEmpty(gridCode)) {
            // 实例为空，网格不为空
            // 点击网格进去的
            return R.ok(baseMapper.statisticsByGridCode(type, gridCode));
        } else {
            return R.ok(baseMapper.statisticsByType(type));
        }
    }
}
