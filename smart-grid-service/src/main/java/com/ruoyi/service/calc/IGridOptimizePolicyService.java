package com.ruoyi.service.calc;

import com.ruoyi.bo.GridOptimizePolicyBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.vo.GridOptimizePolicyVo;

import java.util.List;

/**
 * 网格优化策略Service接口
 *
 * <AUTHOR> developer
 * @date 2024-12-16
 */
public interface IGridOptimizePolicyService {

    /**
     * 查询网格优化策略
     */
    GridOptimizePolicyVo queryById(Long id);

    /**
     * 查询网格优化策略列表
     */
    TableDataInfo<GridOptimizePolicyVo> queryPageList(GridOptimizePolicyBo bo, PageQuery pageQuery);

    /**
     * 查询网格优化策略列表
     */
    List<GridOptimizePolicyVo> queryList(GridOptimizePolicyBo bo);

    /**
     * 新增网格优化策略
     */
    Boolean insertByBo(GridOptimizePolicyBo bo);
}
