package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsDt;
import com.ruoyi.entity.znap.DevDmsTr;
import com.ruoyi.mapper.znap.DevDmsTr2Mapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配变潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransformerPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsDt> {

    private final ISimulationService iSimulationService;

    private final DevDmsTr2Mapper devDmsTr2Mapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.TRANSFORMER.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询配变潮流曲线, retId: {}, psrId: {}", retId, psrId);
        // 查询psrId，这里psrId要做特别处理，单线图获取的psrId和潮流计算程序计算的不一样
        // 根据psrId模糊查询获取id
        LambdaQueryWrapper<DevDmsTr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsTr::getPsrid, psrId);
        DevDmsTr devDmsTr = devDmsTr2Mapper.selectOne(queryWrapper);
        if (devDmsTr == null) {
            return null;
        }
        // 查询配变的潮流数据
        List<SimRetPfDmsDt> transformers = iSimulationService.selectSimRetPfDmsDt(retId, devDmsTr.getId());
        // 转换为DTO
        return transformers.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

}