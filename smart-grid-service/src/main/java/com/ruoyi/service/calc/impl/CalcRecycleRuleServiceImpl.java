package com.ruoyi.service.calc.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.bo.CalcRecycleRuleBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.calc.CalcRecycleRule;
import com.ruoyi.mapper.calc.CalcRecycleRuleMapper;
import com.ruoyi.service.calc.ICalcRecycleRuleService;
import com.ruoyi.vo.CalcRecycleRuleVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 计算实例回收规则Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-12-26
 */
@RequiredArgsConstructor
@Service
public class CalcRecycleRuleServiceImpl implements ICalcRecycleRuleService {

    private final CalcRecycleRuleMapper baseMapper;

    /**
     * 查询计算实例回收规则
     */
    @Override
    public CalcRecycleRuleVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询计算实例回收规则列表
     */
    @Override
    public TableDataInfo<CalcRecycleRuleVo> queryPageList(CalcRecycleRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CalcRecycleRule> lqw = buildQueryWrapper(bo);
        Page<CalcRecycleRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询计算实例回收规则列表
     */
    @Override
    public List<CalcRecycleRuleVo> queryList(CalcRecycleRuleBo bo) {
        LambdaQueryWrapper<CalcRecycleRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CalcRecycleRule> buildQueryWrapper(CalcRecycleRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CalcRecycleRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRecycleDay() != null, CalcRecycleRule::getRecycleDay, bo.getRecycleDay());
        lqw.eq(bo.getRecycleHour() != null, CalcRecycleRule::getRecycleHour, bo.getRecycleHour());
        lqw.eq(bo.getRecycleMin() != null, CalcRecycleRule::getRecycleMin, bo.getRecycleMin());
        lqw.eq(bo.getIsEnable() != null, CalcRecycleRule::getIsEnable, bo.getIsEnable());
        lqw.orderByDesc(CalcRecycleRule::getId);
        return lqw;
    }

    /**
     * 新增计算实例回收规则
     */
    @Override
    public Boolean insertByBo(List<CalcRecycleRuleBo> bo) {
        try {
            List<CalcRecycleRule> calcRecycleRules = BeanUtil.copyToList(bo, CalcRecycleRule.class);
            //先删除原有数据
            LambdaQueryWrapper<CalcRecycleRule> calcRecycleRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            calcRecycleRuleLambdaQueryWrapper.eq(CalcRecycleRule::getIsEnable, 1);
            this.baseMapper.delete(calcRecycleRuleLambdaQueryWrapper);
            this.baseMapper.insertBatch(calcRecycleRules);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 修改计算实例回收规则
     */
    @Override
    public Boolean updateByBo(CalcRecycleRuleBo bo) {
        CalcRecycleRule update = BeanUtil.toBean(bo, CalcRecycleRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CalcRecycleRule entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除计算实例回收规则
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
