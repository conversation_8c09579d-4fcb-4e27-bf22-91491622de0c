package com.ruoyi.service.calc;

import com.ruoyi.bo.ZhOptPlanInfoBo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.vo.ZhOptPlanInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 辅助决策方案详情Service接口
 *
 * <AUTHOR> developer
 * @date 2024-12-18
 */
public interface IZhOptPlanInfoService {

    /**
     * 查询辅助决策方案详情
     */
    ZhOptPlanInfoVo queryById(String id);

    /**
     * 查询辅助决策方案详情列表
     */
    TableDataInfo<ZhOptPlanInfoVo> queryPageList(ZhOptPlanInfoBo bo, PageQuery pageQuery);

    /**
     * 查询辅助决策方案详情列表
     */
    List<ZhOptPlanInfoVo> queryList(ZhOptPlanInfoBo bo);

    /**
     * 修改辅助决策方案详情
     */
    Boolean updateByBo(ZhOptPlanInfoBo bo);

    /**
     * 校验并批量删除辅助决策方案详情信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
