package com.ruoyi.service.calc.curve.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.PowerFlowCurveDto;
import com.ruoyi.entity.simulation.SimRetPfDmsDisconnector;
import com.ruoyi.entity.znap.DevDmsDisconnector;
import com.ruoyi.mapper.znap.DevDmsDisconnectorMapper;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配网刀闸潮流曲线查询策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DmsDisconnectorPowerFlowStrategy implements PowerFlowCurveStrategy<SimRetPfDmsDisconnector> {

    private final ISimulationService iSimulationService;
    private final DevDmsDisconnectorMapper devDmsDisconnectorMapper;

    @Override
    public String getSupportedDeviceType() {
        return DeviceTypeEnum.DMS_DISCONNECTOR.getCode();
    }

    @SneakyThrows
    @Override
    public List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId) {
        log.debug("查询配网刀闸潮流曲线, retId: {}, psrId: {}", retId, psrId);
        LambdaQueryWrapper<DevDmsDisconnector> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DevDmsDisconnector::getPsrid, psrId);
        DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectOne(queryWrapper);
        if (devDmsDisconnector == null) {
            return null;
        }
        // 查询刀闸的潮流数据
        List<SimRetPfDmsDisconnector> disconnectors = iSimulationService.selectSimRetPfDmsDisconnector(retId, devDmsDisconnector.getId());
        // 转换为DTO
        return disconnectors.stream().map(this::convertToDto).collect(Collectors.toList());
    }
}
