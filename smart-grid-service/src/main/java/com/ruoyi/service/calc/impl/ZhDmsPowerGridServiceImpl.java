package com.ruoyi.service.calc.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.entity.calc.ZhDmsPowerGrid;
import com.ruoyi.mapper.calc.ZhDmsPowerGridMapper;
import com.ruoyi.service.calc.IZhDmsPowerGridService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 供电网格台账数据Service业务层处理
 *
 * <AUTHOR> developer
 * @date 2024-08-27
 */
@RequiredArgsConstructor
@Service
public class ZhDmsPowerGridServiceImpl implements IZhDmsPowerGridService {
    private final ZhDmsPowerGridMapper baseMapper;

    @Override
    public List<ZhDmsPowerGrid> queryByIds(Set<String> grids) {
        if (CollectionUtil.isEmpty(grids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ZhDmsPowerGrid> lqw = Wrappers.lambdaQuery();
        lqw.select(ZhDmsPowerGrid::getGridName, ZhDmsPowerGrid::getPsrId);
        return baseMapper.selectList(lqw.in(ZhDmsPowerGrid::getPsrId, grids));
    }
}
