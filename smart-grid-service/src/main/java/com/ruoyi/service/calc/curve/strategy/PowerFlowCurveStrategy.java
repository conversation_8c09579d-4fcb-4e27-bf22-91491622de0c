package com.ruoyi.service.calc.curve.strategy;

import com.ruoyi.dto.PowerFlowCurveDto;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * 潮流曲线查询策略接口
 */
public interface PowerFlowCurveStrategy<T> {
    
    /**
     * 获取策略支持的设备类型
     * @return 设备类型编码
     */
    String getSupportedDeviceType();
    
    /**
     * 查询设备的潮流曲线数据
     * @param retId 潮流计算结果ID
     * @param psrId 设备PSR ID
     * @return 潮流曲线数据列表
     */
    List<PowerFlowCurveDto> queryPowerFlowCurve(Long retId, String psrId);

    @SneakyThrows
    default PowerFlowCurveDto convertToDto(T t) {
        PowerFlowCurveDto powerFlowCurveDto =new PowerFlowCurveDto();
        BeanUtils.copyProperties(t, powerFlowCurveDto);
        return powerFlowCurveDto;
    }
}