package com.ruoyi.service.singMap.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.util.MercatorLineCircleIntersection;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.entity.device.bo.SingMapFigure;
import com.ruoyi.entity.device.bo.SingMapUserData;
import com.ruoyi.entity.psm.AmapSdkCommon;
import com.ruoyi.entity.znap.*;
import com.ruoyi.entity.znap.vo.LinkCbFeederVo;
import com.ruoyi.entity.znap.vo.QueryFeederDevVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.TopoToNodes;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.znap.*;
import com.ruoyi.service.map.impl.SelectCoords;
import com.ruoyi.service.singMap.ISingMapTopologyService;
import com.ruoyi.service.znap.impl.ZnapTopologyServiceImpl;
import com.ruoyi.util.coordinates.CoordinateConverter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 单线图接口处理的figure
 */
@Slf4j
@Service
public class SingMapTopologyServiceImpl implements ISingMapTopologyService {

    @Value("${pmsUrl.amapAppServiceUrl}")
    private String amapAppServiceUrl;

    @Value("${kg-handler.mode}")
    private Boolean kgHandlerMode;

    // 线型的类型集合
    private static List<String> lineStrings = Arrays.asList("Polyline", "Line", "Lines");

    // 根节点ID
    final String rootId = "Root";

    /**
     * 主网开关
     */
    @Autowired
    private DevEmsBreakerMapper devEmsBreakerMapper;

    /**
     * 馈线
     */
    @Autowired
    private ConDmsFeederMapper conDmsFeederMapper;

    /**
     * 主干开关路径
     */
    @Autowired
    private TpMainPathCbMapper tpMainPathCbMapper;

    /**
     * 联络开关
     */
    @Autowired
    private TpLinkCbMapper tpLinkCbMapper;

    @Autowired
    ZnapTopologyServiceImpl znapTopologyService;

    @Autowired
    TopoToNodesMapper topoToNodesMapper;

    /**
     * 获取拓扑节点数据
     * 使用单线图接口 和 znap拓扑  互相互补够成
     */
    @Override
    public ZnapTopology generateNode(String feederId, boolean isZnap) throws IOException {

        ConDmsFeeder feeder = conDmsFeederMapper.selectByPmsFeederId(feederId);
        DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(feeder.getHeadBrkId());

        // =========================== 调用单线图接口 解析单线图 =======================
        ArrayList<Node> nodeList = generateNodeList(feederId);
        Map<String, Node> nodeMap = new HashMap<>();
        for (Node node : nodeList) {
            // 处理空子集
            List<Node> children = node.getChildren();
            // 如果子级有null 需要过滤掉
            if (CollectionUtils.isNotEmpty(children)) {
                node.setChildren(children.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            }

            if (StringUtils.isNotBlank(node.getPsrId())) {
                if (node.isBus()) {
                    node.setEdge(true);
                }
                nodeMap.put(node.getPsrId(), node);
            }
        }

        ZnapTopology znapTopology = new ZnapTopology();
        String startNodePsrId = ZnapUtils.parsePsrId(devEmsBreaker.getPsrid());
        Node startNode = nodeMap.get(startNodePsrId);

        // =========================== 处理znapNdx相关节点 =======================
        Map<String, Node> znapNodeMap = new HashMap<>();
        if (isZnap) {
            znapNodeMap = handleZnapNd(feeder, feederId, startNode, devEmsBreaker, znapTopology);
        }

        // =========================== 处理返回 =======================

        znapTopology.setStartNode(startNode);
        znapTopology.setNodeMap(nodeMap);
        znapTopology.setNodeList(nodeList);

        // 处理联络开关和联络线路开关
        handleContactKg(feeder.getId(), feederId, nodeMap, znapTopology);

        // 处理名称 单线图接口有可能没有接口  我们使用znap的node处理
        if (isZnap) {
            for (Node node : nodeMap.values()) {
                if (StringUtils.isBlank(node.getPsrName()) && StringUtils.isNotBlank(node.getPsrId())) {
                    Node znapNode = znapNodeMap.get(node.getPsrId());
                    if (znapNode != null) {
                        node.setPsrName(znapNode.getPsrName());
                    }
                }
            }
        }
        // 处理psrName为空，查询电网一张图接口
        try {
            extreactDeviceInfo(nodeMap);
        } catch (Exception e) {
            return znapTopology;
        }


        return znapTopology;
    }

    /**
     * 调用电网一张图接口：填充psrName、地理位置信息
     *
     * @param nodeMap
     */
    @SneakyThrows
    private void extreactDeviceInfo(Map<String, Node> nodeMap) {
        // 收集需要查询的节点
        List<Node> nodesToUpdate = nodeMap.values().stream()
                // StringUtils.isBlank(node.getPsrName())
                //                        &&
                .filter(node ->  StringUtils.isNotBlank(node.getPsrId())
                        && !DeviceConstants.VIRTUAL_TYPES.contains(node.getPsrType())
                )
                .collect(Collectors.toList());
        if (nodesToUpdate.isEmpty()) {
            return;
        }

        int batchSize = 200;
        for (int i = 0; i < nodesToUpdate.size(); i += batchSize) {
            List<Node> batchNodes = nodesToUpdate.subList(i, Math.min(i + batchSize, nodesToUpdate.size()));
            // 批量调用参数
            List<Map<String, Object>> batchParams = batchNodes.stream()
                    .map(node -> MapUtil.<String, Object>builder()
                            .put("psrId", node.getPsrId())
                            .put("psrType", node.getPsrType())
                            .put("distribution", 0)
                            .build())
                    .collect(Collectors.toList());

            // 需要接口返回的字段
            AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(
                    batchParams.size(), batchParams)
                    .attrName(Arrays.asList("psrId", "psrType", "coordinate", "psrName"));

            List<AmapSdkCommon.DeviceRspModel<AmapSdkCommon.DeviceInfo>> deviceRspModels =
                    AmapSdkCommon.queryDeviceById(powerGridFilter);
            if (CollectionUtils.isEmpty(deviceRspModels)) {
                continue;
            }
            //  建立映射关系
            Map<String, AmapSdkCommon.DeviceInfo> psrIdToDeviceInfo = new HashMap<>();
            deviceRspModels.forEach(rspModel -> {
                if (rspModel != null && !CollectionUtils.isEmpty(rspModel.getPsrList())) {
                    List<AmapSdkCommon.DeviceInfo> psrList = rspModel.getPsrList();
                    for (AmapSdkCommon.DeviceInfo deviceInfo : psrList) {
                        psrIdToDeviceInfo.put(deviceInfo.getPsrId(), deviceInfo);
                    }
                }
            });
            // 填充数据
            batchNodes.forEach(node -> {
                AmapSdkCommon.DeviceInfo deviceInfo = psrIdToDeviceInfo.get(node.getPsrId());
                if (deviceInfo == null) {
                    return; // 当前节点无对应设备信息
                }
                // 更新节点名称和坐标
                node.setPsrName(deviceInfo.getPsrName());
                String coordinateStr = deviceInfo.getCoordinate();

                try {
                    if (node.isEdge()) {
                        String coordinatePairs = SelectCoords.parseCoordinatePairs(coordinateStr);
                        List<List<double[]>> split = CoordinateConverter.split(coordinatePairs);
                        LineString lineString = CoordinateConverter.convertToSingleLineString(split);
                        node.setGeometry(lineString);
                    } else {
                        GeometryFactory factory = new GeometryFactory();
                        String[] coordinates = coordinateStr.split("\\s+");
                        double x = Double.parseDouble(coordinates[0]);
                        double y = Double.parseDouble(coordinates[1]);
                        Coordinate coordinate = MercatorLineCircleIntersection.mercatorToWgs84(x, y);
                        node.setGeometry(factory.createPoint(coordinate));
                    }
                } catch (Exception e) {
                    log.error("更新坐标名称异常: {}", node.getPsrId(), e);
                }
            });
        }

    }

    // =========================== 生成Node关键问题 =======================

    /**
     * 生成nodeList列表
     * 我们默认从数据库缓存里面取，如果没有调用单线图接口并且解析生成Node
     */
    public ArrayList<Node> generateNodeList(String feederId) throws IOException {
        //查询数据库有没有存此线路的topo关系（三天以内的），如果没有执行解析方法并且新增入库，如果有存则直接返回
        TopoToNodes topoToNodes = topoToNodesMapper.findByFeederIdAndTimeRange(feederId);
        ObjectMapper mapper = new ObjectMapper();
        // 如果拓扑关系有值
        if (topoToNodes != null) {
            // 获取当前时间和记录时间
            Instant now = Instant.now();
            Instant addTime = topoToNodes.getAddTime().toInstant();

            // 计算两个时间点之间的天数差（精确到毫秒转换为天）
            long daysDifference = ChronoUnit.DAYS.between(addTime, now);
            //如果日期大于3天则删除
            if (daysDifference > 2) {
                topoToNodesMapper.deleteByFeederId(feederId);
            } else {
                if (StringUtils.isNotBlank(topoToNodes.getNodesJson())) {
                    // 将 JSON 数组转换为 NodeVo 列表
                    ArrayList<NodeVo> nodeVos = mapper.readValue(topoToNodes.getNodesJson(),
                            mapper.getTypeFactory().constructCollectionType(ArrayList.class, NodeVo.class));
                    return new ArrayList<>(NodeUtils.toNodes(nodeVos));
                }
            }
        }

        List<SingMapFigure> figureList = new ArrayList<>();
        Map<String, SingMapFigure> figureMap = new HashMap<>();
        generateData(feederId, figureList, figureMap);
        // 节点集合
        Map<String, Node> figureIdnodeMap = toNodeMap(figureList);
        ArrayList<Node> nodes = new ArrayList<>(figureIdnodeMap.values());
        ArrayList<NodeVo> nodeVoList = NodeUtils.toNodeVos(nodes);
        //新增到库

        //如果新增异常，则返回解析的
        try {
            topoToNodesMapper.insertTopoNode(feederId, mapper.writeValueAsString(nodeVoList), new Date());
        } catch (Exception e) {
            log.error("插入拓扑异常失败，线路Id: {}", feederId, e);
        }
        return nodes;
    }

    /**
     * 根据线路生成figure数据
     *
     * @param feederId 线路ID
     */
    public void generateData(String feederId, List<SingMapFigure> figureList, Map<String, SingMapFigure> figureMap) throws IOException {
        String res = HttpUtils.get(String.format(amapAppServiceUrl + "/measurement/dms/getPic?psrId=%s&psrType=dkx", feederId));

        JSONObject jsonObject = JSON.parseObject(res);

        if (jsonObject.getJSONObject("reply").getInteger("code") != 1000) {
            return;
        }
        JSONObject result = getValueAsJSONObject(jsonObject, "result");
        if (result == null) {
            return;
        }
        // 获取所有的features集合（有父子级）
        SingMapFigure figure = result.getJSONObject("picData").getObject("figureRoot", SingMapFigure.class);

        // 初始化存储容器
        traverseTree(figure, figureList, figureMap, "");
    }

    /**
     * 从 JSONObject 中获取指定键的值，并尝试转换为 JSONObject
     *
     * @param jsonObject 原始 JSONObject
     * @param key        键名
     * @return 转换后的 JSONObject（若无法转换则返回 null）
     */
    final static JSONObject getValueAsJSONObject(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);  // 获取值（Object 类型）

        // 值为 null（键不存在或值为 null）
        if (value == null) {
            return null;
        }

        // 值本身是 JSONObject 类型 → 直接返回
        if (value instanceof JSONObject) {
            return (JSONObject) value;
        }

        // 值是字符串类型 → 尝试解析为 JSONObject
        if (value instanceof String) {
            String strValue = (String) value;
            return JSON.parseObject(strValue);  // 解析字符串为 JSONObject
        }
        return null;
    }

    /**
     * 递归单线图feature树，并将节点存入 List 和 Map
     *
     * @param figure     当前节点
     * @param figureList 存储所有节点的 List
     * @param figureMap  存储节点ID与节点的 Map
     * @param parentId   父级ID
     */
    public final static void traverseTree(SingMapFigure figure, List<SingMapFigure> figureList, Map<String, SingMapFigure> figureMap, String parentId) {
        if (figure == null) {
            return;
        }

        // 由于单线图返回id会重复  我们需要拼接ID
        if (StringUtils.isNotBlank(parentId)) {
            figure.setId(parentId + "." + figure.getId());
        }

        // 1. 将当前节点加入 List 和 Map
        figureList.add(figure);
        figureMap.put(figure.getId(), figure);
        List<SingMapFigure> childFigures = figure.getChildFigures();

        if (childFigures != null && !childFigures.isEmpty()) {
            // 2. 递归处理子节点（前序遍历）
            for (SingMapFigure child : figure.getChildFigures()) {
                traverseTree(child, figureList, figureMap, figure.getId());
            }
        }
    }

    /**
     * 判断当前figure是为线型
     */
    final boolean isLine(SingMapFigure figure) {
        return lineStrings.contains(figure.getType());
    }

    /**
     * 获取父级ID 我们可以从按道理 从parentFigureID即可
     * 但是数据问题 parentFigureID 可能为空 此时从拼接的ID获取
     */
    String getParentId(SingMapFigure figure) {
        String parentFigureID = figure.getParentFigureID();
        if (StringUtils.isNotBlank(parentFigureID)) {
            return parentFigureID;
        } else {
            // 分割"."获取最后的
            String[] split = StringUtils.split(figure.getId(), ".");
            // 最顶层是Root 所以没必要  类型"Root.F1189.F2223"这样才获取设置吧
            if (split.length > 2) {
                return StringUtils.join(split, ".", 0, split.length - 1);
            }
        }
        return null;
    }


    /**
     * 根据设备ID 获取figure
     *
     * @param psrId   设备ID
     * @param isGisId 有些设备通过PSRID是没法找的 isGisId：true 表示找不到再通过GIS_OID查找
     * @return
     */
    public SingMapFigure getByPsrId(String psrId, boolean isGisId, List<SingMapFigure> figureList) {
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            if (userData.psrEquals(psrId, isGisId)) {
                return figure;
            }
        }
        return null; // 未找到
    }

    public Map<String, Node> toNodeMap(List<SingMapFigure> figureList) {
        Map<String, Node> nodeMap = new HashMap<>();

        // 创建所有节点并存入Map
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            String id = figure.getId();
            String psrId = StringUtils.isNotBlank(userData.getPSRID()) ? userData.getPSRID() : userData.getGIS_OID();
            String psrType = StringUtils.isNotBlank(userData.getPSRType()) ? userData.getPSRType() : userData.getGIS_SBZLX();

            Node node = NodeFactory.createNode(id, psrId, psrType);
            node.setPsrName(StringUtils.isNotBlank(userData.getPSRName()) ? userData.getPSRName() : "");
            // 联络开关
            toSuperLinkProp(figure, node);
            nodeMap.put(id, node);
            if (isLine(figure)) {
                node.setEdge(true);
            }
        }

        // 构建节点树
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            String id = figure.getId();
            Node node = nodeMap.get(id);
            if (node == null) {
                continue;
            }
            Node parent = nodeMap.get(getParentId(figure));
            // 设置父节点
            if (parent != null) {
                parent.addChild(node, -1);
            }

            // 线路有设置链接节点
            if (isLine(figure)) {
                Node sourceNode = nodeMap.get(figure.getSourceFigureId());
                Node targetNode = nodeMap.get(figure.getTargetFigureId());

                if (sourceNode != null) {
                    sourceNode.addEdge(node, true);
                }
                if (targetNode != null) {
                    targetNode.addEdge(node, false);
                }
            }
        }

        return nodeMap;
    }

    /**
     * 联络开关接口节点处理
     *
     * @param figure 开关的figure
     * @param node   当前的节点
     */
    void toSuperLinkProp(SingMapFigure figure, Node node) {
        if (!node.isKg("all")) {
            return;
        }
        try {
            SingMapUserData userData = figure.getUserData();
            String superLinkDiagramPSRID = userData.getSuperLinkDiagramPSRID();
            // 表示单线图柱上开关联络接口
            if (StringUtils.isNotBlank(superLinkDiagramPSRID)) {
                setSuperLinkNode(node, superLinkDiagramPSRID, userData.getSuperLinkDiagramPSRType());
            }
        } catch (Exception ignored) {
        }
    }

    //　电线图的联络节点（注意 站房里里面的没有superLinkDiagramPSRID他也可能是联络开关  这个只是一个补救措施，具体还是调用电网一张图的联络开关接口）
    void setSuperLinkNode(Node node, String superLinkDiagramPSRID, String superLinkDiagramPSRType) {
        node.putProperties("superLinkDiagramPSRID", superLinkDiagramPSRID);
        node.putProperties("superLinkDiagramPSRType", superLinkDiagramPSRType);
    }

    // 获取电线图的联络节点
    public String[] getSuperLinkNode(Node node) {
        HashMap<String, Object> properties = node.getProperties();
        if (properties == null) {
            return null;
        }
        String superLinkDiagramPSRID = (String) properties.getOrDefault("superLinkDiagramPSRID", "");
        if (StringUtils.isBlank(superLinkDiagramPSRID)) {
            return null;
        }
        return new String[]{
                superLinkDiagramPSRID,
                (String) properties.get("superLinkDiagramPSRType")};
    }

    // =========================== 一些topology处理 =======================


    /**
     * 跟据线路ID调用电网一张图的接口 获取联络开关
     *
     * @param feederId 线路ID
     */
    @SneakyThrows
    List<ContactFeederKg> generateCFeederKg(String feederId) {
        Map<String, String> param = new HashMap<>();
        param.put("psrId", feederId);
        param.put("psrType", "dkx");
        AmapSdkCommon.LiaisonInfo<ContactFeederKg> liaisonInfoById = AmapSdkCommon.getLiaisonInfoById(param);
        return liaisonInfoById.getResult();
    }


    // 处理联络开关和联络线路开关
    void handleContactKg(Long znapFeederId, String feederId, Map<String, Node> nodeMap, ZnapTopology znapTopology) {
        // 处理联络开关和联络线路开关
        if (kgHandlerMode) {
            try {
                List<ContactFeederKg> contactFeederKgs = generateCFeederKg(feederId);
                znapTopology.setContactFeederKgs(contactFeederKgs);

                List<Node> contactKgs = contactFeederKgs.stream().map(ContactFeederKg::getKgPsrId).map(nodeMap::get).collect(Collectors.toList());
                znapTopology.setKgContactNodes(contactKgs);
            } catch (Exception e) {
                // 处理联络开关根据znap
                handleContactKgByZnap(znapFeederId, feederId, nodeMap, znapTopology);
            }
        } else {
            // 处理联络开关根据znap
            handleContactKgByZnap(znapFeederId, feederId, nodeMap, znapTopology);
        }
    }


    // =========================== znap =======================
    Map<String, Node> handleZnapNd(ConDmsFeeder feeder, String feederId, Node startNode, DevEmsBreaker devEmsBreaker, ZnapTopology znapTopology) {
        QueryFeederDevVo znapFeederDevVo = znapTopologyService.queryDevList(feeder.getId());
        ArrayList<Node> znapNodeList = new ArrayList<>();
        znapNodeList.add(startNode);
        Map<String, Node> znapNodeMap = znapTopologyService.handleNodeList(znapNodeList, znapFeederDevVo, feederId);

        ArrayList<ZnapNd> znapNdList = new ArrayList<>();
        znapTopologyService.setZnapNdList(Arrays.asList(devEmsBreaker), znapNdList, DevEmsBreaker::getId, DevEmsBreaker::getInd, DevEmsBreaker::getJnd, DevEmsBreaker::getPsrid);
        Map<String, Long> znapIdMap = new HashMap<>();
        // 我们的id和军哥的id映射关系
        znapNdList.forEach(znapNd -> {
            if (znapNd.getNode() != null) {
                znapIdMap.put(znapNd.getNode().getId(), znapNd.getId());
            }
        });
        znapTopology.setZnapIdMap(znapIdMap);
        return znapNodeMap;
    }

    // 处理联络开关根据znap
    void handleContactKgByZnap(Long znapFeederId, String feederId, Map<String, Node> nodeMap, ZnapTopology znapTopology) {
        // 处理联络开关
        handleContactKgsByZnap(znapFeederId, nodeMap, znapTopology);

        // 处理联络线路开关
        handleCFeederKgsByZnap(feederId, znapTopology);

        // 这里手动在处理一下单线图的联络开关（有可能单线图和图摸文件的不一样）
        List<Node> kgContactNodes = znapTopology.getKgContactNodes();
        List<ContactFeederKg> contactFeederKgs = znapTopology.getContactFeederKgs();

        for (Node node : nodeMap.values()) {
            if (!node.isKg("all")) {
                continue;
            }
            String[] superLinkNode = getSuperLinkNode(node);
            // 表示单线图联络节点
            if (superLinkNode != null) {
                Node contNode = nodeMap.get(node.getPsrId());
                if (!kgContactNodes.contains(contNode)) {
                    kgContactNodes.add(contNode);
                    ContactFeederKg contactFeederKg = new ContactFeederKg(contNode.getPsrId(), contNode.getPsrType(), contNode.getPsrName());
                    contactFeederKg.setFeederPsrId(superLinkNode[0]);
                    contactFeederKg.setFeederPsrName("");
                    contactFeederKgs.add(contactFeederKg);
                }
            }
        }
    }

    // 处理联络开关
    void handleContactKgsByZnap(Long znapFeederId, Map<String, Node> nodeMap, ZnapTopology znapTopology) {
        LambdaQueryWrapper<TpMainPathCb> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TpMainPathCb::getFeederId, znapFeederId)
                .orderByAsc(TpMainPathCb::getIdx);
        List<TpMainPathCb> tpMainPathCbs = tpMainPathCbMapper.selectList(queryWrapper);

        Map<String, ArrayList<Node>> paths = new HashMap<>();
        List<Node> kgContactNodes = new ArrayList<>();

        for (TpMainPathCb tpMainPathCb : tpMainPathCbs) {
            String[] strings = ZnapUtils.parsePsrStr(tpMainPathCb.getPsrid());
            if (strings == null) {
                continue;
            }
            String psrId = strings[1];
            String psrType = strings[0];

            String pathId = String.valueOf(tpMainPathCb.getPathId());
            ArrayList<Node> nodes = null;
            // 不存在
            if (!paths.containsKey(pathId)) {
                nodes = new ArrayList<>();
                paths.put(pathId, nodes);
            } else {
                nodes = paths.get(pathId);
            }

            if (strings == null) {
                continue;
            }

            // 主干开关
            Node node = nodeMap.get(psrId);
            if (node == null) {
                continue;
            }
            nodes.add(node);
            if (tpMainPathCb.getIsLink().equals(1L)) {
                kgContactNodes.add(node);
            }
        }

        znapTopology.setKgContactNodes(kgContactNodes);
    }

    // 根据znap处理联络线路开关
    void handleCFeederKgsByZnap(String feederId, ZnapTopology znapTopology) {
        List<LinkCbFeederVo> linkCbFeederVos = tpLinkCbMapper.selectLinkCbFeeder(feederId);
        ArrayList<ContactFeederKg> contactFeederKgs = new ArrayList<>();

        for (LinkCbFeederVo data : linkCbFeederVos) {

            String[] kgIdType = ZnapUtils.parsePsrStr(data.getPsrid());
            String[] feederIdType1 = ZnapUtils.parsePsrStr(data.getFeederId1());
            String[] feederIdType2 = ZnapUtils.parsePsrStr(data.getFeederId2());
            if (feederIdType1 == null || feederIdType2 == null) {
                continue;
            }
            String feederId1 = feederIdType1[1];
            String feederId2 = feederIdType2[1];
            String contactFeederId, contactFeederName;

            if (StringUtils.equals(feederId1, feederId2)) {
                contactFeederId = feederId1;
                contactFeederName = data.getFeederName1();
            } else {
                contactFeederId = feederId2;
                contactFeederName = data.getFeederName2();
            }
            if (StringUtils.isBlank(contactFeederId)) {
                continue;
            }
            ContactFeederKg contactFeederKg = new ContactFeederKg(kgIdType[1], kgIdType[0], data.getName());
            contactFeederKg.setFeederPsrId(contactFeederId);
            contactFeederKg.setFeederPsrName(contactFeederName);
            contactFeederKgs.add(contactFeederKg);
        }
        znapTopology.setContactFeederKgs(contactFeederKgs);
    }
}
