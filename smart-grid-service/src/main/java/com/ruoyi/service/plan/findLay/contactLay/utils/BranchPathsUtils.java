package com.ruoyi.service.plan.findLay.contactLay.utils;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.util.ListUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BranchPathsUtils {

    /**
     * 获取分支的所有路径 以便后续每条路径都可以上联络点
     */
    public static List<List<Node>> getBranchPaths(ContactBranch contactBranch, NodePath nodePath) {
        BranchNode branchNode = contactBranch.getBranchNode();
        List<BranchNode> extraBranchNodes = contactBranch.getExtraBranchNodes();
        Node startNode = contactBranch.getStartNode();

        List<Node> nodes = branchNode.getNodes();

        // 过滤其它排除掉分支节点
        if (!CollectionUtils.isEmpty(extraBranchNodes)) {
            List<Node> extraNodes = new ArrayList<>();
            for (BranchNode extraBranchNode : extraBranchNodes) {
                extraNodes.addAll(extraBranchNode.getNodes());
            }
            // 去重
            ListUtils.distinctInPlace(extraNodes, Node::getId);
            Map<String, Node> extraNodeMap = extraNodes.stream().collect(Collectors.toMap(Node::getId, d -> d));
            nodes = nodes.stream().filter(n -> !extraNodeMap.containsKey(n.getId())).collect(Collectors.toList());
        }

        // 获取所有路径 (末尾)
        List<List<Node>> paths = new ArrayList<>();
        for (Node node : nodes) {
            List<Node> path = nodePath.getPathsByEndId(node.getId());
            if (!CollectionUtils.isEmpty(path)) {
                paths.add(subPaths(path, startNode));
            }
        }

        // 路径去重（某个路径可能全在）
        paths = NodeUtils.filterSubPaths(paths);

        // 路径排序 最长在最前面
        paths.sort((d1, d2) -> d2.size() - d1.size());

        return paths;
    }

    /**
     * 路径截取
     * 末端是中压用户接入点 或者 站房进线开关 或者 不是配变
     */
    private static List<Node> subPaths(List<Node> path, Node startNode) {
        // 截取路径  如果末端是配变  那么就从上一个节点
        // 如果是站房类型 那么进行开关就是
        Node endNode = null;

        // 1、判断是否存在中压用户接入点
        Node usePoint = ListUtils.findLast(path, Node::isUserPoint);
        if (usePoint != null) {
            endNode = usePoint;
        }

        // 2、站房进线开关节点（可能没有开关，不过进线设备也行）
        if (endNode == null) {
            // 表示末节点在站房内
            if (path.get(path.size() - 1).getParent() != null) {
                List<Node> pass = new ArrayList<>();
                for (int i = path.size() - 1; i >= 0; i--) {
                    Node node = path.get(i);
                    // 表示当前走出站房了  那么就找进行开始的开关或者设备
                    if (node.getParent() == null) {
                        endNode = ListUtils.findFirst(pass, n -> n.isKg("all"));
                        // 没有开关 那就默认第一个设备吧
                        if (endNode == null) {
                            endNode = ListUtils.findFirst(pass, n -> !n.isEdge());
                        }
                        break;
                    }
                    pass.add(0, node);
                }
            }
        }

        // 3、末端不是配变
        if (endNode == null) {
            endNode = ListUtils.findLast(path, n -> !n.isPb() && !n.isEdge());
        }

        return NodeUtils.subLiceNode(path, startNode, endNode);
    }
}
