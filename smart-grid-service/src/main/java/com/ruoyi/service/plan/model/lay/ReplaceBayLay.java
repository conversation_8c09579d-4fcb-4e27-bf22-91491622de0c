package com.ruoyi.service.plan.model.lay;

import com.ruoyi.entity.plan.bo.ReplaceBayNodeProp;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.util.PlanProcessUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 替换变电站的间隔
 */
public class ReplaceBayLay extends BaseLay{

    public ReplaceBayLay(List<Node> layNodes) {
        this.layNodes = layNodes;
        this.type = REPLACE_BAY_TYPE;
    }

    private List<Node> layNodes;

    @Override
    public List<Node> getLayNodes() {
        return layNodes == null ? new ArrayList<>() : layNodes;
    }

    @Override
    public int getScope() {
        return 80;
    }

    @Override
    public List<Object> toStuStr() {
        if (CollectionUtils.isEmpty(layNodes)) {
            return new ArrayList<>();
        }
        Node bayKg = NodeUtils.findNode(layNodes, (n) -> n.getReplaceBayNode() != null);
        ReplaceBayNodeProp replaceBayNode = bayKg.getReplaceBayNode();

        ArrayList<Object> strList = new ArrayList<>();
        if (replaceBayNode != null) {
            strList.add("线路");
            strList.add(PlanProcessUtils.toStrikingStu(replaceBayNode.getReplaceFeederName()));

            strList.add("原母线");
            strList.add(PlanProcessUtils.toStrikingStu(replaceBayNode.getSourceBusName()));
            strList.add("原间隔开关");
            strList.add(PlanProcessUtils.toStrikingStu(replaceBayNode.getSourceKgName()));

            strList.add("替换为母线");
            strList.add(PlanProcessUtils.toStrikingStu(replaceBayNode.getReplaceBusName()));
            strList.add("间隔开关");
            strList.add(PlanProcessUtils.toStrikingStu(replaceBayNode.getReplaceKgName()));
        }
        return strList;
    }
}
