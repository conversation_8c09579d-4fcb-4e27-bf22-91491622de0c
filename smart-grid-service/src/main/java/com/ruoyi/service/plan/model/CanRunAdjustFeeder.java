package com.ruoyi.service.plan.model;

import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 能运放调整开关操作组合
 */
@Data
public class CanRunAdjustFeeder {

    public CanRunAdjustFeeder(List<List<FeederTransferCap>> onlyFTrs, List<List<FeederTransferCap>> singleCanFTrs, List<CombFeederTransfer> combCanFTrs) {
        this.onlyFTrs = onlyFTrs;
        this.singleCanFTrs = singleCanFTrs;
        this.combCanFTrs = combCanFTrs;
    }

    // 仅仅减少一部分的
    List<List<FeederTransferCap>> onlyFTrs;

    // 能直接满足的 当前线都减少小于最大 并且 联络新增的也小于最大
    List<List<FeederTransferCap>> singleCanFTrs;

    /**
     * 当前组合线路转供集合
     */
    List<CombFeederTransfer> combCanFTrs;
}
