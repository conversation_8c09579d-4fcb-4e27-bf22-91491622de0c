package com.ruoyi.service.plan;

public interface IConditionService {

    /**
     * 查询分段配变不合理————条件是 配变数量的 一般问题最小值
     *
     * @param supplyArea
     * @return
     */
    Integer unreasonablePBNum(String supplyArea);

    /**
     * 查询挂架配变过多————条件是 配变数量的  一般问题最小值
     *
     * @return
     */
    Integer pylonsPBNum();

    /**
     * 查询大分支无联络———— 条件是 配变数量 一般问题最小值
     *
     * @return
     */
    Integer bigBranchSegmentation();

    /**
     * 查询大分支无联络———— 条件是 配变数量 一般问题最小值
     *
     * @return
     */
    Integer bigBranchCapacityNum();

    /**
     * 查询线路重过载———— 条件是 负载率 一般问题最小值
     *
     * @return
     */
    Integer lineOverloadNum();

}
