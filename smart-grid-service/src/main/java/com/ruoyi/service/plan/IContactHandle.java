package com.ruoyi.service.plan;

import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.vo.BusbarSwitchVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public interface IContactHandle {

    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOpMap, List<NearbyDeviceInfoVo> nearDevs, List<ProcessContactVo> usePContVos, String token);

    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOpMap, List<NearbySubstationInfoVo> bdzBays, List<ProcessContactVo> usePContVos, String token);

    default List<NearNode> toNearNodeList(List<Node> nodeList) {
        return nodeList.stream().map(this::toNearNode).collect(Collectors.toList());
    }

    default NearNode toNearNode(Node node) {
        return new NearNode(node, node.toDeviceCoords());
    }

    /**
     * 过滤掉已经使用的环网柜或者杆塔
     */
    default List<NearbyDeviceInfoVo> filterNearDevByUseContVos(List<NearbyDeviceInfoVo> nearDevs, List<ProcessContactVo> usePContVos) {
        if (CollectionUtils.isEmpty(usePContVos)) {
            return nearDevs;
        }

        Set<String> psrSet = usePContVos.stream().map(d -> StringUtils.isNotBlank(d.getEndStationPsrId()) ? d.getEndStationPsrId() : d.getEndPsrId()).collect(Collectors.toSet());
        return nearDevs.stream().filter(d -> !psrSet.contains(d.getPsrId())).collect(Collectors.toList());
    }

    /**
     * 过滤掉已经使用的也没有剩余间隔的变电站
     */
    default List<NearbySubstationInfoVo> filterNearBdzByUseContVos(List<NearbySubstationInfoVo> nearBdzs, List<ProcessContactVo> usePContVos) {
        if (CollectionUtils.isEmpty(usePContVos)) {
            return nearBdzs;
        }

        // 当前变电站ID集合
        HashSet<String> filterIdSet = new HashSet<>();

        // 已经使用的间隔开关集合
        Set<String> useBayIdSet = usePContVos.stream().map(ProcessContactVo::getEndPsrId).collect(Collectors.toSet());
        for (NearbySubstationInfoVo nearBdz : nearBdzs) {
            // 判断当前变电站是否已经还有剩余的间隔

            // 过滤掉已经使用的剩余间隔开关
            List<BusbarSwitchVo> noUseBayKg = nearBdz.getBusbarSwitchVoList().stream()
                    .filter(d -> !useBayIdSet.contains(d.getSwitchId())).collect(Collectors.toList());

            // 需要过滤掉的变电站
            if (noUseBayKg.isEmpty()) {
                filterIdSet.add(nearBdz.getPsrId());
            }
        }

        return nearBdzs.stream().filter(d -> !filterIdSet.contains(d.getPsrId())).collect(Collectors.toList());
    }
}
