package com.ruoyi.service.plan.impl.test;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

public class FlexibleArrayCombinationWithConditions {

    public static List<List<Integer>> findCombinations(int[][] nums, int maxSum,
                                                       Predicate<List<Integer>> customCondition) {
        List<List<Integer>> result = new ArrayList<>();
        if (nums == null || nums.length == 0) return result;

        backtrack(nums, 0, new ArrayList<>(), 0, maxSum, customCondition, result);
        return result;
    }

    private static void backtrack(int[][] nums, int row, List<Integer> current,
                                  int currentSum, int maxSum,
                                  Predicate<List<Integer>> customCondition,
                                  List<List<Integer>> result) {
        // 检查当前组合是否满足条件
        if (currentSum < maxSum && !current.isEmpty() && customCondition.test(current)) {
            result.add(new ArrayList<>(current));
        }

        // 终止条件
        if (row >= nums.length) {
            return;
        }

        // 选项1：从当前行取一个数字（如果行不为空）
        if (nums[row] != null && nums[row].length > 0) {
            for (int num : nums[row]) {
                if (currentSum + num < maxSum) {
                    current.add(num);
                    backtrack(nums, row + 1, current, currentSum + num, maxSum, customCondition, result);
                    current.remove(current.size() - 1);
                }
            }
        }

        // 选项2：跳过当前行
        backtrack(nums, row + 1, current, currentSum, maxSum, customCondition, result);
    }

    public static void main(String[] args) {
        int[][] nums = {
                {2, 5, 8},
                {3, 6, 9},
                {1, 4, 7},
                {2, 3, 5}
        };
        int maxSum = 20;

        // 示例自定义条件1：组合必须包含至少3个数字
        Predicate<List<Integer>> minSizeCondition = list -> list.size() >= 3;

        // 示例自定义条件2：组合不能同时包含5和3
        Predicate<List<Integer>> excludeCondition = list -> !(list.contains(5) && list.contains(3));

        // 组合条件：总和<20 且 至少3个数字 且 不包含5和3同时出现
        Predicate<List<Integer>> combinedCondition = minSizeCondition.and(excludeCondition);

        // 查找满足条件的组合
        List<List<Integer>> combinations = findCombinations(nums, maxSum, combinedCondition);

        System.out.println("满足条件的组合（总和<20，至少3个数字，不包含5和3同时出现）:");
        for (List<Integer> combo : combinations) {
            int sum = combo.stream().mapToInt(Integer::intValue).sum();
            System.out.println(combo + " (总和: " + sum + ", 大小: " + combo.size() + ")");
        }
    }
}
