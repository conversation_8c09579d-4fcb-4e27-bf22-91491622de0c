package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.SegmentationEnum;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.problem.impl.ProblemIdentifyServiceImpl;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 单辐射无联络线网架方案生成
 * 1、单辐射末端新增与其他临近线路联络
 * 2、新出线路转移单辐射线路负荷并形成联络(单辐射最大负载率大于80%可作为比选方案) TODO 后续考虑
 * 3、单辐射相关用户改接至周边临近线路后退出运行(单辐射最大负载率小于20%可作为比选方案) TODO 后续考虑
 */
@Service
@Slf4j
public class SingleRadiationPlan implements IGeneratePlan {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ProblemIdentifyServiceImpl problemIdentifyService;

    @Autowired
    ISingMapService singMapService;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        // 在结尾末端
        planProcessService.pushLoadingProcess(problemId, "解析网架拓扑结构中");

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();

        // znap拓扑节点构建
        List<Node> allPb = nodePath.getNodeList().stream().filter(Node::isPb).collect(Collectors.toList());

        // 节点路径分析
        planProcessService.pushLoadingProcess(problemId, "问题结构化中");

        // 校验当前是否单辐射问题 有问题抛异常
        if (problemIdentifyService.singleRadiationSegmentation(feederId) != SegmentationEnum.SOMETHING) {
            throw new RuntimeException("当前暂无问题，不需要生成方案！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：问题释义
        pushPlanProcessService.pushSingleRadiationExplain(problemId);

        // (4)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (5)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // TODO 如果当前联络线没有配变 是不是不需要考虑呢

        // 计算放置开关或者联络线路径范围组合

        List<ArrayList<Node>> allNodePaths = nodePath.getAllNodePaths();
        CombAlternateOp combAlternateOp = ContactLayPlanOp.getEndContactCombAltOpByPaths(allNodePaths,nodePath);

        // 给联络开关加装坐标
        baseGeneratePlan.layPositionCoords(combAlternateOp);

        SurePlanOp surePlanOp = null;
        try {
            surePlanOp = processNodeService.handleLayOperateNode(problemId, combAlternateOp, token, feederId, nodePath);
        } catch (Exception e) {
            log.error("生产方案异常！", e);
            throw new RuntimeException("生产方案异常！", e);
        }

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (6)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // 返回所有方案，不限制数量
        List<Plan> resultPlans = plans;

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        // 获取各个分支末端
        return plans;
    }

    /**
     * 所有的分支线通过放置的排序
     */
    void sortBranchPaths(List<ArrayList<Node>> branchPaths) {
        branchPaths.sort((paths1, paths2) -> getSortScore(paths2) - getSortScore(paths1));
    }

    /**
     * 各个分支路径排序
     * 优先末端  末端是杆塔 > 其它（配变什么的）
     */
    int getSortScore(List<Node> paths) {
        Node lastNode = paths.get(paths.size() - 1);
        int result = paths.size();

        // 杆塔
        if (lastNode.isPole()) {
            result = result + 50;
        }
        return result;
    }

}
