package com.ruoyi.service.plan.findLay.replaceBayLay;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.vo.FeederVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.model.sameBusContactPlan.CombReplaceBay;
import com.ruoyi.service.plan.utils.LayNodeFactory;
import com.ruoyi.service.plan.utils.ListCombinations;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import com.ruoyi.vo.BusbarSwitchVo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 更换间隔工具类型
 */
public class FindReplaceBayLay {


    /**
     * 查找更换间隔所有的操作组合 （将所有的同母联络开关都切换到别的间隔）
     *
     *
     * @param canBusKgs 可以更换有备用的间隔集合
     */
    public static List<PlanOperate> findReplaceBays(BusbarSwitchVo mainBusKg, List<BusbarSwitchVo> canBusKgs, HashMap<String, BusbarSwitchVo> contactBusKgMap, NodePath nodePath) {

        // 同母的联络线路开关
        List<FeederVo> someCFeeders = nodePath.getContactFeederVos().stream().filter(f -> {
            BusbarSwitchVo busbarSwitchVo = contactBusKgMap.get(f.getFeederId());
            if (busbarSwitchVo == null) {
                return false;
            }
            return StringUtils.equals(busbarSwitchVo.getBusbarId(), mainBusKg.getBusbarId());
        }).collect(Collectors.toList());

        List<PlanOperate> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(canBusKgs)) {
            return result;
        }

        // =========================== 生成所有可以更换的间隔组合 =======================
        List<List<CombReplaceBay>> combList = new ArrayList<>();
        List<List<ListCombinations.Pair>> combinations = ListCombinations.generateCombinations(someCFeeders, canBusKgs);

        // 输出所有可能的组合
        for (int i = 0; i < combinations.size(); i++) {
            List<CombReplaceBay> comb = new ArrayList<>();
            for (ListCombinations.Pair pair : combinations.get(i)) {
                FeederVo feederVo = (FeederVo) pair.getFirst();
                BusbarSwitchVo busKg = (BusbarSwitchVo) pair.getSecond();
                comb.add(new CombReplaceBay(contactBusKgMap.get(feederVo.getFeederId()), busKg, feederVo.getFeederId(), feederVo.getFeederName()));
            }
            combList.add(comb);
        }

        // 返回所有组合，不限制数量
        for (List<CombReplaceBay> combReplaceBays : combList) {
            // 生成替换节点
            List<BaseLay> lays = combReplaceBays.stream().map(FindReplaceBayLay::createReplaceBayLay).collect(Collectors.toList());
            result.add(PlanOperateFactory.createReplaceLay(lays));
        }
        return result;
    }

    /**
     * 生成线路更换间隔的节点集合
     */
    public static List<Node> createReplaceBayNodes(CombReplaceBay combReplaceBay) {
        BusbarSwitchVo sourceBusKg = combReplaceBay.getSourceBusKg();
        BusbarSwitchVo replaceBusKg = combReplaceBay.getReplaceBusKg();

        String feederId = combReplaceBay.getFeederId();
        String feederName = combReplaceBay.getFeederName();

        // 创建更换开关节点
        Node feederNode = NodeFactory.createNode(UUID.randomUUID().toString(), feederId, feederName);
        feederNode.setType(Node.TYPE_PSR);
        feederNode.setPsrType("dkx");
        feederNode.setEdge(true);

        // 设置联络开关的更换间隔属性
        feederNode.setReplaceBayNode(feederId, feederName, sourceBusKg, replaceBusKg);

        return Collections.singletonList(feederNode);
    }

    public static BaseLay createReplaceBayLay(CombReplaceBay combReplaceBay) {
        return LayNodeFactory.createReplaceBay(createReplaceBayNodes(combReplaceBay));
    }

}
