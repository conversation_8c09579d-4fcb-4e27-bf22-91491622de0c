package com.ruoyi.service.plan.processNode.layHandle.contactHandle.process;

import com.ruoyi.entity.map.ClosestNode;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.processNode.layHandle.ProcessUtils;
import com.ruoyi.vo.BusbarSwitchVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BdzDevProcess {

    /**
     * 加工处理遍变电站新出线
     *
     * @param constantType 联络类型
     */
    public ProcessContactVo processHandle(NearNode nearNode, String constantType, List<String> useBayIds) {
        // 每个节点都单独处理
        // 根据开始和结束节点 调用产生路由
        return processBdzNewLineRoute(nearNode, constantType, useBayIds);
    }

    /**
     * 加工变电站新出线路由
     */
    ProcessContactVo processBdzNewLineRoute(NearNode nearNode, String constantType, List<String> useBayIds) {
        Node node = nearNode.getNode();

        ClosestNode closestNode = nearNode.getClosestNode();
        if (closestNode == null) {
            return null;
        }
        NearbySubstationInfoVo nearBdz = (NearbySubstationInfoVo) closestNode.getClosest();
        BusbarSwitchVo busKg = nearBdz.getCanBay(useBayIds);

        // 路由路径节点集合
        List<Node> nodeRouteNodeList = closestNode.getRouteNodeList();
        ProcessContactVo processContactVo = new ProcessContactVo();

        // 联络线路径节点加工
        List<Node> newPathNodes = processBdzNewLine(nodeRouteNodeList, node, constantType, busKg);

        // 生产加工路径对象
        processContactVo.setTotalLength(closestNode.getLength());
        processContactVo.setContactNodeList(newPathNodes);
        processContactVo.setStartPsrId(node.getPsrId());
        processContactVo.setEndPsrId(busKg.getSwitchId());

        processContactVo.setEndStationPsrId(busKg.getStationPsrId());
        processContactVo.setEndStationPsrType(busKg.getStationPsrType());
        processContactVo.setEndStationPsrName(busKg.getStationPsrName());

        processContactVo.setEndBusPsrId(busKg.getBusbarId());
        processContactVo.setEndBusPsrName(busKg.getBusbarName());

//        processContactVo.setBdzBayKgId(busKg.getSwitchId());
//        processContactVo.setBdzBayKgName(busKg.getSwitchName());
        processContactVo.setContactNode(node);

        return processContactVo;
    }

    /**
     * 加工联络线的路径节点集合
     *
     * @param nodes        路由路径集合节点
     * @param constantType 联络类型 参考ProcessContactBo里面的type
     * @param startNode    结束杆塔节点
     * @param
     */
    List<Node> processBdzNewLine(List<Node> nodes, Node startNode, String constantType, BusbarSwitchVo busKg) {

        List<Node> result = ProcessUtils.processContactNodes(nodes, startNode, constantType, (linkNode) -> {
            linkNode.setBdzNewLine(busKg);
            return null;
        });

        // 2、处理末尾
        if (nodes.size() > 2) {
            Node endNoe = ProcessUtils.toNode(busKg.getSwitchId(), busKg.getSwitchType(), busKg.getSwitchName(), busKg.getLngLat());

            endNoe.setStation(busKg.getStationPsrId(), busKg.getStationPsrType(), busKg.getStationPsrName());
            result.remove(result.size() - 1);
            Node edge = result.get(result.size() - 1);
            endNoe.addEdge(edge, false);
            result.add(endNoe);
        }
        return result;
    }

}
