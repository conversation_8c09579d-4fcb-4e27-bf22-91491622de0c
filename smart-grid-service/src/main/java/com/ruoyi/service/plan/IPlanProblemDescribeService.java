package com.ruoyi.service.plan;

import com.ruoyi.entity.plan.vo.MarkVo;
import com.ruoyi.graph.Node;

import java.util.HashMap;
import java.util.List;

public interface IPlanProblemDescribeService {

    /**
     * 获取故障解决方案详细信息
     *
     * @param id 主键
     */
    List<Object> selectDetails(Long id);

    /**
     * 获取故障解决方案相关问题的不合理标记设备
     * @param id 主键
     */
    List<HashMap<String, Object>> selectFaultMark(Long id);
}
