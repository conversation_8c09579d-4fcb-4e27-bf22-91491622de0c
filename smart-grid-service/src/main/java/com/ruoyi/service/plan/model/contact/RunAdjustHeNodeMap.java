package com.ruoyi.service.plan.model.contact;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 运方调整合闸开关对象
 */
@Data
public class RunAdjustHeNodeMap {

    public RunAdjustHeNodeMap(Node heNode, Node nextNode, List<Node> fenNodes, List<Node> path) {
        this.heNode = heNode;
        this.nextNode = nextNode;
        this.fenNodes = fenNodes;
        this.path = path;
    }

    /**
     * 合闸节点
     */
    Node heNode;

    /**
     * 合闸节点的下一个节点（用于辨别方向）
     */
    Node nextNode;

    /**
     * 分闸节点的集合
     */
    List<Node> fenNodes;

    /**
     * 当前联络开关所处的路径
     */
    List<Node> path;
}
