package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.SegFeeder;
import com.ruoyi.entity.device.StationConfiguration;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.StationNodeUtils;
import com.ruoyi.mapper.device.SegFeederMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.processNode.ProcessNearNode;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.process.BdzDevProcess;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.process.NearDevProcess;
import com.ruoyi.util.coordinates.CoordinateConverter;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.constant.NodeConstants.LINE_TYPE_LINEAR;
import static com.ruoyi.constant.NodeConstants.SHAPE_KEY_FEEDER_DL;
import static com.ruoyi.graph.Node.TYPE_SELF;

/**
 * 新增环网柜处理
 */
@Component
public class AddHwgContactHandle implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ProcessNearNode processNearNode;

    @Autowired
    NearDevProcess nearDevProcess;

    @Autowired
    BdzDevProcess bdzDevProcess;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    SegFeederMapper segFeederMapper;

    @Override
    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbyDeviceInfoVo> nearDevs, List<ProcessContactVo> usePContVos, String token) {

        AddHwgBo addHwgBo = processAddHwg(lay);
        List<Node> hwgNodes = addHwgBo.getHwgNodes();
        Node nodeBay = addHwgBo.getNodeBay();
        NearNode nearNode = toNearNode(nodeBay);

        // 根据use使用的节点过滤掉nearDevs （联络线末尾联络点 不能连接在同一个站房下或者杆塔下）
        nearDevs = filterNearDevByUseContVos(nearDevs, usePContVos);

        // 加工
        processNearNode.processDevRoute(nearNode, nearDevs, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = nearDevProcess.processHandle(nearNode, ProcessContactBo.SEG_ADD_HWG);

        processContactVo.getContactNodeList().addAll(NodeUtils.copyNodes(hwgNodes));
        lay.setProcessContactVo(processContactVo);
    }

    @Override
    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbySubstationInfoVo> bdzBays, List<ProcessContactVo> usePContVos, String token) {

        AddHwgBo addHwgBo = processAddHwg(lay);
        List<Node> hwgNodes = addHwgBo.getHwgNodes();
        Node nodeBay = addHwgBo.getNodeBay();
        NearNode nearNode = toNearNode(nodeBay);

        // 根据use使用的节点过滤掉已使用没有剩余的间隔的变电站
        bdzBays = filterNearBdzByUseContVos(bdzBays, usePContVos);

        // 已经使用的剩余间隔ID集合
        List<String> useBayIds = usePContVos.stream().map(ProcessContactVo::getEndPsrId).collect(Collectors.toList());

        // 加工最近的变电站
        processNearNode.processBdzRoute(nearNode, bdzBays, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = bdzDevProcess.processHandle(nearNode, ProcessContactBo.SEG_ADD_HWG, useBayIds);

        processContactVo.getContactNodeList().addAll(NodeUtils.copyNodes(hwgNodes));
        lay.setProcessContactVo(processContactVo);
    }

    @Data
    private class AddHwgBo {
        public AddHwgBo(Node nodeBay, List<Node> hwgNodes) {
            this.nodeBay = nodeBay;
            this.hwgNodes = hwgNodes;
        }

        Node nodeBay;

        List<Node> hwgNodes;
    }

    private AddHwgBo processAddHwg(ContactLay lay) {

        ProcessContactBo processContact = lay.getProcessContactBo();
        SegBreakNodeBo segBreakNode = processContact.getSegBreakNode();

        // 处理放置的中线坐标点
        segBreakNodePoint(segBreakNode);

        // ===========================  =======================

        // 环网柜节点结合集合
        List<Node> hwgNodes = breakSegAddHwg(segBreakNode);

        // 没有使用的间隔
        List<Node> nodeNoUses = hwgNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
        Node nodeBay = nodeNoUses.get(0);

        return new AddHwgBo(nodeBay, hwgNodes);
    }


    /**
     * 加工导线段打断的中心坐标
     */
    public void segBreakNodePoint(SegBreakNodeBo segBreakNode) {
        Node segEdge = segBreakNode.getSegEdge();

        //1、使用开始和结束
        List<Double> startPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getStartNode().getPsrId(), segBreakNode.getStartNode().getPsrType());
        List<Double> endPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getEndNode().getPsrId(), segBreakNode.getEndNode().getPsrType());

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(startPoint) || com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(endPoint)) {
            SegFeeder segFeeder = segFeederMapper.selectSegFeeder(segEdge.getPsrId());
            if (segFeeder != null && StringUtils.isNotBlank(segFeeder.getCoordinate())) {
                List<List<Double>> coords = CoordinateConverter.parseCoordinates(segFeeder.getCoordinate());
                startPoint = coords.get(0);
                endPoint = coords.get(coords.size() - 1);
            }
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(startPoint) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(endPoint)) {
            LineString lineString = CoordinateConverter.toLineString(Arrays.asList(startPoint, endPoint));
            Point centroid = lineString.getCentroid();

            segBreakNode.setStartPoint(startPoint);
            segBreakNode.setEndPoint(endPoint);
            segBreakNode.setPoint(centroid);
        }
    }

    /**
     * 导线段在中间打断 并且在中间上新的环网柜
     *
     * @param segBreakNode 导线段对线
     */
    List<Node> breakSegAddHwg(SegBreakNodeBo segBreakNode) {

        // 设备类型zf07 获取站房以及以下的node
        List<Double> coordinateList = segBreakNode.toLngLat();
        List<Node> stationNodes = StationNodeUtils.generateStationAndChildren("环网柜", "zf07", coordinateList, 6, new StationConfiguration());

        Node startNode = segBreakNode.getStartNode().clone();
        Node endNode = segBreakNode.getEndNode().clone();
        Node segEdge = segBreakNode.getSegEdge().clone();

        // =========================== 创建两边导线段 =======================

        List<Node> nodeNoKgs = stationNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
        Node firstBayNode = nodeNoKgs.get(0);
        Node lastBayNode = nodeNoKgs.get(nodeNoKgs.size() - 1);

        Node startEdge = createEdge(segEdge, segBreakNode.getStartPoint(), firstBayNode.getGeometry());
        startEdge.setPsrName("起始导线段");

        Node endEdge = createEdge(segEdge, segBreakNode.getEndPoint(), lastBayNode.getGeometry());
        endEdge.setPsrName("结束导线段");

        // =========================== 处理节点 =======================
        // TODO 并且将开关的名称改为”已使用“（后面有空再改为”有XXXX供，至XXXXXX变“）

        //新增起始点和起始导线段的关系 新增第一个间隔和起始导线段的关系
        shtUseLink(startEdge, startNode, firstBayNode, true);

        //新增第二个间隔和结束导线段的关系 结束点和起始结束导线段
        shtUseLink(endEdge, endNode, lastBayNode, false);

        //删除中间导线段,新增导线段的properties属性"remove", "true"
        segEdge.setRemoveNode();

        // =========================== 最后返回 =======================

        stationNodes.addAll(Arrays.asList(startEdge, endEdge, startNode, endNode, segEdge));

        // 设置psrId
        for (Node node : stationNodes) {
            if (StringUtils.isBlank(node.getPsrId())) {
                node.setPsrId(node.getId());
            }
        }

        return stationNodes;
    }

    Node createEdge(Node edge, List<Double> point, Geometry bayGeo) {
        LineString lineString = CoordinateConverter.toLineString(Arrays.asList(point, CoordinateConverter.pointToLngLat(bayGeo.getInteriorPoint())));

        String shapeKey = StringUtils.equals(edge.getPsrType(), "dxd") ? NodeConstants.SHAPE_KEY_FEEDER_JK : NodeConstants.SHAPE_KEY_FEEDER_DL;

        Node result = edge.clone();
        result.setEdge(true);
        result.setId(UUID.randomUUID().toString());
        result.setPsrId(null);

        result.setLineType(LINE_TYPE_LINEAR);
        result.setShapeKey(shapeKey);
        result.setType(TYPE_SELF);
        result.setGeometry(lineString);
        result.putProperties("type", shapeKey);

        return result;
    }

    void shtUseLink(Node edge, Node linkNode, Node bayNode, boolean isSource) {
        //新增起始点和起始导线段的关系
        linkNode.addEdge(edge, isSource);

        //新增第一个间隔和起始导线段的关系
        bayNode.addEdge(edge, !isSource);
        bayNode.setPsrName("已使用");
    }
}
