package com.ruoyi.service.plan;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.bo.PlanBo;
import com.ruoyi.entity.plan.vo.PlanGroupResultVo;
import com.ruoyi.entity.plan.vo.PlanVo;
import com.ruoyi.entity.plan.PlanAnalysisState;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.BranchNodeVo;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.entity.plan.vo.PlanCost;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.vo.BusbarSwitchVo;
import com.ruoyi.vo.ContactSwitchVo;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 故障解决方案Service接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface IPlanService {

    ArrayList<SegBetween> getSegBetweenList(String feederId, String deviceId);

    public List<Node> getMainPath(String feederId);

    public PlanGroupResultVo queryByProblemId(Long problemId);

    /**
     * 查询故障解决方案
     */
    PlanVo queryById(Long id);

    /**
     * 查询故障解决方案列表
     */
    TableDataInfo<PlanVo> queryPageList(PlanBo bo);

    /**
     * 查询故障解决方案列表
     */
    List<PlanVo> queryList(PlanBo bo);

    /**
     * 新增故障解决方案
     */
    Boolean insertByBo(PlanBo bo);

    /**
     * 修改故障解决方案
     */
    Boolean updateByBo(PlanBo bo);

    /**
     * 校验并批量删除故障解决方案信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 按问题id查询相关的故障解决方案信息
     */
    List<PlanVo> byProblemId(Long id);

    /**
     * 分析获取并且获取方案
     */
    String analysisGeneratePlan(GeneratePlanBo generatePlanBo);


    /**
     * 暂停任务
     */
    Boolean pauseTask(Long taskId);

    /**
     * 恢复任务
     */
    Boolean resumeTask(Long taskId);

    /**
     * 停止任务
     */
    Boolean stopTask(String taskId);

    /**
     * 根据问题id查询状态实例
     */
    PlanAnalysisState selectState(String problemId);

    PlanCost planCost(Long planId) throws JsonProcessingException;

    List<ContactSwitchVo> getBusbarSwitchByProblemId(Long problemId);

    List<BranchNodeVo>  getBigBranch(String feederId);
}
