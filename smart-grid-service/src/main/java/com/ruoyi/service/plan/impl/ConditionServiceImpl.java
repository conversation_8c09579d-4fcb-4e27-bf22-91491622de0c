package com.ruoyi.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.Condition;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.plan.enuw.SupplyAreaEnum;
import com.ruoyi.entity.problem.ProblemRuleConfiguration;
import com.ruoyi.mapper.problem.ProblemRuleMapper;
import com.ruoyi.service.plan.IConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 计算每种规则，在特定条件下对应的最小值
 */

@Service
public class ConditionServiceImpl implements IConditionService {
    @Autowired
    ProblemRuleMapper problemRuleMapper;


    /**
     * 查询分段配变不合理————条件是配变数量不合理的一般问题最小值
     *
     * @param supplyArea
     * @return
     */
    @Override
    public Integer unreasonablePBNum(String supplyArea) {
        String translate = SupplyAreaEnum.fromCode(supplyArea).getGrade();
        //查询该线路的分段配变不合理一般问题（问题类型是一般问题，二级分类code是分段配变不合理，条件是线路的供电区域）
        LambdaQueryWrapper<ProblemRuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getProblemSituation, ProblemRuleConfiguration.GENERA);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, PlanConstants.SEG_PB_MUSH_LEVEL);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCondition1, translate + "类");
        ProblemRuleConfiguration problemRuleConfiguration = problemRuleMapper.selectOne(lambdaQueryWrapper);
        return problemRuleConfiguration.getConditionMax1();
    }

    /**
     * 查询挂架配变过多————条件是 配变数量的  一般问题最小值
     *
     * @return
     */
    @Override
    public Integer pylonsPBNum() {
        //查询该线路的挂架配变过多一般问题（问题类型是一般问题，二级分类code是挂架配变过多）
        LambdaQueryWrapper<ProblemRuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getProblemSituation, ProblemRuleConfiguration.GENERA);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, PlanConstants.SEG_XLGJPB_MUSH_LEVEL);
        ProblemRuleConfiguration problemRuleConfiguration = problemRuleMapper.selectOne(lambdaQueryWrapper);
        return problemRuleConfiguration.getConditionMin1();
    }

    /**
     * 查询大分支无联络———— 条件是 配变数量 一般问题最小值
     *
     * @return
     */
    @Override
    public Integer bigBranchSegmentation() {
        //查询挂接配变的条件最小数量要求
        LambdaQueryWrapper<ProblemRuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getProblemSituation, ProblemRuleConfiguration.GENERA);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, PlanConstants.SEG_DFZWLN_MUSH_LEVEL);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCondition1, Condition.DFZWLL_DFZGJPB);
        ProblemRuleConfiguration problemRuleConfiguration = problemRuleMapper.selectOne(lambdaQueryWrapper);
        return problemRuleConfiguration.getConditionMin1();
    }

    /**
     * 查询大分支无联络———— 条件是 配变数量 一般问题最小值
     *
     * @return
     */
    @Override
    public Integer bigBranchCapacityNum() {
        //查询最容量的条件最小数量要求
        LambdaQueryWrapper<ProblemRuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getProblemSituation, ProblemRuleConfiguration.GENERA);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, PlanConstants.SEG_DFZWLN_MUSH_LEVEL);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCondition1, Condition.DFZWLL_ZJRL);
        ProblemRuleConfiguration conditionProblemRuleConfiguration = problemRuleMapper.selectOne(lambdaQueryWrapper);
        return conditionProblemRuleConfiguration.getConditionMin1();
    }

    @Override
    public Integer lineOverloadNum() {
        //查询最容量的条件最小数量要求
        LambdaQueryWrapper<ProblemRuleConfiguration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getProblemSituation, ProblemRuleConfiguration.GENERA);
        lambdaQueryWrapper.eq(ProblemRuleConfiguration::getCategoryLevel2Code, PlanConstants.SEG_XLZGZ_MUSH_LEVEL);
        ProblemRuleConfiguration conditionProblemRuleConfiguration = problemRuleMapper.selectOne(lambdaQueryWrapper);
        return conditionProblemRuleConfiguration.getConditionMin1();
    }
}
