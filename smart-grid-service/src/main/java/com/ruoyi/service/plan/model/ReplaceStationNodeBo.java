package com.ruoyi.service.plan.model;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 替换站房
 */
@Data
public class ReplaceStationNodeBo {
    public ReplaceStationNodeBo(Node stationNode, List<Node> paths) {
        this.stationNode = stationNode;
        this.paths = paths;
    }

    /**
     * 站房节点
     */
    private Node stationNode;

    /**
     * 当前的路径
     */
    private List<Node> paths;

}
