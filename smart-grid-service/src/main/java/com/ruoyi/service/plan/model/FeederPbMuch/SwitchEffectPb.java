package com.ruoyi.service.plan.model.FeederPbMuch;

import com.ruoyi.graph.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
/**
 * 受联络开关影响的路径
 */
public class SwitchEffectPb {
    //起点开关
    private String startPsrId;
    //终点开关
    private String endPsrId;
    //路径nodes
    private List<Node> nodeList;
    //配变数量
    private Integer pbNum;
}
