package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.findLay.contactLay.utils.NumModelUtil;
import com.ruoyi.service.plan.model.SegPbMuchPlan.SegLayPathBo;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.findLay.NodeNumModel;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.problem.ProblemRuleMapper;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.findLay.segLay.FindSegPbMuchLay;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 生成分段内配变数量不合理方案
 */
@Component
@Slf4j
public class SegPbMuchPlan implements IGeneratePlan {

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    ProblemRuleMapper problemRuleMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) throws ExecutionException, InterruptedException {

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<Node> kgContactNodes = nodePath.getContactKgNodes();

        // 主干分段对象
        SegBetween segBetween = segBranchService.getMainSegBetween(nodePath, deviceId, kgContactNodes);

        if (segBetween == null) {
            throw new RuntimeException("暂未获取当前的分段，请确定数据正确性！");
        }

        // 分支对象
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        List<Node> betweenNodes = segBetween.getBetweenNodes(true);
        List<Node> allPb = segBetween.getAllPb();

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // 最大限制
        int maxNums = iConditionService.unreasonablePBNum(deviceFeeder.getSupplyArea());

        // (3)、推送：问题释义
        pushPlanProcessService.pushExplain(problemId, maxNums);

        // (4)、推送：分段识别
        pushPlanProcessService.pushSegIdentify(problemId, segBetween);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // 主干路径
        List<SegLayPathBo> mainPathsBos = getSegLayPaths(betweenNodes, branchNodeMap, maxNums);

        // 计算放置开关或者联络线路径范围组合
        CombAlternateOp combAlternateOp = getLayPositionList(mainPathsBos, maxNums, nodePath);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(combAlternateOp);

        SurePlanOp surePlanOp = processNodeService.handleLayOperateNode(problemId, combAlternateOp, token, feederId, nodePath);

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // 返回所有方案，不限制数量
        List<Plan> resultPlans = plans;

        // TODO 过滤负载率高的线路 以及 专供过去 必须保持负载率通过

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        return resultPlans;
    }


    /**
     * 获取需要递归查找分段的路径
     * 按道理当前主干路径即可，但是 有一种特殊的线路 当前主干线路的大分子配变数量超过最大限定值，那么该大分子需要末端新增联络线
     */
    private List<SegLayPathBo> getSegLayPaths(List<Node> betweenNodes, HashMap<String, BranchNode> branchNodeMap, int maxNums) {

        List<SegLayPathBo> results = new ArrayList<>();

        ArrayList<Node> nodes = new ArrayList<>();

        for (int i = 0; i < betweenNodes.size(); i++) {
            Node node = betweenNodes.get(i);
            List<Node> edges = node.isEdge() ? null : node.getEdges();
            nodes.add(node);
            // 杆塔
            if (node.isPole()) {
                // 杆塔上可能会有分支线 && i < betweenNodes.size() - 1
                if (edges != null && edges.size() > 2) {
                    // 过滤已经主干边 其它的分叉边
                    List<Node> branchEdges = edges.stream().filter(n -> !betweenNodes.contains(n)).collect(Collectors.toList());
                    List<NodeNumModel> nodeNums = NumModelUtil.toPbNums(branchEdges, node, branchNodeMap);
                    for (NodeNumModel nodeNum : nodeNums) {
                        // 单个分支超过最大数量
                        if (NumModelUtil.judgePbNumModel(nodeNum, maxNums)) {
                            results.add(SegLayPathBo.createBranchPath(nodeNum.getNode(), nodeNum.getNextNode()));
                        }
                    }
                }
            }
        }

        results.add(SegLayPathBo.createSegPath(nodes));
        return results;
    }

    /**
     * 获取放置节点集合
     */
    private CombAlternateOp getLayPositionList(List<SegLayPathBo> mainPathsBos, int maxNum, NodePath nodePath) {

        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        List<ArrayList<Node>> allNodePaths = nodePath.getAllNodePaths();

        CombAlternateOp result = new CombAlternateOp();

        // 主干路路径放在分段开关或者联络线
        List<SegLayPathBo> mainSegPathLays = mainPathsBos.stream().filter(SegLayPathBo::isSegPath).collect(Collectors.toList());

        // 大分子路径
        List<SegLayPathBo> branchPathLays = mainPathsBos.stream().filter(n -> !n.isSegPath()).collect(Collectors.toList());

        // =========================== 主干分段开关组合生成 =======================
        // 分支线的开始边集合
        List<Node> branchStartEdges = new ArrayList<>();
        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node branchNextNode = branchPathLay.getBranchNextNode();
            if (branchNextNode != null) {
                branchStartEdges.add(branchNextNode);
            }
        }

        CombAlternateOp segCombAleOp = FindSegPbMuchLay.getLayPositionList(mainSegPathLays, branchNodeMap, maxNum, branchStartEdges);
        result.addAll(segCombAleOp.getAlternateOps());


        // =========================== 大分子末端组合 =======================
        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node startNode = branchPathLay.getBranchStartNode();
            Node nextEdge = branchPathLay.getBranchNextNode();

            List<ContactBranch> bigBranch = findEndContactLay.getPbNumBranch(startNode, nextEdge, maxNum, nodePath);

            CombAlternateOp bigCombAleOp = ContactLayPlanOp.getBigBranchCombAltOp(bigBranch, nodePath, false);

            result.addAll(bigCombAleOp.getAlternateOps());
        }
        //  result.filterEmpty();
        result.setAlternateOps(result.getAlternateOps().stream().filter(n -> !n.isEmpty()).collect(Collectors.toList()));
        return result;
    }
}
