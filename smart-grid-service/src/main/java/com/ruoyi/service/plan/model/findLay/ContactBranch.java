package com.ruoyi.service.plan.model.findLay;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ContactBranch {

    public ContactBranch(List<Node> paths, Node startNode, BranchNode branchNode) {
        this.paths = paths;
        this.startNode = startNode;
        this.branchNode = branchNode;
    }

    /**
     * 当前分支路径
     */
    List<Node> paths;

    // 开始
    Node startNode;

    /**
     * 分支节点
     */
    BranchNode branchNode;

    // 其它分支BranchNode
    List<BranchNode> extraBranchNodes = new ArrayList<>();
}
