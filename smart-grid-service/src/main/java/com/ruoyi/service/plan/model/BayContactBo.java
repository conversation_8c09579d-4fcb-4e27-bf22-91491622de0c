package com.ruoyi.service.plan.model;

import com.ruoyi.graph.Node;
import com.ruoyi.util.coordinates.CoordinateConverter;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * 新增环网柜
 */
@Data
public class BayContactBo {
    public BayContactBo(HwgBayBo hwgBay, ProcessContactBo processContact) {
        this.hwgBay = hwgBay;
        this.processContact = processContact;
    }

    HwgBayBo hwgBay;

    ProcessContactBo processContact;

    // 新上联络线的间隔接点
    Node bayContactNode;

    Geometry bayContactPoint;

    public double[] toBayContactLngLat() {
        if (bayContactPoint == null) {
            return null;
        }
        List<Double> doubles = CoordinateConverter.pointToLngLat((Point) bayContactPoint);
        return CoordinateConverter.listToArr(doubles);
    }

}
