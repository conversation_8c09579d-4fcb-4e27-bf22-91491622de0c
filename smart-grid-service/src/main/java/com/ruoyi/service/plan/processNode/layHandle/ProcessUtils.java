package com.ruoyi.service.plan.processNode.layHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.StationConfiguration;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.utils.StationNodeUtils;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.constant.NodeConstants.LINE_TYPE_LINEAR;
import static com.ruoyi.constant.NodeConstants.SHAPE_KEY_FEEDER_DL;
import static com.ruoyi.graph.Node.TYPE_SELF;

public class ProcessUtils {

    /**
     * 在杆塔上加装开关 左边链接线连接杆塔  右边连接导线段
     */
    public static List<Node> generatePoleKg(Node pole, Node segLine, boolean isCopySource, Boolean switchOpen) {

        Geometry geometry = pole.getGeometry();
        Node target = null;
        Node segSource = segLine.getSource();
        Node segTarget = segLine.getTarget();
        boolean poleIsSource = true;

        // 表示当前线路与当前pole相同 那么需要真正设置poleIsSource
        if ((segSource != null && segSource.equals(pole.getId())) || (segTarget != null && segTarget.equals(pole.getId()))) {
            poleIsSource = segLine.getSource() != null && segLine.getSource().equals(pole.getId());
        }

        if (isCopySource) {
            pole = pole.clone();
            // 另一个目标设备
            target = segLine.getLink(!poleIsSource);
            if (target != null) {
                target = target.clone();
            }
            segLine = segLine.clone();
        }

        // 开始开关  左边链接线连接杆塔  右边连接导线段

        // 创建开关和开关和杆塔的连接线
        Node kg = NodeFactory.createNode(UUID.randomUUID().toString(), geometry.copy());
        kg.setType(Node.TYPE_SELF);
        kg.setShapeKey(NodeConstants.SHAPE_KEY_POLE_LOAD_KG);
        kg.setPsrType("0112");
        kg.setPsrName("分段负荷开关");
        kg.setProperties(new HashMap<String, Object>() {{
            put("isKg", true);
            put("name", "分段开关");
            put("type", NodeConstants.SHAPE_KEY_POLE_LOAD_KG);
        }});
        kg.setPsrId(kg.getId());

        if (switchOpen != null) {
            kg.setSwitchOpen(switchOpen);
        }

        // 创建连接线
        Coordinate coordinate = geometry.getCoordinate();
        double[][] coords = {{coordinate.getX(), coordinate.getY()},
                {coordinate.getX(), coordinate.getY()}};
        Node edge = NodeFactory.createEdge(UUID.randomUUID().toString(), coords);
        edge.setType(Node.TYPE_SELF);
        edge.setShapeKey(NodeConstants.SHAPE_KEY_LINK_LINE);
        edge.setLineType(NodeConstants.LINE_TYPE_LINEAR);
        edge.setProperties(new HashMap<String, Object>() {{
            put("name", "连接线");
            put("type", NodeConstants.SHAPE_KEY_LINK_LINE);
        }});

        // 添加链接关系
        kg.addEdge(edge, !poleIsSource);
        kg.addEdge(segLine, poleIsSource);

        // TODO segLine的target是null 需要处理一下
        if (target != null) {
            target.addEdge(segLine, !poleIsSource);
        }
        pole.addEdge(edge, poleIsSource);
        if (target != null) {
            return Arrays.asList(pole, edge, kg, segLine, target);
        } else {
            return Arrays.asList(pole, edge, kg, segLine);
        }
    }

    public static List<Node> processContactNodes(List<Node> nodes, Node startNode, String constantType, Function<Node, Void> handleLinkNode) {
        if (nodes == null) {
            return null;
        }

        ArrayList<Node> result = new ArrayList<>(nodes);
        // 移除路由路径首节点
        result.remove(0);
        Node startEdge = result.get(0);

        Node linkNode = null;

        // 1、处理首部
        if (StringUtils.equals(constantType, ProcessContactBo.POLE_RANGE)) {
            // 处理首节点新增联络开关
            List<Node> startNodes = generatePoleKg(startNode, startEdge, true, null);
            List<Node> kgs = null;

            // 截取到开关的位置 并且将开始与之连接
            for (int i = startNodes.size() - 1; i >= 0; i--) {
                Node node = startNodes.get(i);
                if (node.isKg("all")) {
                    linkNode = node;
                    kgs = startNodes.subList(0, i + 1);
                    break;
                }
            }
            result.addAll(0, kgs);
        } else if (StringUtils.equals(constantType, ProcessContactBo.STATION_BAY)) {
            // 剩余间隔的开始节点 我们需要重新设置
            linkNode = startNode.clone();
            result.add(0, linkNode);
            // 与线路联络线建立连接关联关系
            linkNode.addEdge(startEdge, true);
        } else {
            linkNode = startNode;
            // 与线路联络线建立连接关联关系
            linkNode.addEdge(startEdge, true);
        }

        handleLinkNode.apply(linkNode);

        return result;
    }

    public static Node toNode(String psrId, String psrType, String psrName, double[] coords) {
        Node device = NodeFactory.createDevice(psrId, coords[0], coords[1]);
        device.setType(Node.TYPE_PSR);

        device.setPsrId(device.getPsrId());
        device.setPsrType(psrType);
        device.setPsrName(psrName);

        device.putProperties("psrId", psrId);
        device.putProperties("psrType", psrType);
        device.putProperties("name", psrName);

        return device;
    }


    /**
     * 根据联络线进行分组
     */
    HashMap<String, List<ProcessContactVo>> prosContactGroup(List<ProcessContactVo> prosContacts) {

        HashMap<String, List<ProcessContactVo>> group = new HashMap<>();
        for (ProcessContactVo processContact : prosContacts) {

            String contactFeederId = processContact.getContactFeederId();
            List<ProcessContactVo> list = new ArrayList<>();

            if (group.containsKey(contactFeederId)) {
                list = group.get(contactFeederId);
            } else {
                group.put(contactFeederId, list);
            }
            list.add(processContact);
        }
        return group;
    }

    /**
     * 提取联络线
     * 不同联络线的取一个即可 并且取最短那个杆塔即可
     *
     * @param allContacts
     * @return
     */
    List<ProcessContactVo> extraProsContact(List<ProcessContactVo> allContacts) {
        HashMap<String, List<ProcessContactVo>> contactGroup = prosContactGroup(allContacts);
        List<ProcessContactVo> result = new ArrayList<>();
        // 只需提取联络线里面的某个即可
        for (List<ProcessContactVo> prosContacts : contactGroup.values()) {
            // 排序 取最短的长度
            prosContacts.sort((pContact1, pContact2) -> {
                double length1 = pContact1.getTotalLength() == null ? 0.0 : pContact1.getTotalLength();
                double length2 = pContact2.getTotalLength() == null ? 0.0 : pContact2.getTotalLength();
                return Double.compare(length1, length2);
            });
            result.add(prosContacts.get(0));
        }
        return result;
    }
}
