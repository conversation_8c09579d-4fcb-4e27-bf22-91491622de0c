package com.ruoyi.service.plan.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.vo.ProblemSchemeAnalysisVo;
import com.ruoyi.mapper.problem.ProblemSchemeAnalysisMapper;
import com.ruoyi.service.websocket.WebSocketServiceHandler;
import com.ruoyi.util.PlanProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;

/**
 * 方案过程
 */
@Service
@Slf4j
public class PlanProcessServiceImpl {

    @Autowired
    ProblemSchemeAnalysisMapper problemSchemeAnalysisMapper;

    @Autowired
    private WebSocketServiceHandler webSocketServiceHandler;

    public List<ProblemSchemeAnalysisVo> getByProblemId(Long problemId) {
        return problemSchemeAnalysisMapper.selectByProblemId(problemId);
    }

    public List<ProblemSchemeAnalysis> queryProblemId(Long problemId) {
        return problemSchemeAnalysisMapper.queryProblemId(problemId);
    }

    public int deleteByProblemId(Long problemId) {
        return problemSchemeAnalysisMapper.deleteByProblemId(problemId);
    }


    /**
     * 给大方法调用，传入problemSchemeAnalysis，存入problemSchemeAnalysis同时关播problemSchemeAnalysis
     *
     * @return
     */
    @Transactional  // 确保整个操作的原子性
    public void pushProcess(ProblemSchemeAnalysis problemSchemeAnalysis) {
        try {
            if (problemSchemeAnalysis.getProblemId() != null) {
                // 是等待类型的对象 不需要保存
                if (!StringUtils.equals(problemSchemeAnalysis.getType(), ProblemSchemeAnalysis.LOADING_TYPE)) {

                    Integer maxIndex = problemSchemeAnalysisMapper.getMaxIndex(problemSchemeAnalysis.getProblemId());

                    problemSchemeAnalysis.setIndex(maxIndex == null ? 1 : maxIndex + 1);

                    //存入分析过程对象
                    problemSchemeAnalysisMapper.insert(problemSchemeAnalysis);
                }
                //广播分析过程对象
                  webSocketServiceHandler.broadcastToUserByToken(problemSchemeAnalysis);
            }
        } catch (Exception e) {
            log.error("过程存储失败或者广播失败", e);
        }
    }

    public void pushLoadingProcess(Long problemId, String content) {
        pushProcess(new ProblemSchemeAnalysis(problemId, null, ProblemSchemeAnalysis.LOADING_TYPE, content, null));
    }

    /**
     * 推送结构化字符串
     */
    public void pushStrStu(Long problemId, String moduleName, String titleBox, String operateType, Object... args) {
        List<Object> stuList = PlanProcessUtils.toList(args);
        pushProcess(new ProblemSchemeAnalysis(problemId, moduleName, ProblemSchemeAnalysis.STRUCTURED, operateType, JSON.toJSONString(stuList), titleBox));
    }

    /**
     * 推送自定义组件
     */
    public void pushComponent(Long problemId, String moduleName, String titleBox, String operateType, String type, Object data) {
        HashMap<String, Object> dataMap = PlanProcessUtils.getComponent(type, data);
        pushProcess(new ProblemSchemeAnalysis(problemId, moduleName, ProblemSchemeAnalysis.COMPONENT, operateType, JSON.toJSONString(dataMap), titleBox));
    }
}
