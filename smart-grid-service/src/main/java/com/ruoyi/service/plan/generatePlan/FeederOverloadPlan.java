package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 线路重过载方案生产
 * 1、重过载线路部分负荷运方调整至已有联络线路。
 * 2、针对重过载线路负载率超80%情况，可考虑部分负荷切转至新出线路，切转点宜设置在重过载线路末端。
 * 3、针对重过载线路重要连接位置缺乏联络情况，考虑与临近线路新上联络，重过载线路部分负荷运方调整至该线路。
 */
@Service
@Slf4j
public class FeederOverloadPlan implements IGeneratePlan {

    @Autowired
    ISingMapService singMapService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) throws ExecutionException, InterruptedException {

        // =========================== 基础信息准备 =======================
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        Map<String, Node> nodeMap = nodePath.getNodeMap();

        double maxLoad = contactHandleService.getMaxFeederLoad();  // 当前配置最大负载率
        double currentLoad = feederNt.getHisMaxLoadRate();

        List<Node> allPb = topologyMap.getAllPb();

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(allPb);

        if (currentLoad < maxLoad) {
            throw new RuntimeException("当前历史线路最大负载率为" + feederNt.getHisMaxLoadRateStr() + "%，并未大于" + maxLoad * 100 + "%，该线路不是线路重过载问题！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // (3)、推送：问题释义
        pushPlanProcessService.pushOverLoadExplain(problemId, maxLoad);

        // (4)、推送：线路重过载识别
        pushPlanProcessService.pushOverLoadIdentify(problemId, deviceFeeder, feederNt, maxLoad, allPb.size());

        SurePlanOp surePlanOp = new SurePlanOp();

        // =========================== 运放调整组合 =======================

        // 获取所有联络线的最大量测值
        List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(nodePath);

        //  获取可以运方调整的联络线开关
        List<ContactFeederKg> canContactFeederKgs = contactHandleService.getCanContactFeederKg(feederId, nodePath, contactFeederNts, maxLoad);

        // 获取能运放调整开关的所有可能组合
        CanRunAdjustFeeder canRunAdjust = contactHandleService.getCanRunAdjust(feederNt, canContactFeederKgs, contactFeederNts, nodePath, maxLoad, nodeMap);

        // 组合转供
        List<CombFeederTransfer> combCanFTrs = canRunAdjust.getCombCanFTrs();

        // 单个转供
        List<List<FeederTransferCap>> singleCanFTrs = canRunAdjust.getSingleCanFTrs();

        List<FeederTransferCap> runAdjustList = priorRunAdjust(singleCanFTrs);

        // （5）推送、推送联络开关
        pushPlanProcessService.pushContactKgFeeder(problemId, nodePath.getContactFeederKgs(), contactFeederNts, maxLoad);

        // 单运方调整的
        if (CollectionUtils.isNotEmpty(runAdjustList)) {
            surePlanOp.addAll(FindRunAdjustLay.findRunAdjustLay(runAdjustList, currentLoad, maxLoad).getPlanOps());
        }

        // 组合运方调整的
        if (CollectionUtils.isNotEmpty(combCanFTrs)) {
            surePlanOp.addAll(FindRunAdjustLay.findCombRunAdjustLay(combCanFTrs, currentLoad, maxLoad).getPlanOps());
        }

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        List<ContactBranch> tfBranch = findEndContactLay.getFeederLoad(nodePath.getStartNode(), nodePath.getStartNextEdge(), feederNt, maxLoad, nodePath, null);
        // =========================== 临近线路新上联络 =======================
        CombAlternateOp combAlternateOp = ContactLayPlanOp.getEndContactCombAltOpByBranches(tfBranch, nodePath);

        // =========================== 变电站新出线 =======================
        CombAlternateOp bdzNewLineCombAltOp = ContactLayPlanOp.getBdzNewLineCombAltOp(tfBranch, nodePath, true);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(combAlternateOp);

        SurePlanOp contactSurePlanOp = processNodeService.handleLayOperateNode(problemId, combAlternateOp, token, feederId, nodePath);
        SurePlanOp bdzSurePlanOp = processNodeService.handleLayOperateNode(problemId, bdzNewLineCombAltOp, token, feederId, nodePath);

        planProcessService.pushLoadingProcess(problemId, "方案集合按照开关数和联络距离排序");

        surePlanOp.addAll(contactSurePlanOp.getPlanOps());
        surePlanOp.addAll(bdzSurePlanOp.getPlanOps());

        // =========================== 方案生成 =======================

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, plans, planLaysMap);

        // 获取各个分支末端
        return plans;
    }

    /**
     * 更具运方调整的组合（单个开关有多个操作）
     * 我们按照一定的优先级 提取其中的一个即可
     */
    public List<FeederTransferCap> priorRunAdjust(List<List<FeederTransferCap>> allCanTransfer) {
        if (CollectionUtils.isEmpty(allCanTransfer)) {
            return null;
        }

        ArrayList<FeederTransferCap> result = new ArrayList<>();

        for (List<FeederTransferCap> transferCaps : allCanTransfer) {
            if (CollectionUtils.isEmpty(transferCaps)) {
                continue;
            }
            // 将当前联络开关的多个操作 我们取其中一个
            // 先排序(两条线专供之后的差值越小越优先) 在提取第一个
            transferCaps.sort((trf1, trf2) -> {
                double val1 = Math.abs(trf1.getSourceChangeLoad() - trf1.getTfContactChangeLoad());
                double val2 = Math.abs(trf2.getSourceChangeLoad() - trf2.getTfContactChangeLoad());
                return Double.compare(val1, val2);
            });
            result.add(transferCaps.get(0));
        }
        return result;
    }

}
