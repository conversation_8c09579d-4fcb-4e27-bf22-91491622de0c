package com.ruoyi.service.plan.processNode.layHandle;

import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.KgLay;
import com.ruoyi.service.plan.model.plan.AlternateOp;
import com.ruoyi.service.plan.model.plan.CombAlternateOp;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.model.plan.SurePlanOp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class KgLayHandle {

    // 开关位置处理
    public void handleKgLay(SurePlanOp surePlanOp) {
        for (PlanOperate planOp : surePlanOp.getPlanOps()) {
            for (BaseLay layNode : planOp.getLayNodeList()) {
                if (layNode.isKgType()) {
                    // 处理开关
                    handleKgLay((KgLay) layNode);
                }
            }
        }
    }

    // 处理开关类型
    void handleKgLay(KgLay layNode) {
        Node psrKg = layNode.getPsrKg();
        Node beforeNode = layNode.getBeforeNode();
        Node afterNode = layNode.getAfterNode();

        if (psrKg != null) {
            Node kg = psrKg.clone();
            kg.setSwitchOpen(layNode.getSwitchOpen());
            layNode.setLayNodes(Collections.singletonList(kg));
        } else {
            layNode.setLayNodes(ProcessUtils.generatePoleKg(beforeNode, afterNode, true, layNode.getSwitchOpen()));
        }
    }
}
