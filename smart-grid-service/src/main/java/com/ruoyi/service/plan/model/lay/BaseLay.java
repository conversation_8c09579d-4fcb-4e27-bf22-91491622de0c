package com.ruoyi.service.plan.model.lay;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.graph.utils.NodeUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.UUID;

/**
 * 放置节点的业务实体
 */
@Data
public abstract class BaseLay implements ILay {
    public BaseLay() {
        id = UUID.randomUUID().toString();
    }

    /**
     * 唯一ID
     */
    String id;

    /**
     * 类型
     */
    String type;

    // 开关相关：开关的状态
    public boolean switchOpen = false;

    // 放置开关
    public static final String KG_TYPE = "kgType";

    // 放置首开关
    public static final String KG_HEAD_TYPE = "kgHeadType";

    // 末端新增联络线
    public static final String END_CONTACT_TYPE = "endContactType";

    // 运放调整
    public static final String RUN_ADJUST_TYPE = "runAdjust";

    // 更换间隔
    public static final String REPLACE_BAY_TYPE = "replaceBay";

    /**
     * 是否联络相关类型的
     */
    public boolean isContactType() {
        // || StringUtils.equals(type, CONTACT_TYPE)
        return StringUtils.equals(type, END_CONTACT_TYPE);
    }

    /**
     * 是否开关相关类型的
     */
    public boolean isKgType() {
        return StringUtils.equals(type, KG_TYPE) || StringUtils.equals(type, KG_HEAD_TYPE);
    }

    /**
     * 运方调整
     */
    public boolean isRunAdjust() {
        return StringUtils.equals(type, RUN_ADJUST_TYPE);
    }

    /**
     * 更换间隔
     */
    public boolean isReplaceBay() {
        return StringUtils.equals(type, REPLACE_BAY_TYPE);
    }

    @Override
    public boolean equals(BaseLay layNode) {
        return NodeUtils.compareNodeLists(getLayNodes(), layNode.getLayNodes());
    }

    @Override
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(getLayNodes());
    }

}
