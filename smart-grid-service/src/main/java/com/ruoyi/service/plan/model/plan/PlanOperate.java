package com.ruoyi.service.plan.model.plan;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.lay.KgLay;
import com.ruoyi.util.ListUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 方案当个操作
 * 单个操作 由各个操作节点Lay组合而成的（例如 新增联络线、上负荷开关、新增环网柜。。。等等）
 */
@Data
public class PlanOperate {

    public PlanOperate() {
        this.id = UUID.randomUUID().toString();
    }

    public PlanOperate(List<BaseLay> layNodeList) {
        this.id = UUID.randomUUID().toString();
        this.layNodeList = layNodeList;
    }

    private String id;

    /**
     * 当前操作类型
     */
    String type;

    /**
     * 当前线路的专供对象
     */
    CombFeederTransfer combFeederTransfer;

    /**
     * 单次方案操作  由lay操作节点集合构成的
     */
    List<BaseLay> layNodeList = new ArrayList<>();

    /**
     * 添加分段开关操作类型
     */
    public static String SEG_KG_TYPE = "segKgType";

    /**
     * 添加临近联络类型
     */
    public static String CONTACT_TYPE = "contactType";

    /**
     * 变电站新出线类型
     */
    public static String BDZ_NEW_LINE_TYPE = "bdzNewLineType";

    /**
     * 运放调整类型
     */
    public static String RUN_ADJUST_TYPE = "runAdjustType";

    /**
     * 替换间隔
     */
    public static String REPLACE_LAY_TYPE = "replaceLayType";

    /**
     * 以上各个类型各个组合在一起的类型（在备选方案里不允许出现这种，只有确定方案可以）
     * （这种几乎很少，做方案时尽能避免这种类型的）
     */
    public static String COMB_TYPE = "combType";

    // =========================== 常用方法 =======================

    public void add(BaseLay layNode) {
        layNodeList.add(layNode);
    }

    public void addAll(List<BaseLay> layNodes) {
        layNodeList.addAll(layNodes);
    }

    /**
     * 当前操作的分数
     */
    public HashMap<String, Double> getLayTotalScope() {
        double total = 0;
        double contactLength = 0;
        for (BaseLay layNode : layNodeList) {
            total += layNode.getScope();
            if (layNode.isContactType()) {
                double contactLength1 = ((ContactLay) layNode).getContactLength();
                contactLength += contactLength1;
            }
        }
        double finalTotal = total;
        double finalContactLength = contactLength;
        return new HashMap<String, Double>() {{
            put("total", finalTotal);
            put("contactLength", finalContactLength);
        }};
    }

    // 过滤空lay
    public void filterEmpty() {
        layNodeList = layNodeList.stream().filter(n -> !n.isEmpty()).collect(Collectors.toList());
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(layNodeList);
    }

    // =========================== 创建方案 =======================
    // 当前方案操作转为方案
    public Plan toPlan(Long problemId) {
        // 将各个放置节点拼接成方案参数数据
        String operateDataStr = toOperateDataStr();
        return new Plan(randomPlanId(), operateDataStr, problemId);
    }

    // TODO 会有重复问题   后面看看你要怎么弄暂时先这样
    private Long randomPlanId() {
        UUID uuid = UUID.randomUUID();
        // 获取UUID的哈希码（转为正数）
        int hashCode = Math.abs(uuid.hashCode());
        // 映射到7位数范围（1000000-9999999）
        return 10000000L + (hashCode % 90000000);
    }

    /**
     * lay集合转为 设备字符串
     */
    public String toOperateDataStr() {
        List<Node> allNodes = toOperateData();
        return NodeUtils.toPlanOperateDataStr(allNodes);
    }

    /**
     * lay集合转为 设备字符串
     */
    public List<Node> toOperateData() {
        List<Node> allNodes = new ArrayList<>();
        // 将所有的放置点数据拼接在一起
        for (BaseLay sureLay : layNodeList) {
            allNodes.addAll(sureLay.getLayNodes());
        }
        return allNodes;
    }

    /**
     * 获取联络类型的lay
     */
    public List<ContactLay> getContactLays() {
        return getLayNodeList().stream().filter(BaseLay::isContactType).map(lay -> (ContactLay) lay).collect(Collectors.toList());
    }

    /**
     *
     */
    public List<KgLay> getKgLays() {
        return getLayNodeList().stream().filter(BaseLay::isKgType).map(lay -> (KgLay) lay).collect(Collectors.toList());
    }

    /**
     * 获取联络关联的lays
     */
    public List<KgLay> getContactLinkKgLays() {
        List<ContactLay> contactLays = getContactLays();
        List<KgLay> kgLays = getKgLays();

        List<KgLay> result = new ArrayList<>();
        for (ContactLay contactLay : contactLays) {
            if (StringUtils.isNotBlank(contactLay.getLinkLayId())) {
                KgLay kgLay = ListUtils.findFirst(kgLays, lay -> StringUtils.equals(lay.getId(), contactLay.getLinkLayId()));
                if (kgLay != null) {
                    result.add(kgLay);
                }
            }
        }
        return result;
    }

    // 判断是否满足负载率
    public boolean canMeetMaxLoad() {
        if (combFeederTransfer == null) {
            return true;
        }
        return combFeederTransfer.isSourceHasPass() && combFeederTransfer.isFTrHasPass();
    }

    /**
     * 方案负载率文本  要结构数据
     */
    public String toPlanLoadStr() {
        if (combFeederTransfer == null) {
            return "{\"status\": \"false\"}";
        }

        try {
            // 创建ObjectMapper实例
            ObjectMapper objectMapper = new ObjectMapper();

            // 构建JSON结构
            Map<String, Object> loadData = new HashMap<>();

            // 基本信息
            loadData.put("sourceLoad", combFeederTransfer.getSourceLoad());
            loadData.put("sourceChangeLoad", combFeederTransfer.getSourceChangeLoad());
            loadData.put("feederId", combFeederTransfer.getFeederId());
            loadData.put("feederName", combFeederTransfer.getFeederName());

            // 转换feederTransferCaps
            List<Map<String, Object>> feederTransferCapsList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(combFeederTransfer.getFeederTransferCaps())) {
                for (FeederTransferCap cap : combFeederTransfer.getFeederTransferCaps()) {
                    Map<String, Object> capData = new HashMap<>();

                    // 分闸节点信息
                    if (cap.getFenNode() != null) {
                        Map<String, Object> fenNodeData = new HashMap<>();
                        fenNodeData.put("id", cap.getFenNode().getId());
                        fenNodeData.put("psrId", cap.getFenNode().getPsrId());
                        fenNodeData.put("psrType", cap.getFenNode().getPsrType());
                        fenNodeData.put("psrName", cap.getFenNode().getPsrName());
                        capData.put("fenNode", fenNodeData);
                    }

                    // 合闸节点信息
                    if (cap.getHeNode() != null) {
                        Map<String, Object> heNodeData = new HashMap<>();
                        heNodeData.put("id", cap.getHeNode().getId());
                        heNodeData.put("psrId", cap.getHeNode().getPsrId());
                        heNodeData.put("psrType", cap.getHeNode().getPsrType());
                        heNodeData.put("psrName", cap.getHeNode().getPsrName());
                        capData.put("heNode", heNodeData);
                    }

                    // 基本信息
                    capData.put("sourcePsrId", cap.getSourcePsrId());
                    capData.put("sourcePsrName", cap.getSourcePsrName());
                    capData.put("tfContactPsrId", cap.getTfContactPsrId());
                    capData.put("tfContactPsrName", cap.getTfContactPsrName());

                    // 负载率信息
                    capData.put("sourceLoad", cap.getSourceLoad());
                    capData.put("sourceChangeLoad", cap.getSourceChangeLoad());
                    capData.put("tfContactLoad", cap.getTfContactLoad());
                    capData.put("tfContactChangeLoad", cap.getTfContactChangeLoad());

                    // 路径信息
                    List<Map<String, Object>> pathsList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(cap.getPaths())) {
                        for (Node pathNode : cap.getPaths()) {
                            Map<String, Object> pathData = new HashMap<>();
                            pathData.put("id", pathNode.getId());
                            pathData.put("psrId", pathNode.getPsrId());
                            pathData.put("psrType", pathNode.getPsrType());
                            pathData.put("psrName", pathNode.getPsrName());
                            pathData.put("isEdge", pathNode.isEdge());
                            pathsList.add(pathData);
                        }
                    }
                    capData.put("paths", pathsList);

                    feederTransferCapsList.add(capData);
                }
            }
            loadData.put("feederTransferCaps", feederTransferCapsList);

            // 转换为JSON字符串
            return objectMapper.writeValueAsString(loadData);

        } catch (Exception e) {
            return "{}";
        }
    }


}
