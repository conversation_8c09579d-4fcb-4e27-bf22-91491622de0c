package com.ruoyi.service.plan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.DevicePoleTransformer;
import com.ruoyi.entity.device.DeviceStationTransformer;
import com.ruoyi.entity.device.bo.QueryDevBo;
import com.ruoyi.entity.znap.ConDmsFeeder;
import com.ruoyi.entity.znap.DevDmsTr;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.mapper.device.DevicePoleTransformerMapper;
import com.ruoyi.mapper.device.DeviceStationTransformerMapper;
import com.ruoyi.mapper.znap.ConDmsFeederMapper;
import com.ruoyi.mapper.znap.DevDmsPoleMapper;
import com.ruoyi.mapper.znap.DevDmsSegmentMapper;
import com.ruoyi.mapper.znap.DevDmsTr2Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TestService {

    @Autowired
    ConDmsFeederMapper conDmsFeederMapper;

    @Autowired
    DevDmsPoleMapper devDmsPoleMapper;

    @Autowired
    DevDmsSegmentMapper devDmsSegmentMapper;

    @Autowired
    DevDmsTr2Mapper devDmsTr2Mapper;

    @Autowired
    DeviceStationTransformerMapper deviceStationTransformerMapper;

    @Autowired
    DevicePoleTransformerMapper devicePoleTransformerMapper;

    List<String> feeders = Arrays.asList("01DKX-8853", "01DKX-8907", "01DKX-36362", "01DKX-8803");

    public List<QueryDevBo> query() {
        ArrayList<QueryDevBo> result = new ArrayList<>();

        for (String feeder : feeders) {
            ConDmsFeeder conDmsFeeder = conDmsFeederMapper.selectByPmsFeederId(feeder);

            List<DevDmsTr> list = devDmsTr2Mapper.selectByFeederId(conDmsFeeder.getId());

            List<QueryDevBo> devs = list.stream().map(d -> {
                String[] strings = ZnapUtils.parsePsrStr(d.getPsrid());
                return new QueryDevBo(strings[1], strings[0]);
            }).collect(Collectors.toList());
            result.addAll(devs);
        }
        QueryWrapper<DeviceStationTransformer> objectQueryWrapper = new QueryWrapper<>();
        List<DeviceStationTransformer> deviceStationTransformers = deviceStationTransformerMapper.selectList(objectQueryWrapper);

        result.addAll(deviceStationTransformers.stream().map(d -> {
            return new QueryDevBo(d.getPsrId(), "0302");
        }).collect(Collectors.toList()));


        QueryWrapper<DevicePoleTransformer> wrapper = new QueryWrapper<>();
        List<DevicePoleTransformer> list = devicePoleTransformerMapper.selectList(wrapper);

        for (DevicePoleTransformer devicePoleTransformer : list) {
            if (result.stream().anyMatch(d -> !StringUtils.equals(d.getDevId(), devicePoleTransformer.getPsrId()))) {
                result.add(new QueryDevBo(devicePoleTransformer.getPsrId(), "0110"));
            }
        }

        return result;
    }
}
