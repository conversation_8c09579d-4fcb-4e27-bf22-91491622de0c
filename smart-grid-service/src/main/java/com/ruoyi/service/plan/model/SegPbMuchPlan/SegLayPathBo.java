package com.ruoyi.service.plan.model.SegPbMuchPlan;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.ArrayList;

/**
 * 判断分段路径需要放在分段和分段主干路径的大分子节点
 */
@Data
public class SegLayPathBo {
    public SegLayPathBo() {
    }

    ArrayList<Node> nodes;

    /**
     * 是否分段分支路径
     */
    boolean isSegPath;

    /**
     * 分支路径的开始节点
     */
    Node branchStartNode;

    /**
     * 分钟路径的开始下一个节点
     */
    Node branchNextNode;

    /**
     * 创建主干分段路径中分配的节点
     */
    public static SegLayPathBo createSegPath(ArrayList<Node> nodes) {
        SegLayPathBo segLayPathBo = new SegLayPathBo();
        segLayPathBo.setNodes(nodes);
        segLayPathBo.setSegPath(true);
        return segLayPathBo;
    }

    /**
     * 创建主干路径分支路径
     */
    public static SegLayPathBo createBranchPath(Node node, Node nextNode) {
        SegLayPathBo segLayPathBo = new SegLayPathBo();
        segLayPathBo.setBranchStartNode(node);
        segLayPathBo.setBranchNextNode(nextNode);
        segLayPathBo.setSegPath(false);
        return segLayPathBo;
    }
}
