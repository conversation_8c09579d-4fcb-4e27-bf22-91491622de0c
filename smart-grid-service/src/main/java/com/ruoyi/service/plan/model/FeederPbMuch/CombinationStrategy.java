package com.ruoyi.service.plan.model.FeederPbMuch;

import com.ruoyi.graph.BranchNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CombinationStrategy {
    //需要调整的开关
    private List<SwitchEffectPb> switchEffectPbCombinationList;
    //需要加联络线的分支
    private List<List<BranchNode>> branchNodeCombinationList;
}
