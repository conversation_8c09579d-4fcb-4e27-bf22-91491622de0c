package com.ruoyi.service.plan.model.lay;

import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.graph.Node;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 运放调整
 */
public class RunAdjustLay extends BaseLay {

    public RunAdjustLay(List<FeederTransferCap> feederTransferCaps) {
        this.feederTransferCaps = feederTransferCaps;
        this.type = RUN_ADJUST_TYPE;
    }

    public List<FeederTransferCap> feederTransferCaps;

    @Override
    public List<Node> getLayNodes() {
        List<Node> nodes = new ArrayList<>();

        // 运放调整
        if (CollectionUtils.isNotEmpty(feederTransferCaps)) {
            for (FeederTransferCap feederTransferCap : feederTransferCaps) {
                nodes.add(feederTransferCap.getHeNode());
                nodes.add(feederTransferCap.getFenNode());
            }
        }

        return nodes;
    }

    @Override
    public int getScope() {
        return 90;
    }

    @Override
    public List<Object> toStuStr() {
        List<Object> strList = new ArrayList<>();
        if (CollectionUtils.isEmpty(feederTransferCaps)) {
            return strList;
        }
        for (FeederTransferCap feederTransferCap : feederTransferCaps) {
            if (feederTransferCap == null) {
                return new ArrayList<>();
            }
            Node fenNode = feederTransferCap.getFenNode();
            Node heNode = feederTransferCap.getHeNode();
            if (fenNode != null) {
                strList.add("分闸：");
                strList.add(fenNode.getPsrName());
            }
            if (heNode != null) {
                strList.add("合闸：");
                strList.add(heNode.getPsrName());
            }
        }
        return strList;
    }

    /**
     * 比较两个FeederTransferCap列表是否相同（仅通过FeederTransferCap的equals(FeederTransferCap fTr)方法比较实体，不考虑顺序）
     *
     * @param list1 第一个FeederTransferCap列表
     * @param list2 第二个FeederTransferCape列表
     * @return 如果两个列表的FeederTransferCap集合完全相同（不考虑顺序），返回true；否则返回false
     */
    public boolean compareFeederTransferCapLists(List<FeederTransferCap> list1, List<FeederTransferCap> list2) {

        // 处理引用相等或null的情况
        if (list1 == list2) return true;

        if (list1 == null || list2 == null) return false; // 其中一个为 null

        // 列表长度不同时直接返回false
        if (list1.size() != list2.size()) return false;

        // 遍历list1中的每个Node
        for (FeederTransferCap feederTransferCap : list1) {

            if (!list2.stream().anyMatch(n -> n.equals(feederTransferCap))) {
                return false;
            }
        }
        return true; // 所有FeederTransferCap都在list2中找到匹配项
    }
}
