package com.ruoyi.service.plan.impl.test;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于测试回溯法递归方法  后期再删除
 */
public class BacktrackMain {

    public static void main(String[] args) {
        // 分支数值（示例数据）
        int[] branches = {3,6,4} ;  // {3,6,4, 4, 4, 4};// , 4, 4, 1,5,2,3,2,5
        List<List<Integer>> switchCombinations = findSwitchCombinations(branches);

        // 打印所有有效组合
        System.out.println("所有可能的开关插入组合：");
        for (List<Integer> combination : switchCombinations) {
            System.out.println(combination);
        }
    }

    /**
     * 查找所有满足条件的开关插入组合
     * @param branches 主干路径上的分支数值数组
     * @return 所有可能的开关位置组合（位置从1开始计数）
     */
    public static List<List<Integer>> findSwitchCombinations(int[] branches) {
        List<List<Integer>> result = new ArrayList<>();
        int n = branches.length;

        // 1. 预处理前缀和数组（prefix[i] = 前i个分支的和）
        int[] prefix = new int[n + 1];
        for (int i = 0; i < n; i++) {
            prefix[i + 1] = prefix[i] + branches[i];
        }

        // 2. 回溯法生成所有分割组合
        backtrack(0, n, prefix, new ArrayList<>(), result);

        return result;
    }

    /**
     * 回溯法递归生成所有有效的分割组合
     * @param current 当前处理的位置（前缀和索引）
     * @param prefix 前缀和数组
     * @param path 当前路径（存储分割点）
     * @param result 所有有效组合的集合
     */
    private static void backtrack(int current, int end, int[] prefix, List<Integer> path, List<List<Integer>> result) {
        // 添加当前分割点（路径记录的是前缀和索引，实际位置需转换）
        path.add(current);

        // 终止条件：到达末尾（最后一个分支之后）
        if (current == end) {
            // 转换为实际开关位置（排除首尾的0和n）
            List<Integer> validCombination = new ArrayList<>();
            for (int i = 1; i < path.size() - 1; i++) {
                validCombination.add(path.get(i));
            }
            result.add(validCombination);
            path.remove(path.size() - 1);
            return;
        }

        // 尝试所有可能的下一个分割点
        for (int next = current + 1; next <= end; next++) {
            int sum = prefix[next] - prefix[current];
            System.out.println(current + "_" + next);
           if (sum >= 10) break; // 超过阈值，后续更大区间也会超，直接终止
            backtrack(next, end, prefix, path, result);
        }

        // 回溯，移除当前分割点
        path.remove(path.size() - 1);
    }
}
