package com.ruoyi.service.plan;

import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.model.plan.CombAlternateOp;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.model.plan.SurePlanOp;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface IProcessNodeService {

    SurePlanOp handleLayOperateNode(Long problemId, CombAlternateOp combAlternateOp, String token, String feederId, NodePath nodePath) throws ExecutionException, InterruptedException;

}
