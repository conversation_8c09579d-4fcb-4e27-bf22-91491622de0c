package com.ruoyi.service.plan.findLay.runAdjustLay;

import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederLoadChange;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.model.plan.SurePlanOp;
import com.ruoyi.service.plan.utils.LayNodeFactory;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FindRunAdjustLay {

    /**
     * 处理运方调整放置位置集合
     * 优先按照联络线添加 其次按照联络开关
     *
     * @param runAdjustList 可以运放调整转供列表
     */
    public static SurePlanOp findRunAdjustLay(List<FeederTransferCap> runAdjustList, double currentLoad, double maxLoad) {

        SurePlanOp surePlanOp = new SurePlanOp();

        HashMap<FeederTransferCap, Boolean> keepMap = new HashMap<>();

        // 按照连接联络线分组
        HashMap<String, List<FeederTransferCap>> contactFeederGroup = runAdjustGroup(runAdjustList, FeederTransferCap::getTfContactPsrId);

        // 按照连接联络线分组
        HashMap<String, List<FeederTransferCap>> heKgGroup = runAdjustGroup(runAdjustList, (ftr -> ftr.getHeNode().getPsrId()));

        // 优先按照线路
        for (List<FeederTransferCap> fTrs : contactFeederGroup.values()) {
            FeederTransferCap fTr = fTrs.get(0);
            // 我们取第一个即可
            surePlanOp.add(toPlanOp(fTr, currentLoad, maxLoad));
            keepMap.put(fTr, true);
        }

        // 线路分组的小于3并且也小于其它的  那么还能在分配
        if (surePlanOp.getSize() < 3 && surePlanOp.getSize() < runAdjustList.size()) {
            // 按照相同的合闸开关分一组
            for (List<FeederTransferCap> fTrs : heKgGroup.values()) {
                FeederTransferCap fTr = fTrs.get(0);
                if (!keepMap.containsKey(fTr)) {
                    surePlanOp.add(toPlanOp(fTr, currentLoad, maxLoad));
                    keepMap.put(fTr, true);
                }
            }
        }

        return surePlanOp;
    }

    /**
     * 处理运方调整放置位置集合（组合运方调整）
     * 优先按照联络线添加 其次按照联络开关
     */
    public static SurePlanOp findCombRunAdjustLay(List<CombFeederTransfer> combCanFTrs, double currentLoad, double maxLoad) {

        SurePlanOp surePlanOp = new SurePlanOp();

        // 组合 我们取最小操作的长度
        List<CombFeederTransfer> minKgCombs = ListUtils.extractMinLengthLists(combCanFTrs, (fTr) -> fTr.getFeederTransferCaps().size());

        for (CombFeederTransfer minKgComb : minKgCombs) {
            List<FeederTransferCap> feederTransferCaps = minKgComb.getFeederTransferCaps();
            surePlanOp.add(toPlanOp(feederTransferCaps, currentLoad, maxLoad));
        }
        return surePlanOp;
    }

    /**
     * 转供线路转layNodes
     */
    static PlanOperate toPlanOp(FeederTransferCap fTr, double currentLoad, double maxLoad) {
        List<FeederTransferCap> fTrs = Collections.singletonList(fTr);
        CombFeederTransfer combFTr = getCombFTr(fTrs, currentLoad, maxLoad);
        //  planOp.setFeederTransferCaps(fTrs);
        return PlanOperateFactory.createRunAdjust(LayNodeFactory.createRunAdjust(fTrs), combFTr);
    }

    /**
     * 转供线路转layNodes
     */
    static PlanOperate toPlanOp(List<FeederTransferCap> fTrs, double currentLoad, double maxLoad) {
        //  planOp.setFeederTransferCaps(fTrs);
        CombFeederTransfer combFTr = getCombFTr(fTrs, currentLoad, maxLoad);
        return PlanOperateFactory.createRunAdjust(LayNodeFactory.createRunAdjust(fTrs), combFTr);
    }

    /**
     * 运方调整列表分组
     *
     * @param runAdjustList 可以运放调整转供列表
     * @param idMapper      获取ID自定义方法
     * @return
     */
    static HashMap<String, List<FeederTransferCap>> runAdjustGroup(List<FeederTransferCap> runAdjustList, Function<FeederTransferCap, String> idMapper) {
        HashMap<String, List<FeederTransferCap>> group = new HashMap<>();

        for (FeederTransferCap feederTransferCap : runAdjustList) {
            String groupId = idMapper.apply(feederTransferCap);
            List<FeederTransferCap> list = new ArrayList<>();
            if (group.containsKey(groupId)) {
                list = group.get(groupId);
            } else {
                group.put(groupId, list);
            }
            list.add(feederTransferCap);
        }
        return group;
    }


    /**
     * 汇总减去的负载率总和
     */
    public static double sumDecrFTr(List<FeederTransferCap> feederTransferCaps) {
        return feederTransferCaps.stream().mapToDouble(FeederTransferCap::getDecrLoad).sum();
    }


    /**
     * 获取组合线路专供
     *
     * @return
     */
    public static CombFeederTransfer getCombFTr(List<FeederTransferCap> feederTransferCaps, double currentLoad, double maxLoad) {

        if (CollectionUtils.isEmpty(feederTransferCaps)) {
            return null;
        }

        // 原线路
        double decrSum = sumDecrFTr(feederTransferCaps);
        double decrLoad = DoubleFormatter.formatToThreeDecimals3(currentLoad - decrSum);
        CombFeederTransfer combFeederTransfer = new CombFeederTransfer(currentLoad, decrLoad, feederTransferCaps);
        combFeederTransfer.setSourceHasPass(decrLoad < maxLoad);
        combFeederTransfer.setFeederId(feederTransferCaps.get(0).getSourcePsrId());
        combFeederTransfer.setFeederName(feederTransferCaps.get(0).getSourcePsrName());

        // 专供线路
        boolean fTrHasPass = true;
        List<FeederLoadChange> feederChangeLoads = new ArrayList<>();
        // 按照转供线路进行分组 来进行判断
        Map<String, List<FeederTransferCap>> feederGroup = feederTransferCaps.stream().collect(Collectors.groupingBy(FeederTransferCap::getTfContactPsrId));

        for (List<FeederTransferCap> fTrs : feederGroup.values()) {
            // 当前被转供路径新增的负载率
            double total = fTrs.stream().mapToDouble(FeederTransferCap::getIncrTfContactLoad).sum();
            FeederTransferCap fTr = fTrs.get(0);
            double tfLoad = fTr.getTfContactLoad();
            double changeLoad = tfLoad + total;

            if (fTr.isFeederType()) {
                // 判断当前转供线路被转供过来是否超过最大值
                if (changeLoad > maxLoad) {
                    fTrHasPass = false;
                }
            } else { // 变电站的后续再判断吧

            }

            feederChangeLoads.add(new FeederLoadChange(fTr.getTfContactPsrId(), fTr.getTfContactPsrName(), tfLoad, changeLoad, fTr.getType()));
        }
        combFeederTransfer.setFTrHasPass(fTrHasPass);
        combFeederTransfer.setFTrChangeLoads(feederChangeLoads);

        return combFeederTransfer;
    }

    // =========================== 查找组合转供 =======================


    /**
     * 判断当前运方调整开关组成的是否回环短路了
     * 只要转供的路径节点有重叠 表示已经回环短路了）
     *
     * @param feederTransferCaps 转供集合
     */
    private static boolean isLoopBack(List<FeederTransferCap> feederTransferCaps) {
        for (int i = 0; i < feederTransferCaps.size(); i++) {
            List<Node> sourcePath = feederTransferCaps.get(i).getPaths();
            for (int j = i + 1; j < feederTransferCaps.size(); j++) {
                List<Node> targetPath = feederTransferCaps.get(j).getPaths();
                // 如果两个路径有重叠那么就表示回环了
                if (NodeUtils.hasCommonNode(sourcePath, targetPath)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取所有可能运放调整的组合
     *
     * @param kgTransfers 所有运放调整开关的列表（二维数组，第二层是单个开关的所有调整）
     * @param currentLoad 当前线路负载率
     * @param maxLoad     最大不能超过的负载率
     */
    public static List<CombFeederTransfer> findCombKTrs(List<List<FeederTransferCap>> kgTransfers, double currentLoad, double maxLoad) {
        // 单个开关所有可能组合集合
        List<List<FeederTransferCap>> combCanFTrs = new ArrayList<>();
        List<CombFeederTransfer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(kgTransfers)) return result;

        backtrackDecrLoad(kgTransfers, 0, new ArrayList<>(), 0, currentLoad, maxLoad, combCanFTrs);

        // 过滤运放调整的转供路径范围不能互相有重叠的（回环 短路的）  以及 转至的联络线的负载率不能超过最大
        for (List<FeederTransferCap> feederTransferCaps : combCanFTrs) {
            CombFeederTransfer combFeederTransfer = getCombFTr(feederTransferCaps, currentLoad, maxLoad);

            if (combFeederTransfer == null) {
                continue;
            }

            // 去重
            if (result.stream().anyMatch(d -> d.equals(combFeederTransfer))) {
                continue;
            }

            // 1、判断是重叠 （只要转供的路径节点有重叠 表示已经回环了那么是不行滴）
            if (isLoopBack(feederTransferCaps)) {
                continue;
            }

            // 2、判断转至的联络线负载率是否都通过
            if (!combFeederTransfer.isFTrHasPass()) {
                continue;
            }

            result.add(combFeederTransfer);
        }

        return result;
    }

    /**
     * 回溯算法探索所有可能运放调整的组合（不考虑联络开关供电返回重叠问题，最后面一步再过滤）
     *
     * @param fTrList     所有运放调整开关的列表（二维数组，第二层是单个开关的所有调整）
     * @param row         当前行号
     * @param current     当前递归的组合
     * @param currentSum  当前已经减去的负载率
     * @param currentLoad 当前线路的负载率
     * @param maxLoad     最大不能超过的负载率
     * @param result      返回
     */
    private static void backtrackDecrLoad(List<List<FeederTransferCap>> fTrList, int row, List<FeederTransferCap> current,
                                          double currentSum, double currentLoad, double maxLoad,
                                          List<List<FeederTransferCap>> result) {
        // 检查当前组合是否满足条件：(当前负载率 - 当前减去的总和) < 最大负载率
        if (!current.isEmpty() && (currentLoad - currentSum) < maxLoad && (currentLoad - currentSum) > 0) {
            result.add(new ArrayList<>(current));
        }

        // 终止条件
        if (row >= fTrList.size()) {
            return;
        }

        // 从当前行取一个（如果行不为空）
        if (fTrList.get(row) != null && !fTrList.get(row).isEmpty()) {
            for (FeederTransferCap ftr : fTrList.get(row)) {
                // 当前需要减少点负载率 + 当前可以减少点
                double incrSum = currentSum + ftr.getDecrLoad();

                if (incrSum <= currentLoad) {  // 防止溢出
                    current.add(ftr);
                    backtrackDecrLoad(fTrList, row + 1, current, incrSum, currentLoad, maxLoad, result);
                    current.remove(current.size() - 1);
                }
            }
        }

        // 跳过当前行
        backtrackDecrLoad(fTrList, row + 1, current, currentSum, currentLoad, maxLoad, result);
    }
}
