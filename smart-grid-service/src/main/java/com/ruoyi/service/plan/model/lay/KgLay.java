package com.ruoyi.service.plan.model.lay;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.util.PlanProcessUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class KgLay extends BaseLay {
    public KgLay(String type, Node psrKg, boolean switchOpen) {
        this.type = type;
        this.switchOpen = switchOpen;
        this.psrKg = psrKg;
    }

    public KgLay(String type, Node beforeNode, Node afterNode, boolean switchOpen) {
        this.type = type;
        this.switchOpen = switchOpen;
        this.beforeNode = beforeNode;
        this.afterNode = afterNode;
    }

    // 开关相关：开关的状态
    private Boolean switchOpen;

    private List<Node> layNodes;

    /**
     * 电网开关 如果有就使用该电网开关
     */
    private Node psrKg;

    /**
     * 上一个和下一个开始的节点 用于这两个节点直接上开关
     */
    private Node beforeNode, afterNode;

    @Override
    public List<Node> getLayNodes() {
        return layNodes == null ? new ArrayList<>() : layNodes;
    }

    @Override
    public int getScope() {
        return 90;
    }

    public int getLayScope() {
        int result = 100;
        // 使用电网设备的高些
        if (beforeNode != null && afterNode != null) {
            result -= 10;
        }
        if (psrKg != null) {
            result -= 5;
        }
        return result;
    }

    @Override
    public List<Object> toStuStr() {
        List<Node> layKgNodes = getLayNodes();
        if (CollectionUtils.isEmpty(layKgNodes)) {
            return new ArrayList<>();
        }
        List<Node> nodes = layKgNodes.stream().filter(Node::isPsrNode).collect(Collectors.toList());
        Node kg = NodeUtils.findNode(layKgNodes, (n) -> n.isKg("all") && !n.isPsrNode());
        Node wlgt = NodeUtils.findNode(nodes, Node::isPole);
        Node seg = NodeUtils.findNode(nodes, Node::isSegFeeder);
        String endKgStr = StringUtils.equals(type, KG_TYPE) ? "分支首开关" : "开关";

        ArrayList<Object> strList = new ArrayList<>();
        if (wlgt != null) {
            strList.add("在");
            strList.add(PlanProcessUtils.toTextBtnStu(wlgt.getPsrId(), wlgt.getPsrType(), wlgt.getPsrName(), null, null));
            strList.add("杆塔");
        }
        if (seg != null) {
            strList.add("和");
            strList.add(PlanProcessUtils.toTextBtnStu(seg.getPsrId(), seg.getPsrType(), seg.getPsrName(), null, null));
            strList.add("导线段处");
        }

        if (kg != null) {
            strList.add("加装");
            strList.add(PlanProcessUtils.toTextBtnStu(kg.getPsrId(), kg.getId(), kg.getPsrType(), kg.getPsrName(), null, null));
        }
        strList.add(endKgStr);

        return strList;
    }

    /**
     * 获取当前分开的节点（匹配第一个即可）
     */
    public Node getFenNode() {
        for (Node node : getLayNodes()) {
            if (node.getSwitchOpen() != null && node.getSwitchOpen()) {
                return node;
            }
        }
        return null;
    }
}
