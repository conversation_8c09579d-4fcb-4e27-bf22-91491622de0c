package com.ruoyi.service.plan.utils;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

// 数组组合工具类型
public class ListCombinations {
    public static void main(String[] args) {
        // 示例列表 - 可修改为任意大小的列表
        List<String> listA = Arrays.asList("A", "B","C");
        List<Integer> listB = Arrays.asList(4, 5);

        List<List<Pair>> combinations = generateCombinations(listA, listB);

        // 输出所有可能的组合
        for (int i = 0; i < combinations.size(); i++) {
            System.out.print("组合 " + (i + 1) + ": ");
            for (Pair pair : combinations.get(i)) {
                System.out.print(pair + " ");
            }
            System.out.println();
        }
    }

    // 生成所有可能的组合
    public static <T, U> List<List<Pair>> generateCombinations(List<T> listA, List<U> listB) {
        List<List<Pair>> result = new ArrayList<>();

        // 如果listB为空，直接返回空组合
        if (listB.isEmpty()) {
            result.add(new ArrayList<>());
            return result;
        }

        // 使用回溯法生成所有可能的排列
        boolean[] used = new boolean[listB.size()];
        LinkedList<Pair> current = new LinkedList<>();
        backtrack(listA, listB, 0, used, current, result);

        return result;
    }

    // 回溯法生成排列
    private static <T, U> void backtrack(List<T> listA, List<U> listB, int indexA,
                                         boolean[] used, LinkedList<Pair> current,
                                         List<List<Pair>> result) {
        // 当处理完listA的所有元素或listB的所有元素都已使用时
        if (indexA == listA.size() || allUsed(used)) {
            result.add(new ArrayList<>(current));
            return;
        }

        T elementA = listA.get(indexA);

        // 尝试将elementA与listB中未使用的每个元素配对
        for (int i = 0; i < listB.size(); i++) {
            if (!used[i]) {
                used[i] = true;
                current.add(new Pair(elementA, listB.get(i)));

                // 处理下一个元素
                backtrack(listA, listB, indexA + 1, used, current, result);

                // 回溯
                current.removeLast();
                used[i] = false;
            }
        }

        // 当listB的元素不够时，允许不配对（即跳过当前元素A）
        if (indexA < listA.size() - 1) {
            backtrack(listA, listB, indexA + 1, used, current, result);
        }
    }

    // 检查是否所有元素都已被使用
    private static boolean allUsed(boolean[] used) {
        for (boolean b : used) {
            if (!b) return false;
        }
        return true;
    }

    // 表示一对元素的类
    @Data
    public static class Pair {
        private final Object first;
        private final Object second;

        public Pair(Object first, Object second) {
            this.first = first;
            this.second = second;
        }

        @Override
        public String toString() {
            return "(" + first + ", " + second + ")";
        }
    }
}
