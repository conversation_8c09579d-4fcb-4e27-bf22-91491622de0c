package com.ruoyi.service.plan.model.findLay;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import lombok.Data;

/**
 * 用于记录每个分叉点  相关数值
 * 用于查找各个大分子、分段时的记录
 */
@Data
public class NodeNumModel {

    public NodeNumModel(int num, Node node, BranchNode branchNode) {
        this.num = num;
        this.node = node;
        this.branchNode = branchNode;
    }

    public NodeNumModel(int num, Node node, Node nextNode) {
        this.num = num;
        this.node = node;
        this.nextNode = nextNode;
    }

    /**
     * 数值大小，不同场景不一样（比如大分子时  当前分叉点下的配变数量）
     */
    private int num;

    /**
     * 总的配变容量
     */
    private double totalCap;

    /**
     * 当前分叉节点
     */
    private Node node;

    /**
     * 当前分叉节点的下一个节点（用于辨别方向而已）
     */
    private Node nextNode;

    /**
     * 分支节点
     */
    BranchNode branchNode;
}
