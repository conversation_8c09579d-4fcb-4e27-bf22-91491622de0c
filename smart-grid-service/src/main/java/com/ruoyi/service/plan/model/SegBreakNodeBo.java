package com.ruoyi.service.plan.model;

import com.ruoyi.graph.Node;
import com.ruoyi.util.coordinates.CoordinateConverter;
import lombok.Data;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * 导线段打断节点设备
 */
@Data
public class SegBreakNodeBo {
    public SegBreakNodeBo(Node startNode, Node segEdge, Node endNode) {
        this.startNode = startNode;
        this.segEdge = segEdge;
        this.endNode = endNode;
    }

    /**
     * 开始节点
     */
    private Node startNode;

    /**
     * 中间导线段
     */
    private Node segEdge;

    /**
     * 结尾节点
     */
    private Node endNode;

    private Geometry point;

    /**
     * 起始坐标
     */
    private List<Double> startPoint;

    /**
     * 结束坐标
     */
    private List<Double> endPoint;


    public List<Double> toLngLat() {
        if (point == null) {
            return null;
        }
        return CoordinateConverter.pointToLngLat((Point) point);
    }
}
