package com.ruoyi.service.plan.impl.test;

import java.util.ArrayList;
import java.util.List;

public class ArrayCombinationWithDifference {

    public static List<List<Integer>> findCombinations(int[][] nums, int maxValue, int fixedDifference) {
        List<List<Integer>> result = new ArrayList<>();
        if (nums == null || nums.length == 0) return result;

        backtrack(nums, 0, new ArrayList<>(), 0, maxValue, fixedDifference, result);
        return result;
    }

    private static void backtrack(int[][] nums, int row, List<Integer> current,
                                  int currentSum, int maxValue, int fixedDifference,
                                  List<List<Integer>> result) {
        // 检查当前组合是否满足条件：(maxValue - currentSum) < fixedDifference
        if (!current.isEmpty() && (maxValue - currentSum) < fixedDifference && (maxValue - currentSum) > 0) {
            result.add(new ArrayList<>(current));
        }

        // 终止条件
        if (row >= nums.length) {
            return;
        }

        // 选项1：从当前行取一个数字（如果行不为空）
        if (nums[row] != null && nums[row].length > 0) {
            for (int num : nums[row]) {
                if (currentSum + num <= maxValue) {  // 防止溢出
                    current.add(num);
                    backtrack(nums, row + 1, current, currentSum + num, maxValue, fixedDifference, result);
                    current.remove(current.size() - 1);
                }
            }
        }

        // 选项2：跳过当前行
        backtrack(nums, row + 1, current, currentSum, maxValue, fixedDifference, result);
    }

    public static void main(String[] args) {
        int[][] nums = {
                {5, 4, 6},
                {3, 5, 7},
                {2, 4, 3},
                {1, 6, 5}
        };
        int maxValue = 20;
        int fixedDifference = 8;

        List<List<Integer>> combinations = findCombinations(nums, maxValue, fixedDifference);

        System.out.println("满足条件的组合（总和 > " + (maxValue - fixedDifference) + "):");
        for (List<Integer> combo : combinations) {
            int sum = combo.stream().mapToInt(Integer::intValue).sum();
            System.out.println(combo + " (总和: " + sum + ", 差值: " + (maxValue - sum) + ")");
        }
    }
}
