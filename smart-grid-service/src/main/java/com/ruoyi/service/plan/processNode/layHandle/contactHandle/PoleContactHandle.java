package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.processNode.ProcessNearNode;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.process.BdzDevProcess;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.process.NearDevProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 杆塔联络线的处理
 */
@Component
public class PoleContactHandle implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ProcessNearNode processNearNode;

    @Autowired
    NearDevProcess nearDevProcess;

    @Autowired
    BdzDevProcess bdzDevProcess;

    @Override
    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbyDeviceInfoVo> nearDevs, List<ProcessContactVo> usePContVos, String token) {

        // 附近节点
        NearNode nearNode = toNearNode(lay);

        // 根据use使用的节点过滤掉nearDevs （联络线末尾联络点 不能连接在同一个站房下或者杆塔下）
        nearDevs = filterNearDevByUseContVos(nearDevs, usePContVos);

        // 加工
        processNearNode.processDevRoute(nearNode, nearDevs, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = nearDevProcess.processHandle(nearNode, ProcessContactBo.POLE_RANGE);

        lay.setProcessContactVo(processContactVo);
    }

    @Override
    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbySubstationInfoVo> bdzBays, List<ProcessContactVo> usePContVos, String token) {

        NearNode nearNode = toNearNode(lay);

        // 根据use使用的节点过滤掉已使用没有剩余的间隔的变电站
        bdzBays = filterNearBdzByUseContVos(bdzBays, usePContVos);

        // 已经使用的剩余间隔ID集合
        List<String> useBayIds = usePContVos.stream().map(ProcessContactVo::getEndPsrId).collect(Collectors.toList());

        // 加工最近的变电站
        processNearNode.processBdzRoute(nearNode, bdzBays, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = bdzDevProcess.processHandle(nearNode, ProcessContactBo.POLE_RANGE, useBayIds);

        // 加装
        lay.setProcessContactVo(processContactVo);
    }

    public NearNode toNearNode(ContactLay lay) {
        return toNearNode(lay.getProcessContactBo().getPole());
    }
}
