package com.ruoyi.service.plan;

import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.graph.SegBetween;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 用于方案
 */
public interface IPlanTestService {

    ArrayList<SegBetween> getSegBetweenList(String feederId, String deviceId);


    /**
     * 生成网架方案
     */
    List<Plan> generateGridPlan(GeneratePlanBo generatePlanBo) throws ExecutionException, InterruptedException;

    void handleFeeder();

    ArrayList<Node> getContactKgs(@RequestParam String feederId);

    public List<HashMap<String, Object>> getAllContactKgSegList(String feederId);

    public List<Node> getMainPath(String feederId);
}
