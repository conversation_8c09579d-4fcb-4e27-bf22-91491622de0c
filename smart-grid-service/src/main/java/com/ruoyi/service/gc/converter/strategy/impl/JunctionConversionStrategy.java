package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsJunction;
import com.ruoyi.mapper.znap.DevDmsJunctionMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 电缆头转换策略
 */
@Slf4j
@Component
public class JunctionConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsJunctionMapper devDmsJunctionMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 215L;
    }

    @Override
    public Integer getDeviceType() {
        return 6; // 电缆头
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsJunction devDmsJunction = devDmsJunctionMapper.selectById(znapId);
        if (devDmsJunction != null) {
            setSingleNodeDevice(
                device,
                devDmsJunction.getNd(),
                devDmsJunction.getBvId(),
                devDmsJunction.getRdfid(),
                devDmsJunction.getMrid(),
                devDmsJunction.getFeederId()
            );
        } else {
            log.warn("未找到电缆头数据，znapId: {}", znapId);
        }
    }
}
