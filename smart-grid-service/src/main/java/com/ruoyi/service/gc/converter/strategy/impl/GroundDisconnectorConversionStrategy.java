package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsDisconnector;
import com.ruoyi.mapper.znap.DevDmsDisconnectorMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 地刀转换策略
 */
@Slf4j
@Component
public class GroundDisconnectorConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsDisconnectorMapper devDmsDisconnectorMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 207L;
    }

    @Override
    public Integer getDeviceType() {
        return 5; // 地刀
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectById(znapId);
        if (devDmsDisconnector != null) {
            setDoubleNodeDevice(
                device,
                devDmsDisconnector.getInd(),
                devDmsDisconnector.getJnd(),
                devDmsDisconnector.getBvId(),
                devDmsDisconnector.getRdfid(),
                devDmsDisconnector.getMrid(),
                devDmsDisconnector.getCabinetId()
            );
        } else {
            log.warn("未找到地刀数据，znapId: {}", znapId);
        }
    }
}
