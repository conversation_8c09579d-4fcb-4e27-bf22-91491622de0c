package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsTr;
import com.ruoyi.mapper.znap.DevDmsTr2Mapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 配变转换策略
 */
@Slf4j
@Component
public class TransformerConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsTr2Mapper devDmsTr2Mapper;

    @Override
    protected Long getSupportedTableNo() {
        return 208L;
    }

    @Override
    public Integer getDeviceType() {
        return 10; // 配变
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsTr devDmsTr = devDmsTr2Mapper.selectById(znapId);
        if (devDmsTr != null) {
            setSingleNodeDevice(
                device,
                devDmsTr.getNd(),
                devDmsTr.getBvId(),
                devDmsTr.getRdfid(),
                devDmsTr.getMrid(),
                devDmsTr.getCabinetId()
            );
        } else {
            log.warn("未找到配变数据，znapId: {}", znapId);
        }
    }
}
