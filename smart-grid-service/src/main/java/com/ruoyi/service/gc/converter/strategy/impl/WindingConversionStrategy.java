package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsWinding;
import com.ruoyi.mapper.znap.DevDmsWindingMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 绕组转换策略
 */
@Slf4j
@Component
public class WindingConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsWindingMapper devDmsWindingMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 210L;
    }

    @Override
    public Integer getDeviceType() {
        return 11; // 绕组
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsWinding devDmsWinding = devDmsWindingMapper.selectById(znapId);
        if (devDmsWinding != null) {
            setSingleNodeDevice(
                device,
                devDmsWinding.getNd(),
                devDmsWinding.getBvId(),
                devDmsWinding.getRdfid(),
                devDmsWinding.getMrid(),
                devDmsWinding.getTrId()
            );
        } else {
            log.warn("未找到绕组数据，znapId: {}", znapId);
        }
    }
}
