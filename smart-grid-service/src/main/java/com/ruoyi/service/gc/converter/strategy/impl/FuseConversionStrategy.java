package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsFuse;
import com.ruoyi.mapper.znap.DevDmsFuseMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 熔断器转换策略
 *
 */
@Slf4j
@Component
public class FuseConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsFuseMapper devDmsFuseMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 214L;
    }

    @Override
    public Integer getDeviceType() {
        return 3; // 熔断器
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsFuse devDmsFuse = devDmsFuseMapper.selectById(znapId);
        if (devDmsFuse != null) {
            setDoubleNodeDevice(
                device,
                devDmsFuse.getInd(),
                devDmsFuse.getJnd(),
                devDmsFuse.getBvId(),
                devDmsFuse.getRdfid(),
                devDmsFuse.getMrid(),
                devDmsFuse.getCabinetId()
            );
        } else {
            log.warn("未找到熔断器数据，znapId: {}", znapId);
        }
    }
}
