package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsLd;
import com.ruoyi.mapper.znap.DevDmsLdMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 负荷转换策略
 */
@Slf4j
@Component
public class LoadConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsLdMapper devDmsLdMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 209L;
    }

    @Override
    public Integer getDeviceType() {
        return 7; // 负荷
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsLd devDmsLd = devDmsLdMapper.selectById(znapId);
        if (devDmsLd != null) {
            setSingleNodeDevice(
                device,
                devDmsLd.getNd(),
                devDmsLd.getBvId(),
                devDmsLd.getRdfid(),
                devDmsLd.getMrid(),
                devDmsLd.getCabinetId()
            );
        } else {
            log.warn("未找到负荷数据，znapId: {}", znapId);
        }
    }
}
