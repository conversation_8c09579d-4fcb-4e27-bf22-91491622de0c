package com.ruoyi.service.gc.converter;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.GridChangeConstants;
import com.ruoyi.entity.gc.*;
import com.ruoyi.entity.znap.*;
import com.ruoyi.graph.Node;
import com.ruoyi.mapper.znap.*;
import com.ruoyi.service.gc.converter.strategy.DeviceConversionStrategy;
import com.ruoyi.service.gc.converter.strategy.DeviceConversionStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 拓扑结构转换器
 * 负责将ZnapTopology转换为网架变更数据库实体
 */
@Slf4j
@Component
public class TopologyConverter {

    @Resource
    private ConDmsFeederMapper conDmsFeederMapper;

    @Resource
    private ConDmsCabinetMapper conDmsCabinetMapper;

    @Resource
    private ConDmsCombinedMapper conDmsCombinedMapper;

    @Resource
    private DeviceConversionStrategyFactory deviceConversionStrategyFactory;

    @Resource
    private DevEmsBreakerMapper devEmsBreakerMapper;
    @Resource
    private DevDmsBreakerMapper devDmsBreakerMapper;

    /**
     * 转换为容器数据
     */
    public List<GcCon> convertToContainers(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过容器转换");
            return new ArrayList<>();
        }

        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (znapIdMap == null) {
            log.warn("znapIdMap为空，跳过容器转换");
            return new ArrayList<>();
        }

        List<GcCon> containers = new ArrayList<>();
        Map<String, Node> uniqueNodes = new HashMap<>();
        for (Node node : topology.getNodeList()) {
            // 检查节点是否有效
            if (node == null || !StringUtils.hasText(node.getPsrType())) {
                continue;
            }
            // 检查是否为容器类型
            if (!isContainerType(node, znapIdMap)) {
                continue;
            }
            // 使用psrId和psrType组合作为key进行去重
            String key = node.getPsrId() + "_" + node.getPsrType();
            if (!uniqueNodes.containsKey(key)) {
                uniqueNodes.put(key, node);
            }
        }
        for (Node node : uniqueNodes.values()) {
            GcCon container = convertNodeToContainer(node, versionId, znapIdMap);
            if (container != null) {
                containers.add(container);
            }
        }
        log.info("转换容器数据完成，共{}个容器", containers.size());
        return containers;
    }

    /**
     * 转换为设备数据
     */
    public List<GcDev> convertToDevices(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过设备转换");
            return new ArrayList<>();
        }

        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (znapIdMap == null) {
            log.warn("znapIdMap为空，跳过设备转换");
            return new ArrayList<>();
        }

        List<GcDev> devices = topology.getNodeList().stream().filter(node -> node != null && isDeviceType(node, znapIdMap)).map(node -> convertNodeToDeviceWithStrategy(node, versionId, znapIdMap)).filter(Objects::nonNull).collect(Collectors.toList());

        log.info("转换设备数据完成，共{}个设备", devices.size());
        return devices;
    }

    /**
     * 使用策略模式转换设备
     */
    private GcDev convertNodeToDeviceWithStrategy(Node node, Long versionId, Map<String, Long> znapIdMap) {
        String nodeId = node.getId();
        if (!StringUtils.hasText(nodeId)) {
            log.warn("节点ID为空，跳过转换");
            return null;
        }

        Long znapId = znapIdMap.get(nodeId);
        if (znapId == null) {
            log.warn("未找到节点[{}]对应的znapId", nodeId);
            return null;
        }

        Long tableNo = znapId >> 48;
        DeviceConversionStrategy strategy = deviceConversionStrategyFactory.getStrategy(tableNo);

        if (strategy != null) {
            return strategy.convertDevice(node, versionId, znapIdMap);
        } else {
            log.warn("未找到表号[{}]对应的转换策略，使用默认转换", tableNo);
            return convertNodeToDeviceDefault(node, versionId, znapId, tableNo);
        }
    }

    /**
     * 默认设备转换方法（兼容旧逻辑）
     */
    private GcDev convertNodeToDeviceDefault(Node node, Long versionId, Long znapId, Long tableNo) {
        GcDev device = new GcDev();
        device.setName(node.getPsrName());
        device.setAliasName(node.getPsrName());
        device.setPsrid(node.getPsrId());
        device.setPsrtype(node.getPsrType());
        device.setVersionId(versionId);
        device.setObjId(znapId);
        device.setObjTableno(tableNo);

        // 设置默认类型
        Integer deviceType = GridChangeConstants.DEVICE_TYPE_MAP.get(tableNo.toString());
        device.setType(deviceType);

        return device;
    }

    /**
     * 转换为电源点数据
     */
    public GcPower convertToPowers(ZnapTopology topology, Long versionId) {
        if (topology == null || topology.getStartNode() == null) {
            log.warn("拓扑结构或起始节点为空，跳过电源点转换");
            return null;
        }
        Node startNode = topology.getStartNode();

        GcPower gcPower = new GcPower();
        gcPower.setVersionId(versionId);

        String id = startNode.getId();
        Long emsBreakerId = topology.getZnapIdMap().get(id);
        if (emsBreakerId != null) {
            DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(emsBreakerId);
            if (devEmsBreaker != null) {
                Long feederId = devEmsBreaker.getFeederId();
                ConDmsFeeder conDmsFeeder = conDmsFeederMapper.selectById(feederId);
                gcPower.setStationId(conDmsFeeder.getSubId());
                gcPower.setHeadNd(conDmsFeeder.getHeadNd());
                gcPower.setCbId(conDmsFeeder.getHeadBrkId());
            }
        }
        return gcPower;
    }


    /**
     * 转换为设备参数数据（开关状态等）
     */
    public List<GcDevParaCb> convertToDeviceParams(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过设备参数转换");
            return new ArrayList<>();
        }
        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        List<GcDevParaCb> params = topology.getNodeList().stream().filter(node -> node != null && StringUtils.hasText(node.getPsrType())).filter(node -> isSwitchType(node, znapIdMap)).map(node -> createDeviceParam(node, versionId, znapIdMap)).filter(Objects::nonNull).collect(Collectors.toList());

        log.info("转换设备参数数据完成，共{}个参数", params.size());
        return params;
    }

    /**
     * 创建设备参数
     */
    private GcDevParaCb createDeviceParam(Node node, Long versionId, Map<String, Long> znapIdMap) {
        try {
            Long nodeId = znapIdMap.get(node.getId());
            if (nodeId == null) {
                log.warn("无法提取节点ID，跳过参数创建，节点: {}", node.getId());
                return null;
            }

            GcDevParaCb param = new GcDevParaCb();
            param.setId(nodeId);
            param.setVersionId(versionId);
            param.setPosValue(extractSwitchPosition(nodeId));
            return param;
        } catch (Exception e) {
            log.error("创建设备参数失败，节点: {}", node.getId(), e);
            return null;
        }
    }


    /**
     * 判断是否为容器类型
     * 变电站、馈线、环网柜、组合开关
     * 变电站、馈线nodeList中没有存，需要手动加
     */
    private boolean isContainerType(Node node, Map<String, Long> znapIdMap) {
        Long id = znapIdMap.get(node.getId());
        if (id == null) {
            // 判断psrType
            boolean isHwg = DeviceConstants.HWG_TYPES.contains(node.getPsrType());
            boolean isKG = DeviceConstants.KG_IN_TYPES.contains(node.getPsrType());
            return isHwg || isKG;
        }
        // 将id右移48位获取表号
        Long tableNo = id >> 48;
        return GridChangeConstants.isContainerType(tableNo);
    }

    /**
     * 判断是否为设备类型
     */
    private boolean isDeviceType(Node node, Map<String, Long> znapIdMap) {
        if (GridChangeConstants.isDeviceType(node.getPsrType())) {
            return true;
        }
        // 绕组没有psrType 需要单独判断表号是不是210
        String id = node.getId();
        if (id != null) {
            Long znapId = znapIdMap.get(id);
            if (znapId != null) {
                Long tableNo = znapId >> 48;
                if (tableNo.equals(210L)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否为开关类型
     */
    private boolean isSwitchType(Node node, Map<String, Long> znapIdMap) {
        String id = node.getId();
        Long znapId = znapIdMap.get(id);
        if (znapId != null) {
            return GridChangeConstants.isSwitchType(znapId >> 48);
        }
        return false;
    }

    /**
     * 转换节点为容器
     * 容器层级关系：变电站 -> 馈线 -> 环网柜 -> 组合开关
     */
    private GcCon convertNodeToContainer(Node node, Long versionId, Map<String, Long> znapIdMap) {
        try {
            GcCon container = createBaseContainer(node, versionId);

            Long znapId = znapIdMap.get(node.getId());
            if (znapId != null) {
                container.setObjId(znapId);
                container.setObjTableno((znapId >> 48));
                // 根据容器类型填充特定信息
                fillContainerSpecificInfo(container, znapId);
            }

            return container;
        } catch (Exception e) {
            log.error("转换容器失败，节点: {}", node.getId(), e);
            return null;
        }
    }

    /**
     * 创建基础容器信息
     */
    private GcCon createBaseContainer(Node node, Long versionId) {
        GcCon container = new GcCon();
        container.setName(node.getPsrName());
        container.setAliasName(node.getPsrName());
        container.setPsrid(node.getPsrId());
        container.setPsrtype(node.getPsrType());
        container.setVersionId(versionId);

        // 设置容器类型
        Integer type = GridChangeConstants.CONTAINER_TYPE_MAP.get(node.getPsrType());
        container.setType(type);

        return container;
    }

    /**
     * 填充容器特定信息
     */
    private void fillContainerSpecificInfo(GcCon container, Long znapId) {
        Integer type = container.getType();
        if (type == null) {
            return;
        }
        switch (type) {
            case 1: // 变电站
                container.setContainerId(null); // 变电站没有上级容器
                break;
            case 2: // 馈线
                fillFeederInfo(container, znapId);
                break;
            case 3: // 环网柜
                fillCabinetInfo(container, znapId);
                break;
            case 4: // 组合开关
                fillCombinedInfo(container, znapId);
                break;
            default:
                log.warn("未知容器类型: {}", type);
        }
    }

    /**
     * 填充馈线信息
     */
    private void fillFeederInfo(GcCon container, Long znapId) {
        ConDmsFeeder feeder = conDmsFeederMapper.selectById(znapId);
        if (feeder != null) {
            container.setContainerId(feeder.getSubId());
            container.setRdfid(feeder.getRdfid());
            container.setMrid(feeder.getMrid());
        }
    }

    /**
     * 填充环网柜信息
     */
    private void fillCabinetInfo(GcCon container, Long znapId) {
        ConDmsCabinet cabinet = conDmsCabinetMapper.selectById(znapId);
        if (cabinet != null) {
            container.setContainerId(cabinet.getFeederId());
            container.setRdfid(cabinet.getRdfid());
            container.setMrid(cabinet.getMrid());
        }
    }

    /**
     * 填充组合开关信息
     */
    private void fillCombinedInfo(GcCon container, Long znapId) {
        ConDmsCombined combined = conDmsCombinedMapper.selectById(znapId);
        if (combined != null) {
            container.setContainerId(combined.getCabinetId());
            container.setRdfid(combined.getRdfid());
            container.setMrid(combined.getMrid());
        }
    }


    /**
     * 提取开关位置状态
     */
    private Integer extractSwitchPosition(Long breakerId) {
        // TODO 先使用原始的开关状态
        DevDmsBreaker devDmsBreaker = devDmsBreakerMapper.selectById(breakerId);
        if (devDmsBreaker == null) {
            // 如果配网开关为空查询主网开关
            DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(breakerId);
            if (devEmsBreaker != null) {
                return Math.toIntExact(devEmsBreaker.getNormalOpen());
            }
        } else {
            return Math.toIntExact(devDmsBreaker.getNormalOpen());
        }
        return GridChangeConstants.SwitchPosition.CLOSE;
    }

    /**
     * 转换并保存馈线段
     * @param topology
     * @param versionId
     * @return
     */
    public List<GcDevParaSegment> convertToDeviceParamsSeg(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过设备参数转换");
            return new ArrayList<>();
        }
        if (topology.getZnapIdMap() == null) {
            log.warn("znapIdMap为空，跳过设备参数转换");
            return new ArrayList<>();
        }
        List<GcDevParaSegment> params = topology.getNodeList().stream().filter(node -> node != null && StringUtils.hasText(node.getPsrType())).filter(node -> node.isSegFeeder()).map(node -> createDeviceParamSeg(node, versionId, topology.getZnapIdMap())).filter(Objects::nonNull).collect(Collectors.toList());
        return params;
    }

    private GcDevParaSegment createDeviceParamSeg(Node node, Long versionId, Map<String, Long> znapIdMap) {
        try {
            Long znapId = znapIdMap.get(node.getId());
            if (znapId == null) {
                log.warn("无法提取节点ID，跳过参数创建，节点: {}", node.getId());
                return null;
            }
            GcDevParaSegment param = new GcDevParaSegment();
            param.setId(znapId);
            param.setVersionId(versionId);
            return param;
        } catch (Exception e) {
            log.error("创建设备参数失败，节点: {}", node.getId(), e);
        }
        return null;
    }
}
