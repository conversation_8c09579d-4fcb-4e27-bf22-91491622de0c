package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsPole;
import com.ruoyi.mapper.znap.DevDmsPoleMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 杆塔转换策略
 */
@Slf4j
@Component
public class PoleConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsPoleMapper devDmsPoleMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 212L;
    }

    @Override
    public Integer getDeviceType() {
        return 8; // 杆塔
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsPole devDmsPole = devDmsPoleMapper.selectById(znapId);
        if (devDmsPole != null) {
            setSingleNodeDevice(
                device,
                devDmsPole.getNd(),
                -1L, // 杆塔没有电压等级
                devDmsPole.getRdfid(),
                devDmsPole.getMrid(),
                devDmsPole.getFeederId()
            );
        } else {
            log.warn("未找到杆塔数据，znapId: {}", znapId);
        }
    }
}
