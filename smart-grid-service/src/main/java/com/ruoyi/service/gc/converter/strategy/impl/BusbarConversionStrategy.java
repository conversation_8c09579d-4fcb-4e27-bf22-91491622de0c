package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsBusbar;
import com.ruoyi.mapper.znap.DevDmsBusbarMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 母线转换策略
 */
@Slf4j
@Component
public class BusbarConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsBusbarMapper devDmsBusbarMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 211L;
    }

    @Override
    public Integer getDeviceType() {
        return 4; // 母线
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsBusbar devDmsBusbar = devDmsBusbarMapper.selectById(znapId);
        if (devDmsBusbar != null) {
            setSingleNodeDevice(device, devDmsBusbar.getNd(), devDmsBusbar.getBvId(), devDmsBusbar.getRdfid(), devDmsBusbar.getMrid(), devDmsBusbar.getCabinetId());
        } else {
            log.warn("未找到母线数据，znapId: {}", znapId);
        }
    }
}
