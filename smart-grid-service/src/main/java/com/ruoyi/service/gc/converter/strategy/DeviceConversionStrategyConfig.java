package com.ruoyi.service.gc.converter.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * 设备转换策略配置类
 * 用于管理所有设备转换策略的配置信息
 */
@Slf4j
@Configuration
public class DeviceConversionStrategyConfig {

    /**
     * 设备类型映射表
     * 表号 -> 设备类型描述
     */
    public static final String[][] DEVICE_TYPE_MAPPINGS = {
        {"100", "主网开关", "EmsBreakerConversionStrategy"},
        {"204", "配网开关", "DmsBreakerConversionStrategy"},
        {"205", "刀闸", "DisconnectorConversionStrategy"},
        {"206", "馈线段", "SegmentConversionStrategy"},
        {"207", "地刀", "GroundDisconnectorConversionStrategy"},
        {"208", "配变", "TransformerConversionStrategy"},
        {"209", "负荷", "LoadConversionStrategy"},
        {"210", "绕组", "WindingConversionStrategy"},
        {"211", "母线", "BusbarConversionStrategy"},
        {"212", "杆塔", "PoleConversionStrategy"},
        {"214", "熔断器", "FuseConversionStrategy"},
        {"215", "电缆头", "JunctionConversionStrategy"}
    };

    /**
     * 获取所有支持的表号
     */
    public static Long[] getSupportedTableNos() {
        Long[] tableNos = new Long[DEVICE_TYPE_MAPPINGS.length];
        for (int i = 0; i < DEVICE_TYPE_MAPPINGS.length; i++) {
            tableNos[i] = Long.parseLong(DEVICE_TYPE_MAPPINGS[i][0]);
        }
        return tableNos;
    }

}
