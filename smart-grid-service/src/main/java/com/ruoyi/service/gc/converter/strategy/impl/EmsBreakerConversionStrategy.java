package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevEmsBreaker;
import com.ruoyi.mapper.znap.DevEmsBreakerMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 主网开关转换策略
 */
@Slf4j
@Component
public class EmsBreakerConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevEmsBreakerMapper devEmsBreakerMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 100L;
    }

    @Override
    public Integer getDeviceType() {
        return 0; // 主网开关
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(znapId);
        if (devEmsBreaker != null) {
            setDoubleNodeDevice(
                device,
                devEmsBreaker.getInd(),
                devEmsBreaker.getJnd(),
                devEmsBreaker.getBvId(),
                devEmsBreaker.getRdfid(),
                devEmsBreaker.getMrid(),
                devEmsBreaker.getSubId()
            );
        } else {
            log.warn("未找到主网开关数据，znapId: {}", znapId);
        }
    }
}
