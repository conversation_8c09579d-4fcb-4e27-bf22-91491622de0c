package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsSegment;
import com.ruoyi.mapper.znap.DevDmsSegmentMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 馈线段转换策略
 */
@Slf4j
@Component
public class SegmentConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsSegmentMapper devDmsSegmentMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 206L;
    }

    @Override
    public Integer getDeviceType() {
        return 9; // 馈线段
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsSegment devDmsSegment = devDmsSegmentMapper.selectById(znapId);
        if (devDmsSegment != null) {
            setDoubleNodeDevice(
                device,
                devDmsSegment.getInd(),
                devDmsSegment.getJnd(),
                devDmsSegment.getBvId(),
                devDmsSegment.getRdfid(),
                devDmsSegment.getMrid(),
                devDmsSegment.getFeederId()
            );
        } else {
            log.warn("未找到馈线段数据，znapId: {}", znapId);
        }
    }
}
