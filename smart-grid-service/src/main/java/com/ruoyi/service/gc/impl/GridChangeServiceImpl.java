package com.ruoyi.service.gc.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.entity.gc.*;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.mapper.gc.*;
import com.ruoyi.service.gc.IGridChangeService;
import com.ruoyi.service.gc.converter.TopologyConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 网架变更服务实现类
 */
@Slf4j
@Service
public class GridChangeServiceImpl implements IGridChangeService {

    @Resource
    private GcSchemaMapper gcSchemaMapper;

    @Resource
    private GcVersionMapper gcVersionMapper;

    @Resource
    private GcConMapper gcConMapper;

    @Resource
    private GcDevMapper gcDevMapper;

    @Resource
    private GcPowerMapper gcPowerMapper;

    @Resource
    private GcDevParaCbMapper gcDevParaCbMapper;

    @Resource
    private TopologyConverter topologyConverter;

    @Resource
    private GcDevParaSegmentMapper gcDevParaSegmentMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("slave")
    public Long saveGridChangeTopology(Long problemId, SingAnalysis singAnalysis, String schemaName, String description) {
        try {
            log.info("开始保存网架变更拓扑结构，problemId: {}, schemaName: {}", problemId, schemaName);

            // 2. 创建方案
            GcSchema schema = createOrGetSchema(problemId, schemaName, description);

            // 3. 创建版本
            GcVersion version = createVersion(schema.getId(), problemId);

            // 4. 转换并保存拓扑数据
            saveTopologyData(version.getId(), singAnalysis);

            log.info("网架变更拓扑结构保存成功，versionId: {}", version.getId());
            return version.getId();

        } catch (Exception e) {
            log.error("保存网架变更拓扑结构失败", e);
            throw new RuntimeException("保存网架变更拓扑结构失败: " + e.getMessage(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGridChangeTopology(Long versionId) {
        try {
            log.info("删除网架变更拓扑结构，versionId: {}", versionId);

            // 删除设备参数
            gcDevParaCbMapper.delete(
                    new LambdaQueryWrapper<GcDevParaCb>().eq(GcDevParaCb::getVersionId, versionId)
            );

            // 删除电源点
            gcPowerMapper.delete(
                    new LambdaQueryWrapper<GcPower>().eq(GcPower::getVersionId, versionId)
            );

            // 删除设备
            gcDevMapper.delete(
                    new LambdaQueryWrapper<GcDev>().eq(GcDev::getVersionId, versionId)
            );

            // 删除容器
            gcConMapper.delete(
                    new LambdaQueryWrapper<GcCon>().eq(GcCon::getVersionId, versionId)
            );

            // 删除版本
            gcVersionMapper.deleteById(versionId);

            log.info("网架变更拓扑结构删除成功，versionId: {}", versionId);
            return true;

        } catch (Exception e) {
            log.error("删除网架变更拓扑结构失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setCurrentVersion(Long versionId, Long problemId) {
        try {
            log.info("设置当前版本，versionId: {}, problemId: {}", versionId, problemId);

            // 将同一问题下的所有版本设为非当前版本
            gcVersionMapper.update(null,
                    new LambdaUpdateWrapper<GcVersion>()
                            .eq(GcVersion::getProblemId, problemId)
                            .set(GcVersion::getIsCurrent, 0)
            );

            // 设置指定版本为当前版本
            gcVersionMapper.update(null,
                    new LambdaUpdateWrapper<GcVersion>()
                            .eq(GcVersion::getId, versionId)
                            .set(GcVersion::getIsCurrent, 1)
            );

            log.info("设置当前版本成功");
            return true;

        } catch (Exception e) {
            log.error("设置当前版本失败", e);
            return false;
        }
    }

    /**
     * 创建方案
     */
    private GcSchema createOrGetSchema(Long problemId, String schemaName, String description) {
        // 创建新方案
        GcSchema schema = new GcSchema();
        schema.setName(schemaName);
        schema.setDescription(description);
        schema.setProblemId(problemId);
        schema.setStatus(1);
        schema.setCreateUser("Admin");
        schema.setCreateDt(new Date());
        schema.setUpdateDt(new Date());
        gcSchemaMapper.insert(schema);
        return schema;
    }

    /**
     * 创建版本
     */
    private GcVersion createVersion(Long schemaId, Long problemId) {
        // 生成版本号
        String versionNum = generateVersionNum(problemId);

        GcVersion version = new GcVersion();
        version.setName("版本_" + versionNum);
        version.setVersionNum(versionNum);
        version.setDescription("自动生成的网架变更版本");
        version.setSchemaId(schemaId);
        version.setProblemId(problemId);
        version.setIsCurrent(1);
        version.setCreateUser("Admin");
        version.setCreateDt(new Date());
        version.setUpdateDt(new Date());

        gcVersionMapper.insert(version);
        return version;
    }

    /**
     * 生成版本号
     */
    private String generateVersionNum(Long problemId) {
        Long count = gcVersionMapper.selectCount(
                new LambdaQueryWrapper<GcVersion>().eq(GcVersion::getProblemId, problemId)
        );
        return "v" + (count + 1);
    }

    /**
     * 保存拓扑数据
     */
    private void saveTopologyData(Long versionId, SingAnalysis singAnalysis) {
        ZnapTopology topology = singAnalysis.getTopologyMap();

        // 转换并保存容器数据
        List<GcCon> containers = topologyConverter.convertToContainers(topology, versionId);
        if (!containers.isEmpty()) {
            gcConMapper.insertBatch(containers);
        }

        // 转换并保存设备数据
        List<GcDev> devices = topologyConverter.convertToDevices(topology, versionId);
        if (!devices.isEmpty()) {
            gcDevMapper.insertBatch(devices);
        }

        // 转换并保存电源点数据
        GcPower power = topologyConverter.convertToPowers(topology, versionId);
        if (power != null) {
            gcPowerMapper.insert(power);
        }

        // 保存设备参数（开关状态）
        List<GcDevParaCb> deviceParams = topologyConverter.convertToDeviceParams(topology, versionId);
        if (!deviceParams.isEmpty()) {
            gcDevParaCbMapper.insertBatch(deviceParams);
        }
        // TODO: 保存其他设备参数 刀闸参数表  熔断器参数表

        // TODO转换并保存馈线段
        List<GcDevParaSegment> deviceParamsSeg = topologyConverter.convertToDeviceParamsSeg(topology, versionId);
        if (!deviceParamsSeg.isEmpty()) {
            gcDevParaSegmentMapper.insertBatch(deviceParamsSeg);
        }
    }
}
