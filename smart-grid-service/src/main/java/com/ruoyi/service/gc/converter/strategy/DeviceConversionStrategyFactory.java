package com.ruoyi.service.gc.converter.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备转换策略工厂
 *
 */
@Slf4j
@Component
public class DeviceConversionStrategyFactory {

    @Resource
    private List<DeviceConversionStrategy> strategies;

    private final Map<Long, DeviceConversionStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (DeviceConversionStrategy strategy : strategies) {
            // 使用配置中定义的表号进行注册
            Long[] supportedTableNos = DeviceConversionStrategyConfig.getSupportedTableNos();
            for (Long tableNo : supportedTableNos) {
                if (strategy.supports(tableNo)) {
                    if (strategyMap.containsKey(tableNo)) {
                        log.warn("表号[{}]已存在策略[{}]，跳过注册[{}]", tableNo, strategyMap.get(tableNo).getClass().getSimpleName(), strategy.getClass().getSimpleName());
                        continue;
                    }
                    strategyMap.put(tableNo, strategy);
                }
            }
        }
    }

    /**
     * 获取设备转换策略
     *
     * @param tableNo 表号
     * @return 转换策略
     */
    public DeviceConversionStrategy getStrategy(Long tableNo) {
        DeviceConversionStrategy strategy = strategyMap.get(tableNo);
        if (strategy == null) {
            log.debug("未找到表号[{}]对应的转换策略", tableNo);
        }
        return strategy;
    }
}
