package com.ruoyi.service.gc.converter.strategy.impl;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.znap.DevDmsBreaker;
import com.ruoyi.mapper.znap.DevDmsBreakerMapper;
import com.ruoyi.service.gc.converter.strategy.AbstractDeviceConversionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 配网开关转换策略
 */
@Slf4j
@Component
public class DmsBreakerConversionStrategy extends AbstractDeviceConversionStrategy {

    @Resource
    private DevDmsBreakerMapper devDmsBreakerMapper;

    @Override
    protected Long getSupportedTableNo() {
        return 204L;
    }

    @Override
    public Integer getDeviceType() {
        return 1; // 配网开关
    }

    @Override
    protected void fillDeviceSpecificInfo(GcDev device, Long znapId) {
        DevDmsBreaker devDmsBreaker = devDmsBreakerMapper.selectById(znapId);
        if (devDmsBreaker != null) {
            setDoubleNodeDevice(
                device,
                devDmsBreaker.getInd(),
                devDmsBreaker.getJnd(),
                devDmsBreaker.getBvId(),
                devDmsBreaker.getRdfid(),
                devDmsBreaker.getMrid(),
                devDmsBreaker.getFeederId()
            );
        } else {
            log.warn("未找到配网开关数据，znapId: {}", znapId);
        }
    }
}
