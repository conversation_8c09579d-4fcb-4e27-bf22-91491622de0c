package com.ruoyi.service.cost;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.entity.cost.Cost;
import com.ruoyi.entity.cost.CostType;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.PlanCost;

import java.util.List;

public interface ICostService {

    Cost selectCost(List<CostType> costList);
    PlanCost computeCost(Plan plan) throws JsonProcessingException;
}
