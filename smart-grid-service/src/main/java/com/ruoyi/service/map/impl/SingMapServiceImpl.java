package com.ruoyi.service.map.impl;

import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.singMap.impl.SingMapTopologyServiceImpl;
import com.ruoyi.service.znap.impl.ZnapTopologyServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SingMapServiceImpl implements ISingMapService {

    @Autowired
    ZnapTopologyServiceImpl znapTopologyService;

    @Autowired
    SingMapTopologyServiceImpl singMapTopologyService;

    /**
     * 分析单线图拓扑数据
     *
     * @param feederId 线路ID
     */
    @Override
    public SingAnalysis analysisSingMap(String feederId, boolean isZnap) {
        try {
            // znap拓扑节点构建 图模文件和单线图有些有可能不一样  然后也没有中压中户接入点下的设备（虽然里面手动补充了但还是有可能问题），然后也没有一些连接线。。。。
            //  ZnapTopology znapTopology = znapTopologyService.generateNode(feederId);
            ZnapTopology znapTopology = singMapTopologyService.generateNode(feederId, isZnap);

            Node startNode = znapTopology.getStartNode();
            //  Map<String, Node> nodeMap = znapTopology.getNodeMap();
            //   ArrayList<Node> nodeList = znapTopology.getNodeList();
            List<Node> kgContactNodes = znapTopology.getKgContactNodes();

            // 节点路径分析
            NodePath nodePath = new NodePath();
            nodePath.analysisPath(startNode, kgContactNodes, znapTopology.getContactFeederKgs());
            //  nodePath.setNodeList(nodeList);
            // nodePath.setNodeMap(nodeMap);

            return new SingAnalysis(znapTopology, nodePath);
        } catch (Exception e) {
        }
        return null;
    }
}
