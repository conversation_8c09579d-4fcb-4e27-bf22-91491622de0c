package com.ruoyi.service.map.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.SelectExternalUtil;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;

import com.ruoyi.entity.map.NRCoords;
import com.ruoyi.entity.map.PsrUri;
import com.ruoyi.entity.map.RequestPayload;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通过id和type查询一张图的坐标
 */
@Component
public class SelectCoords {
    @Value("${selectDeviceCoords.path}")
    private String path;

    public List<NRCoords> SelectCoords(List<PsrIdAndPsrType> idAndPsrTypeList) throws Exception {
        List<Map<String, Object>> mapList = parseResult(getString(idAndPsrTypeList));
        List<NRCoords> nrCoords = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : mapList) {
            List<HashMap<String, String>> hashMapList = (List<HashMap<String, String>>) stringObjectMap.get("psrList");
            for (HashMap<String, String> hashMap : hashMapList) {
                nrCoords.add(new NRCoords(
                        hashMap.get("psrId"),
                        hashMap.get("psrType"),
                        hashMap.get("psrName"),
                        parseCoordinatePairs(hashMap.get("coordinate")),
                        hashMap.get("feederId"),
                        hashMap.get("feederName")));
            }

        }
        return nrCoords;
    }

    /**
     * 调用南瑞接口
     *
     * @return
     * @throws JsonProcessingException
     */
    private String getString(List<PsrIdAndPsrType> psrIdAndPsrTypes) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        List<PsrUri> psrUriList = psrIdAndPsrTypes.parallelStream()
                .map(e -> new PsrUri(e.getPsrId(), e.getPsrType(), "", 0))
                .collect(Collectors.toList());
        RequestPayload requestPayload = new RequestPayload();
        requestPayload.setPsrUriList(psrUriList);
        String targetString = SelectExternalUtil.SelectExternalUtil(path, mapper.writeValueAsString(requestPayload));
        return targetString;
    }

    /**
     * 从 API 响应 JSON 中提取 result 数据
     *
     * @param jsonResponse API 返回的完整 JSON 字符串
     * @return result 字段对应的 List<Map>
     * @throws Exception JSON 解析异常
     */
    public static List<Map<String, Object>> parseResult(String jsonResponse) throws Exception {
        // 解析完整 JSON 为 Map
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> responseMap = mapper.readValue(jsonResponse, Map.class);

        // 获取 reply 信息（可选）
        Map<String, Object> reply = (Map<String, Object>) responseMap.get("reply");
        System.out.println("响应码: " + reply.get("code"));
        System.out.println("响应消息: " + reply.get("msg"));

        // 提取 result 列表
        return (List<Map<String, Object>>) responseMap.get("result");
    }

    public static String parseCoordinatePairs(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        StringBuilder geoString = new StringBuilder();
        String[] segments = input.trim().split(",");
        for (String segment : segments) {
            String[] parts = segment.trim().split("\\s+");
            if (parts.length % 2 != 0) {
                throw new IllegalArgumentException("数据不是偶数个");
            }
            for (int i = 0; i < parts.length; i += 2) {
                try {
                    double x = Double.parseDouble(parts[i]);
                    double y = Double.parseDouble(parts[i + 1]);
                    double[] doubles = transformMercatorToLngLat(x, y);
                    geoString.append(" ").append(doubles[0]).append(" ").append(doubles[1]);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("解析错误" + e.toString());
                }
            }
            geoString.append(",");
        }
        // 删除最后一个，
        geoString.deleteCharAt(geoString.length() - 1);
        return geoString.toString().toString();
    }

    public static double[] transformMercatorToLngLat(double x, double y) {
        double lng = x / 20037508.34 * 180;
        double lat = y / 20037508.34 * 180;
        lat = 180 / Math.PI * (2 * Math.atan(Math.exp(lat * Math.PI / 180)) - Math.PI / 2);
        return new double[]{lng, lat}; //[12727039.383734727, 3579066.6894065146]
    }


}
