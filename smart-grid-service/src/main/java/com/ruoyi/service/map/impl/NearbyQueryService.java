package com.ruoyi.service.map.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.map.DeviceCoords;
import com.ruoyi.entity.map.NearFeeder;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.znap.TopoTraceReal;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.znap.BayQueryMapper;
import com.ruoyi.service.map.INearbyQueryService;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.util.map.LineDistanceQuery;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.WLGT;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestFeederAllDevice;
import static com.ruoyi.util.coordinates.SelectNearDeviceCoords.findNearestPoints;
import static com.ruoyi.util.map.LineDistanceQuery.selectLine;

/**
 * 附近查询服务实现类
 * 统一处理所有最近线路和变电站的查询逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NearbyQueryService implements INearbyQueryService {

    @Resource
    private FeederDeviceMapper feederDeviceMapper;

    @Resource
    private NearFeederMapper nearFeederMapper;

    @Resource
    private KgMapper kgMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private ExecutorService executorService;

    @Resource
    private DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Resource
    private BayQueryMapper bayQueryMapper;

    @Autowired
    private IBayQueryService bayQueryService;

    private final ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    /**
     * 查询一条线附近的线,根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo 查询条件
     * @return 附近线路列表
     */
    @Override
    public List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo) {
        // 目标线路
        List<List<DeviceFeeder>> deviceFeederList = getAllPageDeviceFeedersAsync();

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();

        if (CollectionUtils.isEmpty(feederRangeQueryBo.getCoordinateList())) {
            LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());

            String geoList = feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList();
            if (StringUtils.isBlank(geoList)) {
                return null;
            }

            feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(geoList));
        }
        List<NeedFeederVo> needFeederVos = new ArrayList<>();
        for (int i = 0; i < deviceFeederList.size(); i++) {
            int finalI = i;

            futures.add(CompletableFuture.supplyAsync(() -> selectLine(feederRangeQueryBo, deviceFeederList.get(finalI), needFeederVos)));
        }
        List<DeviceFeeder> feederList = futures.stream().map(CompletableFuture::join).flatMap(List::stream).collect(Collectors.toList());

        if (feederRangeQueryBo.getNum() == -1) {
            selectLine(feederRangeQueryBo, distinctByPsrId(feederList), needFeederVos);
            return distinctByPsrIdVo(needFeederVos);
        }

        return distinctByPsrIdVo(needFeederVos);
    }

    /**
     * 快捷查询附近线路（从数据库最近的线路表查询的）
     *
     * @param feederId 线路ID
     * @param radius   查询半径
     * @return 附近线路列表
     */
    @Override
    public List<DeviceFeeder> selectNearFeeder(String feederId, Double radius) {
        LambdaQueryWrapper<NearFeeder> nearFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        nearFeederLambdaQueryWrapper.eq(NearFeeder::getFeederId, feederId);
        NearFeeder nearFeeder = nearFeederMapper.selectOne(nearFeederLambdaQueryWrapper);

        if (nearFeeder == null) {
            return null;
        }

        List<String> idList = Arrays.stream(nearFeeder.getNearFeederId().split(",")).filter(s -> !s.isEmpty()) // 过滤空字符串
                .map(String::trim) // 去除首尾空格
                .collect(Collectors.toList());

        LambdaQueryWrapper<DeviceFeeder> deviceFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceFeederLambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
        List<DeviceFeeder> deviceFeederList = feederDeviceMapper.selectList(deviceFeederLambdaQueryWrapper);

        FeederRangeQueryBo feederRangeQueryBo = new FeederRangeQueryBo();
        feederRangeQueryBo.setPsrId(feederId);
        feederRangeQueryBo.setRange(radius);
        feederRangeQueryBo.setNum(-1);

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        return selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
    }

    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo 查询条件实体
     * @param deviceFeederList   已知的附近线
     * @return 最近线路列表
     */
    @Override
    public List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList) {
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());
        feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList()));
        List<NeedFeederVo> needFeederVos = new ArrayList<>();
        return selectLine(feederRangeQueryBo, deviceFeederList, needFeederVos);
    }

    /**
     * 线找线（通过一条线找另一条线最近的点）
     *
     * @param doubleArrays 目标点坐标集合
     * @param num          最近几条的数量
     * @param filteredList 线路集合
     * @return 最近线路信息列表
     */
    @Override
    public List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList) {
        // 为每个 double [] 数组创建一个任务
        if (filteredList == null) {
            filteredList = feederDeviceMapper.selectList();
        }
        List<CompletableFuture<List<NeedFeederVo>>> futures = new ArrayList<>();

        for (int i = 0; i < doubleArrays.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用

            // 提交任务到线程池
            List<DeviceFeeder> finalFilteredList = filteredList;
            futures.add(CompletableFuture.supplyAsync(() -> {
                double[] array = doubleArrays.get(index);
                try {
                    return pointBy(array, num, finalFilteredList);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }, executor));
        }

        // 保持结果的嵌套结构
        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务执行失败", e);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 动态分段异步查询所有线路
     *
     * @return 分段的线路列表
     */
    @Override
    public List<List<DeviceFeeder>> getAllPageDeviceFeedersAsync() {
        // 1. 先查询总记录数
        int totalCount = getTotalCount();

        // 2. 根据总记录数和每段大小计算分段数
        int batchSize = 1000; // 每批查询数量
        int batches = (int) Math.ceil((double) totalCount / batchSize);

        // 3. 创建异步任务列表
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();
        for (int i = 0; i < batches; i++) {
            final int offset = i * batchSize;
            futures.add(CompletableFuture.supplyAsync(() -> queryRange(offset, batchSize), executorService));
        }

        return futures.stream().map(CompletableFuture::join)  // 将每个 future 转换为 List<DeviceFeeder>
                .collect(Collectors.toList());
    }

    /**
     * 配合动态分段异步查询所有线路——查询总记录数
     *
     * @return 总记录数
     */
    private int getTotalCount() {
        return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM device_feeder", Integer.class);
    }

    /**
     * 配合动态分段异步查询所有线路——分页查询方法
     *
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 线路列表
     */
    private List<DeviceFeeder> queryRange(int offset, int limit) {
        String sql = "SELECT * FROM device_feeder LIMIT ? OFFSET ?";
        return jdbcTemplate.query(sql, new Object[]{limit, offset}, new BeanPropertyRowMapper<>(DeviceFeeder.class));
    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList 线路列表
     * @return 去重后的线路列表
     */
    private List<NeedFeederVo> distinctByPsrIdVo(List<NeedFeederVo> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        return feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList 线路列表
     * @return 去重后的线路列表
     */
    private List<DeviceFeeder> distinctByPsrId(List<DeviceFeeder> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        return feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
    }

    /**
     * 点找线，根据一个点去查寻最近的线路的最近杆塔
     *
     * @param doubles          目标点坐标
     * @param num              返回数量
     * @param deviceFeederList 线路列表
     * @return 最近线路信息列表
     * @throws IOException IO异常
     */
    @Override
    public List<NeedFeederVo> pointBy(double[] doubles, Integer num, List<DeviceFeeder> deviceFeederList) throws IOException {

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }

        //先查询所有线上所有设备集合（目前只查询了物理杆塔和间隔）
        //查询运行杆塔
        List<DeviceCoords> deviceCoords = new ArrayList<>();
        List<DeviceCoords> wlgtDeviceCoords = processDeviceFeeders(deviceFeederList);
        deviceCoords.addAll(wlgtDeviceCoords);
        //查询所有间隔开关站内
        List<StationKg> stationKgs = kgMapper.selectFeederKg(deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
        //站内的开关要符合特殊类型站房才可以使用
        for (StationKg stationKg : stationKgs) {
            if (DeviceConstants.CONTACT_STATION_TYPES.contains(stationKg.getPsrType())) {
                deviceCoords.add(new DeviceCoords(stationKg.getPsrId(), stationKg.getPsrType(), stationKg.getName(), null, stationKg.getLongitude() + "," + stationKg.getLatitude(), stationKg.getFeeder()));
            }
        }

        // 直接去重获取线路信息
        List<String> feedIdList = deviceCoords.stream().map(DeviceCoords::getFeederId) // 提取feederId
                .filter(Objects::nonNull) // 过滤null值（可选）
                .distinct() // 去重
                .collect(Collectors.toList());
        List<DeviceFeeder> deviceFeeders = feederDeviceMapper.selectBatchIds(feedIdList);
        // 构建psrId到name的映射（键：psrId，值：name）
        Map<String, String> psrIdToNameMap = new HashMap<>();
        for (DeviceFeeder feeder : deviceFeeders) {
            psrIdToNameMap.put(feeder.getPsrId(), feeder.getName());
        }
        // 遍历设备坐标列表，匹配feederId与psrId，设置feederName
        for (DeviceCoords coords : deviceCoords) {
            String feederId = coords.getFeederId();
            // 从映射表中查找对应的name（若不存在，返回null或默认值）
            String feederName = psrIdToNameMap.get(feederId);
            coords.setFeederName(feederName); // 设置feederName
        }
        //查询最近的线路
        List<DeviceCoords> nearFeederdeviceCoordsList = findNearestPoints(deviceCoords, doubles, num);
        //查询最近的线上所有设备集合
        Map<String, List<DeviceCoords>> map = findNearestFeederAllDevice(nearFeederdeviceCoordsList, deviceCoords);

        List<NeedFeederVo> result = new ArrayList<>();

        for (DeviceCoords coords : nearFeederdeviceCoordsList) {
            NeedFeederVo feeder = new NeedFeederVo();
            feeder.setPsrId(coords.getFeederId());
            feeder.setFeedName(coords.getFeederName());
            String[] parts = coords.getCoords().split(",");
            feeder.setRecentlyPoint(new double[]{Double.parseDouble(parts[0].trim()),  // 经度
                    Double.parseDouble(parts[1].trim())   // 纬度
            });
            feeder.setDeviceId(coords.getId());
            feeder.setDeviceType(coords.getType());
            feeder.setDeviceName(coords.getName());
            feeder.setDeviceCoordsList(map.get(coords.getFeederId()));
            result.add(feeder);
        }
        return result;
    }

    /**
     * 批量处理设备馈线数据，关联运行杆塔和物理杆塔信息
     *
     * @param deviceFeederList 待处理的设备馈线列表
     * @return 关联后的设备坐标信息列表
     */
    @Override
    public List<DeviceCoords> processDeviceFeeders(List<DeviceFeeder> deviceFeederList) {
        // 存储最终生成的设备坐标信息
        List<DeviceCoords> deviceCoords = new ArrayList<>();

        // 快速映射表：通过astId查找对应的运行杆塔对象
        // 用于后续快速获取物理杆塔对应的运行杆塔名称和馈线ID
        Map<String, DeviceRunTower> astIdToRunTowerMap = new HashMap<>();

        // 1. 批量收集所有馈线ID，准备一次性查询所有相关运行杆塔
        List<String> allPsrIds = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());

        // 2. 批量查询所有运行杆塔数据（减少数据库交互次数）
        List<DeviceRunTower> allRunTowers = feederDeviceMapper.selectRunTowers(allPsrIds);

        // 3. 构建astId到运行杆塔对象的映射关系
        // 目的：后续通过物理杆塔的astId快速找到对应的运行杆塔信息
        for (DeviceRunTower tower : allRunTowers) {
            astIdToRunTowerMap.put(tower.getAstId(), tower);
        }

        // 4. 收集所有运行杆塔关联的物理杆塔ID
        List<String> allAstIds = allRunTowers.stream().map(DeviceRunTower::getAstId).collect(Collectors.toList());

        // 5. 批量查询所有物理杆塔数据
        List<DeviceWlgt> allWlgts = feederDeviceMapper.selectwlgtList(allAstIds);

        // 6. 组装最终结果：将物理杆塔与运行杆塔信息关联
        for (DeviceWlgt wlgt : allWlgts) {
            // 通过astId查找对应的运行杆塔对象
            DeviceRunTower runTower = astIdToRunTowerMap.get(wlgt.getAstId());

            // 防御性检查：确保运行杆塔存在（理论上应该存在，因为数据是关联查询的）
            if (runTower != null) {
                // 注意：名称字段从运行杆塔对象获取（物理杆塔自身没有名称字段）
                deviceCoords.add(new DeviceCoords(wlgt.getAstId(),              // 设备ID
                        WLGT,                         // 设备类型常量
                        runTower.getName(),     // 设备名称（来自运行杆塔）
                        null,     // 距离
                        wlgt.getGeoPositon(),         // 地理位置坐标
                        runTower.getFeeder()           // 所属馈线ID
                ));
            }
        }

        return deviceCoords;
    }

    /**
     * 根据线路ID查询附近变电站信息
     *
     * @param psrId 线路ID
     * @return 附近变电站信息列表
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearbySubstations(String psrId) {
        try {
            log.info("查询附近变电站信息，psrId: {}", psrId);
            // 查询所有变电站
            List<DeviceSubstation> deviceSubstations = deviceSubstation2Mapper.selectList();
            // 查询变电站的剩余间隔
            List<NearbySubstationInfoVo> nearbySubstations = new ArrayList<>();
            for (DeviceSubstation deviceSubstation : deviceSubstations) {
                NearbySubstationInfoVo substationInfoVo = new NearbySubstationInfoVo();
                BeanUtils.copyProperties(deviceSubstation, substationInfoVo);
                List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(deviceSubstation.getEmsId());
                long remainingBayCount = topoTraceReals.stream().filter(t -> t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留")).count();
                //  查询剩余间隔
                substationInfoVo.setRemainingBayCount((int) remainingBayCount);
                nearbySubstations.add(substationInfoVo);
            }

            return nearbySubstations;
        } catch (Exception e) {
            log.error("查询附近变电站信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询附近变电站信息失败", e);
        }
    }


    /**
     * 获取馈线所属的母线ID
     *
     * @param feederId 馈线ID
     * @return 母线ID，如果未找到返回null
     */
    private String getFeederBusbarId(String feederId) {
        try {
            BusbarSwitchVo busbarSwitchVo = bayQueryService.queryLineTopology(feederId);
            if (busbarSwitchVo != null) {
                return busbarSwitchVo.getBusbarId();
            }
            log.debug("未找到馈线{}的母线信息", feederId);
            return null;
        } catch (Exception e) {
            log.warn("查询馈线{}母线信息失败: {}", feederId, e.getMessage());
            return null;
        }
    }

    /**
     * 获取变电站的剩余间隔数量
     *
     * @param emsId 变电站EMS ID
     * @return 剩余间隔数量
     */
    private List<TopoTraceReal> getRemainingBayCount(String emsId) {
        try {
            if (StringUtils.isEmpty(emsId)) {
                return new ArrayList<>();
            }
            List<TopoTraceReal> topoTraceReals = bayQueryMapper.selectSwitchBySubstation(emsId);
            if (CollectionUtils.isEmpty(topoTraceReals)) {
                return new ArrayList<>();
            }
            return topoTraceReals;
        } catch (Exception e) {
            log.warn("查询变电站{}剩余间隔失败: {}", emsId, e.getMessage());
            return new ArrayList<>();
        }
    }


    /**
     * 检查变电站是否与馈线在同一母线下
     *
     * @param substation 变电站
     * @param feederBusbarId 馈线所属母线ID
     * @return 如果在同一母线下返回true
     */
    private boolean isSameBusbar(DeviceSubstation substation, String feederBusbarId) {
        try {
            if (StringUtils.isEmpty(feederBusbarId) || StringUtils.isEmpty(substation.getEmsId())) {
                return false;
            }
            // 查询变电站下的所有母线
            List<TopoTraceReal> substationBusbars = bayQueryMapper.selectBusbarsBySubstation(substation.getEmsId());
            if (CollectionUtils.isEmpty(substationBusbars)) {
                return false;
            }
            // 检查是否包含相同的母线ID
            return substationBusbars.stream().anyMatch(busbar -> feederBusbarId.equals(busbar.getBusbar()));
        } catch (Exception e) {
            log.warn("检查变电站{}与馈线母线关系失败: {}", substation.getName(), e.getMessage());
            return false;
        }
    }

    /**
     * 内部类：变电站与距离信息
     */
    @Getter
    private static class SubstationWithDistance {
        private final DeviceSubstation substation;
        private final double distance;
        private List<TopoTraceReal> topoTraceReals;

        public SubstationWithDistance(DeviceSubstation substation, double distance, List<TopoTraceReal> topoTraceReals) {
            this.substation = substation;
            this.distance = distance;
            this.topoTraceReals = topoTraceReals;
        }

    }


    /**
     * 根据馈线ID查询最近的变电站列表
     *
     * @param feederId 馈线ID
     * @param radius 查询半径（公里）
     * @param maxCount 返回的最大变电站数量  -1 返回所有，其他正数传多少最大返回多少
     * @parm isSwitch false：只返回备用的，true：返回变电站所有的开关
     * @return
     */
    @Override
    public List<NearbySubstationInfoVo> queryNearestSubstationsByFeeder(String feederId, Double radius, Integer maxCount, boolean isSwitch) {
        try {
            log.info("根据馈线ID查询最近的变电站列表，feederId: {}, radius: {}公里, maxCount: {}", feederId, radius, maxCount);
            // 获取馈线的所有线段
            LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
            DeviceFeeder feeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

            if (feeder == null || StringUtils.isEmpty(feeder.getGeoList())) {
                log.warn("未找到馈线{}的地理坐标", feederId);
                return new ArrayList<>();
            }

            // 解析馈线的所有线段坐标
            List<List<double[]>> feederLineSegments = CoordinateConverter.split(feeder.getGeoList());
            if (CollectionUtils.isEmpty(feederLineSegments)) {
                log.warn("馈线{}坐标解析失败", feederId);
                return new ArrayList<>();
            }
            // 获取馈线所属母线ID（用于过滤同一母线下的变电站）
            String feederBusbarId = getFeederBusbarId(feederId);

            // 查询所有变电站
            List<DeviceSubstation> allSubstations = deviceSubstation2Mapper.selectList();
            if (CollectionUtils.isEmpty(allSubstations)) {
                log.warn("未找到任何变电站数据");
                return new ArrayList<>();
            }

            // 收集所有有效变电站的坐标点
            List<double[]> substationPoints = new ArrayList<>();
            Map<String, DeviceSubstation> pointToSubstationMap = new HashMap<>();

            Map<String, List<TopoTraceReal>> remainingBayCountMap = new HashMap<>();

            for (DeviceSubstation substation : allSubstations) {
                try {
                    if (StringUtils.isEmpty(substation.getGeoPositon())) {
                        continue;
                    }
                    String[] coords = substation.getGeoPositon().split(",");
                    if (coords.length != 2) {
                        continue;
                    }
                    double substationLon = Double.parseDouble(coords[0].trim());
                    double substationLat = Double.parseDouble(coords[1].trim());
                    // 检查剩余间隔数量
                    List<TopoTraceReal> topoTraceReals = getRemainingBayCount(substation.getEmsId());
                    double[] point = new double[]{substationLon, substationLat};
                    if (isSwitch) {
                        substationPoints.add(point);
                        pointToSubstationMap.put(substationLon + "," + substationLat, substation);
                        remainingBayCountMap.put(substation.getEmsId(), topoTraceReals);
                        continue;
                    }
                    List<TopoTraceReal> traceRealsRemainingBay = topoTraceReals.stream().filter(t -> StringUtils.isNotBlank(t.getStartBreakName()) && (t.getStartBreakName().contains("备用") || t.getStartBreakName().contains("预留"))).collect(Collectors.toList());
                    long remainingBayCount = traceRealsRemainingBay.size();
                    if (remainingBayCount <= 0) {
                        continue;
                    }
                    // 检查是否与当前线路在同一母线下
                    if (isSameBusbar(substation, feederBusbarId)) {
                        continue;
                    }
                    substationPoints.add(point);
                    pointToSubstationMap.put(substationLon + "," + substationLat, substation);
                    remainingBayCountMap.put(substation.getEmsId(), traceRealsRemainingBay);
                } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                    log.warn("变电站{}坐标格式错误: {}", substation.getName(), substation.getGeoPositon());
                }
            }

            if (substationPoints.isEmpty()) {
                log.info("未找到符合条件的变电站");
                return new ArrayList<>();
            }

            List<SubstationWithDistance> candidateSubstations = new ArrayList<>();

            for (double[] substationPoint : substationPoints) {
                // 计算变电站到馈线所有线段的最小距离
                double minDistance = LineDistanceQuery.calculateMinDistanceToLine(substationPoint, feederLineSegments);
                // 检查是否在半径范围内
                if (radius != null && minDistance > radius) {
                    continue;
                }
                String key = substationPoint[0] + "," + substationPoint[1];
                DeviceSubstation substation = pointToSubstationMap.get(key);
                if (substation != null) {
                    List<TopoTraceReal> topoTraceReals = remainingBayCountMap.get(substation.getEmsId());
                    candidateSubstations.add(new SubstationWithDistance(substation, minDistance, topoTraceReals));
                }
            }
            // 按距离排序
            candidateSubstations.sort(Comparator.comparingDouble(SubstationWithDistance::getDistance));
            // 如果等于-1 不做限制
            if (maxCount != null && maxCount == -1) {
                maxCount = candidateSubstations.size();
            }
            // 限制返回数量
            if (maxCount != null && maxCount > 0 && maxCount < candidateSubstations.size()) {
                candidateSubstations = candidateSubstations.subList(0, maxCount);
            }
            // 结果对象
            List<NearbySubstationInfoVo> result = new ArrayList<>();

            for (SubstationWithDistance substationWithDistance : candidateSubstations) {
                DeviceSubstation substation = substationWithDistance.getSubstation();
                NearbySubstationInfoVo substationInfoVo = new NearbySubstationInfoVo();
                BeanUtils.copyProperties(substation, substationInfoVo);
                substationInfoVo.setDistance(substationWithDistance.getDistance());
                substationInfoVo.setRemainingBayCount(substationWithDistance.getTopoTraceReals().size());
                List<BusbarSwitchVo> busbarSwitchVoList = getBusbarSwitchVos(substationWithDistance, substation);
                substationInfoVo.setBusbarSwitchVoList(busbarSwitchVoList);
                result.add(substationInfoVo);
            }
            log.info("成功查询到{}个符合条件的变电站", result.size());
            return result;
        } catch (Exception e) {
            log.error("根据馈线ID查询最近变电站失败，feederId: {}, radius: {}, maxCount: {}", feederId, radius, maxCount, e);
            throw new RuntimeException("查询最近变电站失败", e);
        }
    }

    private static List<BusbarSwitchVo> getBusbarSwitchVos(SubstationWithDistance substationWithDistance, DeviceSubstation substation) {
        List<TopoTraceReal> topoTraceReals = substationWithDistance.getTopoTraceReals();
        List<BusbarSwitchVo> busbarSwitchVoList = new ArrayList<>();
        for (TopoTraceReal topoTraceReal : topoTraceReals) {
            BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
            busbarSwitchVo.setStationPsrId(substation.getEmsId());
            busbarSwitchVo.setStationPsrName(substation.getName());
            busbarSwitchVo.setBusbarId(topoTraceReal.getBusbar());
            busbarSwitchVo.setBusbarName(topoTraceReal.getBusbarName());
            busbarSwitchVo.setSwitchId(topoTraceReal.getStartBreak());
            busbarSwitchVo.setSwitchName(topoTraceReal.getStartBreakName());
            busbarSwitchVo.setIsSpare(true);
            busbarSwitchVoList.add(busbarSwitchVo);
        }
        return busbarSwitchVoList;
    }


}
