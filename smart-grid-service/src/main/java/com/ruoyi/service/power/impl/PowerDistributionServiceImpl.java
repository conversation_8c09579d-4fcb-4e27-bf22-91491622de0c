package com.ruoyi.service.power.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.power.Load;
import com.ruoyi.entity.power.enuw.FeederMaxLoadStatus;
import com.ruoyi.entity.power.vo.CapacityAnalysisVo;
import com.ruoyi.entity.power.vo.FeederCapacityVo;
import com.ruoyi.entity.power.vo.FeederHorizontalAnalysisVo;
import com.ruoyi.entity.power.vo.TransformerStateVo;
import com.ruoyi.entity.problem.Statistics;
import com.ruoyi.mapper.device.DeviceFeederCableMapper;
import com.ruoyi.mapper.device.DeviceFeederJkMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.power.IPowerDistributionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PowerDistributionServiceImpl implements IPowerDistributionService {
    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;
    @Autowired
    FeederDeviceMapper feederDeviceMapper;
    @Autowired
    PowerServiceImpl powerService;

    /**
     *配电网——配电网设备水平分析
     */
    @Override
    public FeederHorizontalAnalysisVo feederHorizontalAnalysis(String psrId) {
        List<DeviceFeeder> deviceFeederList = deviceFeederJkMapper.selectDeviceFeeder(psrId);
        List<String> idList = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(idList)){
            return null;
        }
        //架空线
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectJKList(idList);


        //电缆
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectCableList(idList);

        //大馈线
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceFeeder::getPsrId,idList);
        List<DeviceFeeder> deviceFeeder = feederDeviceMapper.selectList(lambdaQueryWrapper);
        Double dkxLength = deviceFeeder.stream().mapToDouble(DeviceFeeder::getLength).sum();


        FeederHorizontalAnalysisVo feederHorizontalAnalysisVo = new FeederHorizontalAnalysisVo();
        //电缆饼图
        List<Statistics> cableStatisticsList =new ArrayList<>();

        if(CollectionUtils.isNotEmpty(deviceFeederCableList)){
            //电缆长度
            feederHorizontalAnalysisVo.setCableLength(deviceFeederCableList.stream().mapToDouble(DeviceFeederCable::getLength).sum()/1000);
            //电缆化率
            feederHorizontalAnalysisVo.setCableConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getCableLength()/dkxLength));

            Statistics cableStatistics = new Statistics();
            cableStatistics.setTypeName("电缆线路");
            cableStatistics.setTypeNum(feederHorizontalAnalysisVo.getCableConversionRate().toString());
            cableStatisticsList.add(cableStatistics);

        }

        //架空线长度
        if(CollectionUtils.isNotEmpty(deviceFeederJkList)){
            feederHorizontalAnalysisVo.setJkLength(Double.valueOf((deviceFeederJkList.stream().mapToInt(DeviceFeederJk::getLength).sum()/1000)));

            Statistics jkStatistics = new Statistics();
            jkStatistics.setTypeName("架空线路");
            BigDecimal b = BigDecimal.valueOf(feederHorizontalAnalysisVo.getCableConversionRate());
            BigDecimal a = new BigDecimal("1");
            BigDecimal result = a.subtract(b);
            jkStatistics.setTypeNum(String.valueOf(result));
            cableStatisticsList.add(jkStatistics);

            //绝缘线路长度
            feederHorizontalAnalysisVo.setIsolationLineLength(  deviceFeederJkList.stream()
                    .filter(device ->StringUtils.isNotBlank(device.getWireType())&& device.getWireType().equals("01"))
                    .mapToDouble(DeviceFeederJk::getLength)
                    .sum()/1000);

            //绝缘化率
            feederHorizontalAnalysisVo.setIsolationConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getIsolationLineLength()/feederHorizontalAnalysisVo.getJkLength()));

            BigDecimal length= BigDecimal.valueOf(feederHorizontalAnalysisVo.getJkLength());
            BigDecimal isLength= BigDecimal.valueOf(feederHorizontalAnalysisVo.getIsolationLineLength());
            //非绝缘长度
            feederHorizontalAnalysisVo.setNoIsolationLineLength(length.subtract(isLength).doubleValue());

            //电缆饼图
            List<Statistics> jkStatisticsList =new ArrayList<>();
            Statistics  isolationLineStatistics = new Statistics();
            isolationLineStatistics.setTypeName("绝缘线路");
            BigDecimal is= isLength.divide(length,4, RoundingMode.HALF_UP);
            isolationLineStatistics.setTypeNum(String.valueOf(is));
            jkStatisticsList.add(isolationLineStatistics);
            Statistics noIsolationLineStatistics = new Statistics();
            noIsolationLineStatistics.setTypeName("非绝缘线路");
            noIsolationLineStatistics.setTypeNum(String.valueOf(a.subtract(is)));
            jkStatisticsList.add(noIsolationLineStatistics);
            feederHorizontalAnalysisVo.setJkStatisticsList(jkStatisticsList);
        }
        feederHorizontalAnalysisVo.setCableStatisticsList(cableStatisticsList);
        return feederHorizontalAnalysisVo;
    }

    /**
     *配电网——配电网容量分析
     */
    @Override
    public CapacityAnalysisVo capacityAnalysis(String code,Integer pageNum,Integer pageSize) {

        //查询网格下的线路
       List<DeviceFeeder> deviceFeederList =  feederDeviceMapper.selectFeederByCode(code);

       //查询线路关联的柱上设备
        List<DevicePoleTransformer> devicePoleTransformerList =  feederDeviceMapper.selectPoleTransformerByFeederIds(deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));

        //查询线路关联的站内变压器
        List<DeviceStationTransformer> deviceStationTransformerList =  feederDeviceMapper.selectStationTransformerByFeederIds(deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));

        //配变容量详情
        List<TransformerStateVo> capacityList = new ArrayList<>();


        int fiveHundred = 0;
        int fiveHundredAndOneThousand = 0;
        int oneThousandAndTwoThousand = 0;
        int fiveThousand = 0;

        //柱上设备统计
        for (DevicePoleTransformer devicePoleTransformer : devicePoleTransformerList) {
            if(devicePoleTransformer.getRatedCapacity()==null){
                continue;
            }
            if(Double.parseDouble(devicePoleTransformer.getRatedCapacity())<=500){
                fiveHundred++;
            }
            if((Double.parseDouble(devicePoleTransformer.getRatedCapacity())>500&&(Double.parseDouble(devicePoleTransformer.getRatedCapacity())<=1000))){
                fiveHundredAndOneThousand++;
            }
            if((Double.parseDouble(devicePoleTransformer.getRatedCapacity())>1000&&(Double.parseDouble(devicePoleTransformer.getRatedCapacity())<=2000))){
                oneThousandAndTwoThousand++;
            }
            if((Double.parseDouble(devicePoleTransformer.getRatedCapacity())>2000)){
                fiveThousand++;
            }
            TransformerStateVo transformerStateVo = new TransformerStateVo();
            //假数据
            transformerStateVo.setTransformerQuantityName(devicePoleTransformer.getName());
            transformerStateVo.setCapacity(Double.parseDouble(devicePoleTransformer.getRatedCapacity()));
            transformerStateVo.setPsrId(devicePoleTransformer.getPsrId());
            transformerStateVo.setType("柱上变压器");
            capacityList.add(transformerStateVo);
        }
        //站内变压器统计
        for (DeviceStationTransformer devicePoleTransformer : deviceStationTransformerList) {
            if(devicePoleTransformer.getInstalledCapacity()==null){
                continue;
            }
            if(Double.parseDouble(devicePoleTransformer.getInstalledCapacity())<=500){
                fiveHundred++;
            }
            if((Double.parseDouble(devicePoleTransformer.getInstalledCapacity())>500&&(Double.parseDouble(devicePoleTransformer.getInstalledCapacity())<=1000))){
                fiveHundredAndOneThousand++;
            }
            if((Double.parseDouble(devicePoleTransformer.getInstalledCapacity())>1000&&(Double.parseDouble(devicePoleTransformer.getInstalledCapacity())<=2000))){
                oneThousandAndTwoThousand++;
            }
            if((Double.parseDouble(devicePoleTransformer.getInstalledCapacity())>2000)){
                fiveThousand++;
            }
            TransformerStateVo transformerStateVo = new TransformerStateVo();

            //假数据
            transformerStateVo.setTransformerQuantityName(devicePoleTransformer.getName());
            transformerStateVo.setCapacity(Double.parseDouble(devicePoleTransformer.getInstalledCapacity()));
            transformerStateVo.setPsrId(devicePoleTransformer.getPsrId());
            transformerStateVo.setType("配电站内变压器");
            capacityList.add(transformerStateVo);
        }

        int total = fiveHundred + fiveHundredAndOneThousand + oneThousandAndTwoThousand + fiveThousand;

        CapacityAnalysisVo capacityAnalysisVo =new CapacityAnalysisVo();

        //配变容量分布柱状图
        if(total>0){
            List<Statistics> capacityColumnarList =new ArrayList<>();
            capacityColumnarList.add(new Statistics("<500KVA",String.valueOf(fiveHundred)));
            capacityColumnarList.add(new Statistics("500KVA——1000KVA",String.valueOf(fiveHundredAndOneThousand)));
            capacityColumnarList.add(new Statistics("1000KVA——2000KVA",String.valueOf(oneThousandAndTwoThousand)));
            capacityColumnarList.add(new Statistics(">2000KVA",String.valueOf(fiveThousand)));
            capacityAnalysisVo.setCapacityColumnarList(capacityColumnarList);
        }
        //饼状图，假的
//        List<Statistics> capacityPieList =new ArrayList<>();
//        capacityPieList.add(new Statistics("正常负载","50"));
//        capacityPieList.add(new Statistics("中等负载","29"));
//        capacityPieList.add(new Statistics("高负载","21"));
//        capacityAnalysisVo.setCapacityPieList(capacityPieList);

        Page<TransformerStateVo> page = new Page<>(pageNum,pageSize, capacityList.size());
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, capacityList.size());
        page.setRecords(capacityList.subList(fromIndex, toIndex));
        capacityAnalysisVo.setCapacityList(page);

        //线路容量分布柱状图
        List<Statistics> totalFeederCapacityList =new ArrayList<>();

        List<FeederCapacityVo> feederCapacityVoList = new ArrayList<>();

        for (DeviceFeeder deviceFeeder : deviceFeederList) {

            FeederCapacityVo feederCapacityVo = new FeederCapacityVo();
            feederCapacityVo.setFeederName(deviceFeeder.getName());
            feederCapacityVo.setCapacity(deviceFeeder.getFeederRateCapacity());
            feederCapacityVo.setAllowAllCapacity(2000.0);
            feederCapacityVoList.add(feederCapacityVo);

            totalFeederCapacityList.add(new Statistics(deviceFeeder.getName(),String.valueOf(deviceFeeder.getFeederRateCapacity())));
        }

        Page<FeederCapacityVo> feederPage = new Page<>(pageNum,pageSize, feederCapacityVoList.size());
        int feederFromIndex = (pageNum - 1) * pageSize;
        int  feederToIndex = Math.min(fromIndex + pageSize, feederCapacityVoList.size());
        feederPage.setRecords(feederCapacityVoList.subList(feederFromIndex, feederToIndex));
        capacityAnalysisVo.setFeederCapacityVoList(feederPage);

        Page<Statistics> feederCapacityPage = new Page<>(pageNum,pageSize, totalFeederCapacityList.size());
        int feederCapacityFromIndex = (pageNum - 1) * pageSize;
        int  feederCapacityToIndex = Math.min(feederCapacityFromIndex + pageSize, totalFeederCapacityList.size());
        feederCapacityPage.setRecords(totalFeederCapacityList.subList(feederCapacityFromIndex, feederCapacityToIndex));
        capacityAnalysisVo.setTotalFeederCapacityList(feederCapacityPage);



        return capacityAnalysisVo;
    }

    /**
     *配电网——供电能力分析（负载分析）
     */
    @Override
    public CapacityAnalysisVo loadAnalysis(String code,String time,Integer pageNum,Integer pageSize) throws ParseException, JsonProcessingException {

        //查询网格下的线路
        List<DeviceFeeder> deviceFeederList =  feederDeviceMapper.selectFeederByCode(code);
        int zeroAndTwo = 0;
        int twoAndFour = 0;
        int fourAndSix = 0;
        int sixAndEight = 0;
        int eightAndTen = 0;
        int ten = 0;
        int total = 0;
        List<TransformerStateVo> capacityList = new ArrayList<>();
        if(CollectionUtils.isEmpty(deviceFeederList)){
            return null;
        }
        for (DeviceFeeder deviceFeeder : deviceFeederList) {
            Load load = powerService.selectFeeDerLoad(time,time, Collections.singletonList(deviceFeeder.getPsrId()));
            if(load==null){
                continue;
            }
            if(load.getMaxDataList().get(0)>=0&&load.getMaxDataList().get(0)<20){
                zeroAndTwo = zeroAndTwo+1;
                total++;
            }
            if(load.getMaxDataList().get(0)>=20&&load.getMaxDataList().get(0)<40){
                twoAndFour++;
                total++;
            }
            if(load.getMaxDataList().get(0)>=40&&load.getMaxDataList().get(0)<60){
                fourAndSix++;
                total++;
            }
            if(load.getMaxDataList().get(0)>=60&&load.getMaxDataList().get(0)<80){
                sixAndEight++;
                total++;
            }
            if(load.getMaxDataList().get(0)>=80&&load.getMaxDataList().get(0)<=100){
                eightAndTen++;
                total++;
            }
            if(load.getMaxDataList().get(0)>100){
                ten++;
                total++;
            }

            TransformerStateVo transformerStateVo = new TransformerStateVo();
            FeederMaxLoadStatus status = FeederMaxLoadStatus.fromLoadRate(load.getMaxDataList().get(0));
            transformerStateVo.setLoadState(status.getName());
            transformerStateVo.setLoadStateInterval(status.action);


            transformerStateVo.setPsrId(deviceFeeder.getPsrId());
            transformerStateVo.setTransformerQuantityName(deviceFeeder.getName());
            transformerStateVo.setLoad(String.valueOf(DoubleFormatter.formatToThreeDecimals2(load.getMaxDataList().get(0))));
            capacityList.add(transformerStateVo);
        }

        CapacityAnalysisVo capacityAnalysisVo = new CapacityAnalysisVo();

        Page<TransformerStateVo> page = new Page<>(pageNum,pageSize, capacityList.size());
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, capacityList.size());
        page.setRecords(capacityList.subList(fromIndex, toIndex));
        capacityAnalysisVo.setCapacityList(page);

        List<Statistics> statisticsList =new ArrayList<>();
        statisticsList.add(new Statistics("0-20%",String.valueOf(zeroAndTwo)));
        statisticsList.add(new Statistics("20-40%",String.valueOf(twoAndFour)));
        statisticsList.add(new Statistics("40-60%",String.valueOf(fourAndSix)));
        statisticsList.add(new Statistics("60-80%",String.valueOf(sixAndEight)));
        statisticsList.add(new Statistics("80-100%",String.valueOf(eightAndTen)));
        statisticsList.add(new Statistics(">100%",String.valueOf(ten)));
        capacityAnalysisVo.setCapacityColumnarList(statisticsList);

        return capacityAnalysisVo;
    }



}
