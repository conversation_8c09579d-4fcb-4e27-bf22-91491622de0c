package com.ruoyi.service.power;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceFeederCable;
import com.ruoyi.entity.device.DeviceFeederJk;
import com.ruoyi.entity.device.PBEntity;
import com.ruoyi.entity.device.bo.DeviceFeederTransformerVolBo;
import com.ruoyi.entity.power.bo.LoadBo;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.PullDownMenuStringSon;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

public interface IPowerService {
    /**
     * 网格资源——查询电力线路资源信息，
     * @param
     * @return
     */
    PowerGridVo selectPower(String code);

    /**
     * 网格资源——查询变电站的统计信息，
     */
    SubstationVo selectSubstation(String code,String strTime,String endTime) throws IOException, ParseException;

    /**
     * 典型日负荷数据展示
     * @param bo
     * @return
     * @throws IOException
     */
    LoadVo selectLoad(LoadBo bo) throws IOException, ParseException;

    List<PullDownMenuStringSon> pullDownMenuSubstation(String code);

    List<PullDownMenuStringSon> pullDownMenuTransformer(String code);


    TransformerVo transformerState(String code,String strTime,String endTime) throws IOException, ParseException;

    /**
     * 查询网格下的所有变电站已经主变
     */

    Page<SubAndTransformerVo> subAndTransformer(String code, Integer pageNum, Integer pageSize);

    R<Void> saveOrderFromJsonString(String json) throws IOException;

    /**
     * 网格资源——查询网格下的所有变电站坐标
     */
    List<PullDownMenuStringSon> substationGeoPositon(String code);
    /**
     * 网格资源——查询网格下大馈线详情
     */
    TableDataInfo<DeviceFeeder> selectDKX(String code, Integer pageNum, Integer pageSize);
    /**
     * 变电站——出线间隔分析
     */
    IntervalAnalysisVo intervalAnalysis(String code);

    /**
     * 网格资源——查询网格下的配变专变信息
     */
    TableDataInfo<PBEntity> selectDeviceFeederTransformerVol(DeviceFeederTransformerVolBo bo);
    /**
     * 网格资源——查询网格下架空线详情
     */
    TableDataInfo<DeviceFeederJk> selectJKX(String code, Integer pageNum, Integer pageSize);
    /**
     * 网格资源——查询网格下电缆线详情
     */
    TableDataInfo<DeviceFeederCable> selectDLX(String code, Integer pageNum, Integer pageSize);
}
