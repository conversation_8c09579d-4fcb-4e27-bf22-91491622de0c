package com.ruoyi.service.power.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.constant.ResourceStatisticsDataType;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.DeviceApiCollector;
import com.ruoyi.entity.power.Load;
import com.ruoyi.entity.power.MaxAndIndex;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.load_forecast.mapper.DeviceSubstationMapper;
import com.ruoyi.mapper.device.*;
import com.ruoyi.mapper.power.NtHighTransformerNewMapper;
import com.ruoyi.mapper.power.PowerMapper;
import com.ruoyi.service.power.IPowerHighPressureService;
import com.ruoyi.service.power.IPowerService;
import com.ruoyi.service.power.IPowerTransformerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.service.power.impl.PowerServiceImpl.maxAndIndex;

@Service
public class PowerHighPressureServiceImpl implements IPowerHighPressureService {

    @Autowired
    IPowerTransformerService iPowerTransformerService;

    @Autowired
    PowerServiceImpl powerServiceImpl;

    @Autowired
    IPowerService iPowerService;

    @Autowired
    NtHighTransformerNewMapper ntHighTransformerNewMapper;

    @Autowired
    PowerMapper powerMapper;

    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    NtFeederMapper ntFeederMapper;
    @Autowired
    DeviceApiJsonMapper deviceApiJsonMapper;

    @Autowired
    DeviceSubstation2Mapper deviceSubstationMapper;

    @Value("${smartgrid.threshold}")
    private int defaultThreshold;
    @Value("${smartgrid.day}")
    private int day;
    private final ObjectMapper objectMapper;

    public PowerHighPressureServiceImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 查询网格统计高压评估主变基础模块
     *
     * @param code
     * @return
     */
    @Override
//    public TransformerFoundationVo transformerFoundation(String code) throws Exception {
//        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
//        try {
//            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
//        } catch (Exception e){
//            return null;
//        }
//        //查主变
////        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(code);
////        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
////        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
////        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
//        List<DeviceZB> zbs = deviceApiCollector.getZbList();
//
//        if (CollectionUtils.isEmpty(zbs)) {
//            return null;
//        }
//        //总容量
//        Double capacity = zbs.stream().mapToDouble(DeviceZB::getRatedCapacity).sum();
//
////        LocalDate today = powerMapper.selectTime().toInstant()
////                .atZone(ZoneId.systemDefault())
////                .toLocalDate();
////        // 获取当前日期（不包含当天，所以从昨天开始计算）
////
////        LocalDate yesterday = today.minusDays(1);
////
////        // 计算7天前的第一天和最后一天（不包含今天）
////        LocalDate firstDay = yesterday.minusDays(day - 1); // 7天前的第一天
////        LocalDate lastDay = yesterday;               // 7天前的最后一天（昨天）
////
////        // 定义日期格式
////        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
////
////        // 转换为字符串
////        String firstDayStr = firstDay.format(formatter);
////        String lastDayStr = lastDay.format(formatter);
////
////
////        Load loadMW = powerServiceImpl.selectTransformerLoadMW(firstDayStr, lastDayStr, ntHighTransformerList.stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));
//
//        TransformerFoundationVo transformerFoundationVo = new TransformerFoundationVo();
//        transformerFoundationVo.setNormalNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 3).count());
//        transformerFoundationVo.setHeavyLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 2).count());
//        transformerFoundationVo.setOverLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 1).count());
//        transformerFoundationVo.setTotalCapacity(capacity);
//        transformerFoundationVo.setTotalNum(zbs.size());
//        transformerFoundationVo.setZbList(zbs);
//        return transformerFoundationVo;
//    }

    public TransformerFoundationVo transformerFoundation(String code) {
        TransformerFoundationVo transformerFoundationVo;
        try {
            transformerFoundationVo = objectMapper.readValue(deviceApiJsonMapper.selectPowerGridVo(code, ResourceStatisticsDataType.HIGHVOLTAGE), TransformerFoundationVo.class);
        } catch (Exception e) {
            return null;
        }
        return transformerFoundationVo;
    }

    /**
     * 查询网格统计高压评估主变基础模块（不需要）
     */
    @Override
    public TransformerQualifyVo transformerQualify(String code) {

        //网格下的变电站
        List<DeviceSubstation> inSubstationList = powerServiceImpl.getMiddleSubstations(code);

        //变电站下的出线间隔
        int totalBayNum = 0;
        //剩余出线间隔
        int remainingBayNum = 0;


        for (DeviceSubstation deviceSubstation : inSubstationList) {
            Integer bayNum = powerMapper.selectBayNum(deviceSubstation.getPsrId());

            Integer remainingNum = powerMapper.selectremainingNum(deviceSubstation.getPsrId());

            totalBayNum = totalBayNum + bayNum;
            remainingBayNum = remainingBayNum + remainingNum;
        }

        TransformerQualifyVo transformerQualifyVo = new TransformerQualifyVo();
        // TODO java.lang.ArithmeticException: / by zero
        if (totalBayNum == 0) {
            totalBayNum = 1;
        }
        transformerQualifyVo.setQualifyUtilizationRate(Double.parseDouble(BigDecimal.valueOf(totalBayNum).subtract(BigDecimal.valueOf(remainingBayNum)).divide(BigDecimal.valueOf(totalBayNum), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString()));
        transformerQualifyVo.setRemainingQualifyNum(remainingBayNum);
        return transformerQualifyVo;
    }

    /**
     * 查询网格统计高压评估主变最大值最小值模块(不需要)
     */
    @Override
    public TransformerPeakVo transformerMaxAndMin(String code) throws ParseException, JsonProcessingException {


        List<DeviceNtHighTransformer> ntHighTransformerList = iPowerTransformerService.getNtHighTransformerList(code);

        LocalDate localDate = powerMapper.selectTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        String time = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        Load loadMW = powerServiceImpl.selectTransformerLoadMW(time, time, ntHighTransformerList.stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));
        Load load = powerServiceImpl.selectTransformerLoad(time, time, ntHighTransformerList.stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));

        TransformerPeakVo transformerPeakVo = new TransformerPeakVo();
        transformerPeakVo.setMaxLoadMW(DoubleFormatter.formatToThreeDecimals2(loadMW.getMaxDataList().stream().mapToDouble(Double::doubleValue).max().getAsDouble()));
        transformerPeakVo.setMinLoadMW(DoubleFormatter.formatToThreeDecimals2(loadMW.getMinDataList().stream().mapToDouble(Double::doubleValue).min().getAsDouble()));
        transformerPeakVo.setMaxLoad(DoubleFormatter.formatToThreeDecimals2(load.getMaxDataList().stream().mapToDouble(Double::doubleValue).max().getAsDouble()));
        transformerPeakVo.setMinLoad(DoubleFormatter.formatToThreeDecimals2(load.getMinDataList().stream().mapToDouble(Double::doubleValue).min().getAsDouble()));

        return transformerPeakVo;
    }

    /**
     * 查询网格统计高压评估线路基础模块(数据库)
     *
     * @param code
     * @return
     */
//    @Override
//    public FeederFoundationVo feederFoundation(String code) throws Exception {
//        List<DeviceFeeder> deviceFeederList = deviceFeederJkMapper.selectDeviceFeeder(code);
//        List<String> idList = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(idList)) {
//            return null;
//        }
//        //架空线
//        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectJKList(idList);
//
//
//        //电缆
//        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectCableList(idList);
//
//        //大馈线
//        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
//        List<DeviceFeeder> deviceFeeder = feederDeviceMapper.selectList(lambdaQueryWrapper);
//        Double dkxLength = deviceFeeder.stream().mapToDouble(DeviceFeeder::getLength).sum();
//
//
//        FeederFoundationVo feederHorizontalAnalysisVo = new FeederFoundationVo();
//
//
//        if (CollectionUtils.isNotEmpty(deviceFeederCableList)) {
//            //电缆长度
//            feederHorizontalAnalysisVo.setCableLength(deviceFeederCableList.stream().mapToDouble(DeviceFeederCable::getLength).sum() / 1000);
//            //电缆化率
//            feederHorizontalAnalysisVo.setCableConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getCableLength() / dkxLength));
//        }
//
//        //架空线长度
//        if (CollectionUtils.isNotEmpty(deviceFeederJkList)) {
//            feederHorizontalAnalysisVo.setJkLength(Double.valueOf((deviceFeederJkList.stream().mapToInt(DeviceFeederJk::getLength).sum() / 1000)));
//
//            //绝缘线路长度
//            feederHorizontalAnalysisVo.setIsolationLineLength(deviceFeederJkList.stream()
//                    .filter(device -> device.getWireType() != null && device.getWireType().equals("01"))
//                    .mapToDouble(DeviceFeederJk::getLength)
//                    .sum() / 1000);
//
//            //绝缘化率
//            feederHorizontalAnalysisVo.setIsolationConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getIsolationLineLength() / feederHorizontalAnalysisVo.getJkLength()));
//
//            BigDecimal length = BigDecimal.valueOf(feederHorizontalAnalysisVo.getJkLength());
//            BigDecimal isLength = BigDecimal.valueOf(feederHorizontalAnalysisVo.getIsolationLineLength());
//            //非绝缘长度
//            feederHorizontalAnalysisVo.setNoIsolationLineLength(length.subtract(isLength).doubleValue());
//        }
//        feederHorizontalAnalysisVo.setLineNum(deviceFeeder.size());
//        feederHorizontalAnalysisVo.setLineLength(dkxLength);
//
//
//        return feederHorizontalAnalysisVo;
//    }

    /**
     * 查询网格统计高压、中压评估线路基础模块(api)
     *
     * @param code
     * @return
     */
//    public FeederFoundationVo feederFoundation(String code) throws Exception {
//        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
//        try {
//            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
//        } catch (Exception e){
//            return null;
//        }
//        List<DeviceFeeder> deviceFeederList = deviceApiCollector.getDeviceFeeders();
//        FeederFoundationVo feederHorizontalAnalysisVo = new FeederFoundationVo();
//        //大馈线总长度
//        Double dkxLength = DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLength).sum());
//        feederHorizontalAnalysisVo.setLineLength(dkxLength);
//        //电缆长度
//        feederHorizontalAnalysisVo.setCableLength(DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getCableLength).sum()));
//        //电缆化率
//        feederHorizontalAnalysisVo.setCableConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getCableLength() / dkxLength));
//        //架空长度
//        feederHorizontalAnalysisVo.setJkLength(DoubleFormatter.formatToThreeDecimals4((deviceFeederList.stream().mapToDouble(DeviceFeeder::getOverheadLength).sum())));
//        //线路供电区域
//        feederHorizontalAnalysisVo.setSupplyArea(deviceFeederList.stream()
//                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
//                .distinct()                     // 去重
//                .collect(Collectors.joining(", ")));
//        //线路条数
//        feederHorizontalAnalysisVo.setLineNum(deviceFeederList.size());
//        //可开放容量
//        feederHorizontalAnalysisVo.setOpenTotalCapacity(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLineCapFree).sum());
//        feederHorizontalAnalysisVo.setDeviceFeeders(deviceFeederList);
////        List<Double> load = deviceFeederList.stream()
////
////        //正常数量
////        feederHorizontalAnalysisVo.setNormalNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) <= 0.5).count());
////        //重载数量
////        feederHorizontalAnalysisVo.setHeavyLoadNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && 0.5 < Double.parseDouble(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) <= 0.8).count());
////        //超载数量
////        feederHorizontalAnalysisVo.setOverLoadNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) > 0.8).count());
//        return feederHorizontalAnalysisVo;
//    }
    public FeederFoundationVo feederFoundation(String code) {
        FeederFoundationVo feederFoundationVo;
        try {
            feederFoundationVo = objectMapper.readValue(deviceApiJsonMapper.selectPowerGridVo(code, ResourceStatisticsDataType.FEEDER), FeederFoundationVo.class);
        } catch (Exception e) {
            return null;
        }
        return feederFoundationVo;
    }

    /**
     * 查询网格统计高压评估线路供电区域（不用）
     *
     * @param code
     * @return
     */
    @Override
    public FeederSupplyAreaVo feederSupplyArea(String code) throws ParseException, JsonProcessingException {
        FeederSupplyAreaVo feederSupplyAreaVo = new FeederSupplyAreaVo();

        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(code);
        //线路供电区域
        feederSupplyAreaVo.setSupplyArea(dmsFeederDeviceList.stream()
                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
                .distinct()                     // 去重
                .collect(Collectors.joining(", ")));

        LocalDate yesterday = powerMapper.selectTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        String time = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 获取当前日期（不包含当天，所以从昨天开始计算）


        // 计算7天前的第一天和最后一天（不包含今天）
        LocalDate firstDay = yesterday.minusDays(day - 1); // 7天前的第一天
        LocalDate lastDay = yesterday;               // 7天前的最后一天（昨天）

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 转换为字符串
        String firstDayStr = firstDay.format(formatter);
        String lastDayStr = lastDay.format(formatter);
        int normalNum = 0;
        int heavyLoadNum = 0;
        int overLoadNum = 0;
        int seriousOverLoadNum = 0;
        int underLoadNum = 0;
        for (DeviceFeeder deviceFeeder : dmsFeederDeviceList) {
            Load load = powerServiceImpl.selectFeeDerLoad(firstDayStr, lastDayStr, Collections.singletonList(deviceFeeder.getPsrId()));
            if (load == null) {
                continue;
            }
            MaxAndIndex maxAndIndexLoad = maxAndIndex(load);
            if (maxAndIndexLoad.getMaxNum() > 100) {
                seriousOverLoadNum = seriousOverLoadNum + 1;
            } else if (maxAndIndexLoad.getMaxNum() >= 90) {
                overLoadNum = overLoadNum + 1;
            } else if (maxAndIndexLoad.getMaxNum() >= 60) {
                heavyLoadNum = heavyLoadNum + 1;
            } else if (maxAndIndexLoad.getMaxNum() >= 30) {
                normalNum = normalNum + 1;
            } else if (maxAndIndexLoad.getMaxNum() >= 0) {
                underLoadNum = underLoadNum + 1;
            }

        }
        feederSupplyAreaVo.setNormalNum(normalNum);
        feederSupplyAreaVo.setHeavyLoadNum(heavyLoadNum);
        feederSupplyAreaVo.setOverLoadNum(overLoadNum);
        feederSupplyAreaVo.setSeriousOverLoadNum(seriousOverLoadNum);
        feederSupplyAreaVo.setUnderLoadNum(underLoadNum);

        return feederSupplyAreaVo;
    }

    /**
     * 查询网格统计高压评估主变最大值最小值模块(不用)
     *
     * @param code
     * @return
     */
    @Override
    public TransformerPeakVo feederMaxAndMin(String code) throws ParseException, JsonProcessingException {

        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(code);

        LocalDate localDate = powerMapper.selectTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        String time = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        Load loadMW = powerServiceImpl.selectFeederLoadMW(time, time, dmsFeederDeviceList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
        Load load = powerServiceImpl.selectFeeDerLoad(time, time, dmsFeederDeviceList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));

        TransformerPeakVo transformerPeakVo = new TransformerPeakVo();
        transformerPeakVo.setMaxLoadMW(DoubleFormatter.formatToThreeDecimals2(loadMW.getMaxDataList().stream().mapToDouble(Double::doubleValue).max().getAsDouble()));
        transformerPeakVo.setMinLoadMW(DoubleFormatter.formatToThreeDecimals2(loadMW.getMinDataList().stream().mapToDouble(Double::doubleValue).min().getAsDouble()));
        transformerPeakVo.setMaxLoad(DoubleFormatter.formatToThreeDecimals2(load.getMaxDataList().stream().mapToDouble(Double::doubleValue).max().getAsDouble()));
        transformerPeakVo.setMinLoad(DoubleFormatter.formatToThreeDecimals2(load.getMinDataList().stream().mapToDouble(Double::doubleValue).min().getAsDouble()));

        return transformerPeakVo;

    }

    /**
     * 公变专变
     *
     * @param code
     * @return
     */
//    @Override
//    public PublicSpecializedVo mediumPublicSpecialized(String code) {
//        //查询网格下的线路
//        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(code);
//        if (CollectionUtils.isEmpty(dmsFeederDeviceList)) {
//            return null;
//        }
//        PublicSpecializedVo publicSpecializedVo = new PublicSpecializedVo();
//        //查询和线路相关的所有专变和公变
//        List<String> lineIdList = dmsFeederDeviceList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
//        List<PBEntity> feederTransformerVols = powerMapper.selectTransformersByFeedId(lineIdList);
//
//        if (CollectionUtils.isNotEmpty(feederTransformerVols)) {
//            // 使用 Stream API 按 pubPrivFlag 分组，并统计每个组的容量总和
//            Map<String, Double> totalCapacityByFlag = feederTransformerVols.stream()
//                    // 过滤掉容量为 null 或空字符串的记录
//                    .filter(t -> t.getRatedCapacity() != null && !t.getRatedCapacity().isEmpty())
//                    .collect(Collectors.groupingBy(
//                            // 按 pubPrivFlag 分组
//                            PBEntity::getPubPrivFlag,
//                            // 计算每组的容量总和（先清理字符串再转换为 double）
//                            Collectors.summingDouble(t -> {
//                                try {
//                                    String cleanValue = t.getRatedCapacity().replaceAll("[^0-9.]", "");
//                                    return Double.parseDouble(cleanValue);
//                                } catch (NumberFormatException e) {
//                                    return 0.0;  // 转换失败时返回 0
//                                }
//                            })
//                    ));
//
//            // 统计每个 pubPrivFlag 值的记录数量
//            Map<String, Long> countByFlag = feederTransformerVols.stream()
//                    .collect(Collectors.groupingBy(
//                            PBEntity::getPubPrivFlag,
//                            Collectors.counting()
//                    ));
//
//            // 提取统计结果
//            double totalCapacity0 = totalCapacityByFlag.getOrDefault("0", 0.0);
//            double totalCapacity1 = totalCapacityByFlag.getOrDefault("1", 0.0);
//            int countFlag0 = countByFlag.getOrDefault("0", 0L).intValue();
//            int countFlag1 = countByFlag.getOrDefault("1", 0L).intValue();
//            publicSpecializedVo.setPublicNum(countFlag0);
//            publicSpecializedVo.setPublicCapacity(totalCapacity0);
//            publicSpecializedVo.setSpecializedNum(countFlag1);
//            publicSpecializedVo.setSpecializedNumCapacity(totalCapacity1);
//        }
//
//
//        return publicSpecializedVo;
//    }

    /**
     * 公变专变(api)
     *
     * @param code
     * @return
     */
    @Override
//    public PublicSpecializedVo mediumPublicSpecialized(String code) throws Exception {
//        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
//        try {
//            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
//        } catch (Exception e){
//            return null;
//        }
//        PublicSpecializedVo publicSpecializedVo = new PublicSpecializedVo();
//        //01专变，03公变
//        //柱上变公变
//        List<DevicePoleTransformer> polePubList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("03")).collect(Collectors.toList());
//        //柱上变专变
//        List<DevicePoleTransformer> polePrvList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("01")).collect(Collectors.toList());
//
//        //柱上变容量
//        Map<String, Double> poleTypeSumMap = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DevicePoleTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//
////        //所有变公变数量
////        List<StationServiceTransformer> servicePubList = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).collect(Collectors.toList());
////        //所有变专变数量
////        List<StationServiceTransformer> servicePrvList = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).collect(Collectors.toList());
////
////        //所有变容量
////        Map<String, Double> serviceTypeSumMap = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(StationServiceTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//
//        //站内变公变
//        List<DeviceStationTransformer> stationPubList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).collect(Collectors.toList());
//        //站内变专变数量
//        List<DeviceStationTransformer> stationPrvList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).collect(Collectors.toList());
//
//        //站内变容量
//        Map<String, Double> stationTypeSumMap = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DeviceStationTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//        //公变数量
//        publicSpecializedVo.setPubNum(polePubList.size() + stationPubList.size());
//        //专变数量
//        publicSpecializedVo.setPrivNum(polePrvList.size() + stationPrvList.size());
//        //公变容量
//        publicSpecializedVo.setPubRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("03") == null ? 0.0 : poleTypeSumMap.get("03")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("03") == null ? 0.0 : stationTypeSumMap.get("03")));
//        //专变容量
//        publicSpecializedVo.setPrivRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("01") == null ? 0 : poleTypeSumMap.get("01")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("01") == null ? 0.0 : stationTypeSumMap.get("01")));
//        publicSpecializedVo.setPubDevicePoleTransformers(polePubList);
//        publicSpecializedVo.setPrvDevicePoleTransformers(polePrvList);
//        publicSpecializedVo.setPubDeviceStationTransformers(stationPubList);
//        publicSpecializedVo.setPrvDeviceStationTransformers(stationPrvList);
//
//        return publicSpecializedVo;
//    }
    public PublicSpecializedVo mediumPublicSpecialized(String code) {
        PublicSpecializedVo publicSpecializedVo;
        try {
            publicSpecializedVo = objectMapper.readValue(deviceApiJsonMapper.selectPowerGridVo(code, ResourceStatisticsDataType.MEDIUDVOLTAGE), PublicSpecializedVo.class);
        } catch (Exception e) {
            return null;
        }
        return publicSpecializedVo;
    }

    /**
     * 查询中压的线路信息(不用)
     *
     * @param code
     * @return
     */
    @Override
    public MediumFeederFoundationVo mediumFeeder(String code) {
        List<DeviceFeeder> deviceFeederList = deviceFeederJkMapper.selectDeviceFeeder(code);
        List<String> idList = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        //架空线
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectJKList(idList);


        //电缆
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectCableList(idList);

        //大馈线
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
        List<DeviceFeeder> deviceFeeder = feederDeviceMapper.selectList(lambdaQueryWrapper);
        Double dkxLength = deviceFeeder.stream().mapToDouble(DeviceFeeder::getLength).sum();


        MediumFeederFoundationVo mediumFeederFoundationVo = new MediumFeederFoundationVo();
        mediumFeederFoundationVo.setLineNum(deviceFeeder.size());
        mediumFeederFoundationVo.setLineLength(dkxLength);

        if (CollectionUtils.isNotEmpty(deviceFeederCableList)) {
            //电缆长度
            mediumFeederFoundationVo.setCableLength(deviceFeederCableList.stream().mapToDouble(DeviceFeederCable::getLength).sum() / 1000);
            mediumFeederFoundationVo.setCableNum(deviceFeederCableList.size());
        }

        //架空线长度
        if (CollectionUtils.isNotEmpty(deviceFeederJkList)) {
            mediumFeederFoundationVo.setJkLength((double) (deviceFeederJkList.stream().mapToInt(DeviceFeederJk::getLength).sum() / 1000));
            mediumFeederFoundationVo.setJkNum(deviceFeederJkList.size());

        }
        return mediumFeederFoundationVo;
    }

    // 获取主变信息
    public TableDataInfo<DeviceZB> selectZB(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException {
        List<DeviceZB> deviceZBS = fetchData(code, ResourceStatisticsDataType.ZBLIST,
                json -> {
                    try {
                        return parseJson(json, new TypeReference<List<DeviceZB>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                });

        if (CollectionUtils.isNotEmpty(deviceZBS)) {
            List<DeviceSubstation> substations = deviceSubstationMapper.selectBatchIds(
                    deviceZBS.stream().map(DeviceZB::getStation).collect(Collectors.toList())
            );

            // 将substations转换为Map，便于快速查找
            Map<String, String> stationIdToNameMap = substations.stream()
                    .collect(Collectors.toMap(
                            DeviceSubstation::getPsrId,
                            DeviceSubstation::getName,
                            (existing, replacement) -> existing
                    ));

            // 将substations转换为Map，便于快速查找
            Map<String, String> stationIdToCoordMap = substations.stream()
                    .collect(Collectors.toMap(
                            DeviceSubstation::getPsrId,
                            DeviceSubstation::getGeoPositon,
                            (existing, replacement) -> existing
                    ));

            // 为每个DeviceZB设置stationName
            for (DeviceZB deviceZB : deviceZBS) {
                String stationId = deviceZB.getStation();
                if (stationIdToNameMap.containsKey(stationId)) {
                    deviceZB.setStationName(stationIdToNameMap.get(stationId));
                    deviceZB.setGeoPositon(stationIdToCoordMap.get(stationId));
                }
            }

            Page<DeviceZB> page = new Page<>(pageNum, pageSize, deviceZBS.size());
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, deviceZBS.size());
            page.setRecords(deviceZBS.subList(fromIndex, toIndex));
            return TableDataInfo.build(page);
        }
        return TableDataInfo.build(new Page<>());
    }

    // 获取柱上专变信息
    public TableDataInfo<DevicePoleTransformer> selectPrvPoleTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException {
        List<DevicePoleTransformer> devicePoleTransformers = fetchData(code, ResourceStatisticsDataType.PRVZSBLIST,
                json -> {
                    try {
                        return parseJson(json, new TypeReference<List<DevicePoleTransformer>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                });
        if (CollectionUtils.isNotEmpty(devicePoleTransformers)) {
            Page<DevicePoleTransformer> page = new Page<>(pageNum, pageSize, devicePoleTransformers.size());
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, devicePoleTransformers.size());
            page.setRecords(devicePoleTransformers.subList(fromIndex, toIndex));
            return TableDataInfo.build(page);
        }
        return TableDataInfo.build(new Page<>());
    }

    // 获取柱上公变信息
    public TableDataInfo<DevicePoleTransformer> selectPubPoleTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException {
        List<DevicePoleTransformer> devicePoleTransformers = fetchData(code, ResourceStatisticsDataType.PUBZSBLIST,
                json -> {
                    try {
                        return parseJson(json, new TypeReference<List<DevicePoleTransformer>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                });
        if (CollectionUtils.isNotEmpty(devicePoleTransformers)) {
            Page<DevicePoleTransformer> page = new Page<>(pageNum, pageSize, devicePoleTransformers.size());
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, devicePoleTransformers.size());
            page.setRecords(devicePoleTransformers.subList(fromIndex, toIndex));
            return TableDataInfo.build(page);
        }
        return TableDataInfo.build(new Page<>());
    }

    // 获取站内专变信息
    public TableDataInfo<DeviceStationTransformer> selectPrvStationTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException {
        List<DeviceStationTransformer> deviceStationTransformers = fetchData(code, ResourceStatisticsDataType.PRVZNBLIST,
                json -> {
                    try {
                        return parseJson(json, new TypeReference<List<DeviceStationTransformer>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                });
        if(CollectionUtils.isNotEmpty(deviceStationTransformers)){
            Page<DeviceStationTransformer> page = new Page<>(pageNum, pageSize, deviceStationTransformers.size());
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, deviceStationTransformers.size());
            page.setRecords(deviceStationTransformers.subList(fromIndex, toIndex));
            return TableDataInfo.build(page);
        }
        return TableDataInfo.build(new Page<>());

    }

    // 获取站内公变信息
    public TableDataInfo<DeviceStationTransformer> selectPubStationTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException {
        List<DeviceStationTransformer> deviceStationTransformers = fetchData(code, ResourceStatisticsDataType.PUBZNBLIST,
                json -> {
                    try {
                        return parseJson(json, new TypeReference<List<DeviceStationTransformer>>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                });
        if(CollectionUtils.isNotEmpty(deviceStationTransformers)){
            Page<DeviceStationTransformer> page = new Page<>(pageNum, pageSize, deviceStationTransformers.size());
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, deviceStationTransformers.size());
            page.setRecords(deviceStationTransformers.subList(fromIndex, toIndex));
            return TableDataInfo.build(page);
        }
       return TableDataInfo.build(new Page<>());
    }

    // 通用数据获取方法
    private <T> List<T> fetchData(String code, Integer dataTypeInt, Function<String, List<T>> parser) throws JsonProcessingException {
        String json = deviceApiJsonMapper.selectPowerGridVo(code, dataTypeInt);
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return parser.apply(json);
    }

    // 通用JSON解析方法
    private <T> List<T> parseJson(String json, TypeReference<List<T>> typeReference) throws JsonProcessingException {
        return objectMapper.readValue(json, typeReference);
    }
}
