package com.ruoyi.service.power.impl;

import cn.hutool.core.io.resource.ClassPathResource;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.power.*;
import com.ruoyi.entity.power.bo.*;
import com.ruoyi.entity.power.enuw.TimeEnum288;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.power.vo.DailyLoadUtilization;
import com.ruoyi.entity.power.vo.MaxLoadUtilization;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.Statistics;
import com.ruoyi.entity.problem.bo.LoadMWExcelBo;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.power.*;
import com.ruoyi.service.power.IPowerService;
import com.ruoyi.service.power.IPowerTransformerService;
import com.ruoyi.util.EnhancedPeakAnalyzer;
import com.ruoyi.util.ExcelExportWithPOI;
import com.ruoyi.util.PeakPeriod;
import com.ruoyi.util.ValleyAnalyzer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ruoyi.util.LoadDataExpander.expandToHalfYear;


@Service
public class PowerTransformerServiceImpl implements IPowerTransformerService {
    @Autowired
    NtHighTransformerNewMapper ntHighTransformerNewMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    FeederLoadMapper feederLoadMapper;

    @Autowired
    FeederLoadMWMapper feederLoadMWMapper;

    @Autowired
    TransformerLoadMWMapper transformerLoadMWMapper;
    @Autowired
    TransformerLoadMapper transformerLoadMapper;


    @Autowired
    IPowerService iPowerService;

    @Autowired
    PowerServiceImpl powerServiceImpl;
    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final ObjectMapper objectMapper;

    public PowerTransformerServiceImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }


    /**
     * 主变——根据网格id查询所有网格
     */
    @Override
    public Page<DeviceNtHighTransformer> girdTransformer(String code, Integer pageNum, Integer pageSize) throws IOException {
        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(code);

        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        Page<DeviceNtHighTransformer> page = new Page<>(pageNum, pageSize, ntHighTransformerList.size());
// 计算分页数据
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, ntHighTransformerList.size());
        page.setRecords(ntHighTransformerList.subList(fromIndex, toIndex));
// 返回分页结果
        return page;
    }

    /**
     * 主变——主变分析
     */
    @Override
    public PowerTransformerVo analysisTransformer(AnalysisBo bo) throws IOException, ParseException {

        PowerTransformerVo powerTransformerVo = new PowerTransformerVo();
        //主变实体
        List<TransformerStateVo> transformerStateVoList = new ArrayList<>();
        //曲线list
        List<CurveListVo> curveListVoList = new ArrayList<>();

        //饼装图
        List<SubstationSummaryVo> summaryList = new ArrayList<>();

        //正常数据
        List<Load> normalLoad = new ArrayList<>();
        //重载数据
        List<Load> heavyLoad = new ArrayList<>();
        //超载数据
        List<Load> overLoad = new ArrayList<>();


        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(bo.getCode());
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        if (CollectionUtils.isEmpty(ntHighTransformerList)) {
            return null;
        }
        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
            //取出主变的负载数据
            Load loadMW = powerServiceImpl.selectTransformerLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            DeviceSubstation deviceSubstation = ntHighTransformerNewMapper.selectDeviceSubstation(ntHighTransformer.getStation());

            TransformerStateVo transformerStateVo = new TransformerStateVo();
            transformerStateVo.setSubstationName(deviceSubstation.getName());
            transformerStateVo.setTransformerQuantityName(ntHighTransformer.getName());
            transformerStateVo.setPsrId(ntHighTransformer.getPsrId());
            MaxAndIndex maxAndIndex = maxAndIndex(loadMW);

            transformerStateVo.setLoad(String.valueOf(DoubleFormatter.formatToThreeDecimals2(maxAndIndex.getMaxNum())));
            transformerStateVo.setMaxLoadMW(String.valueOf(DoubleFormatter.formatToThreeDecimals2(Double.parseDouble(transformerStateVo.getLoad()) * ntHighTransformer.getCapacity())));

            transformerStateVo.setCapacity(ntHighTransformer.getCapacity());
            transformerStateVoList.add(transformerStateVo);

            if (Double.parseDouble(transformerStateVo.getLoad()) <= bo.getThreshold()) {
                transformerStateVo.setLoadState("正常");
                normalLoad.add(loadMW);
            } else if (Double.parseDouble(transformerStateVo.getLoad()) - bo.getThreshold() <= 10) {
                transformerStateVo.setLoadState("重载");
                heavyLoad.add(loadMW);
            } else if (Double.parseDouble(transformerStateVo.getLoad()) - bo.getThreshold() > 10) {
                transformerStateVo.setLoadState("超载");
                overLoad.add(loadMW);
            }
            CurveListVo curveListVo = new CurveListVo();
            curveListVo.setName(ntHighTransformer.getName());
            curveListVo.setId(ntHighTransformer.getPsrId());
            if (isMoreThan7Days(bo.getStrTime(), bo.getEndTime())) {
                curveListVo.setStatisticsList(setStatistics7Days(loadMW, bo.getType()));
                curveListVoList.add(curveListVo);
            } else {
                curveListVo.setStatisticsList(setStatistics(loadMW));
                curveListVoList.add(curveListVo);
            }

        }
        // 计算各列表数量
        int normalCount = normalLoad.size();
        int heavyCount = heavyLoad.size();
        int overCount = overLoad.size();

        // 计算总数
        int total = normalCount + heavyCount + overCount;

        //饼状图
        if (normalCount > 0) {
            setSubstationSummaryVo(normalCount, total, summaryList, "正常");
        }
        if (heavyCount > 0) {
            setSubstationSummaryVo(heavyCount, total, summaryList, "重载");
        }
        if (overCount > 0) {
            setSubstationSummaryVo(overCount, total, summaryList, "超载");
        }
        powerTransformerVo.setSummaryList(summaryList);

        Page<TransformerStateVo> page = new Page<>(bo.getPageNum(), bo.getPageSize(), transformerStateVoList.size());
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), transformerStateVoList.size());
        page.setRecords(transformerStateVoList.subList(fromIndex, toIndex));
        powerTransformerVo.setTransformerStateVoList(page);

        Page<CurveListVo> curveListVoPage = new Page<>(bo.getPageNum(), bo.getPageSize(), curveListVoList.size());
        int curveListVoFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int curveListVoToIndex = Math.min(fromIndex + bo.getPageSize(), curveListVoList.size());
        curveListVoPage.setRecords(curveListVoList.subList(curveListVoFromIndex, curveListVoToIndex));
        powerTransformerVo.setCurveListVoList(curveListVoPage);


        return powerTransformerVo;


    }

    /**
     * 判断2个日期之间是否超过7天
     *
     * @param dateStr1
     * @param dateStr2
     * @return
     */
    public static boolean isMoreThan7Days(String dateStr1, String dateStr2) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析字符串为 LocalDate
        LocalDate date1 = LocalDate.parse(dateStr1, formatter);
        LocalDate date2 = LocalDate.parse(dateStr2, formatter);

        // 计算两个日期的绝对差值（避免负数）
        long daysBetween = Math.abs(ChronoUnit.DAYS.between(date1, date2));

        // 判断是否超过 7 天
        return daysBetween > 7;
    }

    /**
     * 根据id查主变详情
     *
     * @param id
     * @return
     */
    @Override
    public DeviceNtHighTransformer byId(String id) {
        LambdaQueryWrapper<DeviceNtHighTransformer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceNtHighTransformer::getPsrId, id);
        return ntHighTransformerNewMapper.selectOne(lambdaQueryWrapper);
    }

    /**
     * 主变——线路分析
     */
    @Override
    public PowerTransformerVo analysisFeeder(AnalysisBo bo) throws IOException, ParseException {
        PowerTransformerVo powerTransformerVo = new PowerTransformerVo();
        //线路实体
        List<TransformerStateVo> transformerStateVoList = new ArrayList<>();
        //曲线list
        List<CurveListVo> curveListVoList = new ArrayList<>();

        //饼装图
        List<SubstationSummaryVo> summaryList = new ArrayList<>();

        //正常数据
        List<Load> normalLoad = new ArrayList<>();
        //重载数据
        List<Load> heavyLoad = new ArrayList<>();
        //超载数据
        List<Load> overLoad = new ArrayList<>();


        List<DeviceFeeder> feederDeviceList = getFeederDevices(bo.getCode());

        if (CollectionUtils.isEmpty(feederDeviceList)) {
            return null;
        }
        for (DeviceFeeder feederDevice : feederDeviceList) {
            //取出主变的负载数据
            Load loadMW = powerServiceImpl.selectFeeDerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(feederDevice.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            TransformerStateVo transformerStateVo = new TransformerStateVo();
            transformerStateVo.setTransformerQuantityName(feederDevice.getName());
            transformerStateVo.setPsrId(feederDevice.getPsrId());
            MaxAndIndex maxAndIndex = maxAndIndex(loadMW);
            transformerStateVo.setLoad(String.valueOf(DoubleFormatter.formatToThreeDecimals2(maxAndIndex.getMaxNum())));
            transformerStateVo.setMaxLoadMW(String.valueOf(DoubleFormatter.formatToThreeDecimals2((Double.parseDouble(transformerStateVo.getLoad()) * feederDevice.getFeederRateCapacity())/100)));
            transformerStateVo.setCapacity(feederDevice.getFeederRateCapacity());
            transformerStateVoList.add(transformerStateVo);
            if (Double.parseDouble(transformerStateVo.getLoad()) <= bo.getThreshold()) {
                transformerStateVo.setLoadState("正常");
                normalLoad.add(loadMW);
            } else if (Double.parseDouble(transformerStateVo.getLoad()) - bo.getThreshold() <= 10) {
                transformerStateVo.setLoadState("重载");
                heavyLoad.add(loadMW);
            } else if (Double.parseDouble(transformerStateVo.getLoad()) - bo.getThreshold() > 10) {
                transformerStateVo.setLoadState("超载");
                overLoad.add(loadMW);
            }
            CurveListVo curveListVo = new CurveListVo();
            curveListVo.setName(feederDevice.getName());
            curveListVo.setId(feederDevice.getPsrId());
            if (isMoreThan7Days(bo.getStrTime(), bo.getEndTime())) {
                curveListVo.setStatisticsList(setStatistics7Days(loadMW, bo.getType()));
                curveListVoList.add(curveListVo);
            } else {
                curveListVo.setStatisticsList(setStatistics(loadMW));
                curveListVoList.add(curveListVo);
            }
        }
        // 计算各列表数量
        int normalCount = normalLoad.size();
        int heavyCount = heavyLoad.size();
        int overCount = overLoad.size();

        // 计算总数
        int total = normalCount + heavyCount + overCount;

        //饼状图
        if (normalCount > 0) {
            setSubstationSummaryVo(normalCount, total, summaryList, "正常");
        }
        if (heavyCount > 0) {
            setSubstationSummaryVo(heavyCount, total, summaryList, "重载");
        }
        if (overCount > 0) {
            setSubstationSummaryVo(overCount, total, summaryList, "超载");
        }
        powerTransformerVo.setSummaryList(summaryList);

        Page<TransformerStateVo> page = new Page<>(bo.getPageNum(), bo.getPageSize(), transformerStateVoList.size());
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), transformerStateVoList.size());
        page.setRecords(transformerStateVoList.subList(fromIndex, toIndex));
        powerTransformerVo.setTransformerStateVoList(page);

        Page<CurveListVo> curveListVoPage = new Page<>(bo.getPageNum(), bo.getPageSize(), curveListVoList.size());
        int curveListVoFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int curveListVoToIndex = Math.min(fromIndex + bo.getPageSize(), curveListVoList.size());
        curveListVoPage.setRecords(curveListVoList.subList(curveListVoFromIndex, curveListVoToIndex));
        powerTransformerVo.setCurveListVoList(curveListVoPage);


        return powerTransformerVo;
    }

    /**
     * 主变——负载率分析历史记录查询
     */
    @Override
    public Page<PowerTransformerSelectVo> selectLoad(LoadBo bo) throws IOException, ParseException {
        List<PowerTransformerSelectVo> list = new ArrayList<>();

        //所有变电站
        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(bo.getCode());

        //所有主变
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
        List<DeviceNtHighTransformer> ntHighTransformerList = getNtHighTransformerList(bo.getCode());

        for (PullDownMenuStringSon pullDownMenuStringSon : inSubstationList) {
            for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                if (pullDownMenuStringSon.getCode().equals(ntHighTransformer.getStation())) {
                    PowerTransformerSelectVo powerTransformerSelectVo = new PowerTransformerSelectVo();
                    powerTransformerSelectVo.setStationName(pullDownMenuStringSon.getName());
                    powerTransformerSelectVo.setTransformerName(ntHighTransformer.getName());

                    Load load = powerServiceImpl.selectTransformerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
                    if (load == null) {
                        continue;
                    }
                    MaxAndIndex maxAndIndex = maxAndIndex(load);
                    powerTransformerSelectVo.setTime(maxAndIndex.getMaxTime());
                    powerTransformerSelectVo.setMaxLoadKW(DoubleFormatter.formatToThreeDecimals2(maxToDouble(load.getLoadList()) * ntHighTransformer.getCapacity() / 100));
                    powerTransformerSelectVo.setMinLoadKW(DoubleFormatter.formatToThreeDecimals2(minToDouble(load.getLoadList()) * ntHighTransformer.getCapacity() / 100));
                    powerTransformerSelectVo.setAvgLoadKW(DoubleFormatter.formatToThreeDecimals(load.getLoadList().stream().mapToDouble(Double::doubleValue).average().getAsDouble() * ntHighTransformer.getCapacity() / 100));
//                    powerTransformerSelectVo.setLoad();

                    powerTransformerSelectVo.setQOQ(0.0);
                    powerTransformerSelectVo.setOnYear(0.0);
                    list.add(powerTransformerSelectVo);

                }
            }
        }
        Page<PowerTransformerSelectVo> page = new Page<>(bo.getPageNum(), bo.getPageSize(), list.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), list.size());
        page.setRecords(list.subList(fromIndex, toIndex));
// 返回分页结果
        return page;

    }

    /**
     * 主变——负载率历史记录查询
     */
    @Override
    public HistoryAnalysisVo selectLoadHistoryAnalysis(HistoryAnalysisBo bo) throws IOException, ParseException {

        Load loadMW = powerServiceImpl.selectTransformerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getPrsId()));
       if(loadMW == null){
           return null;
       }
        if (isMoreThan7Days(bo.getStrTime(), bo.getEndTime())) {
            List<Statistics> statisticsList = setStatistics7Days(loadMW, bo.getType());
            HistoryAnalysisVo historyAnalysisVo = new HistoryAnalysisVo();
            historyAnalysisVo.setStatisticsList(statisticsList);
            return historyAnalysisVo;
        } else {
            List<Statistics> statisticsList = setStatistics(loadMW);

            Map<LocalDate, List<Statistics>> groupedByDate = statisticsList.stream()
                    .collect(Collectors.groupingBy(
                            stat -> LocalDate.parse(
                                    stat.getTypeName().substring(0, 10), // 截取 "2025-04-01"
                                    DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            )
                    ));
            List<Statistics> returnList = new ArrayList<>();

            if (bo.getType() == 1) {
                groupedByDate.forEach((key, value) -> {
                    Statistics statistics = new Statistics();
                    OptionalDouble avgOpt = value.stream().mapToDouble(e -> Double.parseDouble(e.getTypeNum())).average();
                    statistics.setTypeName(key.toString());
                    statistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals(avgOpt.getAsDouble())));
                    returnList.add(statistics);
                });
            }

            if (bo.getType() == 0) {
                groupedByDate.forEach((key, value) -> {
                    Statistics statistics = new Statistics();
                    statistics.setTypeName(key.toString());
                    statistics.setTypeNum(min(value.stream()
                            .map(Statistics::getTypeNum) // 等价于 stat -> stat.getTypeNum()
                            .collect(Collectors.toList())));
                    returnList.add(statistics);
                });

            }
            if (bo.getType() == 2) {
                groupedByDate.forEach((key, value) -> {
                    Statistics statistics = new Statistics();
                    statistics.setTypeName(key.toString());
                    statistics.setTypeNum(max(value.stream()
                            .map(Statistics::getTypeNum) // 等价于 stat -> stat.getTypeNum()
                            .collect(Collectors.toList())));
                    returnList.add(statistics);
                });

            }
            HistoryAnalysisVo historyAnalysisVo = new HistoryAnalysisVo();
            historyAnalysisVo.setStatisticsList(statisticsList);
            return historyAnalysisVo;
        }


    }

    /**
     * 主变——高峰负载率分析
     */
    @Override
    public LoadAnalysisVo peakLoadAnalysis(LoadAnalysisBo bo) throws IOException, ParseException {

        List<DeviceNtHighTransformer> ntHighTransformerList = getNtHighTransformerList(bo.getCode());

        List<LoadAnalysis> loadAnalysisList = new ArrayList<>();

        List<ThreeStatistics> threeStatisticsList = new ArrayList<>();

        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
            //父级
            LoadAnalysis loadAnalysis = new LoadAnalysis();
            DeviceSubstation deviceSubstation = ntHighTransformerNewMapper.selectDeviceSubstation(ntHighTransformer.getStation());

            List<LoadAnalysis> sonLoadAnalysisList = new ArrayList<>();
            Load loadMW = powerServiceImpl.selectTransformerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
//            Load loadMW = powerServiceImpl.selectLoad("E:\\java\\数据\\用户详细信息说明\\变电站\\负载率.json", "负载率(%)");
            if (loadMW == null) {
                continue;
            }
            TimeLoadGroup timeLoadGroup = groupByFullDateWithStream(loadMW);
            //持续时间
            AtomicReference<Double> durationHours = new AtomicReference<>(0.0);
            //频率
            AtomicInteger exceedCount = new AtomicInteger();
            //平均数
            AtomicReference<Double> averageValue = new AtomicReference<>(0.0);
            //计数
            AtomicInteger count = new AtomicInteger();
            timeLoadGroup.getTimeMap().forEach((key, value) -> {

                List<PeakPeriod> peaks = EnhancedPeakAnalyzer.analyze(value, timeLoadGroup.getLoadMap().get(key),
                        bo.getThreshold(), bo.getDuration());

                if (CollectionUtils.isEmpty(peaks)) {
                    return;
                }
                for (PeakPeriod peak : peaks) {
                    //子级
                    LoadAnalysis sonLoadAnalysis = new LoadAnalysis();
                    sonLoadAnalysis.setTimeWindow(peak.getTimeWindow());
                    sonLoadAnalysis.setExceedCount(peak.getExceedCount());
                    sonLoadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(peak.getDurationHours()));
                    sonLoadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue()));
                    sonLoadAnalysis.setName(ntHighTransformer.getName());
                    sonLoadAnalysisList.add(sonLoadAnalysis);

                    //持续时间
                    durationHours.set(durationHours.get() + peak.getDurationHours());
                    //频率
                    exceedCount.set(exceedCount.get() + peak.getExceedCount());
                    //平均数
                    averageValue.set(averageValue.get() + peak.getAverageValue());
                    //计数
                    count.set(count.get() + 1);

                    ThreeStatistics threeStatistics = new ThreeStatistics();
                    threeStatistics.setTime(key);
                    threeStatistics.setName(ntHighTransformer.getName());
                    threeStatistics.setNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue())));
                    threeStatisticsList.add(threeStatistics);
                }
                loadAnalysis.setName(deviceSubstation.getName());
                loadAnalysis.setSubstationName(ntHighTransformer.getName());
                loadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(durationHours.get()));
                loadAnalysis.setExceedCount(exceedCount.get());
                loadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(averageValue.get() / count.get()));
                loadAnalysis.setLoadAnalysisList(sonLoadAnalysisList);
                loadAnalysisList.add(loadAnalysis);
            });


        }
        Page<LoadAnalysis> page = new Page<>(bo.getPageNum(), bo.getPageSize(), loadAnalysisList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), loadAnalysisList.size());
        page.setRecords(loadAnalysisList.subList(fromIndex, toIndex));
// 返回分页结果
        LoadAnalysisVo loadAnalysisVo = new LoadAnalysisVo();
        loadAnalysisVo.setPage(page);

        Page<ThreeStatistics> threeStatisticsPage = new Page<>(bo.getPageNum(), bo.getPageSize(), threeStatisticsList.size());
// 计算分页数据
        int threeStatisticsFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int threeStatisticsToIndex = Math.min(threeStatisticsFromIndex + bo.getPageSize(), threeStatisticsList.size());
        threeStatisticsPage.setRecords(threeStatisticsList.subList(threeStatisticsFromIndex, threeStatisticsToIndex));


        loadAnalysisVo.setThreeStatisticsList(threeStatisticsPage);
        return loadAnalysisVo;

    }


    /**
     * 查询网格下的所有变电站
     *
     * @param code
     * @return
     */
    @Override
    public List<DeviceNtHighTransformer> getNtHighTransformerList(String code) {
        //所有变电站
        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(code);

        //所有主变
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        return ntHighTransformerList;
    }

    /**
     * 线路——高峰负载率分析
     */
    @Override
    public LoadAnalysisVo peakLoadAnalysisFeeder(LoadAnalysisBo bo) throws IOException, ParseException {
        List<DeviceFeeder> feederDeviceList = getFeederDevices(bo.getCode());

        List<LoadAnalysis> loadAnalysisList = new ArrayList<>();
        List<ThreeStatistics> threeStatisticsList = new ArrayList<>();
        for (DeviceFeeder feederDevice : feederDeviceList) {
            //父级
            LoadAnalysis loadAnalysis = new LoadAnalysis();


            List<LoadAnalysis> sonLoadAnalysisList = new ArrayList<>();
            Load loadMW = powerServiceImpl.selectFeeDerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(feederDevice.getPsrId()));
//            Load loadMW = powerServiceImpl.selectLoad("E:\\java\\数据\\用户详细信息说明\\变电站\\负载率.json", "负载率(%)");

            if (loadMW == null) {
                continue;
            }
            TimeLoadGroup timeLoadGroup = groupByFullDateWithStream(loadMW);

            //持续时间
            AtomicReference<Double> durationHours = new AtomicReference<>(0.0);
            //频率
            AtomicInteger exceedCount = new AtomicInteger();
            //平均数
            AtomicReference<Double> averageValue = new AtomicReference<>(0.0);
            //计数
            AtomicInteger count = new AtomicInteger();
            timeLoadGroup.getTimeMap().forEach((key, value) -> {

                List<PeakPeriod> peaks = EnhancedPeakAnalyzer.analyze(value, timeLoadGroup.getLoadMap().get(key),
                        bo.getThreshold(), bo.getDuration());
                if (CollectionUtils.isEmpty(peaks)) {
                    return;
                }
                for (PeakPeriod peak : peaks) {

                    //子级
                    LoadAnalysis sonLoadAnalysis = new LoadAnalysis();
                    sonLoadAnalysis.setTimeWindow(peak.getTimeWindow());
                    sonLoadAnalysis.setExceedCount(peak.getExceedCount());
                    sonLoadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(peak.getDurationHours()));
                    sonLoadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue()));
                    sonLoadAnalysis.setName(feederDevice.getName());
                    sonLoadAnalysisList.add(sonLoadAnalysis);

                    //持续时间
                    durationHours.set(durationHours.get() + peak.getDurationHours());
                    //频率
                    exceedCount.set(exceedCount.get() + peak.getExceedCount());
                    //平均数
                    averageValue.set(averageValue.get() + peak.getAverageValue());
                    //计数
                    count.set(count.get() + 1);

                    ThreeStatistics threeStatistics = new ThreeStatistics();
                    threeStatistics.setTime(key);
                    threeStatistics.setName(feederDevice.getName());
                    threeStatistics.setNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue())));
                    threeStatisticsList.add(threeStatistics);
                }
                loadAnalysis.setName(feederDevice.getName());

                loadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(durationHours.get()));
                loadAnalysis.setExceedCount(exceedCount.get());
                loadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(averageValue.get() / count.get()));
                loadAnalysis.setLoadAnalysisList(sonLoadAnalysisList);
                loadAnalysisList.add(loadAnalysis);


            });


        }
        Page<LoadAnalysis> page = new Page<>(bo.getPageNum(), bo.getPageSize(), loadAnalysisList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), loadAnalysisList.size());
        page.setRecords(loadAnalysisList.subList(fromIndex, toIndex));
// 返回分页结果
        LoadAnalysisVo loadAnalysisVo = new LoadAnalysisVo();
        loadAnalysisVo.setPage(page);
        Page<ThreeStatistics> threeStatisticsPage = new Page<>(bo.getPageNum(), bo.getPageSize(), threeStatisticsList.size());
// 计算分页数据
        int threeStatisticsFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int threeStatisticsToIndex = Math.min(threeStatisticsFromIndex + bo.getPageSize(), threeStatisticsList.size());
        threeStatisticsPage.setRecords(threeStatisticsList.subList(threeStatisticsFromIndex, threeStatisticsToIndex));
        loadAnalysisVo.setThreeStatisticsList(threeStatisticsPage);
        return loadAnalysisVo;
    }


    /**
     * 主变——低谷负载率分析
     */
    @Override
    public LoadAnalysisVo valleyLoadAnalysis(LoadAnalysisBo bo) throws IOException, ParseException {
        //所有变电站
        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(bo.getCode());

        //所有主变
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);

        List<LoadAnalysis> loadAnalysisList = new ArrayList<>();
        List<ThreeStatistics> threeStatisticsList = new ArrayList<>();
        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
            //父级
            LoadAnalysis loadAnalysis = new LoadAnalysis();
            DeviceSubstation deviceSubstation = ntHighTransformerNewMapper.selectDeviceSubstation(ntHighTransformer.getStation());

            List<LoadAnalysis> sonLoadAnalysisList = new ArrayList<>();
            Load loadMW = powerServiceImpl.selectTransformerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            TimeLoadGroup timeLoadGroup = groupByFullDateWithStream(loadMW);

            //持续时间
            AtomicReference<Double> durationHours = new AtomicReference<>(0.0);
            //频率
            AtomicInteger exceedCount = new AtomicInteger();
            //平均数
            AtomicReference<Double> averageValue = new AtomicReference<>(0.0);
            //计数
            AtomicInteger count = new AtomicInteger();
            timeLoadGroup.getTimeMap().forEach((key, value) -> {

                List<PeakPeriod> peaks = ValleyAnalyzer.analyze(value, timeLoadGroup.getLoadMap().get(key),
                        bo.getThreshold(), bo.getDuration());
                if (CollectionUtils.isEmpty(peaks)) {
                    return;
                }
                for (PeakPeriod peak : peaks) {

                    //子级
                    LoadAnalysis sonLoadAnalysis = new LoadAnalysis();
                    sonLoadAnalysis.setTimeWindow(peak.getTimeWindow());
                    sonLoadAnalysis.setExceedCount(peak.getExceedCount());
                    sonLoadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(peak.getDurationHours()));
                    sonLoadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue()));
                    sonLoadAnalysis.setName(ntHighTransformer.getName());
                    sonLoadAnalysisList.add(sonLoadAnalysis);

                    //持续时间
                    durationHours.set(durationHours.get() + peak.getDurationHours());
                    //频率
                    exceedCount.set(exceedCount.get() + peak.getExceedCount());
                    //平均数
                    averageValue.set(averageValue.get() + peak.getAverageValue());
                    //计数
                    count.set(count.get() + 1);

                    ThreeStatistics threeStatistics = new ThreeStatistics();
                    threeStatistics.setTime(key);
                    threeStatistics.setName(ntHighTransformer.getName());
                    threeStatistics.setNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue())));
                    threeStatisticsList.add(threeStatistics);
                }
                loadAnalysis.setName(ntHighTransformer.getName());
                loadAnalysis.setSubstationName(deviceSubstation.getName());

                loadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(durationHours.get()));
                loadAnalysis.setExceedCount(exceedCount.get());
                loadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(averageValue.get() / count.get()));
                loadAnalysis.setLoadAnalysisList(sonLoadAnalysisList);
                loadAnalysisList.add(loadAnalysis);
            });


        }
        Page<LoadAnalysis> page = new Page<>(bo.getPageNum(), bo.getPageSize(), loadAnalysisList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), loadAnalysisList.size());
        page.setRecords(loadAnalysisList.subList(fromIndex, toIndex));
// 返回分页结果
        LoadAnalysisVo loadAnalysisVo = new LoadAnalysisVo();
        loadAnalysisVo.setPage(page);
        Page<ThreeStatistics> threeStatisticsPage = new Page<>(bo.getPageNum(), bo.getPageSize(), threeStatisticsList.size());
// 计算分页数据
        int threeStatisticsFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int threeStatisticsToIndex = Math.min(threeStatisticsFromIndex + bo.getPageSize(), threeStatisticsList.size());
        threeStatisticsPage.setRecords(threeStatisticsList.subList(threeStatisticsFromIndex, threeStatisticsToIndex));


        loadAnalysisVo.setThreeStatisticsList(threeStatisticsPage);
        return loadAnalysisVo;
    }

    /**
     * 线路——低谷负载率分析
     */
    @Override
    public LoadAnalysisVo valleyLoadAnalysisFeeder(LoadAnalysisBo bo) throws IOException, ParseException {
        List<DeviceFeeder> feederDeviceList = getFeederDevices(bo.getCode());

        List<LoadAnalysis> loadAnalysisList = new ArrayList<>();
        List<ThreeStatistics> threeStatisticsList = new ArrayList<>();
        for (DeviceFeeder feederDevice : feederDeviceList) {
            //父级
            LoadAnalysis loadAnalysis = new LoadAnalysis();


            List<LoadAnalysis> sonLoadAnalysisList = new ArrayList<>();
            Load loadMW = powerServiceImpl.selectFeeDerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(feederDevice.getPsrId()));

            if (loadMW == null) {
                continue;
            }
            TimeLoadGroup timeLoadGroup = groupByFullDateWithStream(loadMW);

            //持续时间
            AtomicReference<Double> durationHours = new AtomicReference<>(0.0);
            //频率
            AtomicInteger exceedCount = new AtomicInteger();
            //平均数
            AtomicReference<Double> averageValue = new AtomicReference<>(0.0);
            //计数
            AtomicInteger count = new AtomicInteger();
            timeLoadGroup.getTimeMap().forEach((key, value) -> {

                List<PeakPeriod> peaks = ValleyAnalyzer.analyze(value, timeLoadGroup.getLoadMap().get(key),
                        bo.getThreshold(), bo.getDuration());
                if (CollectionUtils.isEmpty(peaks)) {
                    return;
                }
                for (PeakPeriod peak : peaks) {

                    //子级
                    LoadAnalysis sonLoadAnalysis = new LoadAnalysis();
                    sonLoadAnalysis.setTimeWindow(peak.getTimeWindow());
                    sonLoadAnalysis.setExceedCount(peak.getExceedCount());
                    sonLoadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(peak.getDurationHours()));
                    sonLoadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue()));
                    sonLoadAnalysis.setName(feederDevice.getName());
                    sonLoadAnalysisList.add(sonLoadAnalysis);

                    //持续时间
                    durationHours.set(durationHours.get() + peak.getDurationHours());
                    //频率
                    exceedCount.set(exceedCount.get() + peak.getExceedCount());
                    //平均数
                    averageValue.set(averageValue.get() + peak.getAverageValue());
                    //计数
                    count.set(count.get() + 1);

                    ThreeStatistics threeStatistics = new ThreeStatistics();
                    threeStatistics.setTime(key);
                    threeStatistics.setName(feederDevice.getName());
                    threeStatistics.setNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(peak.getAverageValue())));
                    threeStatisticsList.add(threeStatistics);
                }
                loadAnalysis.setName(feederDevice.getName());

                loadAnalysis.setDurationHours(DoubleFormatter.formatToThreeDecimals1(durationHours.get()));
                loadAnalysis.setExceedCount(exceedCount.get());
                loadAnalysis.setAverageValue(DoubleFormatter.formatToThreeDecimals2(averageValue.get() / count.get()));
                loadAnalysis.setLoadAnalysisList(sonLoadAnalysisList);
                loadAnalysisList.add(loadAnalysis);


            });


        }
        Page<LoadAnalysis> page = new Page<>(bo.getPageNum(), bo.getPageSize(), loadAnalysisList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), loadAnalysisList.size());
        page.setRecords(loadAnalysisList.subList(fromIndex, toIndex));
// 返回分页结果
        LoadAnalysisVo loadAnalysisVo = new LoadAnalysisVo();
        loadAnalysisVo.setPage(page);
        Page<ThreeStatistics> threeStatisticsPage = new Page<>(bo.getPageNum(), bo.getPageSize(), threeStatisticsList.size());
// 计算分页数据
        int threeStatisticsFromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int threeStatisticsToIndex = Math.min(threeStatisticsFromIndex + bo.getPageSize(), threeStatisticsList.size());
        threeStatisticsPage.setRecords(threeStatisticsList.subList(threeStatisticsFromIndex, threeStatisticsToIndex));


        loadAnalysisVo.setThreeStatisticsList(threeStatisticsPage);
        return loadAnalysisVo;
    }

    /**
     * 查询网格下所有线路
     *
     * @param code
     * @return
     */
    private List<DeviceFeeder> getFeederDevices(String code) {
        //所有线路
        LambdaQueryWrapper<DeviceFeeder> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceFeeder::getGridCode, code);
        List<DeviceFeeder> feederDeviceList = feederDeviceMapper.selectList(lqw);
        return feederDeviceList;

    }

    /**
     * 典型日负荷利用率
     */

    @Override
    public DailyLoadUtilizationVo dailyLoadUtilization(DailyLoadUtilizationBo bo) throws IOException, ParseException {

        List<DailyLoadUtilization> dailyLoadUtilizationList = new ArrayList<>();
        //网格下所有线路
        List<DeviceFeeder> feederDeviceList = getFeederDevices(bo.getCode());

        List<Statistics> statisticsList = new ArrayList<>();


        //网格下所有主变
        List<DeviceNtHighTransformer> ntHighTransformerList = getNtHighTransformerList(bo.getCode());
        for (DeviceFeeder feederDevice : feederDeviceList) {
            DailyLoadUtilization dailyLoadUtilization = new DailyLoadUtilization();
            Load loadMW = powerServiceImpl.selectFeeDerLoad(bo.getTime(), bo.getTime(), Collections.singletonList(feederDevice.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            OptionalDouble avgOpt = loadMW.getLoadList().stream()
                    .mapToDouble(Double::doubleValue) // String → double
                    .average();
            dailyLoadUtilization.setAvailability(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble() / feederDevice.getFeederRateCapacity()) * 100);
            dailyLoadUtilization.setName(feederDevice.getName());
            dailyLoadUtilization.setTime(bo.getTime());
            setAvailabilityInterval(dailyLoadUtilization);

            Statistics statistics = new Statistics();
            statistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble())));
            statistics.setTypeName(feederDevice.getName());
            statisticsList.add(statistics);
            dailyLoadUtilizationList.add(dailyLoadUtilization);
        }
        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
            DailyLoadUtilization dailyLoadUtilization = new DailyLoadUtilization();
            Load loadMW = powerServiceImpl.selectTransformerLoadMW(bo.getTime(), bo.getTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            OptionalDouble avgOpt = loadMW.getLoadList().stream()
                    .mapToDouble(Double::doubleValue) // String → double
                    .average();
            dailyLoadUtilization.setAvailability(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble() / ntHighTransformer.getCapacity()) * 100);
            dailyLoadUtilization.setName(ntHighTransformer.getName());
            dailyLoadUtilization.setTime(bo.getTime());
            setAvailabilityInterval(dailyLoadUtilization);

            Statistics statistics = new Statistics();
            statistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble())));
            statistics.setTypeName(ntHighTransformer.getName());
            statisticsList.add(statistics);
            dailyLoadUtilizationList.add(dailyLoadUtilization);
        }

        DailyLoadUtilizationVo dailyLoadUtilizationVo = new DailyLoadUtilizationVo();

        Page<Statistics> pageList = new Page<>(bo.getPageNum(), bo.getPageSize(), statisticsList.size());
// 计算分页数据
        int fromIndexStatistics = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndexStatistics = Math.min(fromIndexStatistics + bo.getPageSize(), statisticsList.size());
        pageList.setRecords(statisticsList.subList(fromIndexStatistics, toIndexStatistics));

        dailyLoadUtilizationVo.setList(pageList);

        Page<DailyLoadUtilization> page = new Page<>(bo.getPageNum(), bo.getPageSize(), dailyLoadUtilizationList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), dailyLoadUtilizationList.size());
        page.setRecords(dailyLoadUtilizationList.subList(fromIndex, toIndex));

        dailyLoadUtilizationVo.setPage(page);
        return dailyLoadUtilizationVo;
    }

    /**
     * 最大日负荷利用率
     */
    @Override
    public MaxLoadUtilizationVo maxLoadUtilization(DailyLoadUtilizationBo bo) throws IOException, ParseException {
        List<MaxLoadUtilization> maxLoadUtilizationList = new ArrayList<>();
        //网格下所有线路
        List<DeviceFeeder> feederDeviceList = getFeederDevices(bo.getCode());

        List<Statistics> statisticsList = new ArrayList<>();
        List<Statistics> pancakeList = new ArrayList<>();
        Double summaryCapacity = 0.0;
        Double summaryRemainingCapacity = 0.0;
        //网格下所有主变
        List<DeviceNtHighTransformer> ntHighTransformerList = getNtHighTransformerList(bo.getCode());

        for (DeviceFeeder feederDevice : feederDeviceList) {
            MaxLoadUtilization maxLoadUtilization = new MaxLoadUtilization();
            Load loadMW = powerServiceImpl.selectFeeDerLoad(bo.getTime(), bo.getTime(), Collections.singletonList(feederDevice.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            OptionalDouble avgOpt = loadMW.getLoadList().stream()
                    .mapToDouble(Double::doubleValue) // String → double
                    .average();
            maxLoadUtilization.setCapacity(feederDevice.getFeederRateCapacity());
            maxLoadUtilization.setRemainingCapacity(DoubleFormatter.formatToThreeDecimals2(feederDevice.getFeederRateCapacity() * (1 - (avgOpt.getAsDouble() / 100))));
            maxLoadUtilization.setLoadMW(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble()));
            maxLoadUtilization.setSafetyMargin(DoubleFormatter.formatToThreeDecimals2((1 - ((maxLoadUtilization.getCapacity() - maxLoadUtilization.getRemainingCapacity()) / maxLoadUtilization.getCapacity()))) * 100);
            maxLoadUtilization.setName(feederDevice.getName());
            maxLoadUtilization.setTime(bo.getTime());
            summaryCapacity = summaryCapacity + maxLoadUtilization.getCapacity();
            summaryRemainingCapacity = summaryRemainingCapacity + maxLoadUtilization.getRemainingCapacity();

            Statistics statistics = new Statistics();
            statistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble())));
            statistics.setTypeName(feederDevice.getName());
            statisticsList.add(statistics);
            maxLoadUtilizationList.add(maxLoadUtilization);


        }
        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
            MaxLoadUtilization maxLoadUtilization = new MaxLoadUtilization();
            Load loadMW = powerServiceImpl.selectTransformerLoadMW(bo.getTime(), bo.getTime(), Collections.singletonList(ntHighTransformer.getPsrId()));
            if (loadMW == null) {
                continue;
            }
            OptionalDouble avgOpt = loadMW.getLoadList().stream()
                    .mapToDouble(Double::doubleValue) // String → double
                    .average();
            maxLoadUtilization.setCapacity(ntHighTransformer.getCapacity());
            maxLoadUtilization.setRemainingCapacity(DoubleFormatter.formatToThreeDecimals2(ntHighTransformer.getCapacity() * (1 - (avgOpt.getAsDouble() / 100))));
            maxLoadUtilization.setLoadMW(avgOpt.getAsDouble());
            maxLoadUtilization.setSafetyMargin(DoubleFormatter.formatToThreeDecimals2((1 - ((maxLoadUtilization.getCapacity() - maxLoadUtilization.getRemainingCapacity()) / maxLoadUtilization.getCapacity())) * 100));
            maxLoadUtilization.setName(ntHighTransformer.getName());
            maxLoadUtilization.setTime(bo.getTime());
            summaryCapacity = summaryCapacity + maxLoadUtilization.getCapacity();
            summaryRemainingCapacity = summaryRemainingCapacity + maxLoadUtilization.getRemainingCapacity();

            Statistics statistics = new Statistics();
            statistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(avgOpt.getAsDouble())));
            statistics.setTypeName(ntHighTransformer.getName());
            statisticsList.add(statistics);
            maxLoadUtilizationList.add(maxLoadUtilization);
        }

        MaxLoadUtilizationVo maxLoadUtilizationVo = new MaxLoadUtilizationVo();

        Page<Statistics> pageStatistics = new Page<>(bo.getPageNum(), bo.getPageSize(), statisticsList.size());
// 计算分页数据
        int fromIndexStatistics = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndexStatistics = Math.min(fromIndexStatistics + bo.getPageSize(), statisticsList.size());
        pageStatistics.setRecords(statisticsList.subList(fromIndexStatistics, toIndexStatistics));

        maxLoadUtilizationVo.setStatisticsList(pageStatistics);
        Statistics pancakeStatistics = new Statistics();
        pancakeStatistics.setTypeName("剩余容量");
        pancakeStatistics.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(summaryRemainingCapacity)));
        pancakeList.add(pancakeStatistics);

        Statistics pancakeStatistics1 = new Statistics();
        pancakeStatistics1.setTypeName("已用容量");
        pancakeStatistics1.setTypeNum(String.valueOf(DoubleFormatter.formatToThreeDecimals2(summaryCapacity - summaryRemainingCapacity)));
        pancakeList.add(pancakeStatistics1);

        maxLoadUtilizationVo.setPancakeList(pancakeList);
        Page<MaxLoadUtilization> page = new Page<>(bo.getPageNum(), bo.getPageSize(), maxLoadUtilizationList.size());
// 计算分页数据
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), maxLoadUtilizationList.size());
        page.setRecords(maxLoadUtilizationList.subList(fromIndex, toIndex));

        maxLoadUtilizationVo.setPage(page);
        return maxLoadUtilizationVo;

    }

    /**
     * 分批添加线路负载率测试假数据
     *
     * @return
     * @throws Exception
     */
    @Override
    public Boolean testFeeder() throws Exception {
        //所有线路
        List<DeviceFeeder> feederDeviceList = feederDeviceMapper.selectList();
        ClassPathResource resource = new ClassPathResource("负载率1.json");
        Load load = powerServiceImpl.selectLoadFeeder(resource.getFile().getAbsolutePath(), "负载率(%)");
        Load returnLoad = expandToHalfYear(load.getTimeList(), load.getLoadList(), 6);

        Map<String, List<Double>> returnLoadMap = groupByFullDate(returnLoad);
        insertFeederLoad(feederDeviceList, returnLoadMap, 1000);

        return true;
    }

    /**
     * 线路负载率分批策略
     *
     * @param feederDeviceList
     * @param returnLoadMap
     * @param batchSize
     * @throws Exception
     */
    public void insertFeederLoad(List<DeviceFeeder> feederDeviceList,
                                 Map<String, List<Double>> returnLoadMap,
                                 int batchSize) throws Exception {

        // 使用工作窃取线程池提高效率
        ExecutorService executor = Executors.newWorkStealingPool();

        // 按日期分批处理
        returnLoadMap.forEach((dateStr, values) -> {
            CompletableFuture.runAsync(() -> {
                List<FeederLoad> batch = new ArrayList<>(batchSize);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(dateStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                for (DeviceFeeder device : feederDeviceList) {
                    FeederLoad feederLoad = new FeederLoad();
                    feederLoad.setData(values.toString());
                    feederLoad.setRecordingTime(parsedDate);
                    feederLoad.setPsrId(device.getPsrId());

                    MaxAndIndex maxAndIndex = maxAndIndex(values, dateStr);
                    feederLoad.setMaxData(maxAndIndex.getMaxNum());
                    feederLoad.setMaxRecordingTime(maxAndIndex.getMaxTime());
                    feederLoad.setMinData(maxAndIndex.getMinNum());
                    feederLoad.setMinRecordingTime(maxAndIndex.getMinTime());

                    OptionalDouble avgOpt = values.stream()
                            .mapToDouble(Double::doubleValue) // String → double
                            .average();
                    feederLoad.setAvgData(avgOpt.getAsDouble());

                    batch.add(feederLoad);


                    // 达到批次大小时插入并清空
                    if (batch.size() >= batchSize) {
                        feederLoadMapper.insertBatch(batch);
                        batch.clear();
                    }
                }

                // 插入剩余记录
                if (!batch.isEmpty()) {
                    feederLoadMapper.insertBatch(batch);
                }
            }, executor);
        });

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);
    }


    /**
     * 分批添加线路功率测试假数据
     *
     * @return
     * @throws Exception
     */
    @Override
    public Boolean testFeederMW() throws Exception {
        //所有线路
        List<DeviceFeeder> feederDeviceList = feederDeviceMapper.selectList();
        ClassPathResource resource = new ClassPathResource("功率1.json");
        Load loadMW = powerServiceImpl.selectLoadFeeder(resource.getFile().getAbsolutePath(), "有功功率(MW)");
        Load returnLoadMW = expandToHalfYear(loadMW.getTimeList(), loadMW.getLoadList(), 6);


        Map<String, List<Double>> returnLoadMap = groupByFullDate(returnLoadMW);
        insertFeederLoadMW(feederDeviceList, returnLoadMap, 1000);

        return true;
    }

    /**
     * 线路功率分批策略
     *
     * @param feederDeviceList
     * @param returnLoadMap
     * @param batchSize
     * @throws Exception
     */
    public void insertFeederLoadMW(List<DeviceFeeder> feederDeviceList,
                                   Map<String, List<Double>> returnLoadMap,
                                   int batchSize) throws Exception {

        // 使用工作窃取线程池提高效率
        ExecutorService executor = Executors.newWorkStealingPool();

        // 按日期分批处理
        returnLoadMap.forEach((dateStr, values) -> {
            CompletableFuture.runAsync(() -> {
                List<FeederLoadMW> batch = new ArrayList<>(batchSize);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(dateStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                for (DeviceFeeder device : feederDeviceList) {
                    FeederLoadMW feederLoad = new FeederLoadMW();
                    feederLoad.setData(values.toString());
                    feederLoad.setRecordingTime(parsedDate);
                    feederLoad.setPsrId(device.getPsrId());


                    MaxAndIndex maxAndIndex = maxAndIndex(values, dateStr);
                    feederLoad.setMaxData(maxAndIndex.getMaxNum());
                    feederLoad.setMaxRecordingTime(maxAndIndex.getMaxTime());
                    feederLoad.setMinData(maxAndIndex.getMinNum());
                    feederLoad.setMinRecordingTime(maxAndIndex.getMinTime());

                    OptionalDouble avgOpt = values.stream()
                            .mapToDouble(Double::doubleValue) // String → double
                            .average();
                    feederLoad.setAvgData(avgOpt.getAsDouble());
                    batch.add(feederLoad);
                    // 达到批次大小时插入并清空
                    if (batch.size() >= batchSize) {
                        feederLoadMWMapper.insertBatch(batch);
                        batch.clear();
                    }
                }

                // 插入剩余记录
                if (!batch.isEmpty()) {
                    feederLoadMWMapper.insertBatch(batch);
                }
            }, executor);
        });

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);
    }


    /**
     * 分批添加主变负载率测试假数据
     *
     * @return
     * @throws Exception
     */
    @Override
    public Boolean testTransformer() throws Exception {
        //所有线路

        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList();
        ClassPathResource resource = new ClassPathResource("负载率1.json");
        Load load = powerServiceImpl.selectLoadFeeder( resource.getFile().getAbsolutePath(), "负载率(%)");
        Load returnLoad = expandToHalfYear(load.getTimeList(), load.getLoadList(), 6);

        Map<String, List<Double>> returnLoadMap = groupByFullDate(returnLoad);
        insertTransformerLoad(ntHighTransformerList, returnLoadMap, 1000);

        return true;
    }

    /**
     * 主变负载率分批策略
     *
     * @param ntHighTransformerList
     * @param returnLoadMap
     * @param batchSize
     * @throws Exception
     */
    public void insertTransformerLoad(List<DeviceNtHighTransformer> ntHighTransformerList,
                                      Map<String, List<Double>> returnLoadMap,
                                      int batchSize) throws Exception {

        // 使用工作窃取线程池提高效率
        ExecutorService executor = Executors.newWorkStealingPool();

        // 按日期分批处理
        returnLoadMap.forEach((dateStr, values) -> {
            CompletableFuture.runAsync(() -> {
                List<TransformerLoad> batch = new ArrayList<>(batchSize);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(dateStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    TransformerLoad transformerLoad = new TransformerLoad();
                    transformerLoad.setData(values.toString());
                    transformerLoad.setRecordingTime(parsedDate);
                    transformerLoad.setPsrId(ntHighTransformer.getPsrId());

                    MaxAndIndex maxAndIndex = maxAndIndex(values, dateStr);
                    transformerLoad.setMaxData(maxAndIndex.getMaxNum());
                    transformerLoad.setMaxRecordingTime(maxAndIndex.getMaxTime());
                    transformerLoad.setMinData(maxAndIndex.getMinNum());
                    transformerLoad.setMinRecordingTime(maxAndIndex.getMinTime());

                    OptionalDouble avgOpt = values.stream()
                            .mapToDouble(Double::doubleValue) // String → double
                            .average();
                    transformerLoad.setAvgData(avgOpt.getAsDouble());

                    batch.add(transformerLoad);

                    // 达到批次大小时插入并清空
                    if (batch.size() >= batchSize) {
                        transformerLoadMapper.insertBatch(batch);
                        batch.clear();
                    }
                }

                // 插入剩余记录
                if (!batch.isEmpty()) {
                    transformerLoadMapper.insertBatch(batch);
                }
            }, executor);
        });

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);
    }


    /**
     * 分批添加主变功率率测试假数据
     *
     * @return
     * @throws Exception
     */
    @Override
    public Boolean testTransformerMW() throws Exception {

        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList();
        ClassPathResource resource = new ClassPathResource("功率1.json");
        Load loadMW = powerServiceImpl.selectLoadFeeder( resource.getFile().getAbsolutePath(), "有功功率(MW)");
        Load returnLoadMW = expandToHalfYear(loadMW.getTimeList(), loadMW.getLoadList(), 6);

        Map<String, List<Double>> returnLoadMap = groupByFullDate(returnLoadMW);
        insertTransformerMWLoad(ntHighTransformerList, returnLoadMap, 1000);

        return true;
    }


    @Override
    public boolean synchronous() throws IOException {
        File file = ResourceUtils.getFile("E:\\java\\数据\\容量\\feeder.json");
        List<DeviceFeeder> feederDevice = objectMapper.readValue(file, new TypeReference<List<DeviceFeeder>>() {
        });
        List<DeviceFeeder> updateFeederDevice = new ArrayList<>();

//        for (FeederDevice device : feederDevice) {
//            FeederDevice update = new FeederDevice();
//            update.setPsrId(device.getAstId());
//            update.setSetCapacity(device.getFeederRateCapacity().toString());
//            updateFeederDevice.add(update);
//
//        }
//        feederDeviceMapper.updateBatch(updateFeederDevice);

        File file1 = ResourceUtils.getFile("E:\\java\\数据\\容量\\mainTransformer.json");
        List<DeviceNtHighTransformer> ntHighTransformerList = objectMapper.readValue(file1, new TypeReference<List<DeviceNtHighTransformer>>() {
        });
        List<DeviceNtHighTransformer> updateNtHighTransformerList = new ArrayList<>();

//        for (NtHighTransformer ntHighTransformer : ntHighTransformerList) {
//            NtHighTransformer update = new NtHighTransformer();
//            update.setPsrId(ntHighTransformer.getPsrId());
//            if(ntHighTransformer.getValue().split(" ")[0].equals("null")){
//                System.out.println(ntHighTransformer.getPsrId()+ntHighTransformer.getName());
//            }else {
//                update.setCapacity(Double.parseDouble(ntHighTransformer.getValue().split(" ")[0]));
//            }
//
//            updateNtHighTransformerList.add(update);
//
//        }
//        ntHighTransformerNewMapper.updateBatch(updateNtHighTransformerList);

        return false;
    }

    /**
     * 主变——负荷变化趋势展示和特征分析
     */
    @Override
    public LoadMWTrendAndAnalysisVo transformerLoadMWTrendAndAnalysis(AnalysisBo bo) throws ParseException, JsonProcessingException {

        Load loadMW = powerServiceImpl.selectTransformerLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getPsrId()));
        if(loadMW==null){
            return null;
        }
        LoadMWTrendAndAnalysisVo loadMWTrendAndAnalysisVo = new LoadMWTrendAndAnalysisVo();
        if (isMoreThan7Days(bo.getStrTime(), bo.getEndTime())) {

            loadMWTrendAndAnalysisVo.setActual(setStatistics7Days(loadMW, bo.getType()));
            return loadMWTrendAndAnalysisVo;
        } else {
            List<Statistics> statisticsList = setStatistics(loadMW);

            loadMWTrendAndAnalysisVo.setActual(statisticsList);
            return loadMWTrendAndAnalysisVo;
        }

    }


    /**
     * 线路——负荷变化趋势展示和特征分析
     */
    @Override
    public LoadMWTrendAndAnalysisVo feederLoadMWTrendAndAnalysis(AnalysisBo bo) throws ParseException, JsonProcessingException {

        Load loadMW = powerServiceImpl.selectFeederLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getPsrId()));
        if(loadMW==null){
            return null;
        }
        LoadMWTrendAndAnalysisVo loadMWTrendAndAnalysisVo = new LoadMWTrendAndAnalysisVo();
        if (isMoreThan7Days(bo.getStrTime(), bo.getEndTime())) {

            loadMWTrendAndAnalysisVo.setActual(setStatistics7Days(loadMW, bo.getType()));
            return loadMWTrendAndAnalysisVo;
        } else {
            List<Statistics> statisticsList = setStatistics(loadMW);

            loadMWTrendAndAnalysisVo.setActual(statisticsList);
            return loadMWTrendAndAnalysisVo;
        }

    }

    /**
     * 主变负荷导出
     * @param bo
     * @param response
     * @throws Exception
     */
    @Override
    public void transformerLoadMWExcel(LoadMWExcelBo bo, HttpServletResponse response) throws Exception {
        Load loadMW = powerServiceImpl.selectTransformerLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getId()));
        List<ExcelLoadMW> loadMWList = new ArrayList<>();
        if(loadMW==null){
            return;
        }
        List<Statistics> statisticsList = setStatistics(loadMW);
        for (Statistics statistics : statisticsList) {
            ExcelLoadMW excelLoadMW = new ExcelLoadMW();
            excelLoadMW.setSubstationName(bo.getParentName());
            excelLoadMW.setTransformerName(bo.getName());
            excelLoadMW.setTime(statistics.getTypeName());
            excelLoadMW.setLoadMW(statistics.getTypeNum());
            loadMWList.add(excelLoadMW);

        }
        // 2. 创建导出服务实例
        ExcelExportWithPOI exporter = new ExcelExportWithPOI();

        // 3. 执行导出
        try {
            exporter.transformerExportToResponse(loadMWList, response, bo.getParentName()+"-"+bo.getName()+"负荷数据报表");
        } catch (Exception e) {
            // 处理异常
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
        }
    }
    /**
     * 线路负荷导出
     * @param bo
     * @param response
     */
    @Override
    public void feederLoadMWExcel(LoadMWExcelBo bo, HttpServletResponse response) throws ParseException, JsonProcessingException {
        Load loadMW = powerServiceImpl.selectFeederLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getId()));
        List<ExcelLoadMW> loadMWList = new ArrayList<>();
        if(loadMW==null){
            return;
        }
        List<Statistics> statisticsList = setStatistics(loadMW);
        for (Statistics statistics : statisticsList) {
            ExcelLoadMW excelLoadMW = new ExcelLoadMW();
            excelLoadMW.setSubstationName(bo.getParentName());
            excelLoadMW.setTransformerName(bo.getName());
            excelLoadMW.setTime(statistics.getTypeName());
            excelLoadMW.setLoadMW(statistics.getTypeNum());
            loadMWList.add(excelLoadMW);

        }
        // 2. 创建导出服务实例
        ExcelExportWithPOI exporter = new ExcelExportWithPOI();

        // 3. 执行导出
        try {
            exporter.feederExportToResponse(loadMWList, response, bo.getName()+"负荷数据报表");
        } catch (Exception e) {
            // 处理异常
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            e.printStackTrace();
        }
    }
    /**
     * 主变功率分批策略
     *
     * @param ntHighTransformerList
     * @param returnLoadMap
     * @param batchSize
     * @throws Exception
     */
    public void insertTransformerMWLoad(List<DeviceNtHighTransformer> ntHighTransformerList,
                                        Map<String, List<Double>> returnLoadMap,
                                        int batchSize) throws Exception {

        // 使用工作窃取线程池提高效率
        ExecutorService executor = Executors.newWorkStealingPool();

        // 按日期分批处理
        returnLoadMap.forEach((dateStr, values) -> {
            CompletableFuture.runAsync(() -> {
                List<TransformerLoadMW> batch = new ArrayList<>(batchSize);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(dateStr);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    TransformerLoadMW transformerLoadMW = new TransformerLoadMW();
                    transformerLoadMW.setData(values.toString());
                    transformerLoadMW.setRecordingTime(parsedDate);
                    transformerLoadMW.setPsrId(ntHighTransformer.getPsrId());

                    MaxAndIndex maxAndIndex = maxAndIndex(values, dateStr);
                    transformerLoadMW.setMaxData(maxAndIndex.getMaxNum());
                    transformerLoadMW.setMaxRecordingTime(maxAndIndex.getMaxTime());
                    transformerLoadMW.setMinData(maxAndIndex.getMinNum());
                    transformerLoadMW.setMinRecordingTime(maxAndIndex.getMinTime());

                    OptionalDouble avgOpt = values.stream()
                            .mapToDouble(Double::doubleValue) // String → double
                            .average();
                    transformerLoadMW.setAvgData(avgOpt.getAsDouble());
                    batch.add(transformerLoadMW);

                    // 达到批次大小时插入并清空
                    if (batch.size() >= batchSize) {
                        transformerLoadMWMapper.insertBatch(batch);
                        batch.clear();
                    }
                }

                // 插入剩余记录
                if (!batch.isEmpty()) {
                    transformerLoadMWMapper.insertBatch(batch);
                }
            }, executor);
        });

        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.HOURS);
    }


    private static void setAvailabilityInterval(DailyLoadUtilization dailyLoadUtilization) {
        if (dailyLoadUtilization.getAvailability() >= 95) {
            dailyLoadUtilization.setAvailabilityInterval("≥95%");
            dailyLoadUtilization.setState("过载（危险）");
        }
        if (dailyLoadUtilization.getAvailability() >= 85 && dailyLoadUtilization.getAvailability() < 95) {
            dailyLoadUtilization.setAvailabilityInterval("85%~95%");
            dailyLoadUtilization.setState("高负荷（预警）");
        }
        if (dailyLoadUtilization.getAvailability() >= 65 && dailyLoadUtilization.getAvailability() < 85) {
            dailyLoadUtilization.setAvailabilityInterval("65%~85%");
            dailyLoadUtilization.setState("高效安全（最佳）");
        }
        if (dailyLoadUtilization.getAvailability() >= 40 && dailyLoadUtilization.getAvailability() < 65) {
            dailyLoadUtilization.setAvailabilityInterval("40%~65%");
            dailyLoadUtilization.setState("合理低载");
        }
        if (dailyLoadUtilization.getAvailability() < 40) {
            dailyLoadUtilization.setAvailabilityInterval("<40%");
            dailyLoadUtilization.setState("轻载（低效）");
        }
    }


    /**
     * 封装曲线图
     *
     * @param load
     * @return
     */
    private List<Statistics> setStatistics(Load load) {
        return IntStream.range(0, Math.min(load.getLoadList().size(), load.getTimeList().size()))
                .mapToObj(i -> new Statistics(load.getTimeList().get(i),
                        String.valueOf(DoubleFormatter.formatToThreeDecimals2(load.getLoadList().get(i)))))
                .sorted(Comparator.comparing(stat -> {
                    // 提取yyyy-MM-dd部分并转换为LocalDate比较
                    String[] parts = stat.getTypeName().split(" "); // 分割日期和时间
                    return LocalDate.parse(parts[0]); // 解析日期部分
                }))
                .collect(Collectors.toList());
    }

    /**
     * 封装超过7天的曲线图
     *
     * @param load
     * @return
     */
    private List<Statistics> setStatistics7Days(Load load, Integer type) {
        List<Statistics> statisticsList = new ArrayList<>();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        List<String> result = new ArrayList<>();
        for (String dateTimeStr : load.getMinRecordingTimeList()) {
            // 解析原始字符串为 LocalDateTime
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, inputFormatter);
            // 格式化为 yyyy-MM-dd
            String dateOnlyStr = dateTime.format(outputFormatter);
            result.add(dateOnlyStr);
        }

        if (type == 0) {
            statisticsList = IntStream.range(0, Math.min(load.getMinDataList().size(), result.size()))
                    .mapToObj(i -> new Statistics(result.get(i), String.valueOf(DoubleFormatter.formatToThreeDecimals2(load.getMinDataList().get(i)))))
                    .collect(Collectors.toList());
        }

        if (type == 1) {
            statisticsList = IntStream.range(0, Math.min(load.getAvgDataList().size(), result.size()))
                    .mapToObj(i -> new Statistics(result.get(i), String.valueOf(DoubleFormatter.formatToThreeDecimals2(load.getAvgDataList().get(i)))))
                    .collect(Collectors.toList());
        }
        if (type == 2) {

            statisticsList = IntStream.range(0, Math.min(load.getMaxDataList().size(), result.size()))
                    .mapToObj(i -> new Statistics(result.get(i), String.valueOf(DoubleFormatter.formatToThreeDecimals2(load.getMaxDataList().get(i)))))
                    .collect(Collectors.toList());
        }

        return statisticsList;
    }


    private static void setSubstationSummaryVo(int normalCount, int total, List<SubstationSummaryVo> summaryList, String type) {
        SubstationSummaryVo substationSummaryVo = new SubstationSummaryVo();
        substationSummaryVo.setCount(normalCount);
        substationSummaryVo.setProportion(DoubleFormatter.formatToThreeDecimals((double) normalCount / total * 100));
        substationSummaryVo.setFieldValue(type);
        summaryList.add(substationSummaryVo);
    }

    /**
     * 计算负载率的最大值，最小值以及对应发生的时间段
     *
     * @param load
     */
    private static String maxLoad(Load load) {
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, load.getLoadList().size())
                .reduce((a, b) -> load.getLoadList().get(a) >= load.getLoadList().get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            return load.getLoadList().get(maxIndex).toString();
        }
        return null;
    }

    /**
     * 计算负载率的最大值，最小值以及对应发生的时间段
     *
     * @param load
     */
    private static MaxAndIndex maxAndIndex(Load load) {
        MaxAndIndex maxAndIndex = new MaxAndIndex();
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, load.getLoadList().size())
                .reduce((a, b) -> load.getLoadList().get(a) >= load.getLoadList().get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            Double maxValue = load.getLoadList().get(maxIndex);
            maxAndIndex.setMaxNum(maxValue);
            maxAndIndex.setMaxTime(load.getTimeList().get(maxIndex));
        }


        OptionalInt minIndexOpt = IntStream.range(0, load.getLoadList().size())
                .reduce((a, b) -> load.getLoadList().get(a) <= load.getLoadList().get(b) ? a : b);

        if (minIndexOpt.isPresent()) {
            int minIndex = minIndexOpt.getAsInt();
            Double maxValue = load.getLoadList().get(minIndex);
            maxAndIndex.setMinNum(maxValue);
            maxAndIndex.setMinTime(load.getTimeList().get(minIndex));
        }
        return maxAndIndex;
    }

    /**
     * 计算最大值
     */
    private static String max(List<String> stringList) {
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, stringList.size())
                .reduce((a, b) -> Double.parseDouble(stringList.get(a)) >= Double.parseDouble(stringList.get(b)) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            return stringList.get(maxIndex);
        }
        return null;
    }

    /**
     * 计算最大值
     */
    private static String min(List<String> stringList) {
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, stringList.size())
                .reduce((a, b) -> Double.parseDouble(stringList.get(a)) <= Double.parseDouble(stringList.get(b)) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            return stringList.get(maxIndex);
        }
        return null;
    }

    /**
     * 计算最大值
     */
    private static Double maxToDouble(List<Double> stringList) {
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, stringList.size())
                .reduce((a, b) -> stringList.get(a) >= stringList.get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            return stringList.get(maxIndex);
        }
        return null;
    }

    /**
     * 计算最大值
     */
    private static Double minToDouble(List<Double> stringList) {
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, stringList.size())
                .reduce((a, b) -> stringList.get(a) <= stringList.get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            return stringList.get(maxIndex);
        }
        return null;
    }


    private static MaxAndIndex maxAndIndex(List<Double> doubles, String time) {
        MaxAndIndex maxAndIndex = new MaxAndIndex();
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, doubles.size())
                .reduce((a, b) -> doubles.get(a) >= doubles.get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            Double maxValue = doubles.get(maxIndex);
            maxAndIndex.setMaxNum(maxValue);
            TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(maxIndex);  // 直接使用枚举常量
            String timeStr = timeEnum288.getTimeString();
            maxAndIndex.setMaxTime(time + " " + timeStr);
        }


        OptionalInt minIndexOpt = IntStream.range(0, doubles.size())
                .reduce((a, b) -> doubles.get(a) <= doubles.get(b) ? a : b);

        if (minIndexOpt.isPresent()) {
            int minIndex = minIndexOpt.getAsInt();
            Double maxValue = doubles.get(minIndex);
            maxAndIndex.setMinNum(maxValue);
            TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(minIndex);  // 直接使用枚举常量
            String timeStr = timeEnum288.getTimeString();
            maxAndIndex.setMinTime(time + " " + timeStr);
        }
        return maxAndIndex;
    }


    // 基于完整日期的分组方法（Stream API版）
    public static TimeLoadGroup groupByFullDateWithStream(Load load) {
        validateInput(load);
        Map<String, List<Double>> loadMap = IntStream.range(0, load.getTimeList().size())
                .boxed()
                .collect(Collectors.groupingBy(
                        i -> extractDateKey(load.getTimeList().get(i)),
                        LinkedHashMap::new,
                        Collectors.mapping(load.getLoadList()::get, Collectors.toList())
                ));
        Map<String, List<String>> timeMap = IntStream.range(0, load.getTimeList().size())
                .boxed()
                .collect(Collectors.groupingBy(
                        i -> extractDateKey(load.getTimeList().get(i)),
                        LinkedHashMap::new,
                        Collectors.mapping(load.getTimeList()::get, Collectors.toList())
                ));
        TimeLoadGroup timeLoadGroup = new TimeLoadGroup();
        timeLoadGroup.setLoadMap(loadMap);
        timeLoadGroup.setTimeMap(timeMap);

        return timeLoadGroup;
    }

    public static Map<String, List<Double>> groupByFullDate(Load load) {
        validateInput(load);

        return IntStream.range(0, load.getTimeList().size())
                .boxed()
                .collect(Collectors.groupingBy(
                        i -> extractDateKey(load.getTimeList().get(i)),
                        LinkedHashMap::new,
                        Collectors.mapping(load.getLoadList()::get, Collectors.toList())
                ));

    }

    // 提取完整日期键（格式：yyyy-MM-dd）
    private static String extractDateKey(String timeStr) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(timeStr,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return dateTime.format(formatter);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid time format: " + timeStr);
        }
    }

    // 输入数据校验
    private static void validateInput(Load load) {
        if (load.getTimeList().size() != load.getLoadList().size()) {
            throw new IllegalArgumentException("Time and load lists must have same size");
        }
    }


}


