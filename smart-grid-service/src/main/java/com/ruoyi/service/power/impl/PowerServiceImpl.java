package com.ruoyi.service.power.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.common.utils.util.MercatorLineCircleIntersection;
import com.ruoyi.common.utils.util.StringByDate;
import com.ruoyi.constant.ResourceStatisticsDataType;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.bo.DeviceFeederTransformerVolBo;
import com.ruoyi.entity.device.vo.DeviceApiCollector;
import com.ruoyi.entity.power.*;
import com.ruoyi.entity.power.bo.LoadBo;
import com.ruoyi.entity.power.enuw.LoadStatus;
import com.ruoyi.entity.power.enuw.TimeEnum288;
import com.ruoyi.entity.power.select.LoadData;
import com.ruoyi.entity.power.select.SelectLoadFeeder;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.ProblemGrid;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.Statistics;
import com.ruoyi.mapper.device.DeviceApiJsonMapper;
import com.ruoyi.mapper.device.DeviceFeederCableMapper;
import com.ruoyi.mapper.device.DeviceFeederJkMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.power.MiddleSubstationNewMapper;
import com.ruoyi.mapper.power.NtHighTransformerNewMapper;
import com.ruoyi.mapper.power.PowerMapper;
import com.ruoyi.service.power.IPowerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class PowerServiceImpl implements IPowerService {

    @Autowired
    PowerMapper powerMapper;


    @Autowired
    MiddleSubstationNewMapper middleSubstationNewMapper;

    @Autowired
    NtHighTransformerNewMapper ntHighTransformerNewMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;
    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Autowired
    DeviceApiJsonMapper deviceApiJsonMapper;

    private final ObjectMapper objectMapper;

    public PowerServiceImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }


    public void split() throws JsonProcessingException {
//        List<ProblemGrid> problemGrids = feederDeviceMapper.selectAll();
        List<String> strings = Arrays.asList(
        "JS-NJ-GC-ZQZ",
        "JS-NJ-QX-QXS",
        "JS-NJ-LS-DP",
        "JS-NJ-JBXQ-YJC",
        "JS-NJ-JBXQ-SWC",
        "JS-NJ-GC-GCZ",
        "JS-NJ-JBXQ-JKC",
        "JS-NJ-QH-XJKXB",
        "JS-NJ-JN-KXYDB",
        "JS-NJ-LS-JQ",
        "JS-NJ-QX-ZDHX",
        "JS-NJ-LS-HF",
        "JS-NJ-JN-KXYXB",
        "JS-NJ-LS-ZT",
        "JS-NJ-JN-CH",
        "JS-NJ-LH-LC",
        "JS-NJ-JN-QLKCY",
        "JS-NJ-YH-NZ",
        "JS-NJ-PK-QL",
        "JS-NJ-JBXQ-DC");

        final int[] count = {1};
        for (String  psrId : strings) {
//            final String psrId = problemGrid.getPsrId();

            try {
                log.info("开始处理网格ID: {}, 当前第{}个", psrId, count[0]);
                long startTime = System.currentTimeMillis();

                String json = powerMapper.selectDeviceApiJson(psrId);
                if (json == null) {
                    return;
                }
                //资源统计基础数据
                PowerGridVo power = selectPower(psrId);
                //高压主变统计数据
                TransformerFoundationVo transformerFoundationVo = transformerFoundation(psrId);
                //高压中压线路资源
                FeederFoundationVo feederFoundationVo = feederFoundation(psrId);
                //主变配变资源
                PublicSpecializedVo publicSpecializedVo = mediumPublicSpecialized(psrId);
                if (power != null) {
                    Integer selectCount = deviceApiJsonMapper.selectCount(psrId);
                    if (selectCount > 1) {
                        deviceApiJsonMapper.deleteGrodCode(psrId);
                    }
                    //存储基础数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.BASE, objectMapper.writeValueAsString(power));
                    //存储高压数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.HIGHVOLTAGE, objectMapper.writeValueAsString(transformerFoundationVo));
                    //存储中压数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.MEDIUDVOLTAGE, objectMapper.writeValueAsString(publicSpecializedVo));
                    //存储线路数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.FEEDER, objectMapper.writeValueAsString(feederFoundationVo));
                    //存储柱上变公变数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.PUBZSBLIST, objectMapper.writeValueAsString(publicSpecializedVo.getPubDevicePoleTransformers()));
                    //存储柱上变专变数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.PRVZSBLIST, objectMapper.writeValueAsString(publicSpecializedVo.getPrvDevicePoleTransformers()));
                    //存储站内变公变数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.PUBZNBLIST, objectMapper.writeValueAsString(publicSpecializedVo.getPubDeviceStationTransformers()));
                    //存储站内变专变数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.PRVZNBLIST, objectMapper.writeValueAsString(publicSpecializedVo.getPrvDeviceStationTransformers()));
                    //存储主变数据
                    deviceApiJsonMapper.insertPowerGridVo(psrId, ResourceStatisticsDataType.ZBLIST, objectMapper.writeValueAsString(transformerFoundationVo.getZbList()));
                    log.info("成功处理网格ID: {}, 耗时: {}ms", psrId, System.currentTimeMillis() - startTime);
                } else {
                    log.warn("网格ID: {} 查询结果为空", psrId);
                }

                count[0]++;
            } catch (Exception e) {
                log.error("处理网格ID: {} 时发生异常", psrId, e);
            }

        }
        log.info("所有网格数据处理完成");
    }


    /**
     * 查询电力线路资源信息
     */
    @Override
//    public PowerGridVo selectPower(String code) {
//
//        PowerGridVo powerLineVo = new PowerGridVo();
////        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(code);
//        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
//        try {
//             deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
//        } catch (Exception e){
//            return null;
//        }
//
//        List<DeviceFeeder> dmsFeederDeviceList = deviceApiCollector.getDeviceFeeders();
//        //空判断
//        if (CollectionUtils.isEmpty(dmsFeederDeviceList)) {
//            // List为null或空的处理逻辑
//            return null;
//        }
//        //线路供电区域
//        powerLineVo.setSupplyArea(dmsFeederDeviceList.stream()
//                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
//                .distinct()                     // 去重
//                .collect(Collectors.joining(", ")));
//
//        //总长度
//        Double lineLength = dmsFeederDeviceList.stream()
//                // 提取BigDecimal字段
//                .mapToDouble(DeviceFeeder::getLength)
//                // 过滤空值（如果字段可能为null）
//                .filter(Objects::nonNull)
//                // 累加计算，初始值为0，使用BigDecimal加法
//                .sum();
//        //电缆长度
//        Double cableLength = dmsFeederDeviceList.stream()
//                // 提取BigDecimal字段
//                .mapToDouble(DeviceFeeder::getCableLength)
//                // 过滤空值（如果字段可能为null）
//                .filter(Objects::nonNull)
//                // 累加计算，初始值为0，使用BigDecimal加法
//                .sum();
//
//        //电缆转化率
//        Double cableConversion = DoubleFormatter.formatToThreeDecimals(cableLength / lineLength);
//
//        //封装对象
//        powerLineVo.setLineNum(dmsFeederDeviceList.size());
//        powerLineVo.setLineLength(lineLength);
//        powerLineVo.setCableConversion(cableConversion);
//
//
//        //环网柜
////        Integer substationHWG = powerMapper.selectSubstationHWG(code);
//        Integer substationHWG = deviceApiCollector.getHwgList().size();
//
//        //柱上变数量
//        Integer poleTransformer = deviceApiCollector.getPoleTransformerList().size();
//        //配电室
////        Integer substationPDS = powerMapper.selectSubstationPDS(code);
//        Integer substationPDS = deviceApiCollector.getPdsList().size();
//
//        List<String> lineIdList = dmsFeederDeviceList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
//        //变电站内隔离开关
////        Integer stationIsolateKg = powerMapper.selectStationIsolateKg(lineIdList);
//        //变电站内负荷开关
////        Integer stationLoadKg = powerMapper.selectStationLoadKg(lineIdList);
//        //开关站
////        Integer SubstationKGZ = powerMapper.selectSubstationKGZ(lineIdList);
//        Integer SubstationKGZ = deviceApiCollector.getKgzList().size();
//
//        powerLineVo.setSwitchStations(SubstationKGZ);
//        powerLineVo.setPoleSwitches(poleTransformer);
//        powerLineVo.setDistributionRoomNum(substationPDS);
//        powerLineVo.setRingMainUnitNum(substationHWG);
//
//        //公变专变
////        List<PBEntity> feederTransformerVols = powerMapper.selectTransformersByFeedId(lineIdList);
//
////        if (CollectionUtils.isNotEmpty(dmsFeederDeviceList)) {
////            //公变数量
////            int count = (int) feederTransformerVols.stream()
////                    .filter(e -> e.getPubPrivFlag().equals("0"))
////                    .count();
////            powerLineVo.setPubNum(count);
////
////            Map<String, Double> typeSumMap = feederTransformerVols.stream()
////                    .filter(e -> e.getPubPrivFlag() != null && e.getRatedCapacity() != null)
////                    .collect(Collectors.groupingBy(
////                            PBEntity::getPubPrivFlag,
////                            Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))
////                    ));
////            powerLineVo.setPubRatedCapacity(typeSumMap.get("0"));
////            powerLineVo.setPrivRatedCapacity(typeSumMap.get("1"));
////
////            //专变数量
////            powerLineVo.setPrivNum(feederTransformerVols.size() - count);
////        }
//        //01专变，03公变
//        //柱上变公变数量
//        int polePubCount = (int) deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("03")).count();
//        //柱上变专变数量
//        int polePrvCount = (int) deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("01")).count();
//
//        //柱上变容量
//        Map<String, Double> poleTypeSumMap = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DevicePoleTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//
////        //所有变公变数量
////        int servicePubCount = (int) deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).count();
////        //所有变专变数量
////        int servicePrvCount = (int) deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).count();
////
////        //所有变容量
////        Map<String, Double> serviceTypeSumMap = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(StationServiceTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//
//        //站内变公变数量
//        int stationPubCount = (int) deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).count();
//        //站内变专变数量
//        int stationPrvCount = (int) deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).count();
//
//        //站内变容量
//        Map<String, Double> stationTypeSumMap = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DeviceStationTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
//        //公变数量
//        powerLineVo.setPubNum(polePubCount + stationPubCount);
//        //专变数量
//        powerLineVo.setPrivNum(polePrvCount + stationPrvCount);
//        //公变容量
//        powerLineVo.setPubRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("03") == null ? 0.0 : poleTypeSumMap.get("03")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("03") == null ? 0.0 : stationTypeSumMap.get("03")));
//        //专变容量
//        powerLineVo.setPrivRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("01") == null ? 0 : poleTypeSumMap.get("01")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("01") == null ? 0.0 : stationTypeSumMap.get("01")));
//
//        return powerLineVo;
//    }
    public PowerGridVo selectPower(String code){
        PowerGridVo powerGridVo;
        try {
            powerGridVo = objectMapper.readValue(deviceApiJsonMapper.selectPowerGridVo(code,ResourceStatisticsDataType.BASE), PowerGridVo.class);
        } catch (Exception e) {
            return null;
        }
        return powerGridVo;
    }

    /**
     * 根据网格编码查询该网格下的变电站
     */
    @Override
    public SubstationVo selectSubstation(String code, String strTime, String endTime) throws IOException, ParseException {

        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        SubstationVo substationVo = new SubstationVo();
        substationVo.setNum(inSubstationList.size());
        //总容量
        substationVo.setTotalCapacity(inSubstationList.stream().mapToDouble(DeviceSubstation::getStationCapacity).sum());
        //总主变台数
        substationVo.setTransformerQuantity(inSubstationList.stream().mapToLong(DeviceSubstation::getTransformerQuantity).sum());
        //主变容量图
        long totalCount = inSubstationList.size();
        // 2. 统计每个值的出现次数
        Map<Double, Long> countByValue = inSubstationList.stream().collect(Collectors.groupingBy(DeviceSubstation::getStationCapacity, Collectors.counting()));
        // 3. 生成统计结果实体列表
        List<SubstationSummaryVo> results = countByValue.entrySet().stream().map(entry -> new SubstationSummaryVo(entry.getKey().toString(), entry.getValue(), Math.round((double) entry.getValue() / totalCount * 100) / 100.0

        )).collect(Collectors.toList());
        substationVo.setSummaryList(results);
        List<String> ntHighTransformerPsrIdList = selectNtHighTransformer(code).stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList());
        Load load = selectTransformerLoad(strTime, endTime, ntHighTransformerPsrIdList);
        if (load == null) {
            return null;
        } else {

            OptionalDouble averageOpt = load.getLoadList().stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue) // String → double
                    .average(); // 计算平均值
            substationVo.setLoad(DoubleFormatter.formatToThreeDecimals(averageOpt.getAsDouble()));
            return substationVo;
        }

    }


    /**
     * 网格资源——典型日负荷数据展示，
     */

    @Override
    public LoadVo selectLoad(LoadBo bo) throws IOException, ParseException {
        //前置，将bo里面的时间转化成负荷要求的格式


        //取出主变的功率数据
        Load loadMW = selectTransformerLoadMW(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getLoadId()));
        //取出主变的负载数据
        Load load = selectTransformerLoad(bo.getStrTime(), bo.getEndTime(), Collections.singletonList(bo.getLoadId()));
        //返回的实体
        LoadVo loadVo = new LoadVo();


        //假数据，等后期接入真实数据
        MaxAndIndex maxAndIndex = maxAndIndex(loadMW);
        loadVo.setMaxLoad(maxAndIndex.getMaxNum());
        loadVo.setMaxLoadTime(maxAndIndex.getMaxTime());
        loadVo.setMinLoad(maxAndIndex.getMinNum());
        loadVo.setMinLoadTime(maxAndIndex.getMinTime());
        loadVo.setDifferenceLoad(DoubleFormatter.formatToThreeDecimals(loadVo.getMaxLoad() - loadVo.getMinLoad()));
        loadVo.setDifferenceLoadGrowthRate(DoubleFormatter.formatToThreeDecimals((loadVo.getMaxLoad() - loadVo.getMinLoad()) / loadVo.getMaxLoad()));

        OptionalDouble averageOpt = load.getAvgDataList().stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue) // String → double
                .average(); // 计算平均值

        loadVo.setAvgLoad(DoubleFormatter.formatToThreeDecimals(averageOpt.getAsDouble()));

        OptionalDouble avgOpt = loadMW.getAvgDataList().stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue) // String → double
                .average(); // 计算平均值

        loadVo.setAvgLoadMW(DoubleFormatter.formatToThreeDecimals(avgOpt.getAsDouble()));

        List<Statistics> statistics = IntStream.range(0, Math.min(loadMW.getLoadList().size(), loadMW.getTimeList().size())).mapToObj(i -> new Statistics(loadMW.getTimeList().get(i), loadMW.getLoadList().get(i).toString())).collect(Collectors.toList());
        loadVo.setCurveList(statistics);
        return loadVo;
    }


    public Load selectLoadFeeder(String type, String name) throws IOException {
        File file = ResourceUtils.getFile(type);
        SelectLoadFeeder selectLoadFeeder = objectMapper.readValue(file, SelectLoadFeeder.class);
        Load load = new Load();
        for (LoadData series : selectLoadFeeder.getResult().getSeries()) {
            if (series.getName().equals(name)) {
                load.setLoadList(series.getData().stream().map(d -> d == null ? 0.0 : d).collect(Collectors.toList()));
            }
        }

        load.setTimeList(selectLoadFeeder.getResult().getAxis().getData());
        return load;
    }

    /**
     * 网格资源——查询网格下变电站的下拉列表，
     */
    @Override
    public List<PullDownMenuStringSon> pullDownMenuSubstation(String code) {
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        List<PullDownMenuStringSon> pullDownMenuSubstation = new ArrayList<>(inSubstationList.stream().filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getName() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(DeviceSubstation::getName, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getPsrId(), o.getName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                )).values());
        return pullDownMenuSubstation;
    }


    /**
     * 网格资源——查询变电站下的主变的下拉列表，
     */
    @Override
    public List<PullDownMenuStringSon> pullDownMenuTransformer(String code) {
        QueryWrapper<DeviceNtHighTransformer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station", code);
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(queryWrapper);
        List<PullDownMenuStringSon> pullDownMenuTransformer = new ArrayList<>(ntHighTransformerList.stream().filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getName() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(DeviceNtHighTransformer::getName, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getPsrId(), o.getName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                )).values());
        return pullDownMenuTransformer;
    }


    /**
     * 主变运行状况评价
     *
     * @param code
     * @return
     */
    @Override
    public TransformerVo transformerState(String code, String strTime, String endTime) throws IOException, ParseException {
        TransformerVo transformerVo = new TransformerVo();
        List<TransformerStateVo> transformerStateVoList = new ArrayList<>();
        //变电站
        int normalNum = 0;
        int heavyLoadNum = 0;
        int overLoadNum = 0;
        int seriousOverLoadNum = 0;
        int underLoadNum = 0;
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(DeviceSubstation::getPsrId).distinct().collect(Collectors.toList()));
        Map<String, String> substationNewMap = inSubstationList.stream().collect(Collectors.toMap(DeviceSubstation::getPsrId, DeviceSubstation::getName));
        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);

        for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {

            TransformerStateVo transformerStateVo = new TransformerStateVo();
            transformerStateVo.setSubstationName(substationNewMap.get(ntHighTransformer.getStation()));
            //将所有主变相关的负载数据查出来
            //取出主变的负载数据
            Load loadMW = selectTransformerLoadMW(strTime, endTime, selectNtHighTransformer(code).stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));
            //取出主变的负载数据
            Load load = selectTransformerLoad(strTime, endTime, selectNtHighTransformer(code).stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));
            if (loadMW == null || load == null) {
                continue;
            }
            transformerStateVo.setTransformerQuantityName(ntHighTransformer.getName());
            transformerStateVo.setLoadState(ntHighTransformer.getLoadState());
            transformerStateVo.setCapacity(ntHighTransformer.getCapacity());
            MaxAndIndex maxAndIndexLoadMW = maxAndIndex(loadMW);
            MaxAndIndex maxAndIndexLoad = maxAndIndex(load);
            transformerStateVo.setMaxLoadMW(String.valueOf(DoubleFormatter.formatToThreeDecimals2(maxAndIndexLoadMW.getMaxNum())));
            transformerStateVo.setLoad(String.valueOf(DoubleFormatter.formatToThreeDecimals2(maxAndIndexLoad.getMaxNum())));

            transformerStateVo.setTime(maxAndIndexLoad.getMaxTime());

            transformerStateVoList.add(transformerStateVo);

            LoadStatus status = LoadStatus.fromLoadRate(load.getMaxDataList().get(0));
            transformerStateVo.setLoadState(status.getName());
            transformerStateVo.setLoadStateInterval(status.action);

            if (Double.parseDouble(transformerStateVo.getLoad()) > 100) {
                seriousOverLoadNum = seriousOverLoadNum + 1;
            } else if (Double.parseDouble(transformerStateVo.getLoad()) >= 90 && Double.parseDouble(transformerStateVo.getLoad()) <= 100) {
                overLoadNum = overLoadNum + 1;
            } else if (Double.parseDouble(transformerStateVo.getLoad()) >= 60 && Double.parseDouble(transformerStateVo.getLoad()) < 90) {
                heavyLoadNum = heavyLoadNum + 1;
            } else if (Double.parseDouble(transformerStateVo.getLoad()) >= 30 && Double.parseDouble(transformerStateVo.getLoad()) < 60) {
                normalNum = normalNum + 1;
            } else if (Double.parseDouble(transformerStateVo.getLoad()) >= 0 && Double.parseDouble(transformerStateVo.getLoad()) < 30) {
                underLoadNum = underLoadNum + 1;
            }

        }
        int total = normalNum + heavyLoadNum + overLoadNum + seriousOverLoadNum + underLoadNum;

        if (normalNum > 0) {
            transformerVo.setNormalNum(normalNum);
            transformerVo.setNormalAccountFor(DoubleFormatter.formatToThreeDecimals2((double) normalNum / total));
        }
        if (heavyLoadNum > 0) {
            transformerVo.setHeavyLoadNum(heavyLoadNum);
            transformerVo.setHeavyLoadAccountFor(DoubleFormatter.formatToThreeDecimals2((double) heavyLoadNum / total));
        }
        if (overLoadNum > 0) {
            transformerVo.setOverLoadNum(overLoadNum);
            transformerVo.setOverLoadAccountFor(DoubleFormatter.formatToThreeDecimals2((double) overLoadNum / total));
        }
        if (seriousOverLoadNum > 0) {
            transformerVo.setSeriousOverLoadNum(seriousOverLoadNum);
            transformerVo.setSeriousOverLoadAccountFor(DoubleFormatter.formatToThreeDecimals2((double) seriousOverLoadNum / total));
        }
        if (underLoadNum > 0) {
            transformerVo.setUnderLoadNum(underLoadNum);
            transformerVo.setUnderLoadAccountFor(DoubleFormatter.formatToThreeDecimals2((double) underLoadNum / total));
        }
        transformerVo.setList(transformerStateVoList);
        return transformerVo;
    }


    /**
     * 查询网格下的所有变电站以及主变
     */
    @Override
    public Page<SubAndTransformerVo> subAndTransformer(String code, Integer pageNum, Integer pageSize) {
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(DeviceSubstation::getPsrId).distinct().collect(Collectors.toList()));

        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        List<SubAndTransformerVo> list = new ArrayList<>();
        for (DeviceSubstation middleSubstationNew : inSubstationList) {
            for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                if (middleSubstationNew.getPsrId().equals(ntHighTransformer.getStation())) {
                    SubAndTransformerVo subAndTransformerVo = new SubAndTransformerVo();
                    subAndTransformerVo.setSubId(middleSubstationNew.getPsrId());
                    subAndTransformerVo.setSubName(middleSubstationNew.getName());
                    subAndTransformerVo.setTransformerId(ntHighTransformer.getPsrId());
                    subAndTransformerVo.setTransformerName(ntHighTransformer.getName());
                    list.add(subAndTransformerVo);
                }
            }
        }
        Page<SubAndTransformerVo> page = new Page<>(pageNum, pageSize, list.size());

// 计算分页数据
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, list.size());
        page.setRecords(list.subList(fromIndex, toIndex));

// 返回分页结果
        return page;

    }


    @Override
    public R<Void> saveOrderFromJsonString(String json) throws IOException {

        File file = ResourceUtils.getFile("E:\\java\\数据\\station.json");
        List<DeviceSubstation> parents = objectMapper.readValue(file, new TypeReference<List<DeviceSubstation>>() {
        });
        // 设置关联关系
        parents.forEach(parent -> {
            if (parent.getMainTransformer() != null) {
                parent.getMainTransformer().forEach(child -> child.setMiddleSubstationNew(parent));
            }
        });

        List<DeviceNtHighTransformer> addNtHighTransformer = new ArrayList<>();
        for (DeviceSubstation parent : parents) {
            if (parent.getMainTransformer() != null) {
                addNtHighTransformer.addAll(parent.getMainTransformer());
            }

        }
//        middleSubstationNewMapper.insertBatch(parents);
        ntHighTransformerNewMapper.insertBatch(addNtHighTransformer);


        return null;
    }

    /**
     * 网格资源——查询网格下的所有变电站坐标
     */
    @Override
    public List<PullDownMenuStringSon> substationGeoPositon(String code) {
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        List<PullDownMenuStringSon> pullDownMenuSubstation = new ArrayList<>(inSubstationList.stream().filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getName() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(DeviceSubstation::getName, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getGeoPositon(), o.getName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                )).values());
        return pullDownMenuSubstation;
    }

    /**
     * 网格资源——查询网格下大馈线详情
     */
    @Override
    public TableDataInfo<DeviceFeeder> selectDKX(String code, Integer pageNum, Integer pageSize) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(pageSize);
        pageQuery.setPageNum(pageNum);
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper();

        lambdaQueryWrapper.eq(DeviceFeeder::getGridCode, code);
        Page<DeviceFeeder> page = feederDeviceMapper.selectVoPage(pageQuery.build(), lambdaQueryWrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 变电站——出线间隔分析
     */
    @Override
    public IntervalAnalysisVo intervalAnalysis(String code) {

        //网格下的变电站
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);

        //变电站下的出线间隔
        int totalBayNum = 0;
        //剩余出线间隔
        int remainingBayNum = 0;

        //出线间隔柱状图
        List<Statistics> statisticsList = new ArrayList<>();

        //剩余出线间隔柱状图
        List<Statistics> remainingStatisticsList = new ArrayList<>();
        for (DeviceSubstation deviceSubstation : inSubstationList) {
            Integer bayNum = powerMapper.selectBayNum(deviceSubstation.getPsrId());
            Statistics statistics = new Statistics();
            statistics.setTypeName(deviceSubstation.getName());
            statistics.setTypeNum(bayNum.toString());
            statisticsList.add(statistics);

            Integer remainingNum = powerMapper.selectremainingNum(deviceSubstation.getPsrId());
            Statistics remainingStatistics = new Statistics();
            remainingStatistics.setTypeName(deviceSubstation.getName());
            remainingStatistics.setTypeNum(remainingNum.toString());
            remainingStatisticsList.add(remainingStatistics);

            totalBayNum = totalBayNum + bayNum;
            remainingBayNum = remainingBayNum + remainingNum;
        }


        List<Statistics> pieAvailability = new ArrayList<>();
        for (Statistics statistics : statisticsList) {
            BigDecimal a = BigDecimal.valueOf(Integer.parseInt(statistics.getTypeNum()));
            BigDecimal b = BigDecimal.valueOf(totalBayNum).subtract(BigDecimal.valueOf(remainingBayNum));
            Statistics newStatistics = new Statistics();
            newStatistics.setTypeName(statistics.getTypeName());
            newStatistics.setTypeNum(a.divide(b, 2, RoundingMode.HALF_UP).toString());
            pieAvailability.add(newStatistics);
        }

        IntervalAnalysisVo intervalAnalysisVo = new IntervalAnalysisVo();
        BigDecimal total = BigDecimal.valueOf(totalBayNum);
        BigDecimal remain = BigDecimal.valueOf(remainingBayNum);

        intervalAnalysisVo.setAvailability(Double.parseDouble(total.subtract(remain).divide(total, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString()));

        intervalAnalysisVo.setTotalBayNum(totalBayNum);

        intervalAnalysisVo.setRemainingBayNum(remainingBayNum);

        intervalAnalysisVo.setTotalAvailability(statisticsList);

        intervalAnalysisVo.setRemainingAvailability(remainingStatisticsList);

        intervalAnalysisVo.setPieAvailability(pieAvailability);

        return intervalAnalysisVo;
    }

    /**
     * 网格资源——查询网格下的配变专变信息
     */
    @Override
    public TableDataInfo<PBEntity> selectDeviceFeederTransformerVol(DeviceFeederTransformerVolBo bo) {
        List<DeviceFeeder> dmsFeederDeviceList = powerMapper.selectMiddleDmsFeederDevice(bo.getCode());

        List<String> lineIdList = dmsFeederDeviceList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());


        List<PBEntity> feederTransformerVols = powerMapper.selectTransformersByFeedId(lineIdList);

        // 1. 提取所有 feederId
        List<String> feederIds = feederTransformerVols.stream().map(PBEntity::getFeeder).distinct().collect(Collectors.toList());

        // 2. 批量查询所有馈线信息（假设有批量查询方法）
        Map<String, DeviceFeeder> feederMap = feederDeviceMapper.selectByIds(feederIds).stream().collect(Collectors.toMap(DeviceFeeder::getPsrId, feeder -> feeder));

        // 3. 提取所有变电站ID
        List<String> stationIds = feederMap.values().stream().map(DeviceFeeder::getStartStation).distinct().collect(Collectors.toList());

        // 4. 批量查询所有变电站信息（假设有批量查询方法）
        Map<String, DeviceSubstation> substationMap = feederDeviceMapper.selectDeviceSubstations(stationIds).stream().collect(Collectors.toMap(DeviceSubstation::getPsrId, station -> station));

        feederTransformerVols.parallelStream().forEach(transformer -> {
            DeviceFeeder feeder = feederMap.get(transformer.getFeeder());
            if (feeder != null) {
                DeviceSubstation substation = substationMap.get(feeder.getStartStation());
                if (substation != null) {
                    transformer.setSubstationId(substation.getPsrId());
                    transformer.setSubstationName(substation.getName());
                }
            }
        });
        List<PBEntity> deviceFeederTransformerVolVoList = new ArrayList<>();
        if (bo.getType().equals("0")) {
            deviceFeederTransformerVolVoList = feederTransformerVols.stream().filter(e -> e.getPubPrivFlag().equals("0")).collect(Collectors.toList());
        }
        if (bo.getType().equals("1")) {
            deviceFeederTransformerVolVoList = feederTransformerVols.stream().filter(e -> e.getPubPrivFlag().equals("1")).collect(Collectors.toList());
        } else {
            deviceFeederTransformerVolVoList = feederTransformerVols;
        }

        Page<PBEntity> page = new Page<>(bo.getPageNum(), bo.getPageSize(), deviceFeederTransformerVolVoList.size());
        int fromIndex = (bo.getPageNum() - 1) * bo.getPageSize();
        int toIndex = Math.min(fromIndex + bo.getPageSize(), deviceFeederTransformerVolVoList.size());
        page.setRecords(deviceFeederTransformerVolVoList.subList(fromIndex, toIndex));
        return TableDataInfo.build(page);
    }

    /**
     * 网格资源——查询网格下架空线详情
     */
    @Override
    public TableDataInfo<DeviceFeederJk> selectJKX(String code, Integer pageNum, Integer pageSize) {
        List<DeviceFeeder> deviceFeederList = deviceFeederJkMapper.selectDeviceFeeder(code);
        List<String> idList = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        //架空线
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectJKList(idList);

        Page<DeviceFeederJk> page = new Page<>(pageNum, pageSize, deviceFeederJkList.size());
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, deviceFeederJkList.size());
        page.setRecords(deviceFeederJkList.subList(fromIndex, toIndex));
        return TableDataInfo.build(page);
    }

    /**
     * 网格资源——查询网格下电缆线详情
     */
    @Override
    public TableDataInfo<DeviceFeederCable> selectDLX(String code, Integer pageNum, Integer pageSize) {
        List<DeviceFeeder> deviceFeederList = deviceFeederJkMapper.selectDeviceFeeder(code);
        List<String> idList = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        //电缆
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectCableList(idList);

        Page<DeviceFeederCable> page = new Page<>(pageNum, pageSize, deviceFeederCableList.size());
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, deviceFeederCableList.size());
        page.setRecords(deviceFeederCableList.subList(fromIndex, toIndex));
        return TableDataInfo.build(page);
    }

    /**
     * 计算负载率的最大值，最小值以及对应发生的时间段
     *
     * @param load
     */
    static MaxAndIndex maxAndIndex(Load load) {
        MaxAndIndex maxAndIndex = new MaxAndIndex();
        //最大峰值，最大时间
        OptionalInt maxIndexOpt = IntStream.range(0, load.getLoadList().size()).reduce((a, b) -> load.getLoadList().get(a) >= load.getLoadList().get(b) ? a : b);

        if (maxIndexOpt.isPresent()) {
            int maxIndex = maxIndexOpt.getAsInt();
            Double maxValue = load.getLoadList().get(maxIndex);
            maxAndIndex.setMaxNum(maxValue);
            maxAndIndex.setMaxTime(load.getTimeList().get(maxIndex));
        }


        OptionalInt minIndexOpt = IntStream.range(0, load.getLoadList().size()).reduce((a, b) -> load.getLoadList().get(a) <= load.getLoadList().get(b) ? a : b);

        if (minIndexOpt.isPresent()) {
            int minIndex = minIndexOpt.getAsInt();
            Double maxValue = load.getLoadList().get(minIndex);
            maxAndIndex.setMinNum(maxValue);
            maxAndIndex.setMinTime(load.getTimeList().get(minIndex));
        }
        return maxAndIndex;
    }


    /**
     * 根据网格编码，查询网格内的所有变电站
     *
     * @param code
     * @return
     */
    List<DeviceSubstation> getMiddleSubstations(String code) {
        //查询该网格编码的详细网格信息
        PowerGrid powerGrid = powerMapper.selectPowerGrid(code);
        //查询所有变电站的所有信息
        List<DeviceSubstation> substationList = middleSubstationNewMapper.selectList();
        List<DeviceSubstation> inSubstationList = new ArrayList<>();
        //遍历所有变电站的坐标是否在该网格内部
        List<Coordinate> coordinates = new ArrayList<>();

        // 1. 去除首尾的方括号 []
        String trimmed = powerGrid.getWkt().substring(1, powerGrid.getWkt().length() - 1);
        trimmed = trimmed.substring(1, trimmed.length() - 1);

        // 2. 按 "],[" 分割成单个坐标点
        String[] points = trimmed.split("\\],\\[");
        for (String point : points) {
            // 3. 按 "," 分割经度和纬度
            String[] xy = point.split(",");
            double x = Double.parseDouble(xy[0].trim());
            double y = Double.parseDouble(xy[1].trim());
            coordinates.add(new Coordinate(x, y));
        }
        for (DeviceSubstation substation : substationList) {
            String x = Arrays.asList(substation.getGeoPositon().split(",")).get(0);
            String y = Arrays.asList(substation.getGeoPositon().split(",")).get(1);
            if (MercatorLineCircleIntersection.isPointInPolygon(Double.parseDouble(x), Double.parseDouble(y), coordinates)) {
                inSubstationList.add(substation);
            }

        }
        return inSubstationList;
    }

    /**
     * 查询时间范围内的所有符合的负载率
     *
     * @return
     */
    public Load selectTransformerLoad(String strTime, String endTime, List<String> psrId) throws ParseException, JsonProcessingException {
        List<TransformerLoad> transformerLoadList = powerMapper.selectTransformerLoad(StringByDate.stringByDate(strTime), StringByDate.stringByDate(endTime), psrId);
        if (CollectionUtils.isEmpty(transformerLoadList)) {
            return null;
        }
        Map<String, List<Double>> map = new HashMap<>();
        List<Double> maxDataList = new ArrayList<>();
        List<String> maxRecordingTimeList = new ArrayList<>();
        List<Double> minDataList = new ArrayList<>();
        List<String> minRecordingTimeList = new ArrayList<>();
        List<Double> avgDataList = new ArrayList<>();
        for (TransformerLoad transformerLoad : transformerLoadList) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String datePart = dateFormat.format(transformerLoad.getRecordingTime());


            ObjectMapper mapper = new ObjectMapper();
            List<Double> doubleList = mapper.readValue(transformerLoad.getData(), new TypeReference<List<Double>>() {
            });
            map.put(datePart, doubleList);
            maxDataList.add(transformerLoad.getMaxData());
            maxRecordingTimeList.add(transformerLoad.getMaxRecordingTime());
            minDataList.add(transformerLoad.getMinData());
            minRecordingTimeList.add(transformerLoad.getMinRecordingTime());
            avgDataList.add(transformerLoad.getAvgData());

        }
        Load load = new Load();
        List<String> timeList = new ArrayList<>();
        List<Double> loadList = new ArrayList<>();
        map.forEach((key, value) -> {
            List<String> myTimeList = new ArrayList<>();
            loadList.addAll(value);
            int i = 0;
            for (Double v : value) {
                TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(i);  // 直接使用枚举常量
                String timeStr = timeEnum288.getTimeString();
                myTimeList.add(key + " " + timeStr);
                i++;
            }
            timeList.addAll(myTimeList);

        });
        load.setLoadList(loadList);
        load.setTimeList(timeList);
        load.setMaxDataList(maxDataList);
        load.setMaxRecordingTimeList(maxRecordingTimeList);
        load.setMinDataList(minDataList);
        load.setMinRecordingTimeList(minRecordingTimeList);
        load.setAvgDataList(avgDataList);
        return load;

    }


    /**
     * 查询时间范围内的所有符合的负载率
     *
     * @return
     */
    public Load selectFeeDerLoad(String strTime, String endTime, List<String> psrId) throws ParseException, JsonProcessingException {
        List<FeederLoad> feederLoadList = powerMapper.selectFeeDerLoad(StringByDate.stringByDate(strTime), StringByDate.stringByDate(endTime), psrId);
        if (CollectionUtils.isEmpty(feederLoadList)) {
            return null;
        }
        Map<String, List<Double>> map = new HashMap<>();
        List<Double> maxDataList = new ArrayList<>();
        List<String> maxRecordingTimeList = new ArrayList<>();
        List<Double> minDataList = new ArrayList<>();
        List<String> minRecordingTimeList = new ArrayList<>();
        List<Double> avgDataList = new ArrayList<>();
        for (FeederLoad feederLoad : feederLoadList) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String datePart = dateFormat.format(feederLoad.getRecordingTime());


            ObjectMapper mapper = new ObjectMapper();
            List<Double> doubleList = mapper.readValue(feederLoad.getData(), new TypeReference<List<Double>>() {
            });
            map.put(datePart, doubleList);
            maxDataList.add(feederLoad.getMaxData());
            maxRecordingTimeList.add(feederLoad.getMaxRecordingTime());
            minDataList.add(feederLoad.getMinData());
            minRecordingTimeList.add(feederLoad.getMinRecordingTime());
            avgDataList.add(feederLoad.getAvgData());

        }
        Load load = new Load();
        List<String> timeList = new ArrayList<>();
        List<Double> loadList = new ArrayList<>();
        map.forEach((key, value) -> {
            List<String> myTimeList = new ArrayList<>();
            loadList.addAll(value);
            int i = 0;
            for (Double v : value) {
                TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(i);  // 直接使用枚举常量
                String timeStr = timeEnum288.getTimeString();
                myTimeList.add(key + " " + timeStr);
                i++;
            }
            timeList.addAll(myTimeList);

        });
        load.setLoadList(loadList);
        load.setTimeList(timeList);
        load.setMaxDataList(maxDataList);
        load.setMaxRecordingTimeList(maxRecordingTimeList);
        load.setMinDataList(minDataList);
        load.setMinRecordingTimeList(minRecordingTimeList);
        load.setAvgDataList(avgDataList);
        return load;

    }

    /**
     * 查询时间范围内的所有符合的功率
     *
     * @return
     */
    public Load selectTransformerLoadMW(String strTime, String endTime, List<String> psrId) throws ParseException, JsonProcessingException {
        List<TransformerLoadMW> transformerLoadMWList = powerMapper.selectTransformerLoadMW(StringByDate.stringByDate(strTime), StringByDate.stringByDate(endTime), psrId);
        if (CollectionUtils.isEmpty(transformerLoadMWList)) {
            return null;
        }
        Map<String, List<Double>> map = new HashMap<>();
        List<Double> maxDataList = new ArrayList<>();
        List<String> maxRecordingTimeList = new ArrayList<>();
        List<Double> minDataList = new ArrayList<>();
        List<String> minRecordingTimeList = new ArrayList<>();
        List<Double> avgDataList = new ArrayList<>();


        for (TransformerLoadMW transformerLoadMW : transformerLoadMWList) {
            ObjectMapper mapper = new ObjectMapper();
            List<Double> doubleList = mapper.readValue(transformerLoadMW.getData(), new TypeReference<List<Double>>() {
            });
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String datePart = dateFormat.format(transformerLoadMW.getRecordingTime());


            map.put(datePart, doubleList);
            maxDataList.add(transformerLoadMW.getMaxData());
            maxRecordingTimeList.add(transformerLoadMW.getMaxRecordingTime());
            minDataList.add(transformerLoadMW.getMinData());
            minRecordingTimeList.add(transformerLoadMW.getMinRecordingTime());
            avgDataList.add(transformerLoadMW.getAvgData());
        }
        Load load = new Load();
        List<String> timeList = new ArrayList<>();
        List<Double> loadList = new ArrayList<>();
        map.forEach((key, value) -> {
            List<String> myTimeList = new ArrayList<>();
            loadList.addAll(value);
            int i = 0;
            for (Double v : value) {
                TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(i);  // 直接使用枚举常量
                String timeStr = timeEnum288.getTimeString();

                myTimeList.add(key + " " + timeStr);
                i++;
            }
            timeList.addAll(myTimeList);

        });
        load.setLoadList(loadList);
        load.setTimeList(timeList);
        load.setMaxDataList(maxDataList);
        load.setMaxRecordingTimeList(maxRecordingTimeList);
        load.setMinDataList(minDataList);
        load.setMinRecordingTimeList(minRecordingTimeList);
        load.setAvgDataList(avgDataList);
        return load;

    }

    /**
     * 查询时间范围内的所有符合的功率
     *
     * @return
     */
    public Load selectFeederLoadMW(String strTime, String endTime, List<String> psrId) throws ParseException, JsonProcessingException {
        List<FeederLoadMW> feederLoadMWList = powerMapper.selectFeeDerLoadMW(StringByDate.stringByDate(strTime), StringByDate.stringByDate(endTime), psrId);
        if (CollectionUtils.isEmpty(feederLoadMWList)) {
            return null;
        }
        Map<String, List<Double>> map = new HashMap<>();
        List<Double> maxDataList = new ArrayList<>();
        List<String> maxRecordingTimeList = new ArrayList<>();
        List<Double> minDataList = new ArrayList<>();
        List<String> minRecordingTimeList = new ArrayList<>();
        List<Double> avgDataList = new ArrayList<>();


        for (FeederLoadMW feederLoadMW : feederLoadMWList) {
            ObjectMapper mapper = new ObjectMapper();
            List<Double> doubleList = mapper.readValue(feederLoadMW.getData(), new TypeReference<List<Double>>() {
            });
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String datePart = dateFormat.format(feederLoadMW.getRecordingTime());


            map.put(datePart, doubleList);
            maxDataList.add(feederLoadMW.getMaxData());
            maxRecordingTimeList.add(feederLoadMW.getMaxRecordingTime());
            minDataList.add(feederLoadMW.getMinData());
            minRecordingTimeList.add(feederLoadMW.getMinRecordingTime());
            avgDataList.add(feederLoadMW.getAvgData());
        }
        Load load = new Load();
        List<String> timeList = new ArrayList<>();
        List<Double> loadList = new ArrayList<>();
        map.forEach((key, value) -> {
            List<String> myTimeList = new ArrayList<>();
            loadList.addAll(value);
            int i = 0;
            for (Double v : value) {
                TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(i);  // 直接使用枚举常量
                String timeStr = timeEnum288.getTimeString();

                myTimeList.add(key + " " + timeStr);
                i++;
            }
            timeList.addAll(myTimeList);

        });
        load.setLoadList(loadList);
        load.setTimeList(timeList);
        load.setMaxDataList(maxDataList);
        load.setMaxRecordingTimeList(maxRecordingTimeList);
        load.setMinDataList(minDataList);
        load.setMinRecordingTimeList(minRecordingTimeList);
        load.setAvgDataList(avgDataList);
        return load;

    }


    public List<DeviceNtHighTransformer> selectNtHighTransformer(String code) {
        List<DeviceSubstation> inSubstationList = getMiddleSubstations(code);
        List<String> substationPsrIdList = inSubstationList.stream().map(DeviceSubstation::getPsrId).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceNtHighTransformer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceNtHighTransformer::getStation, substationPsrIdList);
        return ntHighTransformerNewMapper.selectList(lambdaQueryWrapper);
    }


    /**
     * 查询网格统计高压、中压评估线路基础模块(api)
     *
     * @param code
     * @return
     */
    public FeederFoundationVo feederFoundation(String code) throws Exception {
        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
        try {
            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
        } catch (Exception e) {
            return null;
        }
        List<DeviceFeeder> deviceFeederList = deviceApiCollector.getDeviceFeeders();
        FeederFoundationVo feederHorizontalAnalysisVo = new FeederFoundationVo();
        //大馈线总长度
        Double dkxLength = DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLength).sum());
        feederHorizontalAnalysisVo.setLineLength(dkxLength);
        //电缆长度
        feederHorizontalAnalysisVo.setCableLength(DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getCableLength).sum()));
        //电缆化率
        feederHorizontalAnalysisVo.setCableConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getCableLength() / dkxLength));
        //架空长度
        feederHorizontalAnalysisVo.setJkLength(DoubleFormatter.formatToThreeDecimals4((deviceFeederList.stream().mapToDouble(DeviceFeeder::getOverheadLength).sum())));
        //线路供电区域
        feederHorizontalAnalysisVo.setSupplyArea(deviceFeederList.stream()
                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
                .distinct()                     // 去重
                .collect(Collectors.joining(", ")));
        //线路条数
        feederHorizontalAnalysisVo.setLineNum(deviceFeederList.size());
        //可开放容量
        feederHorizontalAnalysisVo.setOpenTotalCapacity(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLineCapFree).sum());
        feederHorizontalAnalysisVo.setDeviceFeeders(deviceFeederList);
        return feederHorizontalAnalysisVo;
    }

    public TransformerFoundationVo transformerFoundation(String code) throws Exception {
        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
        try {
            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
        } catch (Exception e) {
            return null;
        }
        //查主变
//        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(code);
//        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
//        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
//        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        List<DeviceZB> zbs = deviceApiCollector.getZbList();

        if (CollectionUtils.isEmpty(zbs)) {
            return null;
        }
        //总容量
        Double capacity = zbs.stream().mapToDouble(DeviceZB::getRatedCapacity).sum();

//        LocalDate today = powerMapper.selectTime().toInstant()
//                .atZone(ZoneId.systemDefault())
//                .toLocalDate();
//        // 获取当前日期（不包含当天，所以从昨天开始计算）
//
//        LocalDate yesterday = today.minusDays(1);
//
//        // 计算7天前的第一天和最后一天（不包含今天）
//        LocalDate firstDay = yesterday.minusDays(day - 1); // 7天前的第一天
//        LocalDate lastDay = yesterday;               // 7天前的最后一天（昨天）
//
//        // 定义日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//
//        // 转换为字符串
//        String firstDayStr = firstDay.format(formatter);
//        String lastDayStr = lastDay.format(formatter);
//
//
//        Load loadMW = powerServiceImpl.selectTransformerLoadMW(firstDayStr, lastDayStr, ntHighTransformerList.stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));

        TransformerFoundationVo transformerFoundationVo = new TransformerFoundationVo();
        transformerFoundationVo.setNormalNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 3).count());
        transformerFoundationVo.setHeavyLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 2).count());
        transformerFoundationVo.setOverLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 1).count());
        transformerFoundationVo.setTotalCapacity(capacity);
        transformerFoundationVo.setTotalNum(zbs.size());
        transformerFoundationVo.setZbList(zbs);
        return transformerFoundationVo;
    }

    public PublicSpecializedVo mediumPublicSpecialized(String code) throws Exception {
        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
        try {
            deviceApiCollector = objectMapper.readValue(powerMapper.selectDeviceApiJson(code), DeviceApiCollector.class);
        } catch (Exception e) {
            return null;
        }
        PublicSpecializedVo publicSpecializedVo = new PublicSpecializedVo();
        //01专变，03公变
        //柱上变公变
        List<DevicePoleTransformer> polePubList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("03")).collect(Collectors.toList());
        //柱上变专变
        List<DevicePoleTransformer> polePrvList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("01")).collect(Collectors.toList());

        //柱上变容量
        Map<String, Double> poleTypeSumMap = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DevicePoleTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));

//        //所有变公变数量
//        List<StationServiceTransformer> servicePubList = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).collect(Collectors.toList());
//        //所有变专变数量
//        List<StationServiceTransformer> servicePrvList = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).collect(Collectors.toList());
//
//        //所有变容量
//        Map<String, Double> serviceTypeSumMap = deviceApiCollector.getStationServiceTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(StationServiceTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));

        //站内变公变
        List<DeviceStationTransformer> stationPubList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).collect(Collectors.toList());
        //站内变专变数量
        List<DeviceStationTransformer> stationPrvList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).collect(Collectors.toList());

        //站内变容量
        Map<String, Double> stationTypeSumMap = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DeviceStationTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
        //公变数量
        publicSpecializedVo.setPubNum(polePubList.size() + stationPubList.size());
        //专变数量
        publicSpecializedVo.setPrivNum(polePrvList.size() + stationPrvList.size());
        //公变容量
        publicSpecializedVo.setPubRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("03") == null ? 0.0 : poleTypeSumMap.get("03")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("03") == null ? 0.0 : stationTypeSumMap.get("03")));
        //专变容量
        publicSpecializedVo.setPrivRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("01") == null ? 0 : poleTypeSumMap.get("01")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("01") == null ? 0.0 : stationTypeSumMap.get("01")));
        publicSpecializedVo.setPubDevicePoleTransformers(polePubList);
        publicSpecializedVo.setPrvDevicePoleTransformers(polePrvList);
        publicSpecializedVo.setPubDeviceStationTransformers(stationPubList);
        publicSpecializedVo.setPrvDeviceStationTransformers(stationPrvList);

        return publicSpecializedVo;
    }
}
