package com.ruoyi.service.power.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.constant.ResourceStatisticsDataType;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.DeviceApiCollector;
import com.ruoyi.entity.power.vo.FeederFoundationVo;
import com.ruoyi.entity.power.vo.PowerGridVo;
import com.ruoyi.entity.power.vo.PublicSpecializedVo;
import com.ruoyi.entity.power.vo.TransformerFoundationVo;
import com.ruoyi.entity.psm.*;
import com.ruoyi.mapper.device.DeviceApiJsonMapper;
import com.ruoyi.mapper.device.DeviceSubstation2Mapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.device.NtFeederMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.*;

@Service
@Slf4j
public class PowerApiServiceImpl2 implements MiddleCommon {
    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    NtFeederMapper ntFeederMapper;

    @Autowired
    DeviceSubstation2Mapper deviceSubstation2Mapper;

    @Autowired
    DeviceApiJsonMapper deviceApiJsonMapper;

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final int THREAD_POOL_SIZE = 10; // 线程池大小，可根据实际情况调整

    public void test() throws Exception {
//        List<ProblemGrid> problemGrids = feederDeviceMapper.selectAll();
//        log.info("开始处理网格数据，总数量: {}", problemGrids.size());

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<String> psrIdList = Arrays.asList("JS-NJ-JBXQ-YCY",
                "JS-NJ-YH-XSQXB",
                "JS-NJ-JBXQ-LPK",
                "JS-NJ-GL-LJ",
                "JS-NJ-QH-XJKDB",
                "JS-NJ-PK-SQ",
                "JS-NJ-XW-BJDL",
                "JS-NJ-GL-ZYM",
                "JS-NJ-PK-ZJB",
                "JS-NJ-JBXQ-DC",
                "JS-NJ-PK-QL");
        // 计数器
        final int[] count = {1};

        for (String psrId : psrIdList) {
//            final String psrId = problemGrid.getPsrId();
            executorService.submit(() -> {
                try {
                    log.info("开始处理网格ID: {}, 当前第{}个", psrId, count[0]);
                    long startTime = System.currentTimeMillis();

                    DeviceApiCollector deviceApiCollector = selectFeederByGrid(psrId);
                    if (deviceApiCollector != null) {
                        deviceApiJsonMapper.insert(psrId, mapper.writeValueAsString(deviceApiCollector));
                        log.info("成功处理网格ID: {}, 耗时: {}ms", psrId, System.currentTimeMillis() - startTime);
                    } else {
                        log.warn("网格ID: {} 查询结果为空", psrId);
                    }

                    count[0]++;
                } catch (Exception e) {
                    log.error("处理网格ID: {} 时发生异常", psrId, e);
                }
            });
        }
       /* for (ProblemGrid problemGrid : problemGrids) {
            final String psrId = problemGrid.getPsrId();
            executorService.submit(() -> {
                try {
                    log.info("开始处理网格ID: {}, 当前第{}个", psrId, count[0]);
                    long startTime = System.currentTimeMillis();

                    DeviceApiCollector deviceApiCollector = selectFeederByGrid(psrId);
                    if (deviceApiCollector != null) {
                        deviceApiJsonMapper.insert(psrId, mapper.writeValueAsString(deviceApiCollector));
                        log.info("成功处理网格ID: {}, 耗时: {}ms", psrId, System.currentTimeMillis() - startTime);
                    } else {
                        log.warn("网格ID: {} 查询结果为空", psrId);
                    }

                    count[0]++;
                } catch (Exception e) {
                    log.error("处理网格ID: {} 时发生异常", psrId, e);
                }
            });
        }*/

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.MINUTES)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("所有网格数据处理完成");
    }




    /**
     * 查询网格下的设备
     */
    public DeviceApiCollector selectFeederByGrid(String code) throws Exception {
        log.debug("开始查询网格下的设备，网格ID: {}", code);
        long startTime = System.currentTimeMillis();

        List<DeviceFeeder> deviceFeeders = feederDeviceMapper.selectFeederByCode(code);
        if (CollectionUtils.isEmpty(deviceFeeders)) {
            log.warn("网格ID: {} 下没有找到馈线设备", code);
            return null;
        }
        log.debug("网格ID: {} 下找到馈线设备数量: {}", code, deviceFeeders.size());

        List<String> idList = deviceFeeders.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());

        // 环网柜
        log.debug("开始查询环网柜信息");
        List<DeviceHWG> hwgList = hwg(idList);
        log.debug("查询到环网柜数量: {}", hwgList.size());

        // 开关站
        log.debug("开始查询开关站信息");
        List<DeviceKGZ> kgzList = kgz(idList);
        log.debug("查询到开关站数量: {}", kgzList.size());

        // 配电室
        log.debug("开始查询配电室信息");
        List<DevicePDS> pdsList = pds(idList);
        log.debug("查询到配电室数量: {}", pdsList.size());

        // 柱上变
        log.debug("开始查询柱上变信息");
        List<DevicePoleTransformer> poleTransformerList = zsb(idList);
        log.debug("查询到柱上变数量: {}", poleTransformerList.size());

        // 站内变
        log.debug("开始查询站内变信息");
        List<DeviceStationTransformer> deviceStationTransformerList = znbyq(idList);
        log.debug("查询到站内变数量: {}", deviceStationTransformerList.size());

        // 所有变
        log.debug("开始查询所有变信息");
        List<StationServiceTransformer> stationServiceTransformerList = syb(idList);
        log.debug("查询到所有变数量: {}", stationServiceTransformerList.size());

        // 变电站id
        List<String> stationIdList = deviceFeeders.stream().map(DeviceFeeder::getStartStation).collect(Collectors.toList());

        // 变电站
        log.debug("开始查询变电站信息");
        List<DeviceSubstation> stationList = deviceSubstation2Mapper.selectBatchIds(stationIdList);
        log.debug("查询到变电站数量: {}", stationList.size());

        // 获取主变信息
        log.debug("开始获取主变信息");
        List<DeviceZB> zbs = new ArrayList<>();
        for (DeviceSubstation deviceSubstation : stationList) {
            if (StringUtils.isEmpty(deviceSubstation.getSgId())) {
                log.debug("变电站ID: {} 没有SGID，跳过", deviceSubstation.getPsrId());
                continue;
            }

            log.debug("开始查询变电站基础拓扑，SGID: {}", deviceSubstation.getSgId());
            OccaAnalysisUtil.BaseResponse<OccaAnalysisUtil.SubstationResult> substationResultBaseResponse = OccaAnalysisUtil.substationBasicTopo(deviceSubstation.getSgId());
            List<OccaAnalysisUtil.TransformerInfo> transformerInfoVos = substationResultBaseResponse.getResult().getTransformerInfoVos();

            for (OccaAnalysisUtil.TransformerInfo transformerInfoVo : transformerInfoVos) {
                DeviceZB deviceZB = new DeviceZB();
                deviceZB.setPsrId(transformerInfoVo.getId());
                deviceZB.setName(transformerInfoVo.getName());
                deviceZB.setStation(deviceSubstation.getPsrId());
                deviceZB.setType(transformerInfoVo.getLoadType());
                deviceZB.setLoadRate(transformerInfoVo.getLoadRate());
                deviceZB.setRatedCapacity(transformerInfoVo.getRatedPower());
                deviceZB.setHisMaxLoad(transformerInfoVo.getHisMaxLoad());
                zbs.add(deviceZB);
            }
            log.debug("变电站ID: {} 下找到主变数量: {}", deviceSubstation.getPsrId(), transformerInfoVos.size());
        }
        log.debug("总共获取到主变数量: {}", zbs.size());

        // 查询柱上变容量
        log.debug("开始查询柱上变容量信息");
        List<Map<String, Object>> zsbDssAstInfo = AstRequestUtils.getDSSAstInfo(middleHeader, ZSB, getAllFeederId(poleTransformerList.stream().map(DevicePoleTransformer::getAstId).collect(Collectors.toList())));
        log.debug("获取到柱上变容量信息数量: {}", zsbDssAstInfo.size());

        // 查询站内变容量
        log.debug("开始查询站内变容量信息");
        List<Map<String, Object>> znbDssAstInfo = AstRequestUtils.getDSSAstInfo(middleHeader, ZNPDBYQ, getAllFeederId(deviceStationTransformerList.stream().map(DeviceStationTransformer::getAstId).collect(Collectors.toList())));
        log.debug("获取到站内变容量信息数量: {}", znbDssAstInfo.size());

        // 查询所有变容量
        log.debug("开始查询所有变容量信息");
        List<Map<String, Object>> sybDssAstInfo = AstRequestUtils.getDSSAstInfo(middleHeader, SYB, getAllFeederId(stationServiceTransformerList.stream().map(StationServiceTransformer::getAstId).collect(Collectors.toList())));
        log.debug("获取到所有变容量信息数量: {}", sybDssAstInfo.size());

        // 查询线路长度基本信息
        log.debug("开始处理馈线设备信息");
        for (DeviceFeeder deviceFeeder : deviceFeeders) {
            log.debug("开始处理馈线ID: {}", deviceFeeder.getPsrId());
            long feederStartTime = System.currentTimeMillis();

            // 查询线路的长度基本信息
            List<Map<String, Object>> mapList = new ArrayList<>();
            Map<String, Object> parm = new HashMap<>();
            parm.put("classId", "300");
            parm.put("distribution", "0");
            parm.put("provinceId", "ff80808149f52e24014a039871840007");
            parm.put("psrId", deviceFeeder.getPsrId());
            parm.put("psrType", "dkx");
            mapList.add(parm);

            AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(parm.size(), mapList);
            String baseInfo = AmapSdkCommon.getBaseInfo(parm);
            JsonNode rootNode = mapper.readTree(baseInfo);

            try {
                deviceFeeder.setLength(mapper.convertValue(rootNode.path("result").get(0).path("key_length").path("value"), Double.class));
                log.debug("馈线ID: {} 设置长度: {}", deviceFeeder.getPsrId(), deviceFeeder.getLength());
            } catch (Exception e) {
                deviceFeeder.setLength(0.0);
                log.warn("馈线ID: {} 设置长度时发生异常，使用默认值0.0", deviceFeeder.getPsrId(), e);
            }

            try {
                deviceFeeder.setCableLength(mapper.convertValue(rootNode.path("result").get(0).path("key_cableLength").path("value"), Double.class));
                log.debug("馈线ID: {} 设置电缆长度: {}", deviceFeeder.getPsrId(), deviceFeeder.getCableLength());
            } catch (IllegalArgumentException e) {
                deviceFeeder.setCableLength(0.0);
                log.warn("馈线ID: {} 设置电缆长度时发生异常，使用默认值0.0", deviceFeeder.getPsrId(), e);
            }

            try {
                deviceFeeder.setOverheadLength(mapper.convertValue(rootNode.path("result").get(0).path("key_overheadLength").path("value"), Double.class));
                log.debug("馈线ID: {} 设置架空长度: {}", deviceFeeder.getPsrId(), deviceFeeder.getOverheadLength());
            } catch (IllegalArgumentException e) {
                deviceFeeder.setOverheadLength(0.0);
                log.warn("馈线ID: {} 设置架空长度时发生异常，使用默认值0.0", deviceFeeder.getPsrId(), e);
            }

            // 查询线路的开放容量信息
            String ycId = ntFeederMapper.selectByFeederId(deviceFeeder.getPsrId());
            if (StringUtils.isNotEmpty(ycId)) {
                log.debug("开始查询线路开放容量信息，YCID: {}", ycId);
                OccaAnalysisUtil.BaseResponse<OccaAnalysisUtil.LineResult> lineResultBaseResponse = OccaAnalysisUtil.queryLineInfo(ycId);
                if (lineResultBaseResponse.getResult().getLineInfo() == null || lineResultBaseResponse.getResult().getLineInfo().getLineCapFree() == null) {
                    deviceFeeder.setLineCapFree(0.0);
                    log.debug("馈线ID: {} 线路开放容量为空，使用默认值0.0", deviceFeeder.getPsrId());
                } else {
                    deviceFeeder.setLineCapFree(lineResultBaseResponse.getResult().getLineInfo().getLineCapFree());
                    log.debug("馈线ID: {} 设置线路开放容量: {}", deviceFeeder.getPsrId(), deviceFeeder.getLineCapFree());
                }
            } else {
                log.debug("馈线ID: {} 没有找到YCID", deviceFeeder.getPsrId());
                deviceFeeder.setLineCapFree(0.0);
            }

            log.debug("完成处理馈线ID: {}, 耗时: {}ms", deviceFeeder.getPsrId(), System.currentTimeMillis() - feederStartTime);
        }

        // 封装柱上变的容量
        log.debug("开始封装柱上变容量信息");
        for (DevicePoleTransformer devicePoleTransformer : poleTransformerList) {
            for (Map<String, Object> stringObjectMap : zsbDssAstInfo) {
                if (devicePoleTransformer.getAstId().equals(stringObjectMap.get("astId"))) {
                    if (stringObjectMap.get("ratedCapacity") != null) {
                        devicePoleTransformer.setRatedCapacity(stringObjectMap.get("ratedCapacity").toString());
                    } else {
                        devicePoleTransformer.setRatedCapacity("0");
                    }
                }
            }
        }

        // 封装站内变的容量
        log.debug("开始封装站内变容量信息");
        for (DeviceStationTransformer deviceStationTransformer : deviceStationTransformerList) {
            for (Map<String, Object> stringObjectMap : znbDssAstInfo) {
                if (deviceStationTransformer.getAstId().equals(stringObjectMap.get("astId"))) {
                    if (stringObjectMap.get("ratedCapacity") != null) {
                        deviceStationTransformer.setRatedCapacity(stringObjectMap.get("ratedCapacity").toString());
                    } else {
                        deviceStationTransformer.setRatedCapacity("0");
                    }
                }
            }
        }

        // 封装所有变的容量
        log.debug("开始封装所有变容量信息");
        for (StationServiceTransformer stationServiceTransformer : stationServiceTransformerList) {
            for (Map<String, Object> stringObjectMap : sybDssAstInfo) {
                if (stationServiceTransformer.getAstId().equals(stringObjectMap.get("astId"))) {
                    if (stringObjectMap.get("ratedCapacity") != null) {
                        stationServiceTransformer.setRatedCapacity(stringObjectMap.get("ratedCapacity").toString());
                    } else {
                        stationServiceTransformer.setRatedCapacity("0");
                    }
                }
            }
        }

        DeviceApiCollector deviceApiCollector = new DeviceApiCollector();
        deviceApiCollector.setDeviceFeeders(deviceFeeders);
        deviceApiCollector.setHwgList(hwgList);
        deviceApiCollector.setKgzList(kgzList);
        deviceApiCollector.setPdsList(pdsList);
        deviceApiCollector.setPoleTransformerList(poleTransformerList);
        deviceApiCollector.setDeviceStationTransformerList(deviceStationTransformerList);
        deviceApiCollector.setStationServiceTransformerList(stationServiceTransformerList);
        deviceApiCollector.setZbList(zbs);

        log.info("完成查询网格ID: {} 下的设备信息，总耗时: {}ms", code, System.currentTimeMillis() - startTime);
        //资源统计基础数据
        PowerGridVo power = selectPower(deviceApiCollector);
        //中压公变专变
        PublicSpecializedVo publicSpecializedVo = mediumPublicSpecialized(deviceApiCollector);
        //线路
        FeederFoundationVo feederFoundationVo = feederFoundation(deviceApiCollector);
        //高压主变数据
        TransformerFoundationVo transformerFoundationVo = transformerFoundation(deviceApiCollector);
        //存储基础数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.BASE,objectMapper.writeValueAsString(power));
        //存储高压数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.HIGHVOLTAGE,objectMapper.writeValueAsString(transformerFoundationVo));
        //存储中压数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.MEDIUDVOLTAGE,objectMapper.writeValueAsString(publicSpecializedVo));
        //存储线路数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.FEEDER,objectMapper.writeValueAsString(feederFoundationVo));
        //存储柱上变公变数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.PUBZSBLIST,objectMapper.writeValueAsString(publicSpecializedVo.getPubDevicePoleTransformers()));
        //存储柱上变专变数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.PRVZSBLIST,objectMapper.writeValueAsString(publicSpecializedVo.getPrvDevicePoleTransformers()));
        //存储站内变公变数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.PUBZNBLIST,objectMapper.writeValueAsString(publicSpecializedVo.getPubDeviceStationTransformers()));
        //存储站内变专变数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.PRVZNBLIST,objectMapper.writeValueAsString(publicSpecializedVo.getPrvDeviceStationTransformers()));
        //存储主变数据
        deviceApiJsonMapper.insertPowerGridVo(code, ResourceStatisticsDataType.ZBLIST,objectMapper.writeValueAsString(zbs));
        return deviceApiCollector;
    }

    // 其他方法保持不变...
    private List<DeviceHWG> hwg(List<String> feedrIdList) throws Exception {
        log.debug("查询环网柜信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DeviceHWG> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(HWG1)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DeviceHWG.class, item -> {
            for (DeviceHWG deviceHWG : item) {
                list.add(deviceHWG);
            }
        });
        return list;
    }

    private List<DeviceKGZ> kgz(List<String> feedrIdList) throws Exception {
        log.debug("查询开关站信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DeviceKGZ> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(KGZ)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DeviceKGZ.class, item -> {
            for (DeviceKGZ deviceKGZ : item) {
                list.add(deviceKGZ);
            }
        });
        return list;
    }

    private List<DevicePDS> pds(List<String> feedrIdList) throws Exception {
        log.debug("查询配电室信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DevicePDS> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(PDS)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DevicePDS.class, item -> {
            for (DevicePDS devicePDS : item) {
                list.add(devicePDS);
            }
        });
        return list;
    }

    private List<DevicePoleTransformer> zsb(List<String> feedrIdList) throws Exception {
        log.debug("查询柱上变信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DevicePoleTransformer> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(ZSB)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DevicePoleTransformer.class, item -> {
            for (DevicePoleTransformer devicePoleTransformer : item) {
                list.add(devicePoleTransformer);
            }
        });
        return list;
    }

    private List<DeviceStationTransformer> znbyq(List<String> feedrIdList) throws Exception {
        log.debug("查询站内变信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DeviceStationTransformer> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(ZNPDBYQ)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DeviceStationTransformer.class, item -> {
            for (DeviceStationTransformer deviceStationTransformer : item) {
                list.add(deviceStationTransformer);
            }
        });
        return list;
    }

    private List<StationServiceTransformer> syb(List<String> feedrIdList) throws Exception {
        log.debug("查询所有变信息，馈线ID列表大小: {}", feedrIdList.size());
        List<StationServiceTransformer> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(SYB)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), StationServiceTransformer.class, item -> {
            for (StationServiceTransformer stationServiceTransformer : item) {
                list.add(stationServiceTransformer);
            }
        });
        return list;
    }

    private List<DeviceFeeder> dkx(List<String> feedrIdList) throws Exception {
        log.debug("查询大馈线信息，馈线ID列表大小: {}", feedrIdList.size());
        List<DeviceFeeder> list = new ArrayList<>();
        this.middleQueryLoop(FilterBuild.distributionNetwork(DKX)
                .filterParam("psrState", "in", "10,20")
                .filterParam("feeder", "in", getAllFeederId(feedrIdList)), DeviceFeeder.class, item -> {
            for (DeviceFeeder deviceFeeder : item) {
                list.add(deviceFeeder);
            }
        });
        return list;
    }

    private List<DeviceZB> zb(List<String> stationId) throws Exception {
        log.debug("查询主变信息，变电站ID列表大小: {}", stationId.size());
        List<DeviceZB> list = new ArrayList<>();
        this.middleTFSQueryLoop(FilterBuild.transmissionNetwork(ZB)
                .filterParam("psrState", "in", "10,20")
                .filterParam("station", "in", getAllFeederId(stationId)), DeviceZB.class, item -> {
            for (DeviceZB deviceZB : item) {
                list.add(deviceZB);
            }
        });
        return list;
    }

    public String getAllFeederId(List<String> feedrIdList) {
        if (CollectionUtils.isEmpty(feedrIdList)) {
            log.warn("馈线ID列表为空");
            return "";
        }
        String ids = feedrIdList.stream().collect(Collectors.joining(","));
        log.debug("拼接馈线ID结果: {}", ids);
        return ids;
    }


    /**
     * 处理统计基础信息
     */

    public PowerGridVo selectPower(DeviceApiCollector deviceApiCollector) throws Exception {

        PowerGridVo powerLineVo = new PowerGridVo();
        List<DeviceFeeder> dmsFeederDeviceList = deviceApiCollector.getDeviceFeeders();
        //空判断
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dmsFeederDeviceList)) {
            // List为null或空的处理逻辑
            return null;
        }
        //线路供电区域
        powerLineVo.setSupplyArea(dmsFeederDeviceList.stream()
                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
                .distinct()                     // 去重
                .collect(Collectors.joining(", ")));

        //总长度
        Double lineLength = dmsFeederDeviceList.stream()
                // 提取BigDecimal字段
                .mapToDouble(DeviceFeeder::getLength)
                // 过滤空值（如果字段可能为null）
                .filter(Objects::nonNull)
                // 累加计算，初始值为0，使用BigDecimal加法
                .sum();
        //电缆长度
        Double cableLength = dmsFeederDeviceList.stream()
                // 提取BigDecimal字段
                .mapToDouble(DeviceFeeder::getCableLength)
                // 过滤空值（如果字段可能为null）
                .filter(Objects::nonNull)
                // 累加计算，初始值为0，使用BigDecimal加法
                .sum();

        //电缆转化率
        Double cableConversion = DoubleFormatter.formatToThreeDecimals(cableLength / lineLength);

        //封装对象
        powerLineVo.setLineNum(dmsFeederDeviceList.size());
        powerLineVo.setLineLength(lineLength);
        powerLineVo.setCableConversion(cableConversion);


        //环网柜
        Integer substationHWG = deviceApiCollector.getHwgList().size();
        //柱上变数量
        Integer poleTransformer = deviceApiCollector.getPoleTransformerList().size();
        //配电室
        Integer substationPDS = deviceApiCollector.getPdsList().size();
        //开关站
        Integer SubstationKGZ = deviceApiCollector.getKgzList().size();

        powerLineVo.setSwitchStations(SubstationKGZ);
        powerLineVo.setPoleSwitches(poleTransformer);
        powerLineVo.setDistributionRoomNum(substationPDS);
        powerLineVo.setRingMainUnitNum(substationHWG);

        //01专变，03公变
        //柱上变公变数量
        int polePubCount = (int) deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("03")).count();
        //柱上变专变数量
        int polePrvCount = (int) deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("01")).count();

        //柱上变容量
        Map<String, Double> poleTypeSumMap = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DevicePoleTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));

        //站内变公变数量
        int stationPubCount = (int) deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).count();
        //站内变专变数量
        int stationPrvCount = (int) deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).count();

        //站内变容量
        Map<String, Double> stationTypeSumMap = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DeviceStationTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
        //公变数量
        powerLineVo.setPubNum(polePubCount + stationPubCount);
        //专变数量
        powerLineVo.setPrivNum(polePrvCount + stationPrvCount);
        //公变容量
        powerLineVo.setPubRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("03") == null ? 0.0 : poleTypeSumMap.get("03")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("03") == null ? 0.0 : stationTypeSumMap.get("03")));
        //专变容量
        powerLineVo.setPrivRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("01") == null ? 0 : poleTypeSumMap.get("01")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("01") == null ? 0.0 : stationTypeSumMap.get("01")));

        return powerLineVo;
    }
    /**
     * 公变专变(api)
     */
    public PublicSpecializedVo mediumPublicSpecialized(DeviceApiCollector deviceApiCollector) throws Exception {

        PublicSpecializedVo publicSpecializedVo = new PublicSpecializedVo();
        //01专变，03公变
        //柱上变公变
        List<DevicePoleTransformer> polePubList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("03")).collect(Collectors.toList());
        //柱上变专变
        List<DevicePoleTransformer> polePrvList = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature().equals("01")).collect(Collectors.toList());
        //柱上变容量
        Map<String, Double> poleTypeSumMap = deviceApiCollector.getPoleTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DevicePoleTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));

        //站内变公变
        List<DeviceStationTransformer> stationPubList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("03")).collect(Collectors.toList());
        //站内变专变数量
        List<DeviceStationTransformer> stationPrvList = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> StringUtils.isNotEmpty(e.getUseNature()) && e.getUseNature().equals("01")).collect(Collectors.toList());

        //站内变容量
        Map<String, Double> stationTypeSumMap = deviceApiCollector.getDeviceStationTransformerList().stream().filter(e -> e.getUseNature() != null && e.getRatedCapacity() != null).collect(Collectors.groupingBy(DeviceStationTransformer::getUseNature, Collectors.summingDouble(e -> Double.parseDouble(e.getRatedCapacity()))));
        //公变数量
        publicSpecializedVo.setPubNum(polePubList.size() + stationPubList.size());
        //专变数量
        publicSpecializedVo.setPrivNum(polePrvList.size() + stationPrvList.size());
        //公变容量
        publicSpecializedVo.setPubRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("03") == null ? 0.0 : poleTypeSumMap.get("03")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("03") == null ? 0.0 : stationTypeSumMap.get("03")));
        //专变容量
        publicSpecializedVo.setPrivRatedCapacity((poleTypeSumMap.size() == 0 ? 0.0 : poleTypeSumMap.get("01") == null ? 0 : poleTypeSumMap.get("01")) + (stationTypeSumMap.size() == 0 ? 0.0 : stationTypeSumMap.get("01") == null ? 0.0 : stationTypeSumMap.get("01")));
        publicSpecializedVo.setPubDevicePoleTransformers(polePubList);
        publicSpecializedVo.setPrvDevicePoleTransformers(polePrvList);
        publicSpecializedVo.setPubDeviceStationTransformers(stationPubList);
        publicSpecializedVo.setPrvDeviceStationTransformers(stationPrvList);

        return publicSpecializedVo;
    }

    /**
     * 线路
     * @param deviceApiCollector
     * @return
     * @throws Exception
     */
    public FeederFoundationVo feederFoundation(DeviceApiCollector deviceApiCollector) throws Exception {
        List<DeviceFeeder> deviceFeederList = deviceApiCollector.getDeviceFeeders();
        FeederFoundationVo feederHorizontalAnalysisVo = new FeederFoundationVo();
        //大馈线总长度
        Double dkxLength = DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLength).sum());
        feederHorizontalAnalysisVo.setLineLength(dkxLength);
        //电缆长度
        feederHorizontalAnalysisVo.setCableLength(DoubleFormatter.formatToThreeDecimals4(deviceFeederList.stream().mapToDouble(DeviceFeeder::getCableLength).sum()));
        //电缆化率
        feederHorizontalAnalysisVo.setCableConversionRate(DoubleFormatter.formatToThreeDecimals2(feederHorizontalAnalysisVo.getCableLength() / dkxLength));
        //架空长度
        feederHorizontalAnalysisVo.setJkLength(DoubleFormatter.formatToThreeDecimals4((deviceFeederList.stream().mapToDouble(DeviceFeeder::getOverheadLength).sum())));
        //线路供电区域
        feederHorizontalAnalysisVo.setSupplyArea(deviceFeederList.stream()
                .map(DeviceFeeder::getSupplyArea)  // 提取要去重的字段
                .distinct()                     // 去重
                .collect(Collectors.joining(", ")));
        //线路条数
        feederHorizontalAnalysisVo.setLineNum(deviceFeederList.size());
        //可开放容量
        feederHorizontalAnalysisVo.setOpenTotalCapacity(deviceFeederList.stream().mapToDouble(DeviceFeeder::getLineCapFree).sum());
        feederHorizontalAnalysisVo.setDeviceFeeders(deviceFeederList);
        //查询重载等状态
        LambdaQueryWrapper<NtFeeder> ntFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ntFeederLambdaQueryWrapper.in(NtFeeder::getPsrId, deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
        List<NtFeeder> loadList = ntFeederMapper.selectList(ntFeederLambdaQueryWrapper);
        //正常数量
        feederHorizontalAnalysisVo.setNormalNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) <= 0.5).count());
        //重载数量
        feederHorizontalAnalysisVo.setHeavyLoadNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && 0.5 < Double.parseDouble(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) <= 0.8).count());
        //超载数量
        feederHorizontalAnalysisVo.setOverLoadNum((int) loadList.stream().filter(e -> StringUtils.isNotEmpty(e.getHisMaxLoadRate()) && Double.parseDouble(e.getHisMaxLoadRate()) > 0.8).count());
        return feederHorizontalAnalysisVo;
    }

    /**
     * 主变
     * @return
     * @throws Exception
     */
    public TransformerFoundationVo transformerFoundation(DeviceApiCollector deviceApiCollector) throws Exception {
        //查主变
//        List<PullDownMenuStringSon> inSubstationList = iPowerService.pullDownMenuSubstation(code);
//        LambdaQueryWrapper<DeviceNtHighTransformer> lqw = new LambdaQueryWrapper<>();
//        lqw.in(DeviceNtHighTransformer::getStation, inSubstationList.stream().map(PullDownMenuStringSon::getCode).distinct().collect(Collectors.toList()));
//        List<DeviceNtHighTransformer> ntHighTransformerList = ntHighTransformerNewMapper.selectList(lqw);
        List<DeviceZB> zbs = deviceApiCollector.getZbList();

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(zbs)) {
            return null;
        }
        //总容量
        Double capacity = zbs.stream().mapToDouble(DeviceZB::getRatedCapacity).sum();

//        LocalDate today = powerMapper.selectTime().toInstant()
//                .atZone(ZoneId.systemDefault())
//                .toLocalDate();
//        // 获取当前日期（不包含当天，所以从昨天开始计算）
//
//        LocalDate yesterday = today.minusDays(1);
//
//        // 计算7天前的第一天和最后一天（不包含今天）
//        LocalDate firstDay = yesterday.minusDays(day - 1); // 7天前的第一天
//        LocalDate lastDay = yesterday;               // 7天前的最后一天（昨天）
//
//        // 定义日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//
//        // 转换为字符串
//        String firstDayStr = firstDay.format(formatter);
//        String lastDayStr = lastDay.format(formatter);
//
//
//        Load loadMW = powerServiceImpl.selectTransformerLoadMW(firstDayStr, lastDayStr, ntHighTransformerList.stream().map(DeviceNtHighTransformer::getPsrId).collect(Collectors.toList()));

        TransformerFoundationVo transformerFoundationVo = new TransformerFoundationVo();
        transformerFoundationVo.setNormalNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 3).count());
        transformerFoundationVo.setHeavyLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 2).count());
        transformerFoundationVo.setOverLoadNum((int) zbs.stream().filter(e -> e.getType() != null && e.getType() == 1).count());
        transformerFoundationVo.setTotalCapacity(capacity);
        transformerFoundationVo.setTotalNum(zbs.size());
        transformerFoundationVo.setZbList(zbs);
        return transformerFoundationVo;
    }
}
