package com.ruoyi.service.power;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.entity.power.vo.CapacityAnalysisVo;
import com.ruoyi.entity.power.vo.FeederHorizontalAnalysisVo;

import java.text.ParseException;

public interface IPowerDistributionService {
    /**
     *配电网——配电网设备水平分析
     */
    FeederHorizontalAnalysisVo feederHorizontalAnalysis(String psrId);
    /**
     *配电网——配电网容量分析
     */
    CapacityAnalysisVo capacityAnalysis(String code,Integer pageNum,Integer pageSize);

    /**
     *配电网——供电能力分析（负载分析）
     */
    CapacityAnalysisVo loadAnalysis(String code,String time,Integer pageNum,Integer pageSize) throws ParseException, JsonProcessingException;


}
