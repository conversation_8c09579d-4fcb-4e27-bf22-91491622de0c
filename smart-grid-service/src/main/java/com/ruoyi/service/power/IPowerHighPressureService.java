package com.ruoyi.service.power;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.entity.device.DevicePoleTransformer;
import com.ruoyi.entity.device.DeviceStationTransformer;
import com.ruoyi.entity.device.DeviceZB;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.vo.ProblemVo;

import java.text.ParseException;
import java.util.List;

public interface IPowerHighPressureService {
    /**
     * 查询网格统计高压评估主变基础模块
     * @param code
     * @return
     */
    TransformerFoundationVo transformerFoundation(String code) throws Exception;

    /**
     * 查询网格统计高压评估主变基础模块
     */
    TransformerQualifyVo transformerQualify(String code);

    /**
     * 查询网格统计高压评估主变最大值最小值模块
     */
    TransformerPeakVo transformerMaxAndMin(String code) throws ParseException, JsonProcessingException;

    /**
     * 查询网格统计高压评估线路基础模块
     * @param code
     * @return
     */
    FeederFoundationVo feederFoundation(String code) throws Exception;
    /**
     * 查询网格统计高压评估线路供电区域
     * @param code
     * @return
     */
    FeederSupplyAreaVo feederSupplyArea(String code) throws ParseException, JsonProcessingException;

    /**
     * 查询网格统计高压评估主变最大值最小值模块
     * @param code
     * @return
     */
    TransformerPeakVo feederMaxAndMin(String code) throws ParseException, JsonProcessingException;

    /**
     * 查询中压的主变信息
     * @param code
     * @return
     */
    PublicSpecializedVo mediumPublicSpecialized(String code) throws Exception;
    /**
     * 查询中压的线路信息
     * @param code
     * @return
     */
    MediumFeederFoundationVo mediumFeeder(String code);

    TableDataInfo<DeviceZB> selectZB(String code, Integer pageNum, Integer pageSize)throws JsonProcessingException;

    TableDataInfo<DevicePoleTransformer> selectPubPoleTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException;

    TableDataInfo<DevicePoleTransformer> selectPrvPoleTransformer(String code, Integer pageNum, Integer pageSize) throws JsonProcessingException;

    TableDataInfo<DeviceStationTransformer> selectPubStationTransformer(String code, Integer pageNum, Integer pageSize)throws JsonProcessingException;

    TableDataInfo<DeviceStationTransformer> selectPrvStationTransformer(String code, Integer pageNum, Integer pageSize)throws JsonProcessingException;
}
