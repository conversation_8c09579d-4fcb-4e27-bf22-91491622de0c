package com.ruoyi.service.power;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.entity.power.bo.DailyLoadUtilizationBo;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.power.bo.AnalysisBo;
import com.ruoyi.entity.power.bo.HistoryAnalysisBo;
import com.ruoyi.entity.power.bo.LoadAnalysisBo;
import com.ruoyi.entity.power.bo.LoadBo;
import com.ruoyi.entity.power.vo.*;
import com.ruoyi.entity.problem.bo.LoadMWExcelBo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

public interface IPowerTransformerService {
    /**
     * 主变——根据网格id查询所有网格
     */
    Page<DeviceNtHighTransformer> girdTransformer(String code, Integer pageNum, Integer pageSize) throws IOException;

    /**
     * 主变——主变分析
     */
    PowerTransformerVo analysisTransformer(AnalysisBo bo) throws IOException, ParseException;

    /**
     * 根据id查主变详情
     * @param id
     * @return
     */
    DeviceNtHighTransformer byId(String id);

    /**
     * 主变——线路分析
     */
    PowerTransformerVo analysisFeeder(AnalysisBo bo) throws IOException, ParseException;

    /**
     * 主变——负载率分析历史记录查询
     */
    Page<PowerTransformerSelectVo> selectLoad(LoadBo bo) throws IOException, ParseException;

    /**
     * 主变——负载率历史记录查询
     */
    HistoryAnalysisVo selectLoadHistoryAnalysis(HistoryAnalysisBo bo) throws IOException, ParseException;

    /**
     * 主变——高峰负载率分析
     */
    LoadAnalysisVo peakLoadAnalysis(LoadAnalysisBo bo) throws IOException, ParseException;

    List<DeviceNtHighTransformer> getNtHighTransformerList(String code);
    /**
     * 线路——高峰负载率分析
     */
    LoadAnalysisVo peakLoadAnalysisFeeder(LoadAnalysisBo bo) throws IOException, ParseException;
    /**
     * 主变——低谷负载率分析
     */
    LoadAnalysisVo valleyLoadAnalysis(LoadAnalysisBo bo) throws IOException, ParseException;

    /**
     * 线路——低谷负载率分析
     */
    LoadAnalysisVo valleyLoadAnalysisFeeder(LoadAnalysisBo bo) throws IOException, ParseException;

    /**
     * 典型日负荷利用率
     */
    DailyLoadUtilizationVo dailyLoadUtilization(DailyLoadUtilizationBo bo) throws IOException, ParseException;

      /**
     * 最大日负荷利用率
     */
      MaxLoadUtilizationVo maxLoadUtilization(DailyLoadUtilizationBo bo) throws IOException, ParseException;

    Boolean testFeeder() throws Exception;


    Boolean testFeederMW() throws Exception;

    Boolean testTransformer() throws Exception;

    Boolean testTransformerMW() throws Exception;

    boolean synchronous() throws IOException;

    /**
     * 主变——负荷变化趋势展示和特征分析
     */
    LoadMWTrendAndAnalysisVo transformerLoadMWTrendAndAnalysis(AnalysisBo bo) throws ParseException, JsonProcessingException;

    LoadMWTrendAndAnalysisVo feederLoadMWTrendAndAnalysis(AnalysisBo bo) throws ParseException, JsonProcessingException;

    void transformerLoadMWExcel(LoadMWExcelBo bo, HttpServletResponse response) throws Exception;

    void feederLoadMWExcel(LoadMWExcelBo bo, HttpServletResponse response) throws ParseException, JsonProcessingException;
}
