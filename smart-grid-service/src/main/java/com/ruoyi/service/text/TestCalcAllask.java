package com.ruoyi.service.text;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapProxy;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.entity.calc.SimPfRetNew;
import com.ruoyi.listener.CalcListener;
import com.ruoyi.mapper.calc.TrendCalcMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 潮流计算
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TestCalcAllask {


    @Value("${znap-service.powerRangeUrl}")
    private String powerRangeUrl;
    @Value("${znap-service.calcUrl}")
    private String calcUrl;

    private final TrendCalcMapper trendCalcMapper;
    ObjectMapper objectMapper = new ObjectMapper() {{
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addDeserializer(Date.class, new DateDeserializers.TimestampDeserializer());
        this.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        this.registerModule(simpleModule);
        this.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
    }};


    public void loadFlowCalculation(String feederPsrId) throws Exception {

        List<SimPfRetNew> simPfRetList = new ArrayList<>();

        List<String> feederIds = new ArrayList<>();
        String psrId = "PD_dkx_" + feederPsrId;
        feederIds.add(psrId);

        // 1. 查询线路供电范围
        DevParams devParams = new DevParams().setGroupId("nanjing").setFeederIds(feederIds).setDevTypes(Arrays.asList(208, 209));

        String rsp = HttpUtils.postBody(powerRangeUrl, Collections.emptyMap(), devParams);
        List<FeederPowerRange> feederPowerRangeList = objectMapper.readValue(rsp, new TypeReference<List<FeederPowerRange>>() {
        });
        // 2. 数据分组处理
        Map<String, Map<String, List<String>>> feederGrp = new HashMap<>();
        for (FeederPowerRange feederPowerRange : feederPowerRangeList) {
            String feederId = feederPowerRange.getFeederId();
            Map<String, List<String>> psrTypeGrp = feederGrp.computeIfAbsent(feederId, key -> new HashMap<>());
            List<FeederPower> devList = feederPowerRange.getDevList();
            for (FeederPower feederPower : devList) {
                String psrType = feederPower.getPsrType();
                String grpKey;
                if ("0302".equals(psrType) || "0110".equals(psrType)) {
                    grpKey = "dt";
                } else {
                    grpKey = "ld";
                }
                psrTypeGrp.computeIfAbsent(grpKey, key -> new ArrayList<>()).addAll(getDefaultList(feederPower.getPsrId()));
            }
        }
        // 3. 循环批量调用计算潮流接口
        for (String feederId : feederGrp.keySet()) {
            CalcParams calcParams = new CalcParams("nanjing", "PD_dkx_" + feederId);
            Map<String, List<String>> psrTypeGrp = feederGrp.get(feederId);
            List<String> dtList = psrTypeGrp.get("dt");
            List<String> ldList = psrTypeGrp.get("ld");
            if (CollectionUtil.isEmpty(dtList) || CollectionUtil.isEmpty(ldList)) {
                continue;
            }
            TypeReference<List<BigDecimal>> curveType = new TypeReference<List<BigDecimal>>() {
            };
            Map<String, Object> source = MapUtil.<String, Object>builder()
                    .put("id", "PD_dkx_" + feederId)
                    .put("vec_v_kv", mrKvValue())
                    .put("vec_va", mrVaValue()).build();
            // 查询配变、负荷曲线
            List<VecRunDomain> dtCurveList = handlerCurveList(dtList, curveType);
            List<VecRunDomain> dlCurveList = handlerCurveList(ldList, curveType);
            calcParams.setVec_run_dt(dtCurveList).setVec_run_ld(dlCurveList).setVec_run_source(Collections.singletonList(source)).setIs_pf(1);
            String resStr = HttpUtils.postBody(calcUrl, Collections.emptyMap(), calcParams);
            SimPfRetNew simPfRet = objectMapper.readValue(resStr, new TypeReference<SimPfRetNew>() {
            });
            simPfRetList.add(simPfRet);
        }
      //  CalcListener calcListener = new CalcListener(trendCalcMapper, simPfRetList, consumer);
       // calcListener.start();
    }

    private List<VecRunDomain> handlerCurveList(List<String> psrIds, TypeReference<List<BigDecimal>> curveType) throws IOException {
        List<VecRunDomain> dtCurveList = new ArrayList<>();
        for (Map<String, Object> info : queryCurveList(psrIds)) {
            MapProxy mapProxy = MapProxy.create(info);
            String id = mapProxy.getStr("id");
            String vecPStr = mapProxy.getStr("vecP"), vecQStr = mapProxy.getStr("vecQ");
            List<BigDecimal> vecP = StringUtils.isBlank(vecPStr) ? IntStream.range(0, 96).mapToObj(i -> new BigDecimal("0.0")).collect(Collectors.toList()) : objectMapper.readValue(vecPStr, curveType);
            List<BigDecimal> vecQ = StringUtils.isBlank(vecQStr) ? IntStream.range(0, 96).mapToObj(i -> new BigDecimal("0.0")).collect(Collectors.toList()) : objectMapper.readValue(vecQStr, curveType);
            dtCurveList.add(new VecRunDomain().setId(id)
                    .setVec_p(vecP.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList()))
                    .setVec_q(vecQ.stream().map(BigDecimal::abs).map(item -> item.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.0") : item).collect(Collectors.toList())));
        }
        return dtCurveList;
    }


    private List<Map<String, Object>> queryCurveList(List<String> dtList) {
        if (CollectionUtil.isNotEmpty(dtList)) {
            return getDefaultList(trendCalcMapper.queryCurveListByPsrId(dtList));
        }
        return Collections.emptyList();
    }

    private <T> List<T> getDefaultList(List<T> list) {
        return list == null ? Collections.emptyList() : list;
    }

    public static List<Double> mrKvValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            double randomDouble = RandomUtil.randomDouble(10, 12);
            doubleList.add(new BigDecimal(randomDouble).setScale(4, RoundingMode.HALF_UP).doubleValue());
        }
        return doubleList;
    }

    public static List<Double> mrVaValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            doubleList.add(0.0d);
        }
        return doubleList;
    }

    @Accessors(chain = true)
    @Data
    public static class DevParams {
        private String groupId;
        private List<String> feederIds;
        private List<Integer> devTypes = new ArrayList<>();
    }

    @Accessors(chain = true)
    @Data
    public static class CalcParams {
        public CalcParams(String groupId, String feeder_id) {
            this.groupId = groupId;
            this.feeder_id = feeder_id;
        }

        /**
         * 消息态
         */
        private String groupId;
        /**
         * 馈线id
         */
        private String feeder_id;
        /**
         * 是否潮流计算
         */
        private int is_pf;
        /**
         * 是否断路计算
         */
        private int is_sc;
        /**
         * 添加的光伏设备属性
         */
        private List<Map<String, Object>> vec_para_gen = new ArrayList<>();
        /**
         * 添加的光伏设备曲线有功无功
         */
        private List<Map<String, Object>> vec_run_gen = new ArrayList<>();
        /**
         * 负荷设备信息
         */
        private List<VecRunDomain> vec_run_ld = new ArrayList<>();
        /**
         * 配变设备信息
         */
        private List<VecRunDomain> vec_run_dt = new ArrayList<>();
        /**
         * 电源信息
         */
        private List<Map<String, Object>> vec_run_source = new ArrayList<>();
    }


    @Accessors(chain = true)
    @Data
    public static class VecRunDomain {
        private String id;
        private List<BigDecimal> vec_p;
        private List<BigDecimal> vec_q;
    }

    @Data
    public static class FeederPowerRange {
        private String feederId;
        private List<FeederPower> devList;
    }

    @Data
    public static class FeederPower {
        private String psrType;
        private List<String> psrId;
    }
}
