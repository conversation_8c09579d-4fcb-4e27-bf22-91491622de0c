package com.ruoyi.service.text;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.metadata.Head;
import com.beust.ah.A;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.psm.AmapSdkCommon;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.map.impl.SelectCoords;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.util.LineStringToPolygon;
import com.ruoyi.util.PolygonExpander;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class TestNearFeederService {


    @Autowired
    ISingMapService singMapService;

    public HashMap<String, List<List<Double>>> getNearFeederPolygon(String feederId, String type, double radio) throws IOException {
        HashMap<String, List<List<Double>>> result = new HashMap<>();

        // 坐标
        if (StringUtils.equals(type, "1") || StringUtils.equals(type, "2")) {
            List<List<double[]>> point = StringUtils.equals(type, "1") ? getPoint(feederId) : getPbPoint(feederId);
            List<List<Double>> fill = LineStringToPolygon.convertToPolygon(point);
            // 向外扩大1000米
            List<List<Double>> expanded = PolygonExpander.expandPolygon(fill, radio);
            result.put("fill", fill);
            result.put("expandedFill", expanded);
        }
        if (StringUtils.equals(type, "3")) {
            // 缓存区域
            List<List<double[]>> point = getPoint(feederId);
            List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(point, radio);
            result.put("fill", bufferPolygon);
        }


        return result;
    }

    private static List<List<double[]>> getPoint(String feederId) throws IOException {
        // 批量调用参数
        List<Map<String, Object>> params = new ArrayList<>();
        params.add(MapUtil.<String, Object>builder()
                .put("psrId", feederId)
                .put("psrType", "dkx")
                .put("distribution", 0).build());
        AmapSdkCommon.PowerGridFilter powerGridFilter = AmapSdkCommon.PowerGridFilter.construction(params.size(), params).attrName(Arrays.asList("psrId", "psrType", "coordinate", "psrName"));

        List<AmapSdkCommon.DeviceRspModel<AmapSdkCommon.DeviceInfo>> deviceInfos = AmapSdkCommon.queryDeviceById(powerGridFilter);

        for (AmapSdkCommon.DeviceRspModel<AmapSdkCommon.DeviceInfo> rspModel : deviceInfos) {
            if (rspModel != null && !CollectionUtils.isEmpty(rspModel.getPsrList())) {
                List<AmapSdkCommon.DeviceInfo> psrList = rspModel.getPsrList();
                for (AmapSdkCommon.DeviceInfo deviceInfo : psrList) {
                    System.out.println(deviceInfo);
                    String coordinateStr = deviceInfo.getCoordinate();

                    String coordinatePairs = SelectCoords.parseCoordinatePairs(coordinateStr);

                    return CoordinateConverter.split(coordinatePairs);
                }
            }
        }
        return null;
    }

    private List<List<double[]>> getPbPoint(String feederId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<double[]> coords = new ArrayList<>();
        List<List<double[]>> result = new ArrayList<>();
        result.add(coords);

        for (Node node : nodePath.getPbList()) {
            Geometry geometry = node.getGeometry();
            if (geometry != null) {
                List<Double> doubles = CoordinateConverter.convertGeometryToDoubleList(geometry);
                coords.add(new double[]{doubles.get(0), doubles.get(1)});
            }
        }
        return result;
    }


    public static void main(String[] args) throws IOException {
        List<List<double[]>> point = getPoint("10DKX-463361");
        System.out.println(point);
    }
}
