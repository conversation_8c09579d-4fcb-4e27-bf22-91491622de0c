package com.ruoyi.service.text;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.JsonUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.entity.calc.*;
import com.ruoyi.entity.device.bo.DevInfoBo;
import com.ruoyi.mapper.calc.*;
import com.ruoyi.service.calc.TrendCalcService;
import com.ruoyi.service.electricity.impl.ElectricityAnalysisServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class TrendTextCalcServiceImpl {

    @Value("${znap-service.powerRangeUrl}")
    private String powerRangeUrl = "";

    @Value("${znap-service.calcUrl}")
    private String calcUrl = "";


    private static final String groupId = "nanjing";

    private final PmsZhTelemetryValueMapper pmsZhTelemetryValueMapper;

    private final TrendCalcMapper trendCalcMapper;

    private final CalcInstanceInfoMapper calcInstanceInfoMapper;

    private final CalcRelationShipMapper calcRelationShipMapper;

    private final CalcAlarmRuleMapper calcAlarmRuleMapper;

    ObjectMapper objectMapper = new ObjectMapper();
    private static final TypeReference<List<BigDecimal>> BIG_DECIMAL_LIST_TYPE_REF = new TypeReference<List<BigDecimal>>() {
    };



    public R<?> calcSimulation(Map<String, Object> params) {

        // 查询网格所属线路
        List<ZhDmsGridFeeder> zhDmsGridFeeders = trendCalcMapper.queryFeederByGridCode(params.get("gridCode").toString());
        if (CollectionUtil.isEmpty(zhDmsGridFeeders)) {
            throw new RuntimeException("该网格所属馈线为空无法计算");
        }
        long problemId = Long.parseLong(params.get("problemId").toString());

        // 构建计算实例
        CalcInstanceInfo calcInstanceInfo = new CalcInstanceInfo();
        calcInstanceInfo.setGridCode(zhDmsGridFeeders.get(0).getPowerGridCode());
        calcInstanceInfo.setGridName(zhDmsGridFeeders.get(0).getPowerGridName());
        calcInstanceInfo.setState(0);
        calcInstanceInfo.setStartTime(new Date());
        calcInstanceInfo.setEndTime(null);
        calcInstanceInfo.setIsAuto(0);
        calcInstanceInfo.setIsRecycle(0);
        calcInstanceInfo.setInstanceId(buildInstanceId(zhDmsGridFeeders.get(0).getPowerGridCode()));
        calcInstanceInfo.setProblemId(problemId);
        // 新增实例记录
        calcInstanceInfoMapper.insert(calcInstanceInfo);

        if (params.get("calcWarnRule") != null) {
            List<CalcAlarmRule> calcWarnRules = JsonUtils.parseArray(JSONUtil.parseArray(params.get("calcWarnRule")).toString(), CalcAlarmRule.class);
            calcWarnRules = calcWarnRules.stream().map(item -> {
                item.setInstanceId(calcInstanceInfo.getInstanceId());
                return item;
            }).collect(Collectors.toList());
            calcAlarmRuleMapper.insertBatch(calcWarnRules);
        }

        // 构建计算参数
        CalcParams calcParams = new CalcParams();
        calcParams.setGroupId(groupId);
        calcParams.setVec_para_gen(new ArrayList<>());
        calcParams.setVec_run_gen(new ArrayList<>());
        calcParams.setIs_pf(1);
        calcParams.setIs_sc(1);

        // 存放实例关系
        List<CalcRelationShip> calcRelationShips = new ArrayList<>();
        List<String> feederIds = zhDmsGridFeeders.stream().map(ZhDmsGridFeeder::getFeeder).collect(Collectors.toList());

        // 异步调用
//        CompletableFuture.supplyAsync(() -> {
        for (String feederId : feederIds) {
            calcParams.setFeeder_id(feederId);
            // 循环计算馈线
            // 查询馈线负荷以及其特征曲线
            List<String> loadPsrIds = buildPsrIds(feederId, 209);
            List<VecRunDomain> ldVecRunDomains = new ArrayList<>();
            if (CollectionUtil.isEmpty(loadPsrIds)) {
                log.info("当前馈线：{}，查询不到对应负荷设备", feederId);
            } else {
                ldVecRunDomains = mockdata(loadPsrIds, "370000");

            }
            calcParams.setVec_run_ld(ldVecRunDomains);
            List<String> pbPsrIds = buildPsrIds(feederId, 208);

            List<VecRunDomain> pbVecRunDomains = new ArrayList<>();
            if (CollectionUtil.isEmpty(pbPsrIds)) {
                log.info("当前馈线：{}，查询不到对应配变设备", feederId);
            } else {
                pbVecRunDomains = mockdata(pbPsrIds, "0110");

            }
            calcParams.setVec_run_dt(pbVecRunDomains);
            if (CollectionUtil.isEmpty(pbPsrIds) && CollectionUtil.isEmpty(loadPsrIds)) {
                log.info("当前馈线：{}，查询不到对应配变设备和负荷设备跳过计算", feederId);
                continue;
            }

            // 电源参数
            Map<Object, Object> source = MapUtil.builder().put("id", feederId).put("vec_v_kv", mrKvValue()).put("vec_va", mrVaValue()).build();
            calcParams.setVec_run_source(Collections.singletonList(source));
            String result = null;
            try {
                result = HttpUtils.postBody(calcUrl, null, calcParams);
            } catch (IOException e) {
                log.error(e.getMessage());
            }
            JSONObject resultJson = JSONUtil.parseObj(result);
            if (resultJson.get("msgId") != null) {
                String msgId = resultJson.get("msgId").toString();
                log.info("当前馈线：{}，计算成功，消息id：{}", feederId, msgId);
                CalcRelationShip calcRelationShip = new CalcRelationShip();
                calcRelationShip.setInstanceId(calcInstanceInfo.getInstanceId());
                calcRelationShip.setMsgId(msgId);
                calcRelationShips.add(calcRelationShip);
            }
        }
        // 新增实例关系
        calcRelationShipMapper.insertBatch(calcRelationShips);
//            return null;
//        });
        return R.ok("消息发送成功", calcInstanceInfo);
    }

    /**
     * 模拟数据
     * @param pbPsrIds
     * @param psrType
     * @return
     */
    private List<VecRunDomain> mockdata(List<String> pbPsrIds, String psrType) {
        List<VecRunDomain> vecRunDomains = new ArrayList<>();
        for (String pbPsrId : pbPsrIds) {
            VecRunDomain vecRunDomain = new VecRunDomain();
            vecRunDomain.setId(pbPsrId);
            // [20.8, 21.44, 19.18, 20.24, 18.94, 18.34, 16.66, 16.78, 16.7, 18.36, 17.4, 15.5, 15.6, 17.94, 16.32, 17.28, 15.24, 17.0, 18.78, 15.64, 18.6, 20.7, 16.88, 21.14, 31.56, 26.04, 28.02, 28.64, 32.54, 24.34, 19.46, 19.5, 23.66, 25.92, 19.76, 18.44, 16.5, 15.68, 20.06, 19.24, 20.36, 19.24, 18.34, 20.74, 25.46, 31.24, 21.9, 23.2, 19.2, 19.76, 20.48, 25.64, 23.36, 19.4, 19.18, 20.04, 18.56, 21.72, 24.48, 18.48, 18.86, 20.46, 25.36, 19.66, 20.72, 18.84, 17.74, 23.34, 23.32, 25.02, 33.68, 29.68, 35.56, 28.6, 29.54, 40.26, 36.74, 29.92, 40.48, 37.4, 40.6, 44.06, 46.4, 48.66, 48.68, 40.94, 45.74, 42.3, 44.64, 40.58, 35.32, 33.02, 32.0, 36.86, 33.36, 29.3]
            // 生成随机数
            List<BigDecimal> vecP = ElectricityAnalysisServiceImpl.generateRandomDoubles(96, 15, 50).stream().map(BigDecimal::new).collect(Collectors.toList());
            List<BigDecimal> vecQ = ElectricityAnalysisServiceImpl.generateRandomDoubles(96, 5, 7).stream().map(BigDecimal::new).collect(Collectors.toList());
            //保留四位小数
            vecP = vecP.stream().map(item -> new BigDecimal(item.toPlainString()).setScale(4, RoundingMode.HALF_UP)).collect(Collectors.toList());
            vecQ = vecQ.stream().map(item -> new BigDecimal(item.toPlainString()).setScale(4, RoundingMode.HALF_UP)).collect(Collectors.toList());
            vecRunDomain.setVec_p(vecP);
            vecRunDomain.setVec_q(vecQ);
            vecRunDomain.setVecP(JsonUtils.toJsonString(vecP));
            vecRunDomain.setVecQ(JsonUtils.toJsonString(vecQ));
            vecRunDomain.setId("PD_" + psrType + "_" + pbPsrId);
            vecRunDomains.add(vecRunDomain);
        }
        return vecRunDomains;
    }

    private List<VecRunDomain> mockByDevInfos(List<DevInfo> devInfos) {
        ArrayList<VecRunDomain> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(devInfos)) {
            return result;
        }

        for (DevInfo devInfo : devInfos) {
            result.addAll(mockdata(devInfo.getPsrId(), devInfo.getPsrType()));
        }

        return result;
    }


    public String buildInstanceId(String gridCode) {
        StringBuilder instanceId = new StringBuilder();
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        String formatDate = dateFormat.format(date);
        instanceId.append(gridCode);
        instanceId.append("_");
        instanceId.append(formatDate);
        instanceId.append("_");
        List<CalcInstanceInfo> calcInstanceInfos = calcInstanceInfoMapper.selectList();
        int count = calcInstanceInfos.stream().mapToInt(CalcInstanceInfo::getId).max().orElse(0);
        count++;
        instanceId.append(count);

        return instanceId.toString();
    }



    public R<?> findPowerRangeByFeeder(String feederId,List<Integer> devTypes) {
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(devTypes);
        String result = null;
        try {
            result = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.ok(JSONUtil.parseArray(result));
    }


    // 构建负荷或者陪伴ID集合
    public List<String> buildPsrIds(String feederId, int type) {
        // 查询负荷id
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(Collections.singletonList(type));
        String loadPsrInfo = null;
        try {
            loadPsrInfo = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray jsonArray = JSONUtil.parseArray(loadPsrInfo);
        if (CollectionUtil.isNotEmpty(jsonArray)) {
            PowerRangeResult bean = BeanUtil.toBean(jsonArray.get(0), PowerRangeResult.class);
            if (CollectionUtil.isNotEmpty(bean.getDevList())) {
                List<DevInfo> devList = bean.getDevList();
                return devList.get(0).getPsrId();
            }
        }
        return null;
    }

    public R<?> calc(CalcParams param) {
        param.setGroupId(groupId);
        String result;
        try {
            // 查询馈线负荷以及其特征曲线
            List<DevInfo> loadDevs = buildGroupDevs(param.getFeeder_id(), 209);
            if (CollectionUtil.isEmpty(loadDevs)) {
                throw new RuntimeException("负荷数据为空");
            }

            List<DevInfo> pbDevs = buildGroupDevs(param.getFeeder_id(), 208);
            if (CollectionUtil.isEmpty(pbDevs)) {
                throw new RuntimeException("配变数据为空");
            }

            List<VecRunDomain> loadPvs = mockByDevInfos(loadDevs);
            List<VecRunDomain> pbPvs = mockByDevInfos(pbDevs);

            // 负荷曲线
            param.setVec_run_ld(loadPvs);

            // 配变曲线
            param.setVec_run_dt(pbPvs);

            param.setIs_pf(1);

            param.setVec_para_gen(new ArrayList<>());
            param.setVec_run_gen(new ArrayList<>());

            param.setIs_sc(1);

            // v是 电压等级 上下波动 5%的范围内   va全是0 (电压等级我们使用线路的点呀等级即可)

            // 电源参数
            Map<Object, Object> source = MapUtil.builder().put("id", param.getFeeder_id()).put("vec_v_kv", mrKvValue()).put("vec_va", mrVaValue()).build();

            param.setVec_run_source(Collections.singletonList(source));
            result = HttpUtils.postBody(calcUrl, null, param);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
        return R.ok(JSONUtil.parseObj(result));
    }


    public TableDataInfo<AlarmDoMain> alarmByMsgId(PageQuery pageQuery, String msgId) {
        TableDataInfo<AlarmDoMain> build = new TableDataInfo<>();
        try {
            if (StringUtils.isBlank(msgId) && msgId == null) {
                throw new RuntimeException("消息msgId不能为空");
            }
            // 根据msgId查询记录表
            Map<String, Object> simPfRetInfo = trendCalcMapper.queryRetInfoByMsgId(msgId);
            if (simPfRetInfo == null) {
                build.setCode(500);
                throw new RuntimeException("消息不存在");
            }
            if (simPfRetInfo.get("endDt") == null) {
                build.setCode(200);
                build.setTotal(-1);
                throw new RuntimeException("潮流计算还没有结束");
            }
            if (simPfRetInfo.get("pfRet") == null) {
                build.setCode(500);
                throw new RuntimeException("潮流计算失败");
            }
            int retId = Integer.parseInt(simPfRetInfo.get("id").toString());
            // 查询告警
            List<AlarmDoMain> breakList = trendCalcMapper.queryBreakByRetId(retId);
            if (CollectionUtil.isEmpty(breakList)) {
                // 没有告警约定好code设置为1
                build.setMsg("暂无告警信息");
                build.setCode(-1);
                return build;
            }
            List<AlarmDoMain> alarmDoMains = breakList.stream().skip((long) (pageQuery.getPageNum() - 1) * pageQuery.getPageSize()).limit(pageQuery.getPageSize()).collect(Collectors.toList());
            build.setPageNum(pageQuery.getPageNum());
            build.setPageSize(pageQuery.getPageSize());
            build.setMsg("查询成功");
            build.setCode(200);
            build.setTotal(breakList.size());
            build.setRows(alarmDoMains);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return build;
    }


    public R querySegmentByPsrIdAndPsrType(String psrId, String psrType) {
        List<String> poleTypes = Arrays.asList("startPole", "stopPole");
        List<String> segmentPsrIdList = new ArrayList<>();
        if (psrType.equals("wlgt")) {
            // 转换psrId
            psrId = handlerPsr(psrId);
        }
        for (String poleType : poleTypes) {
            // 构建查询参数
            List<Map<Object, Object>> psrParams = genPsrParams(psrId, poleType);
            // 通用资源查询
            String post = null;
            try {
                post = HttpUtils.postBody(BusinessCenterConstant.BASE_URL + BusinessCenterConstant.TELEMETRY_URL, BusinessCenterConstant.COMMON_HEADER, psrParams);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String message = JSONUtil.parseObj(post).get("message").toString();
            if ("成功".equals(message)) {
                String psrTypeResult = JSONUtil.parseObj(JSONUtil.parseObj(post).get("result").toString()).get("dxd").toString();
                String records = JSONUtil.parseObj(psrTypeResult).get("records").toString();
                JSONArray jsonArray = JSONUtil.parseArray(records);
                if (CollectionUtil.isNotEmpty(jsonArray)) {
                    String segmentPsrId = JSONUtil.parseObj(jsonArray.get(0)).get("psrId").toString();
                    segmentPsrIdList.add(segmentPsrId);
                }
            }
        }
        return R.ok(segmentPsrIdList);
    }


    private String handlerPsr(String psrId) {
        List<Map<Object, Object>> param = new ArrayList<>();
        List<Map<Object, Object>> filters = new ArrayList<>();
        Map<Object, Object> filterOne = MapUtil.builder().put("compare", "in").put("fieldName", "psrState").put("fieldValue", "10,20").build();
        Map<Object, Object> filterTwo = MapUtil.builder().put("compare", "in").put("fieldName", "astId").put("fieldValue", psrId).build();
        filters.add(filterOne);
        filters.add(filterTwo);

        Map<Object, Object> build = MapUtil.builder().put("id", "100001").put("psrType", "0103").put("distribution", 0).put("params", MapUtil.builder().put("filters", filters).build()).build();
        param.add(build);
        HttpResponse stationStr = HttpUtil.createPost("http://egw.jn.js.sgcc.com.cn/psr-center/psrDSService/commonQuery").headerMap(BusinessCenterConstant.COMMON_HEADER, true).body(JSONUtil.toJsonStr(param)).execute();
        if (stationStr.isOk()) {
            JSONObject jsonObjectPsr = JSONUtil.parseObj(stationStr.body());
            if ("成功".equals(jsonObjectPsr.get("message"))) {
                JSONArray jsonArray = JSONUtil.parseArray(JSONUtil.parseObj(JSONUtil.parseObj(jsonObjectPsr.get("result")).get("0103")).get("records"));
                for (Object item : jsonArray) {
                    if (JSONUtil.parseObj(item).get("psrId") != null) {
                        return JSONUtil.parseObj(item).get("psrId").toString();
                    }
                }
            }
        } else {
            throw new RuntimeException("astId转化psrId失败");
        }
        return null;
    }

    public static String getSubStr(String feeder) {
        int startIndex = feeder.indexOf("_");
        int endIndex = feeder.indexOf("_", startIndex + 1);
        if (endIndex != -1) {
            return feeder.substring(endIndex + 1);
        } else {
            return "";
        }
    }

    public static List<Double> mrVaValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            doubleList.add(0.0);
        }
        return doubleList;
    }

    public static List<Double> mrKvValue() {
        List<Double> doubleList = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            double randomDouble = RandomUtil.randomDouble(10, 12);
            doubleList.add(new BigDecimal(randomDouble).setScale(4, RoundingMode.HALF_UP).doubleValue());
        }
        return doubleList;
    }


    public static List<Map<Object, Object>> genPsrParams(String psrId, String type) {
        List<Map<Object, Object>> param = new ArrayList<>();
        List<Map<Object, Object>> filters = new ArrayList<>();

        Map<Object, Object> filterOne = MapUtil.builder().put("compare", "=").put("fieldName", type).put("fieldValue", psrId).build();
        filters.add(filterOne);

        Map<Object, Object> filterTwo = MapUtil.builder().put("compare", "in").put("fieldName", "psrState").put("fieldValue", "10,20").build();
        filters.add(filterTwo);

        Map<Object, Object> build = MapUtil.builder().put("id", "100001").put("psrType", "dxd").put("distribution", 0).put("params", MapUtil.builder().put("filters", filters).put("page", 1).put("size", 10).build()).build();
        param.add(build);
        return param;
    }

    public interface BusinessCenterConstant {

        String BASE_URL = "http://egw.jn.js.sgcc.com.cn";
        String TELEMETRY_URL = "/psr-center/psrDSService/commonQuery";
        Map<String, String> COMMON_HEADER = Collections.unmodifiableMap(new HashMap<String, String>() {
            {
                this.put("x-system-code", "ztgl-app-06103087-daf9-4690-b7df-6d3a9f63f3e3");
                this.put("Authorization", "Bearer 6f0ad858-05a2-4525-b113-d938063d6b88");
                this.put("x-application-code", "app");
                this.put("Content-Type", "application/json");
                this.put(Header.ACCEPT_ENCODING.getValue(), "identity");
            }
        });
    }

    public List<DevInfoBo> buildDevs(String feederId, int type) {
        // 查询负荷id
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(Collections.singletonList(type));
        String loadPsrInfo = null;
        try {
            loadPsrInfo = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray jsonArray = JSONUtil.parseArray(loadPsrInfo);
        List<DevInfoBo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(jsonArray)) {
            PowerRangeResult bean = BeanUtil.toBean(jsonArray.get(0), PowerRangeResult.class);
            if (CollectionUtil.isNotEmpty(bean.getDevList())) {
                List<DevInfo> devList = bean.getDevList();
                for (DevInfo devInfo : devList) {
                    List<DevInfoBo> devs = devInfo.getPsrId().stream().map(id -> new DevInfoBo(id, devInfo.getPsrType())).collect(Collectors.toList());
                    result.addAll(devs);
                }
            }
        }
        return result;
    }

    public List<DevInfo> buildGroupDevs(String feederId, int type) {
        // 查询负荷id
        DevParams devParams = new DevParams();
        devParams.setGroupId(groupId);
        devParams.setFeederIds(Collections.singletonList(feederId));
        devParams.setDevTypes(Collections.singletonList(type));
        String loadPsrInfo = null;
        try {
            loadPsrInfo = HttpUtils.postBody(powerRangeUrl, null, devParams);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray jsonArray = JSONUtil.parseArray(loadPsrInfo);
        List<DevInfoBo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(jsonArray)) {
            PowerRangeResult bean = BeanUtil.toBean(jsonArray.get(0), PowerRangeResult.class);
            if (CollectionUtil.isNotEmpty(bean.getDevList())) {
                return bean.getDevList();
            }
        }
        return null;
    }
}
