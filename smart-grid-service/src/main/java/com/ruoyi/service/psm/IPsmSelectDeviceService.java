package com.ruoyi.service.psm;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.entity.device.DeviceSubstation;

import java.util.List;
import java.util.Map;

public interface IPsmSelectDeviceService {
    /**
     * 内网电网一张图查找网格下的变电站
     */
     List<DeviceSubstation> psmSelectDeviceSubstation(String code) throws Exception;
    /**
     * 内网电网一张图查找网格下的配网设备数据
     */
    List<Map<String, List<Map<String, Object>>>> psmSelectDevice(String code, List<String> typeList) throws Exception;

    /**
     * 根据网格id查询网格坐标
     * @param gridCode
     * @return
     */
    public List<List<Double>> getCoordinatesByGridCode(String gridCode) throws JsonProcessingException;
}
