package com.ruoyi.service.psm.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.SelectExternalUtil;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.map.PsrQuery;
import com.ruoyi.entity.map.PsrQueryInfo;
import com.ruoyi.entity.psm.MiddleCommon;
import com.ruoyi.entity.psm.PsmRequest;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.psm.IPsmSelectDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.*;

import static com.ruoyi.common.utils.util.MercatorLineCircleIntersection.wgs84ToMercatorList;
import static com.ruoyi.entity.cost.DeviceType.*;
import static com.ruoyi.util.coordinates.CoordinateConverter.parseCoordinates2;

@Service
@Slf4j
public class PsmSelectDeviceServiceImpl implements IPsmSelectDeviceService, MiddleCommon {
    @Value("${pmsUrl.spaceSelectDevice}")
    private String url;

    @Autowired
    ProblemMapper problemMapper;


    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 内网电网一张图查找网格下的变电站
     */
    @Override
    public List<DeviceSubstation> psmSelectDeviceSubstation(String code) throws Exception {
        String mercatorString = getMercatorString(code);
        if (StringUtils.isEmpty(mercatorString)) {
            return null;
        }
        //封装请求体
        PsmRequest psmRequest = new PsmRequest();
        //网格坐标
        psmRequest.setPolygon(mercatorString.substring(0, mercatorString.length() - 1));

        PsrQuery psrQuery = new PsrQuery();
        psrQuery.setPsrType(BDZ);
        psrQuery.setDistribution(1);

        PsrQueryInfo psrQueryInfo = new PsrQueryInfo();
        List<PsrQuery> psrQueryList = new ArrayList<>();
        psrQueryList.add(psrQuery);

        psrQueryInfo.setPsrQueryList(new ArrayList<>(psrQueryList));

        psmRequest.setPsrQueryInfo(psrQueryInfo);
        //访问接口获取数据
        String result = result(psmRequest);
        //开始解析数据
        List<Map<String, List<Map<String, Object>>>> resultMap = analysisPmsJson(result);
        List<DeviceSubstation> deviceSubstationList = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : resultMap.get(0).get(BDZ)) {
            DeviceSubstation deviceSubstation = new DeviceSubstation();
            deviceSubstation.setPsrId(stringObjectMap.get("psrId").toString());
            deviceSubstation.setName(stringObjectMap.get("psrName").toString());
            deviceSubstationList.add(deviceSubstation);
        }
        return deviceSubstationList;
    }

    /**
     * 查询配网的设备的结果
     */
    @Override
    public List<Map<String, List<Map<String, Object>>>> psmSelectDevice(String code,List<String> typeList) throws Exception {

        String mercatorString = getMercatorString(code);
        if (StringUtils.isEmpty(mercatorString)) {
            return null;
        }
        if(CollectionUtils.isEmpty(typeList)){
            typeList = Arrays.asList(HWG1, HWG2, KGZ, PDS, ZSB, ZNPDBYQ, SYB,DKX);
        }
        //访问接口获取数据
        String result = result(getPsmRequest(mercatorString, typeList));
        List<Map<String, List<Map<String, Object>>>> lists = analysisPmsJson(result);
        return lists;
    }



    /**
     * 设备请求实体list
     *
     * @throws Exception
     */
    public PsmRequest getPsmRequest(String mercatorString, List<String> typeList) {

        //封装请求体
        PsmRequest psmRequest = new PsmRequest();
        //网格坐标
        psmRequest.setPolygon(mercatorString.substring(0, mercatorString.length() - 1));

        List<PsrQuery> psrQueryList = new ArrayList<>();

        for (String type : typeList) {
            PsrQuery psrQuery = new PsrQuery();
            psrQuery.setPsrType(type);
            psrQuery.setDistribution(0);
            psrQueryList.add(psrQuery);

        }
        PsrQueryInfo psrQueryInfo = new PsrQueryInfo();

        psrQueryInfo.setPsrQueryList(psrQueryList);

        psmRequest.setPsrQueryInfo(psrQueryInfo);
        return psmRequest;
    }

    /**
     * 获取网格坐标
     *
     * @param code
     */
    public String getMercatorString(String code) throws JsonProcessingException {
        String coords = problemMapper.selectGridCoords(code);
        if (StringUtils.isBlank(coords)) {
            return null;
        }
        //将json坐标转成数组
        List<List<Double>> list = parseCoordinates2(coords);
        List<List<BigDecimal>> mercatorList = new ArrayList<>();
        String mercatorString = "";
        //将经纬度坐标数组转成墨卡托数组
        for (List<Double> doubles : list) {
            mercatorList.add(wgs84ToMercatorList(doubles.get(0), doubles.get(1)));
        }
        //将墨卡托数组保留小数点后7位，同时拼接成字符串
        for (List<BigDecimal> bigDecimals : mercatorList) {
            DecimalFormat df = new DecimalFormat("#.#######");
            df.setRoundingMode(RoundingMode.HALF_UP);
            df.setDecimalFormatSymbols(DecimalFormatSymbols.getInstance(Locale.US));
            mercatorString = mercatorString + df.format(bigDecimals.get(0)) + " " + df.format(bigDecimals.get(1)) + " ";
        }
        return mercatorString;

    }
    /**
     * 根据网格id查询网格坐标
     * @param gridCode
     * @return
     * @throws JsonProcessingException
     */
    public List<List<Double>> getCoordinatesByGridCode(String gridCode) throws JsonProcessingException {
        if (StringUtils.isEmpty(gridCode)) {
            log.warn("网格id为空");
            return null;
        }
        String coords = problemMapper.selectGridCoords(gridCode);
        if (StringUtils.isBlank(coords)) {
            log.warn("未查询到网格{}坐标信息", gridCode);
            return null;
        }
        //将json坐标转成数组
        return parseCoordinates2(coords);
    }
    /**
     * 电网一张图查设备数据
     **/
    private String result(PsmRequest psmRequest) throws JsonProcessingException {

        String targetString = SelectExternalUtil.SelectExternalUtil(url, mapper.writeValueAsString(psmRequest));
        return targetString;
    }

    /**
     * 解析Pms的json数据
     *
     * @param jsonString
     * @return
     * @throws Exception
     */
    public List<Map<String, List<Map<String, Object>>>> analysisPmsJson(String jsonString) throws Exception {
        JsonNode rootNode = mapper.readTree(jsonString);
        List<Map<String, List<Map<String, Object>>>> returnList = new ArrayList<>();
        for (JsonNode jsonNode : rootNode.path("result").path("psrDataList")) {
            Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
            List<Map<String, Object>> lsit = new ArrayList<>();
            JsonNode seriesArray = jsonNode.path("psrList");
            for (JsonNode ndoe : seriesArray) {
                Map<String, Object> hashMap = mapper.convertValue(ndoe, Map.class);
                lsit.add(hashMap);
            }
            resultMap.put(lsit.get(0).get("psrType").toString(), lsit);
            returnList.add(resultMap);
        }
        // 获取series数组
        return returnList;
    }
}
