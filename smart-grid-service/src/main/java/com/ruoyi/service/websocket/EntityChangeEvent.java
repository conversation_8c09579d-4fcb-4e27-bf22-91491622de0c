// EntityChangeEvent.java
package com.ruoyi.service.websocket;


import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import org.springframework.context.ApplicationEvent;

public class EntityChangeEvent extends ApplicationEvent {
    private final ProblemSchemeAnalysis entity;
    private final EventType eventType;

    public EntityChangeEvent(Object source, ProblemSchemeAnalysis entity, EventType eventType) {
        super(source);
        this.entity = entity;
        this.eventType = eventType;
    }

    public ProblemSchemeAnalysis getEntity() {
        return entity;
    }

    public EventType getEventType() {
        return eventType;
    }

    public enum EventType {
        CREATE, UPDATE, DELETE
    }
}
