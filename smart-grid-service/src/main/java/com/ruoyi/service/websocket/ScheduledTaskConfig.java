package com.ruoyi.service.websocket;

import kotlin.jvm.Synchronized;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.ScheduledFuture;

@Configuration
@EnableScheduling
public class ScheduledTaskConfig implements SchedulingConfigurer {

    @Autowired
    private WebSocketServiceHandler webSocketServiceHandler;

    private ScheduledTaskRegistrar taskRegistrar;
    private ScheduledFuture<?> scheduledFuture;

    // 任务状态
    private boolean taskRunning = false;
    private long fixedRate = 60 * 1000;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        this.taskRegistrar = taskRegistrar;
    }



    // 停止任务
    @Synchronized
    public void stopTask() {
        if (taskRunning && scheduledFuture != null) {
            scheduledFuture.cancel(false);
            taskRunning = false;
        }
    }

}
