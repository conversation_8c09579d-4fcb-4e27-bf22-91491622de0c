// WebSocketService.java
package com.ruoyi.service.websocket;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.annotation.WsRequestMapping;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.framework.websocket.BaseWebSocketService;
import com.ruoyi.framework.websocket.WebSocketSessionManager;
import com.ruoyi.framework.websocket.WebSocketTextMessage;
import com.ruoyi.mapper.plan.PlanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@WsRequestMapping(value = "/ws", tokenRequired = true)
@EnableWebSocket
@Slf4j
public class WebSocketServiceHandler extends BaseWebSocketService {
    @Autowired
    PlanMapper planMapper;
    private final WebSocketSessionManager sessionManager = new WebSocketSessionManager();

    private final ConcurrentHashMap<Long, ProblemSchemeAnalysis> cachedEntities = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, WebSocketSession> tokenSessionMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> tokenProblemIdMap = new ConcurrentHashMap<>();
    private boolean isEnabled = true;
    private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        System.out.println("WebSocket服务已启动，等待连接...");
    }

    @EventListener
    public void handleEntityChange(EntityChangeEvent event) {
        if (!isEnabled) return;

        ProblemSchemeAnalysis entity = event.getEntity();
        switch (event.getEventType()) {
            case CREATE:
            case UPDATE:
                cachedEntities.put(entity.getId(), entity);
//                webSocketHandler.pushEntity(entity);
                break;
            case DELETE:
                cachedEntities.remove(entity.getId());
                break;
        }
    }

    // 向指定用户广播ProblemSchemeAnalysis实体
    public void broadcastToUserByToken(ProblemSchemeAnalysis problemSchemeAnalysis) {
        Long id = problemSchemeAnalysis.getProblemId();

        List<String> keys = tokenProblemIdMap.entrySet().stream()
                .filter(entry -> entry.getValue().equals(id)) // 过滤条件
                .map(Map.Entry::getKey) // 提取 key
                .collect(Collectors.toList()); // 收集为列表

        for (String key : keys) {
            WebSocketSession session = tokenSessionMap.get(key);
            if (session != null && session.isOpen()) {
                try {
                    String jsonMessage = objectMapper.writeValueAsString(problemSchemeAnalysis);
                    session.sendMessage(new TextMessage(jsonMessage));
                    log.info("已向相关问题用户推送消息，id: {}", id);
                } catch (IOException e) {
                    log.error("向相关问题用户推送消息失败，id: {}", id, e);
                    // 发送失败时可以选择移除失效的Session
                    tokenSessionMap.remove(id);
                }
            } else {
                log.warn("没找到改问题id相关用户，id: {}", id);
            }
        }

    }

    @Override
    public WebSocketSessionManager getWebSocketSessionManager() {
        return sessionManager;
    }

    @Override
    public String broadCastChannelKey() {
        return null;
    }

    @Override
    public boolean onSessionConnected(WebSocketSession webSocketSession) {
        URI uri = webSocketSession.getUri();
        String query = uri.getQuery(); // 获取 "problemId=1"
        log.info("开始连接websocket.....{}", query);
        Map<String, String> params = parseQueryString(query);
        Long problemId = Long.parseLong(params.get("problemId"));
        // 从请求参数获取Token
//        Object tokenObj = webSocketSession.getAttributes().get(WebSocketAccessInterceptor.TOKEN_KEY);
        Object tokenObj = webSocketSession.getAttributes().get("token");

        if (tokenObj == null) {
            log.warn("WebSocket会话[{}]连接失败：缺少token参数", webSocketSession.getId());
            return false;
        }
        String token = (String) tokenObj;
        if (token.isEmpty()) {
            log.warn("WebSocket会话[{}]连接失败：token为空", webSocketSession.getId());
            return false;
        }

        //token绑定用户
        tokenSessionMap.put(token, webSocketSession);
        tokenProblemIdMap.put(token, problemId);


        //token绑定问题

        log.info("WebSocket会话[{}]已连接，关联token[{}]", webSocketSession.getId(), token);
        return true;
    }

    // 辅助方法：解析 query 参数
    private Map<String, String> parseQueryString(String query) {
        Map<String, String> result = new HashMap<>();
        if (query == null || query.isEmpty()) {
            return result;
        }
        String[] params = query.split("&");
        for (String param : params) {
            String[] keyValue = param.split("=");
            if (keyValue.length == 2) {
                result.put(keyValue[0], keyValue[1]);
            }
        }
        return result;
    }

    @Override
    public void onSessionClosed(WebSocketSession webSocketSession) {
        Object tokenObj = webSocketSession.getAttributes().get("token");
        String token = (String) tokenObj;

        tokenSessionMap.remove(token);
        tokenProblemIdMap.remove(token);

        log.info("WebSocket会话[{}]已关闭", webSocketSession.getId());
    }

    @Override
    protected void handleRecvTextMessage(WebSocketSession webSocketSession, WebSocketTextMessage webSocketTextMessage) {
        log.info("接收到WebSocket消息: {}", webSocketTextMessage);
    }


    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("WebSocket会话[{}]已断开，状态码：{}，原因：{}", session.getId(), status.getCode(), status.getReason());
    }


}
