package com.ruoyi.service.znap.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.entity.znap.*;
import com.ruoyi.mapper.znap.BayQueryMapper;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.ListUtils;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 拓扑查询服务实现类
 */
@Service
@Slf4j
public class BayQueryServiceImpl implements IBayQueryService {

    @Resource
    private BayQueryMapper bayQueryMapper;

    /**
     * 根据线路ID查询线路所属的开关（间隔）和母线和变电站
     */
    @Override
    public BusbarSwitchVo queryLineTopology(String psrId) {
        try {
            // 根据psrId查询ems_ld_link获取线路D5000ID
            EmsLdLink emsLdLink = bayQueryMapper.selectLineByPsrId(psrId);
            if (emsLdLink == null) {
                log.warn("未找到psrId为{}的线路信息", psrId);
                return null;
            }
            String lineId = emsLdLink.getId().toString();
            // 根据线路ID查询线路拓扑信息
            TopoLoadNj topoLoadNj = bayQueryMapper.selectLineTopoInfo(lineId);
            if (topoLoadNj == null) {
                log.warn("未找到线路ID为{}的拓扑信息", lineId);
                return null;
            }
            // 根据线路ID查询拓扑关系
            TopoTraceReal topoTraceReal = bayQueryMapper.selectLineTopoTrace(lineId);
            if (topoTraceReal == null) {
                log.warn("未找到线路ID为{}的拓扑关系", lineId);
                return null;
            }
            // 查询相关设备信息
            TopoSubstationNj substationInfo = null;
            if (StringUtils.isNotBlank(topoLoadNj.getSubstation())) {
                substationInfo = bayQueryMapper.selectSubstationInfo(topoLoadNj.getSubstation());
            }

            BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
            busbarSwitchVo.setFeederId(psrId);
            busbarSwitchVo.setFeederName(emsLdLink.getFdName());
            busbarSwitchVo.setStationPsrId(topoLoadNj.getSubstation());
            busbarSwitchVo.setStationPsrName(substationInfo != null ? substationInfo.getName() : null);
            busbarSwitchVo.setBusbarId(topoTraceReal.getBusbar());
            busbarSwitchVo.setBusbarName(topoTraceReal.getBusbarName());
            busbarSwitchVo.setSwitchId(topoTraceReal.getStartBreak());
            busbarSwitchVo.setSwitchName(topoTraceReal.getStartBreakName());

            return busbarSwitchVo;
        } catch (Exception e) {
            log.error("查询线路拓扑信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询线路拓扑信息失败", e);
        }
    }

    /**
     * 根据线路查询当前所属的母线下都所有开关（间隔）并且识别备用的开关
     */
    @Override
    public List<BusbarSwitchVo> queryBusbarSwitches(String psrId) {
        try {
            // 先获取线路的基本拓扑信息
            BusbarSwitchVo switchVo = queryLineTopology(psrId);
            if (switchVo == null) {
                return null;
            }
            String busbarId = switchVo.getBusbarId();
            if (StringUtils.isBlank(busbarId)) {
                log.warn("线路{}未找到对应的母线信息", psrId);
                return null;
            }
            // 查询同母线下的所有线路和间隔
            List<TopoTraceReal> busbarLines = bayQueryMapper.selectLinesByBusbar(busbarId);
            if (CollectionUtils.isEmpty(busbarLines)) {
                log.warn("母线{}下未找到任何线路", busbarId);
                return null;
            }
            List<BusbarSwitchVo> result = new ArrayList<>();
            // 组装开关信息
            for (TopoTraceReal trace : busbarLines) {
                BusbarSwitchVo busKg = createBusKgByTpReal(trace, busbarId, switchVo.getBusbarName());
                if (busKg != null) {
                    result.add(busKg);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("查询母线开关信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询母线开关信息失败", e);
        }
    }

    /**
     * 根据线路id查询变电站下所有的开关信息
     *
     * @param psrId 线路PSR ID
     * @return
     */
    @Override
    public List<BusbarSwitchVo> querySubstationBusbarSwitches(String psrId) {
        try {
            // 先查询所有的母线
            List<TopoTraceReal> topoTraceReals = querySubstationBusbars(psrId);
            List<BusbarSwitchVo> result = new ArrayList<>();
            for (TopoTraceReal topoTraceReal : topoTraceReals) {
                String busbarId = topoTraceReal.getBusbar();
                if (StringUtils.isBlank(busbarId)) {
                    log.warn("线路{}未找到对应的母线信息", psrId);
                    return null;
                }
                // 查询同母线下的所有线路和间隔
                List<TopoTraceReal> busbarLines = bayQueryMapper.selectLinesByBusbar(busbarId);
                if (CollectionUtils.isEmpty(busbarLines)) {
                    log.warn("母线{}下未找到任何线路", busbarId);
                    return null;
                }
                // 组装开关信息
                for (TopoTraceReal trace : busbarLines) {
                    BusbarSwitchVo busKg = createBusKgByTpReal(trace, busbarId, topoTraceReal.getBusbarName());
                    if (busKg != null) {
                        result.add(busKg);
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.error("查询母线开关信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询母线开关信息失败", e);
        }
    }

    /**
     * 根据线路查询所属的变电站下所有母线
     */
    @Override
    public List<TopoTraceReal> querySubstationBusbars(String psrId) {
        try {
            // 先获取线路的基本拓扑信息
            BusbarSwitchVo switchVo = queryLineTopology(psrId);
            if (switchVo == null) {
                return null;
            }
            String substationId = switchVo.getStationPsrId();
            if (StringUtils.isBlank(substationId)) {
                log.warn("线路{}未找到对应的变电站信息", psrId);
                return null;
            }
            // 查询变电站下的所有母线
            List<TopoTraceReal> substationBusbars = bayQueryMapper.selectBusbarsBySubstation(substationId);
            if (CollectionUtils.isEmpty(substationBusbars)) {
                log.warn("变电站{}下未找到任何母线", substationId);
                return null;
            }
            return substationBusbars;
        } catch (Exception e) {
            log.error("查询变电站母线信息失败，psrId: {}", psrId, e);
            throw new RuntimeException("查询变电站母线信息失败", e);
        }
    }

    /**
     * 传教母线开关 根据real
     *
     * @param trace
     * @param busId   母线ID
     * @param busName 母线名称
     */
    BusbarSwitchVo createBusKgByTpReal(TopoTraceReal trace, String busId, String busName) {
        BusbarSwitchVo busbarSwitchVo = new BusbarSwitchVo();
        if (StringUtils.isBlank(trace.getLoadMrid())) {
            return null;
        }
        EmsLdLink emsLdLink = bayQueryMapper.selectLineById(Long.parseLong(trace.getLoadMrid()));
        if (emsLdLink != null) {
            busbarSwitchVo.setFeederId(emsLdLink.getPmsPsrId());
        }
        TopoLoadNj topoLoadNj = bayQueryMapper.selectLineTopoInfo(trace.getLoadMrid());
        if (ObjectUtil.isNotEmpty(topoLoadNj)) {
            busbarSwitchVo.setStationPsrId(topoLoadNj.getSubstation());
        }
        busbarSwitchVo.setFeederName(trace.getLoadName());
        busbarSwitchVo.setSwitchId(trace.getStartBreak());
        busbarSwitchVo.setSwitchName(trace.getStartBreakName());

        busbarSwitchVo.setBusbarId(busId);
        busbarSwitchVo.setBusbarName(busName);
        String startBreakName = trace.getStartBreakName();
        if (StringUtils.isNotBlank(startBreakName)) {
            // 开关名称包含预留 和 备用的开关
            busbarSwitchVo.setIsSpare(startBreakName.contains("备用") || startBreakName.contains("预留"));
        }
        return busbarSwitchVo;
    }

    // 获取当前线路同母线下的其它线路ID
    public List<String> getBusUnderFeederIds(String feederId) {
        List<String> result = new ArrayList<>();

        // 先获取线路的基本拓扑信息
        BusbarSwitchVo switchVo = queryLineTopology(feederId);
        if (switchVo == null) {
            return result;
        }
        String busId = switchVo.getBusbarId();
        if (StringUtils.isBlank(busId)) {
            return result;
        }
        // 查询同母线下的所有线路和间隔
        List<TopoTraceReal> busbarLines = bayQueryMapper.selectLinesByBusbar(busId);

        if (CollectionUtils.isEmpty(busbarLines)) {
            return result;
        }

        // 组装开关信息
        for (TopoTraceReal trace : busbarLines) {
            EmsLdLink emsLdLink = bayQueryMapper.selectLineById(Long.parseLong(trace.getLoadMrid()));
            if (emsLdLink != null) {
                result.add(emsLdLink.getPmsPsrId());
            }
        }
        // 去查
        return ListUtils.distinctByKey(result, d -> d);
    }
}
