package com.ruoyi.service.znap.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Iterables;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.znap.*;
import com.ruoyi.entity.znap.vo.LinkCbFeederVo;
import com.ruoyi.entity.znap.vo.QueryFeederDevVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.mapper.device.PbMapper;
import com.ruoyi.mapper.znap.*;
import com.ruoyi.service.device.impl.QueryStationDevService;
import com.ruoyi.service.znap.IZnapTopologyService;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Stream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ZnapTopologyServiceImpl implements IZnapTopologyService {

    /**
     * 馈线
     */
    @Autowired
    private ConDmsFeederMapper conDmsFeederMapper;

    /**
     * 配网开关
     */
    @Autowired
    private DevDmsBreakerMapper devDmsBreakerMapper;

    @Autowired
    PbMapper pbMapper;

    /**
     * 配网熔断器
     */
    @Autowired
    private DevDmsFuseMapper devDmsFuseMapper;

    /**
     * 主网开关
     */
    @Autowired
    private DevEmsBreakerMapper devEmsBreakerMapper;


    /**
     * 配网分段
     */
    @Autowired
    private DevDmsSegmentMapper devDmsSegmentMapper;

    /**
     * 配网杆塔
     */
    @Autowired
    private DevDmsPoleMapper devDmsPoleMapper;

    /**
     * 配网母线
     */
    @Autowired
    private DevDmsBusbarMapper devDmsBusbarMapper;

    /**
     * 配网变压器
     */
    @Autowired
    private DevDmsTr2Mapper devDmsTr2Mapper;

    /**
     * 中压用户接入点
     */
    @Autowired
    private DevDmsLdMapper devDmsLdMapper;

    /**
     * 终端
     */
    @Autowired
    private DevDmsJunctionMapper devDmsJunctionMapper;

    /**
     * 配网环网柜表(站房容器)
     */
    @Autowired
    private ConDmsCabinetMapper conDmsCabinetMapper;

    /**
     * 主干开关路径
     */
    @Autowired
    private TpMainPathCbMapper tpMainPathCbMapper;

    /**
     * 联络开关
     */
    @Autowired
    private TpLinkCbMapper tpLinkCbMapper;

    /**
     * 刀闸 0306
     */
    @Autowired
    private DevDmsDisconnectorMapper devDmsDisconnectorMapper;

    /**
     * 配网绕组
     */
    @Autowired
    private DevDmsWindingMapper devDmsWindingMapper;

    @Autowired
    private QueryStationDevService queryStationDevService;

    /**
     * 获取拓扑节点数据
     */
    @Override
    public ZnapTopology generateNode(String pmsFeederId) {
        ConDmsFeeder feeder = conDmsFeederMapper.selectByPmsFeederId(pmsFeederId);
        if (feeder == null) {
            return null;
        }
        Long feederId = feeder.getId();
        ArrayList<ZnapNd> mainZnapNdList = new ArrayList<>();
        // 其它的联络线 为什么要查询其它联络线呢：当前线路的单线图可能有其它联络线的设备
        List<Long> contactFeederIds = getContactFeederId(feederId);

        // =========================== 提取数据 =======================
        // 主网开关（当前拓扑图的起始开关）
        DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(feeder.getHeadBrkId());
        List<DevEmsBreaker> devEmsBreakers = Arrays.asList(devEmsBreaker);
        // 当前线路下的主设备
        QueryFeederDevVo mainFeederDevVo = queryDevList(feederId);
        ArrayList<QueryFeederDevVo> otherFeederDevVos = new ArrayList<>();
        // 其它的联络线的设备
//        for (Long contactFeederId : contactFeederIds) {
//            otherFeederDevVos.add(queryDevList(contactFeederId));
//        }
        ArrayList<QueryFeederDevVo> allFeederDevVos = new ArrayList<>(otherFeederDevVos);
        allFeederDevVos.add(0, mainFeederDevVo);


        // =========================== ZnapNd节点 =======================
        //
        setZnapNdList(devEmsBreakers, mainZnapNdList, DevEmsBreaker::getId, DevEmsBreaker::getInd, DevEmsBreaker::getJnd, DevEmsBreaker::getPsrid);
        // 处理ZnapNd节点
        Map<String, ZnapNd> mainZnapNdMap = handleZnapNdList(mainZnapNdList, mainFeederDevVo);

        // 添加其它联络线的
        ArrayList<ZnapNd> otherZnapNdList = new ArrayList<>();// mainZnapNdList
        Map<String, ZnapNd> otherZnapNdMap = new HashMap<>();// mainZnapNdMap
        for (QueryFeederDevVo otherFeederDevVo : otherFeederDevVos) {
            ArrayList<ZnapNd> tmps = new ArrayList<>();
            Map<String, ZnapNd> tmpMap = handleZnapNdList(tmps, otherFeederDevVo);
            otherZnapNdList.addAll(tmps);
            otherZnapNdMap.putAll(tmpMap);
        }

        // =========================== 节点集合 =======================
        List<Node> startNodes = ZnapUtils.toNodes(devEmsBreakers, DevEmsBreaker::getPsrid, DevEmsBreaker::getName, false);
        // 创建所有节点并存入Map
        ArrayList<Node> mainNodeList = new ArrayList<>();
        mainNodeList.addAll(startNodes);
        Map<String, Node> mainNodeMap = handleNodeList(mainNodeList, mainFeederDevVo, pmsFeederId);

        // 所有
        ArrayList<Node> allNodeList = new ArrayList<>(mainNodeList);
        HashMap<String, Node> allNodeMap = new HashMap<>(mainNodeMap);
        for (QueryFeederDevVo otherFeederDevVo : otherFeederDevVos) {
            ArrayList<Node> tmpNodes = new ArrayList<>();
            Map<String, Node> tmpNodeMap = handleNodeList(tmpNodes, otherFeederDevVo, null);
            allNodeList.addAll(tmpNodes);
            //  allNodeMap.putAll(tmpNodeMap);
            tmpNodeMap.forEach((key, value) ->
                    allNodeMap.merge(key, value, (oldValue, newValue) -> oldValue) // 如果键存在，保留旧值
            );
        }

        // =========================== 使用ind和jnd和nd构建map =======================
        Map<String, List<ZnapNd>> mainZnapNdGroupMap = getZnapNdGroupMap(mainZnapNdList, allNodeMap);
        Map<String, List<ZnapNd>> otherZnapNdGroupMap = getZnapNdGroupMap(otherZnapNdList, allNodeMap);

        ArrayList<ZnapNd> addZnapList = new ArrayList<>();
        // 当前线路的数据可能有其它单线图的设备 我们这里需要处理
        for (ZnapNd znapNd : mainZnapNdList) {
            if (znapNd.isNd()) {
                handleZnaps(znapNd.getNd(), otherZnapNdGroupMap, mainZnapNdGroupMap, mainZnapNdMap, addZnapList, allNodeMap);
            } else {
                handleZnaps(znapNd.getInd(), otherZnapNdGroupMap, mainZnapNdGroupMap, mainZnapNdMap, addZnapList, allNodeMap);
                handleZnaps(znapNd.getJnd(), otherZnapNdGroupMap, mainZnapNdGroupMap, mainZnapNdMap, addZnapList, allNodeMap);
                // 由于
            }
        }
        mainZnapNdList.addAll(addZnapList);
        if (!addZnapList.isEmpty()) {
            mainZnapNdGroupMap = getZnapNdGroupMap(mainZnapNdList, allNodeMap);
        }

        // =========================== 父节点 =======================
        handleParentNode(mainFeederDevVo, allNodeMap, allFeederDevVos);

        // =========================== 结尾组装 =======================
        Node startNode = startNodes.get(0);
        ZnapTopology znapTopology = new ZnapTopology();
        znapTopology.setStartNode(startNode);
        znapTopology.setNodeMap(mainNodeMap);
        znapTopology.setNodeList(mainNodeList);
        // 处理主干开关和分段路径
        handleMainKgPath(feederId, mainNodeList, mainNodeMap, znapTopology);

        // 设置链接节点
        handleLink(startNode, mainZnapNdMap, mainZnapNdGroupMap, new HashMap<>(), new HashMap<>());
        // 处理联络线路开关
        handleLinkCbFeeder(pmsFeederId, znapTopology);
        Map<String, Long> znapIdMap = new HashMap<>();
        // 我们的id和军哥的id映射关系
        mainZnapNdList.forEach(znapNd -> {
            if (znapNd.getNode() != null) {
                znapIdMap.put(znapNd.getNode().getId(), znapNd.getId());
            }
        });
        znapTopology.setZnapIdMap(znapIdMap);
        return znapTopology;
    }

    /**
     * 处理节点的连接关系
     */
    private static void handleLink(Node startNode, Map<String, ZnapNd> znapNdMap, Map<String, List<ZnapNd>> znapNdGroupMap, Map<String, Node> useNodeMap, Map<String, Boolean> useZnapIdMap) {
        Node currentNode = startNode;
        HashMap<String, Node> extraMap = new HashMap<>();

        // 递归获取查找
        while (currentNode != null) {
            ZnapNd znapNd = znapNdMap.get(currentNode.getId());
            if (znapNd == null) {
                break;
            }
            List<String> znapIds = znapNd.getIds();
            List<Node> devs = new ArrayList<>();
            List<Node> edges = new ArrayList<>();

            // 查找相关联的节点集合  并且过滤已经操作过的
            for (String znapId : znapIds) {

                List<ZnapNd> znapNds = znapNdGroupMap.get(znapId);
                List<Node> nodes = znapNds.stream().map(ZnapNd::getNode).collect(Collectors.toList());
                nodes = nodes.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());
                Node busNode = NodeUtils.findNode(nodes, Node::isBus);
                useZnapIdMap.put(znapId, true);

                // 表示有母线
                if (busNode != null) {
                    List<Node> tmpDevs = new ArrayList<>();
                    List<Node> tmpEdges = new ArrayList<>();
                    // 重新设置节点集合
                    nodes = nodes.stream().filter(n -> !n.equals(busNode)).collect(Collectors.toList());

                    // 不是终端头 需要处理
                    if (!currentNode.isJunction() && currentNode.isEdge()) {
                        // 查找终端头
                        Node juNode = NodeUtils.findNode(nodes, Node::isJunction);
                        if (juNode != null) {
                            juNode.addEdge(currentNode, false);
                            currentNode = juNode;
                        }
                    } else {
                        nodes.add(currentNode);
                    }
                    extraMap.put(currentNode.getId(), currentNode);

                    setDevEdges(nodes, busNode, tmpDevs, tmpEdges);
                    handleZnapLink(useNodeMap, busNode,
                            extraMap, tmpDevs, tmpEdges, znapNdMap, znapNdGroupMap, useZnapIdMap, currentNode);
                    continue;
                }
                setDevEdges(nodes, currentNode, devs, edges);
            }
            currentNode = handleZnapLink(useNodeMap, currentNode,
                    extraMap, devs, edges, znapNdMap, znapNdGroupMap, useZnapIdMap, null);
        }
    }

    static void setDevEdges(List<Node> nodes, Node currentNode, List<Node> devs,
                            List<Node> edges) {
        for (Node node : nodes) {
            if (!node.equals(currentNode)) {
                if (node.isEdge()) {
                    edges.add(node);
                } else {
                    devs.add(node);
                }
            }
        }
    }

    static Node handleZnapLink(Map<String, Node> useNodeMap, Node currentNode,
                               HashMap<String, Node> extraMap, List<Node> devs,
                               List<Node> edges, Map<String, ZnapNd> znapNdMap, Map<String, List<ZnapNd>> znapNdGroupMap,
                               Map<String, Boolean> useZnapIdMap, Node reverseNode) {
        Node nextNode = null;
        useNodeMap.put(currentNode.getId(), currentNode);
        List<Node> loopNodes = new ArrayList<>();

        // 母线当成设备处理
        boolean currentIsEdge = currentNode.isBus() ? false : currentNode.isEdge();

        // 过滤已经
        if (currentIsEdge) { // 边
            if (devs.size() == 1) {
                // 表示当前
                Node dev = devs.get(0);
                dev.addEdge(currentNode, false);
                useNodeMap.put(dev.getId(), dev);
                // 下一个节点
                nextNode = dev;
            } else if (devs.size() > 1) {
                Node sourceNode = null;
                for (Node dev : devs) {
                    if (dev.isPole() || dev.isJunction()) {
                        sourceNode = dev;
                        break;
                    }
                }
                if (sourceNode == null) {
                    sourceNode = devs.get(0);
                }

                sourceNode.addEdge(currentNode, false);
                loopNodes.add(sourceNode);
                useNodeMap.put(sourceNode.getId(), sourceNode);

                for (Node node : devs) {
                    if (!sourceNode.equals(node)) {
                        // 创建一个虚拟线
                        Node edge = NodeFactory.createNode("link_" + sourceNode.getPsrId() + "_" + node.getPsrId(), null, null);
                        edge.setEdge(true);
                        sourceNode.addEdge(edge, true);
                        node.addEdge(edge, false);
                        useNodeMap.put(edge.getId(), edge);
                        useNodeMap.put(node.getId(), node);

                        loopNodes.add(node);
                    }
                }
            } else if (edges.size() == 1) {
                // 中间有终端头
                Node edge = edges.get(0);
                Node dev = NodeFactory.createNode("link_" + currentNode.getPsrId() + "_" + edge.getPsrId(), null, "0203");
                dev.addEdge(currentNode, false);
                dev.addEdge(edge, true);
                nextNode = edge;
            }
        } else { // 设备节点

            // 表示图模文件的两个节点中间没有连接线
            for (Node dev : devs) {
                // 创建一个虚拟线
                Node edge = NodeFactory.createNode("link_" + currentNode.getPsrId() + "_" + dev.getPsrId(), null, null);
                edge.setEdge(true);
                currentNode.addEdge(edge, reverseNode != null && reverseNode.equals(dev) ? false : true);
                dev.addEdge(edge, reverseNode != null && reverseNode.equals(dev) ? true : false);
                useNodeMap.put(edge.getId(), edge);
                useNodeMap.put(dev.getId(), dev);

                boolean isNextEdge = dev.isPole() || dev.isJunction();

                // 如果当前设备是可以连接的杆塔
                if (isNextEdge && !edges.isEmpty()) {
                    // 当前节不是杆塔  如果有杆塔设备那么线路全部在杆塔上
                    // 当前是杆塔 如果遇到杆塔那么就单独
                    if (isNextEdge) {
                        Node segEdge = edges.remove(0);
                        dev.addEdge(segEdge, true);
                        useNodeMap.put(segEdge.getId(), segEdge);
                        loopNodes.add(segEdge);
                    } else {
                        for (Node segEdge : edges) {
                            dev.addEdge(segEdge, true);
                            useNodeMap.put(segEdge.getId(), segEdge);
                            loopNodes.add(segEdge);
                        }
                        edges.clear();
                    }
                } else {
                    loopNodes.add(dev);
                }
            }

            for (Node edge : edges) {
                currentNode.addEdge(edge, reverseNode != null && reverseNode.equals(edge) ? false : true);
                useNodeMap.put(edge.getId(), edge);
                loopNodes.add(edge);
            }
        }
        for (Node node : loopNodes) {
            if (extraMap.containsKey(node.getId())) {
                continue;
            }
            handleLink(node, znapNdMap, znapNdGroupMap, useNodeMap, useZnapIdMap);
        }
        return nextNode;
    }

    /**
     * 处理用户下的配变
     */
    void handleUserPb(String pmsFeederId, ArrayList<Node> nodeList, Map<String, Node> nodeMap, List<Node> userNodes) {
        if (CollectionUtil.isEmpty(userNodes)) {
            return;
        }
        // 9acdb769-53a7-439f-beb9-6a6f93159185
        //  图模文件上没有用户接入点的相关设备 我们这里单独手动处理

        List<String> userPsrIds = userNodes.stream().filter(n -> StringUtils.isNotBlank(n.getPsrId())).map(Node::getPsrId).collect(Collectors.toList());

        // 用户接入点下的配变
        List<StationPb> stationPbs = pbMapper.selectMiddleUserPb(pmsFeederId, userPsrIds);
        Map<String, StationPb> pbMap = stationPbs.stream().collect(Collectors.toMap(StationPb::getPsrId, d -> d));
        HashMap<String, ArrayList<Node>> pbGroupNodeMap = processorStation(nodeList, nodeMap, stationPbs, StationPb::getStation, StationPb::getPsrId, StationPb::getPsrType, StationPb::getName, false);

        // 处理站房ID对于中压用户接入点ID
        HashMap<String, String> stationIdToUserId = new HashMap<>();
        for (StationPb stationPb : stationPbs) {
            stationIdToUserId.put(stationPb.getStation(), stationPb.getJoinEc());
        }

        // 站房
        List<String> stationIds = stationPbs.stream().map(pb -> pb.getStation()).collect(Collectors.toList());
        stationIds = stationIds.stream().distinct().collect(Collectors.toList());

        List<DeviceUserStation> userStations = queryStationDevService.getUserStations(stationIds);
        if (CollectionUtils.isEmpty(userStations)) {
            return;
        }
        processorStation(nodeList, nodeMap, userStations, DeviceUserStation::getPsrId, DeviceUserStation::getPsrId, DeviceUserStation::getStationType, DeviceUserStation::getName, false);

        // 母线
        List<DeviceStationGeneratrix> stationBus = queryStationDevService.getStationBus(pmsFeederId, stationIds);
        HashMap<String, ArrayList<Node>> busGroupNodeMap = processorStation(nodeList, nodeMap, stationBus, DeviceStationGeneratrix::getStation, DeviceStationGeneratrix::getPsrId, (n) -> "0311", DeviceStationGeneratrix::getName, true);

        // 开关
        List<StationKg> stationKgs = queryStationDevService.getStationKgs(pmsFeederId, stationIds);
        HashMap<String, ArrayList<Node>> KgGroupNodeMap = processorStation(nodeList, nodeMap, stationKgs, StationKg::getStation, StationKg::getPsrId, StationKg::getPsrType, StationKg::getName, false);
        Map<String, StationKg> kgMap = stationKgs.stream().collect(Collectors.toMap(StationKg::getPsrId, d -> d));

        // 设置关联关系  单个站房单个站房的设置
        for (DeviceUserStation userStation : userStations) {

            Node stationNode = nodeMap.get(userStation.getPsrId());
            String userId = stationIdToUserId.get(userStation.getPsrId());
            if (stationNode == null || StringUtils.isBlank(userId)) {
                continue;
            }
            Node userNode = nodeMap.get(userId);

            ArrayList<Node> busNodes = busGroupNodeMap.get(userStation.getPsrId());
            ArrayList<Node> kgNodes = KgGroupNodeMap.get(userStation.getPsrId());
            ArrayList<Node> pbNodes = pbGroupNodeMap.get(userStation.getPsrId());
            if (CollectionUtils.isEmpty(busNodes)) {
                continue;
            }
            if (kgNodes == null) {
                kgNodes = new ArrayList<>();
            }
            if (pbNodes == null) {
                pbNodes = new ArrayList<>();
            }

            List<Node> nodes = Stream.concat(kgNodes.stream(), pbNodes.stream()).collect(Collectors.toList());

            // 设置父节点
            Iterable<Node> mergedIterable = Iterables.concat(pbNodes, busNodes, kgNodes);
            // 遍历合并后的可迭代对象
            for (Node node : mergedIterable) {
                if (node != null) {
                    stationNode.addChild(node, -1);
                }
            }

            // =========================== 设置关联关系 =======================

            // TODO 目前没法确定站内的设备对应那条母线  所以我们就默认第一条来做吧（按道理根据线路去查询只有一个但是呢zf08类型的不是）
            Node busNode = busNodes.get(0);
            // 按照间隔分组
            HashMap<String, ArrayList<Node>> bayGroup = new HashMap<>();
            for (Node node : nodes) {
                String bay = node.isPb() ? pbMap.get(node.getPsrId()).getBay() : kgMap.get(node.getPsrId()).getBay();
                if (StringUtils.isBlank(bay)) {
                    continue;
                }
                ArrayList<Node> bayNodes = bayGroup.get(bay);
                // 没有设置过 需要设置塞进去
                if (bayNodes == null) {
                    bayNodes = new ArrayList<>();
                    bayNodes.add(busNode);
                    bayGroup.put(bay, bayNodes);
                }
                bayNodes.add(node);
            }

            // 是否将中压用户接入点设置连接
            boolean isLinkUser = false;
            // 构建链接关系  按照间隔设置连接
            for (ArrayList<Node> bayNodes : bayGroup.values()) {
                if (bayNodes.size() <= 1) {
                    continue;
                }
                // 排序 母线 > 开关(断路器) > 配变
                bayNodes.sort((node1, node2) -> getStationSortNum(node2) - getStationSortNum(node1));

                // 创建连接
                for (int i = bayNodes.size() - 1; i > 0; i--) {
                    Node currentNode = bayNodes.get(i);
                    Node nextNode = bayNodes.get(i - 1);


                    boolean needSetLinkUser = false;
                    // 最后一个不是配变  并且 不是备用开关 那么就设置链接中压用户
                    if (isLinkUser == false && i == bayNodes.size() - 1 && currentNode.isKg("all") && !NodeUtils.isNotUsed(currentNode)) {

                        needSetLinkUser = true;
                        Node userEdge = NodeFactory.createNode("link_" + userNode.getPsrId(), null, null);
                        userEdge.setEdge(true);

                        userNode.addEdge(userEdge, true);
                        currentNode.addEdge(userEdge, false);

                        nodeList.add(userEdge);
                        isLinkUser = true;
                    }

                    // 创建一个虚拟线
                    Node edge = NodeFactory.createNode("link_" + currentNode.getPsrId(), null, null);
                    edge.setEdge(true);

                    currentNode.addEdge(edge, needSetLinkUser == true ? true : false);
                    nextNode.addEdge(edge, needSetLinkUser == true ? false : true);
                    nodeList.add(edge);
                }
            }
            // 表示当前中压用户还没有与当前站房构建链接 那么就直接和母线构建
            if (isLinkUser == false) {

                Node userEdge = NodeFactory.createNode("link_" + userNode.getPsrId(), null, null);
                userEdge.setEdge(true);

                userNode.addEdge(userEdge, true);
                busNode.addEdge(userEdge, false);
                nodeList.add(userEdge);
            }

        }
    }

    int getStationSortNum(Node node) {
        // 排序 母线 > 开关(断路器) > 配变
        if (node.isBus()) {
            return 100;
        } else if (node.isKg("in")) {
            Map numObj = new HashMap<String, Integer>() {{
                put("0305", 50);
                put("0306", 49);
                put("0307", 48);
            }};
            return numObj.get(node.getPsrType()) == null ? 50 : (int) numObj.get(node.getPsrType());
        } else {
            return 1;
        }
    }

    /**
     * Node转换方法
     */
    public <T> HashMap<String, ArrayList<Node>> processorStation(ArrayList<Node> nodeList, Map<String, Node> nodeMap, List<T> list
            , Function<T, String> stationMapper, Function<T, String> psrIdMapper
            , Function<T, String> psrTypeMapper, Function<T, String> psrNameMapper, boolean isLine) {
        List<Node> newNodes = new ArrayList<>(ZnapUtils.toNodes(list, psrIdMapper, psrTypeMapper, psrNameMapper, isLine));
        Map<String, Node> newNodeMap = newNodes.stream().collect(Collectors.toMap(Node::getPsrId, node -> node));
        nodeList.addAll(newNodes);
        nodeMap.putAll(newNodeMap);

        // 将站房分组
        HashMap<String, ArrayList<Node>> stationGroup = new HashMap<>();
        for (T data : list) {
            String station = stationMapper.apply(data);
            ArrayList<Node> nodes = stationGroup.get(station);
            // 没有设置过 需要设置塞进去
            if (nodes == null) {
                nodes = new ArrayList<>();
                stationGroup.put(station, nodes);
            }
            Node node = newNodeMap.get(psrIdMapper.apply(data));
            nodes.add(node);
        }
        return stationGroup;
    }

    /**
     * 根据当前线路查询的配网开关可能查不出来联络开关 这里特别处理一下
     */
    public void handleLinkBreak(List<DevDmsBreaker> devDmsBreakers, Map<String, DevDmsBreaker> devDmsBreakersMap, Long feederId) {
        // 联络开关
        List<TpLinkCb> tpLinkCbs = tpLinkCbMapper.selectByFeederId(feederId);

        if (CollectionUtils.isEmpty(tpLinkCbs)) {
            return;
        }

        // 根据联络开关查询配网开关
        LambdaQueryWrapper<DevDmsBreaker> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DevDmsBreaker::getId, tpLinkCbs.stream().map(TpLinkCb::getId).collect(Collectors.toList()));

        List<DevDmsBreaker> linkBreakers = devDmsBreakerMapper.selectList(queryWrapper);

        for (DevDmsBreaker linkBreaker : linkBreakers) {
            if (!devDmsBreakersMap.containsKey(linkBreaker.getPsrid())) {
                devDmsBreakers.add(linkBreaker);
                devDmsBreakersMap.put(linkBreaker.getPsrid(), linkBreaker);
            }

//            if (!devDmsBreakers.stream().anyMatch(n -> n.getId().equals(linkBreaker.getId()))) {
//                devDmsBreakers.add(linkBreaker);
//            }
        }
    }

    /**
     * 处理联络开关和线路
     *
     * @param feederId 线路ID
     */
    public void handleLinkCbFeeder(String feederId, ZnapTopology znapTopology) {
        List<LinkCbFeederVo> linkCbFeederVos = tpLinkCbMapper.selectLinkCbFeeder(feederId);
        ArrayList<ContactFeederKg> contactFeederKgs = new ArrayList<>();

        for (LinkCbFeederVo data : linkCbFeederVos) {

            String[] kgIdType = ZnapUtils.parsePsrStr(data.getPsrid());
            String[] feederIdType1 = ZnapUtils.parsePsrStr(data.getFeederId1());
            String[] feederIdType2 = ZnapUtils.parsePsrStr(data.getFeederId2());
            if (feederIdType1 == null || feederIdType2 == null) {
                continue;
            }
            String feederId1 = feederIdType1[1];
            String feederId2 = feederIdType2[1];
            String contactFeederId, contactFeederName;

            if (StringUtils.equals(feederId1, feederId2)) {
                contactFeederId = feederId1;
                contactFeederName = data.getFeederName1();
            } else {
                contactFeederId = feederId2;
                contactFeederName = data.getFeederName2();
            }
            if (StringUtils.isBlank(contactFeederId)) {
                continue;
            }

            ContactFeederKg contactFeederKg = new ContactFeederKg(kgIdType[1], kgIdType[0], data.getName());
            contactFeederKg.setFeederPsrId(contactFeederId);
            contactFeederKg.setFeederPsrName(contactFeederName);
            contactFeederKgs.add(contactFeederKg);
        }
        znapTopology.setContactFeederKgs(contactFeederKgs);
    }

    Node getNodeByMap(Map<String, Node> nodeMap, String psrid) {
        return nodeMap.get(ZnapUtils.parsePsrId(psrid));
    }

    /**
     * 获取配变绕组
     *
     * @param devDmsTrs
     * @return
     */
    List<DevDmsWinding> getWindings(List<DevDmsTr> devDmsTrs) {
        List<Long> ids = devDmsTrs.stream().map(dev -> dev.getId()).collect(Collectors.toList());
        LambdaQueryWrapper<DevDmsWinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DevDmsWinding::getTrId, ids);

        return devDmsWindingMapper.selectList(queryWrapper);
    }

    /**
     * 处理主干开关和主干路径
     */
    void handleMainKgPath(Long feederId, ArrayList<Node> nodeList, Map<String, Node> nodeMap, ZnapTopology znapTopology) {
        LambdaQueryWrapper<TpMainPathCb> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TpMainPathCb::getFeederId, feederId)
                .orderByAsc(TpMainPathCb::getIdx);

        List<TpMainPathCb> tpMainPathCbs = tpMainPathCbMapper.selectList(queryWrapper);

        Map<String, ArrayList<Node>> paths = new HashMap<>();

        List<Node> kgContactNodes = new ArrayList<>();
        List<Node> kgMainNodes = new ArrayList<>();

        for (TpMainPathCb tpMainPathCb : tpMainPathCbs) {
            String[] strings = ZnapUtils.parsePsrStr(tpMainPathCb.getPsrid());
            if (strings == null) {
                continue;
            }
            String psrId = strings[1];
            String psrType = strings[0];

            String pathId = String.valueOf(tpMainPathCb.getPathId());
            ArrayList<Node> nodes = null;
            // 不存在
            if (!paths.containsKey(pathId)) {
                nodes = new ArrayList<>();
                paths.put(pathId, nodes);
            } else {
                nodes = paths.get(pathId);
            }

            if (strings == null) {
                continue;
            }

            // 主干开关
            Node node = nodeMap.get(psrId);
            // 表示没有
            if (node == null) {
                node = NodeFactory.createNode(tpMainPathCb.getPsrid(), psrId, psrType);
                nodeList.add(node);
            }
            nodes.add(node);
            kgMainNodes.add(node);
            if (tpMainPathCb.getIsLink().equals(1L)) {
                kgContactNodes.add(node);
            }
        }
        //  znapTopology.setKgMainNodes(kgMainNodes);
        znapTopology.setKgContactNodes(kgContactNodes);
      //  znapTopology.setPaths(paths);
    }

    // 设置节点ND集合
    void setNdMap(Map<String, List<ZnapNd>> znapNdGroupMap, ZnapNd znapNd, String id) {
        if (znapNd == null || StringUtils.isBlank(id)) {
            return;
        }
        if (!znapNdGroupMap.containsKey(id)) {
            znapNdGroupMap.put(id, new ArrayList<>(Arrays.asList(znapNd)));
        } else {
            znapNdGroupMap.get(id).add(znapNd);
        }
    }

    // 设置父节点
    <T> void setParentNode(List<T> list, Map<String, Node> nodeMap, HashMap<Long, Node> parentZnapIdMap, Function<T, String> getIdFunc, Function<T, Long> getParentIdFunc) {
        for (T t : list) {
            Node node = getNodeByMap(nodeMap, getIdFunc.apply(t));
            Node parent = parentZnapIdMap.get(getParentIdFunc.apply(t));
            if (parent != null && node != null) {
                parent.addChild(node, -1);
            }
        }
    }

    public <T> void setZnapNdList(List<T> list, ArrayList<ZnapNd> znapNdList, Function<T, Long> getIdFunc, Function<T, Long> getNdFunc,
                                  Function<T, String> getPsridFunc) {
        for (T t : list) {
            Long nd = getNdFunc.apply(t);
            String psrid = getPsridFunc.apply(t);
            ZnapNd znapNd = new ZnapNd(getIdFunc.apply(t), true, nd, psrid);
            znapNdList.add(znapNd);
        }
    }

    public <T> void setZnapNdList(List<T> list, ArrayList<ZnapNd> znapNdList, Function<T, Long> getIdFunc, Function<T, Long> getIndFunc,
                                  Function<T, Long> getJndFunc, Function<T, String> getPsridFunc) {
        for (T t : list) {
            znapNdList.add(getZnapNd(t, getIdFunc, getIndFunc,
                    getJndFunc, getPsridFunc));
        }
    }

    <T> ZnapNd getZnapNd(T t, Function<T, Long> getIdFunc, Function<T, Long> getIndFunc,
                         Function<T, Long> getJndFunc, Function<T, String> getPsridFunc) {
        try {
            Long ind = getIndFunc.apply(t);
            Long jnd = getJndFunc.apply(t);
            String psrid = getPsridFunc.apply(t);
            return new ZnapNd(getIdFunc.apply(t), ind, jnd, psrid);
        } catch (Exception e) {
            System.out.println("ind");
        }
        return null;
    }


    // 获取关联的另一个节点
    void setEdgeLink(Map<String, List<Node>> znapNdGroupMap, String id, Node edge, boolean isSource) {
        List<Node> nodes = znapNdGroupMap.get(id);
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        List<Node> devNodes = nodes.stream().filter(n -> !n.equals(edge) && !n.isEdge()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(devNodes)) {
            return;
        }

        if (devNodes.size() > 1) {
            // 当前线连接的设备按道理应该只有一个都  但是呢图模文件没有连接线的 导致多余的设备也是当前
            // 导线段只会和杆塔  母线  开关链接
        } else {
            devNodes.get(0).addEdge(edge, isSource);
        }

    }

    public List<Long> getContactFeederId(Long feeder) {
        LambdaQueryWrapper<TpLinkCb> queryWrapper0 = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<TpLinkCb> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper0.eq(TpLinkCb::getFeederId0, feeder);
        queryWrapper1.eq(TpLinkCb::getFeederId1, feeder);


        List<TpLinkCb> tpLinkCbs0 = tpLinkCbMapper.selectList(queryWrapper0);
        List<TpLinkCb> tpLinkCbs1 = tpLinkCbMapper.selectList(queryWrapper1);

        List<Long> list = tpLinkCbs0.stream().map(TpLinkCb::getFeederId1).collect(Collectors.toList());
        List<Long> list0 = tpLinkCbs1.stream().map(TpLinkCb::getFeederId0).collect(Collectors.toList());
        list.addAll(list0);

        list = ListUtils.distinctByKey(list, d -> d);
        return list;
    }

    /**
     * 根据线路ID查询当前线路线路下的线路集合
     *
     * @param feederId
     * @return
     */
    public QueryFeederDevVo queryDevList(Long feederId) {

        // 配网开关
        List<DevDmsBreaker> devDmsBreakers = devDmsBreakerMapper.selectByFeederId(feederId);
        // 根据当前线路查询的配网开关可能查不出来联络开关 这里特别处理一下
        Map<String, DevDmsBreaker> devDmsBreakersMap = ZnapUtils.convertMap(devDmsBreakers, DevDmsBreaker::getPsrid);
        handleLinkBreak(devDmsBreakers, devDmsBreakersMap, feederId);

        // 配网熔断器
        List<DevDmsFuse> devDmsFuses = devDmsFuseMapper.selectByFeederId(feederId);

        // 刀闸 0203
        List<DevDmsDisconnector> devDmsDisconnectors = devDmsDisconnectorMapper.selectByFeederId(feederId);
        // 配变母线
        List<DevDmsBusbar> devDmsBusbars = devDmsBusbarMapper.selectByFeederId(feederId);
        // 配网分段
        List<DevDmsSegment> devDmsSegments = devDmsSegmentMapper.selectByFeederId(feederId);
        // 配网杆塔
        List<DevDmsPole> devDmsPoles = devDmsPoleMapper.selectByFeederId(feederId);
        // 配网变压器
        List<DevDmsTr> devDmsTrs = devDmsTr2Mapper.selectByFeederId(feederId);
        // 终端
        List<DevDmsJunction> devDmsJunctions = devDmsJunctionMapper.selectByFeederId(feederId);
        // 变压器的绕组
        // List<DevDmsWinding> devDmsWindings = getWindings(devDmsTrs);
        // 中压用户接入点
        List<DevDmsLd> devDmsLds = devDmsLdMapper.selectByFeederId(feederId);
        // 配网环网柜表(站房容器)
        List<ConDmsCabinet> conDmsCabinets = conDmsCabinetMapper.selectByFeederId(feederId);

        return new QueryFeederDevVo(devDmsBreakers, devDmsFuses, devDmsDisconnectors, devDmsBusbars, devDmsSegments, devDmsPoles, devDmsTrs, devDmsLds, conDmsCabinets, devDmsJunctions);
    }

    Map<String, List<ZnapNd>> getZnapNdGroupMap(ArrayList<ZnapNd> mainZnapNdList, HashMap<String, Node> allNodeMap) {
        Map<String, List<ZnapNd>> znapNdGroupMap = new HashMap<>();
        for (ZnapNd znapNd : mainZnapNdList) {
            if (znapNd.getNode() == null) {
                Node node = getNodeByMap(allNodeMap, znapNd.getPsrid());
                znapNd.setNode(node);
            }

            if (znapNd.isNd()) {
                setNdMap(znapNdGroupMap, znapNd, String.valueOf(znapNd.getNd()));
            } else {
                setNdMap(znapNdGroupMap, znapNd, String.valueOf(znapNd.getInd()));
                setNdMap(znapNdGroupMap, znapNd, String.valueOf(znapNd.getJnd()));
            }
        }
        return znapNdGroupMap;
    }

    void handleZnaps(Long id, Map<String, List<ZnapNd>> otherZnapNdGroupMap,
                     Map<String, List<ZnapNd>> mainZnapNdGroupMap,
                     Map<String, ZnapNd> mainZnapNdMap, List<ZnapNd> addZnapList,
                     HashMap<String, Node> allNodeMap) {
        if (id == null) {
            return;
        }
        String znapId = String.valueOf(id);
        List<ZnapNd> znapNds = mainZnapNdGroupMap.get(znapId);
        List<ZnapNd> zds = otherZnapNdGroupMap.get(znapId);
        if (zds != null && znapNds != null) {
            for (ZnapNd zd : zds) {
                if (!mainZnapNdMap.containsKey(zd.getPsrid())) {
                    znapNds.add(zd);
                    addZnapList.add(zd);
                    mainZnapNdMap.put(zd.getPsrid(), zd);
                }
            }
        }
        // 由于图模文件的问题 当前的设备本来是在这条线上 但是呢没在我们这里做一个补救措施 目前就先只做刀闸的
//        if (znapNds != null && CollectionUtils.isEmpty(zds) && znapNds.size() == 1 && id != -1) {
//            DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectByIJnd(id);
//            ZnapNd zd = null;
//            if (devDmsDisconnector != null) {
//                zd = getZnapNd(devDmsDisconnector, DevDmsDisconnector::getInd, DevDmsDisconnector::getJnd, DevDmsDisconnector::getPsrid);
//                Node node = getNodeByMap(allNodeMap, zd.getPsrid());
//                if (node == null) {
//                    node = ZnapUtils.toNode(devDmsDisconnector, DevDmsDisconnector::getPsrid, DevDmsDisconnector::getName, false);
//                }
//                zd.setNode(node);
//            } else {
//                DevDmsBreaker breaker = devDmsBreakerMapper.selectByIJnd(id);
//                if (breaker != null) {
//                    zd = getZnapNd(breaker, DevDmsBreaker::getInd, DevDmsBreaker::getJnd, DevDmsBreaker::getPsrid);
//                    Node node = getNodeByMap(allNodeMap, zd.getPsrid());
//                    if (node == null) {
//                        node = ZnapUtils.toNode(breaker, DevDmsBreaker::getPsrid, DevDmsBreaker::getName, false);
//                    }
//                    zd.setNode(node);
//                }
//            }
//            if (zd != null) {
//                if (!mainZnapNdMap.containsKey(zd.getPsrid())) {
//                    znapNds.add(zd);
//                    addZnapList.add(zd);
//                    mainZnapNdMap.put(zd.getPsrid(), zd);
//                }
//            } else {
//                System.out.println(id);
//            }
//        }
    }

    // 处理ZnapNd节点
    Map<String, ZnapNd> handleZnapNdList(ArrayList<ZnapNd> znapNdList, QueryFeederDevVo feederDevVo) {
        setZnapNdList(feederDevVo.getDevDmsBreakers(), znapNdList, DevDmsBreaker::getId,
                DevDmsBreaker::getInd, DevDmsBreaker::getJnd, DevDmsBreaker::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsFuses(), znapNdList, DevDmsFuse::getId, DevDmsFuse::getInd,
                DevDmsFuse::getJnd, DevDmsFuse::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsDisconnectors(), znapNdList, DevDmsDisconnector::getId,
                DevDmsDisconnector::getInd, DevDmsDisconnector::getJnd, DevDmsDisconnector::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsSegments(), znapNdList, DevDmsSegment::getId, DevDmsSegment::getInd,
                DevDmsSegment::getJnd, DevDmsSegment::getPsrid);

        // 单节点Nd
        setZnapNdList(feederDevVo.getDevDmsBusbars(), znapNdList, DevDmsBusbar::getId, DevDmsBusbar::getNd, DevDmsBusbar::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsPoles(), znapNdList, DevDmsPole::getId, DevDmsPole::getNd, DevDmsPole::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsTrs(), znapNdList, DevDmsTr::getId, DevDmsTr::getNd, DevDmsTr::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsLds(), znapNdList, DevDmsLd::getId, DevDmsLd::getNd, DevDmsLd::getPsrid);
        setZnapNdList(feederDevVo.getDevDmsJunctions(), znapNdList, DevDmsJunction::getId, DevDmsJunction::getNd, DevDmsJunction::getPsrid);

        //  setZnapNdList(devDmsWindings, znapNdList,DevDmsWinding::getId, DevDmsWinding::getNd, DevDmsWinding::getPsrid);

        return znapNdList.stream().collect(Collectors.toMap(ZnapNd::getPsrid, znapNd -> znapNd));
    }

    public Map<String, Node> handleNodeList(ArrayList<Node> nodeList, QueryFeederDevVo feederDevVo, String pmsFeederId) {
        List<Node> ldNodes = ZnapUtils.toNodes(feederDevVo.getDevDmsLds(), DevDmsLd::getPsrid, DevDmsLd::getName, false);
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsBreakers(), DevDmsBreaker::getPsrid, DevDmsBreaker::getName, false));// 配网开关
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsFuses(), DevDmsFuse::getPsrid, DevDmsFuse::getName, false));// 配网熔断器
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsDisconnectors(), DevDmsDisconnector::getPsrid, DevDmsDisconnector::getName, false));// 刀闸
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsBusbars(), DevDmsBusbar::getPsrid, DevDmsBusbar::getName, true)); // 配变母线
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsSegments(), DevDmsSegment::getPsrid, DevDmsSegment::getName, true)); // 配网分段
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsPoles(), DevDmsPole::getPsrid, DevDmsPole::getName, false)); // 配网杆塔
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsTrs(), DevDmsTr::getPsrid, DevDmsTr::getName, false)); // 配网杆塔
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getDevDmsJunctions(), DevDmsJunction::getPsrid, DevDmsJunction::getName, false)); // 终端

        nodeList.addAll(ldNodes); // 中压用户接入点
        nodeList.addAll(ZnapUtils.toNodes(feederDevVo.getConDmsCabinets(), ConDmsCabinet::getPsrid, ConDmsCabinet::getName, false)); // 站房容器
        // nodeList.addAll(ZnapUtils.toNodes(devDmsWindings, DevDmsWinding::getPsrid, false)); // 配网绕组
        // 去重
        ListUtils.distinctInPlace(nodeList, Node::getPsrId);
        Map<String, Node> nodeMap = nodeList.stream().collect(Collectors.toMap(Node::getPsrId, node -> node));


        if (StringUtils.isNotBlank(pmsFeederId)) {
            // 由于图模文件没有中压用户接入点下的配变设备和父级站房我们这里特殊处理
            handleUserPb(pmsFeederId, nodeList, nodeMap, ldNodes);
        }

        return nodeMap;
    }

    /**
     * 处理父节点
     */
    void handleParentNode(QueryFeederDevVo feederDevVo, Map<String, Node> nodeMap, ArrayList<QueryFeederDevVo> allFeederDevVos) {
        // 父级容器znapId节点的map
        HashMap<Long, Node> parentZnapIdMap = new HashMap<>();
        for (QueryFeederDevVo allFeederDevVo : allFeederDevVos) {
            List<ConDmsCabinet> conDmsCabinets = allFeederDevVo.getConDmsCabinets();
            for (ConDmsCabinet conDmsCabinet : conDmsCabinets) {
                Node node = getNodeByMap(nodeMap, conDmsCabinet.getPsrid());
                if (node != null) {
                    parentZnapIdMap.put(conDmsCabinet.getId(), node);
                }
            }

        }

        setParentNode(feederDevVo.getDevDmsBreakers(), nodeMap, parentZnapIdMap, DevDmsBreaker::getPsrid, DevDmsBreaker::getCabinetId);
        setParentNode(feederDevVo.getDevDmsDisconnectors(), nodeMap, parentZnapIdMap, DevDmsDisconnector::getPsrid, DevDmsDisconnector::getCabinetId);
        setParentNode(feederDevVo.getDevDmsBusbars(), nodeMap, parentZnapIdMap, DevDmsBusbar::getPsrid, DevDmsBusbar::getCabinetId);
        setParentNode(feederDevVo.getDevDmsTrs(), nodeMap, parentZnapIdMap, DevDmsTr::getPsrid, DevDmsTr::getCabinetId);
    }

}
