package com.ruoyi.service.znap;

import com.ruoyi.entity.device.vo.ByFeederBusBarAndBreaker;
import com.ruoyi.entity.znap.TopoTraceReal;
import com.ruoyi.vo.BusbarSwitchVo;

import java.util.List;

/**
 * 拓扑查询服务接口
 */
public interface IBayQueryService {

    /**
     * 根据线路ID查询线路所属的开关（间隔）和母线和变电站
     *
     * @param psrId 线路PSR ID
     * @return 线路拓扑信息
     */
    BusbarSwitchVo queryLineTopology(String psrId);

    /**
     * 根据线路查询当前所属的母线下都所有开关（间隔）并且识别备用的开关
     *
     * @param psrId 线路PSR ID
     * @return 母线开关信息
     */
    List<BusbarSwitchVo> queryBusbarSwitches(String psrId);


    /**
     * 根据线路id查询变电站下所有的开关信息
     *
     * @param psrId 线路PSR ID
     * @return 母线下所有开关信息
     */
    List<BusbarSwitchVo> querySubstationBusbarSwitches(String psrId);

    /**
     * 根据线路查询所属的变电站下所有母线
     *
     * @param psrId 线路PSR ID
     * @return 变电站母线信息
     */
    List<TopoTraceReal> querySubstationBusbars(String psrId);

    /**
     *获取当前线路同母线下的所有线路ID（包括本线路）
     * @param feederId
     * @return
     */
    public List<String> getBusUnderFeederIds(String feederId);
}
