package com.ruoyi.scheduled;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.entity.calc.*;
import com.ruoyi.mapper.calc.*;
import com.ruoyi.util.RandomDoubleGenerator;
import com.ruoyi.vo.MainPathVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 定时查询计算实例数据，刷新实例状态,计算告警数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CalcAlarmTask {

    private final CalcInstanceInfoMapper calcInstanceInfoMapper;

    private final CalcRelationShipMapper calcRelationShipMapper;

    private final TrendCalcMapper trendCalcMapper;

    private final CalcAlarmRuleMapper calcAlarmRuleMapper;

    private final CalcAlarmInfoMapper calcAlarmInfoMapper;

    private final ZhTransferInfoMapper zhTransferInfoMapper;

    private final ZhTransferBreakInfoMapper zhTransferBreakInfoMapper;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // 告警类型常量
    private static final String ALARM_TYPE_BACKWARDS = "倒送";
    private static final String ALARM_TYPE_OVERLOAD = "重过载";
    private static final String ALARM_TYPE_VOLTAGE = "电压越限";
    private static final String ALARM_TYPE_LOSS = "高网损";

    // 超时时间常量
    private static final long ALARM_CALCULATION_TIMEOUT = 8;
    private static final TimeUnit ALARM_CALCULATION_TIMEOUT_UNIT = TimeUnit.MINUTES;

    @Scheduled(cron = "0/10 * * * * ?")
    public void doSync() {
        // 查询未完成的计算实例
        List<CalcInstanceInfo> calcInstanceInfos = findUnfinishedInstances();
        if (CollectionUtil.isEmpty(calcInstanceInfos)) {
            return;
        }
        // 遍历没有计算完成的实例
        for (CalcInstanceInfo calcInstanceInfo : calcInstanceInfos) {
            String instanceId = calcInstanceInfo.getInstanceId();
            // 查询实例下的每条关联线路
            List<CalcRelationShip> calcRelationShips = findRelationshipsByInstanceId(instanceId);
            // 如果没有关联线路计算信息，并且超过10分钟，计算失败
            if (CollectionUtil.isEmpty(calcRelationShips)) {
                handleMissingRelationships(calcInstanceInfo, instanceId);
                continue;
            }
            List<String> msgIds = calcRelationShips.stream()
                    .map(CalcRelationShip::getMsgId)
                    .distinct()
                    .collect(Collectors.toList());
            // 通过msgId查询计算结果表
            List<SimPfRet> simPfRets = trendCalcMapper.querySimPfRetByMsgIds(msgIds);
            if (CollectionUtil.isEmpty(simPfRets)) {
                handleMissingResults(calcInstanceInfo, instanceId);
                continue;
            }

            if (!areAllCalculationsFinished(simPfRets)) {
                log.warn("当前实例：{}，还未计算结束", instanceId);
                continue;
            }

            updateInstanceStatus(calcInstanceInfo, simPfRets);
            syncState(calcInstanceInfo, instanceId);

            if (calcInstanceInfo.getState() == 1) {
                processSuccessfulCalculation(calcInstanceInfo, simPfRets, instanceId);
            }
        }
    }

    private List<CalcInstanceInfo> findUnfinishedInstances() {
        return calcInstanceInfoMapper.selectList(new LambdaQueryWrapper<CalcInstanceInfo>()
                .eq(CalcInstanceInfo::getIsAuto, 0)
                .isNull(CalcInstanceInfo::getEndTime));
    }

    private List<CalcRelationShip> findRelationshipsByInstanceId(String instanceId) {
        return calcRelationShipMapper.selectList(new LambdaQueryWrapper<CalcRelationShip>()
                .eq(CalcRelationShip::getInstanceId, instanceId));
    }

    private void handleMissingRelationships(CalcInstanceInfo calcInstanceInfo, String instanceId) {
        // 检查是否超过10分钟
        if (isCalculationTimedOut(calcInstanceInfo.getStartTime())) {
            calcInstanceInfo.setState(2);
            calcInstanceInfo.setEndTime(new Date());
            syncState(calcInstanceInfo, instanceId);
            log.warn("当前实例：{}，没有关联线路计算信息，并且超过10分钟，计算失败", instanceId);
        }
    }

    private void handleMissingResults(CalcInstanceInfo calcInstanceInfo, String instanceId) {
        // 检查是否超过10分钟
        if (isCalculationTimedOut(calcInstanceInfo.getStartTime())) {
            calcInstanceInfo.setState(2);
            calcInstanceInfo.setEndTime(new Date());
            syncState(calcInstanceInfo, instanceId);
            log.warn("当前实例：{}，没有计算结果，并且超过10分钟，计算失败", instanceId);
        }
    }

    private boolean isCalculationTimedOut(Date startTime) {
        if (startTime == null) {
            return false;
        }
        long diffMinutes = (System.currentTimeMillis() - startTime.getTime()) / (60 * 1000);
        return diffMinutes > 10;
    }

    /**
     * 判断是否所有的计算都已完成
     *
     * @param simPfRets 结果
     * @return
     */
    private boolean areAllCalculationsFinished(List<SimPfRet> simPfRets) {
        long finishedCount = simPfRets.stream()
                .filter(item -> item.getEndDt() != null)
                .count();
        return simPfRets.size() == finishedCount;
    }

    /**
     * 如果全部失败状态为2，否则为1，取最大的时间作为计算结束时间
     *
     * @param calcInstanceInfo
     * @param simPfRets
     */
    private void updateInstanceStatus(CalcInstanceInfo calcInstanceInfo, List<SimPfRet> simPfRets) {
        long failedCount = simPfRets.stream()
                .filter(item -> item.getPfRet() == -1)
                .count();

        calcInstanceInfo.setState(simPfRets.size() == failedCount ? 2 : 1);

        Optional<SimPfRet> maxSimPfRet = simPfRets.stream()
                .max(Comparator.comparing(SimPfRet::getEndDt));
        calcInstanceInfo.setEndTime(maxSimPfRet.map(SimPfRet::getEndDt).orElse(new Date()));
    }

    /**
     * 处理计算成功的情况
     *
     * @param calcInstanceInfo
     * @param simPfRets
     * @param instanceId
     */
    private void processSuccessfulCalculation(CalcInstanceInfo calcInstanceInfo, List<SimPfRet> simPfRets, String instanceId) {
        List<SimPfRet> okSimPfRets = simPfRets.stream()
                .filter(item -> item.getPfRet() == 1)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(okSimPfRets)) {
            return;
        }
        // 查询告警规则
        List<CalcAlarmRule> calcAlarmRules = calcAlarmRuleMapper.selectList(
                new LambdaQueryWrapper<CalcAlarmRule>()
                        .eq(CalcAlarmRule::getInstanceId, instanceId));

        if (CollectionUtil.isEmpty(calcAlarmRules)) {
            return;
        }

        Map<String, CalcAlarmRule> calcAlarmRuleMap = calcAlarmRules.stream()
                .collect(Collectors.toMap(CalcAlarmRule::getDescription, Function.identity()));

        executeAlarmCalculations(okSimPfRets, calcAlarmRuleMap, instanceId);
    }

    /**
     * 处理告警计算,重过载，到送，电压越线，高网损
     *
     * @param okSimPfRets      计算成功的结果
     * @param calcAlarmRuleMap 告警规则映射
     * @param instanceId       实例ID
     */
    private void executeAlarmCalculations(List<SimPfRet> okSimPfRets, Map<String, CalcAlarmRule> calcAlarmRuleMap, String instanceId) {
        // 创建任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 提交各个告警计算任务
        futures.add(CompletableFuture.runAsync(() -> {
            try {
                handleBackWards(okSimPfRets, calcAlarmRuleMap, instanceId);
            } catch (Exception e) {
                log.error("倒送告警计算异常", e);
            }
        }, threadPoolTaskExecutor));

        futures.add(CompletableFuture.runAsync(() -> {
            try {
                handlerSegmentAlarm(okSimPfRets, calcAlarmRuleMap, instanceId);
            } catch (Exception e) {
                log.error("导线段重过载告警计算异常", e);
            }
        }, threadPoolTaskExecutor));

        futures.add(CompletableFuture.runAsync(() -> {
            try {
                handlerBreak(okSimPfRets, calcAlarmRuleMap, instanceId);
            } catch (Exception e) {
                log.error("开关重过载告警计算异常", e);
            }
        }, threadPoolTaskExecutor));

        futures.add(CompletableFuture.runAsync(() -> {
            try {
                handlerLoss(okSimPfRets, calcAlarmRuleMap, instanceId);
            } catch (Exception e) {
                log.error("高网损告警计算异常", e);
            }
        }, threadPoolTaskExecutor));

        futures.add(CompletableFuture.runAsync(() -> {
            try {
                handlerVoltgeOver(okSimPfRets, calcAlarmRuleMap, instanceId);
            } catch (Exception e) {
                log.error("电压越限告警计算异常", e);
            }
        }, threadPoolTaskExecutor));

        try {
            // 等待所有任务完成，设置合理的超时时间
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(ALARM_CALCULATION_TIMEOUT, ALARM_CALCULATION_TIMEOUT_UNIT);
            // 所有告警计算完成后，处理转供信息
//            handlerTransInfo(instanceId);
        } catch (InterruptedException e) {
            log.error("告警计算线程被中断", e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("告警计算执行异常", e);
        } catch (TimeoutException e) {
            log.error("告警计算超时", e);
        }
    }


    public void handlerTransInfo(String instanceId) {
        Date date = DateUtils.getNowDate();
        // 通过instanceId查询所有出问题的线路
        List<Map<String, Object>> questionFeeders = trendCalcMapper.queryQuestionFeeders(instanceId);
        // 查询该线路可转供的路线
        for (Map<String, Object> questionFeeder : questionFeeders) {
            String feederId = "PD_dkx_" + questionFeeder.get("feederId").toString();
            Long retId = (Long) questionFeeder.get("retId");
            // 可转供线路
            List<Map<String, Object>> toFeeders = trendCalcMapper.queryToFeeder(feederId);
            if (CollectionUtil.isEmpty(toFeeders)) {
                // 没有对应的转供路线
                continue;
            }
            // 有的话需要计算当前路线负载电流
            double allCurrent = calculateCurrent(feederId, retId);
            double questionAllCurrent = allCurrent;
            for (Map<String, Object> toFeeder : toFeeders) {

                //计算总体的
                String toFeederPsrId = toFeeder.get("tofeederpsrid").toString();
                double rateCurrent = calculateCurrent(toFeederPsrId, retId);
                log.info("转供线路：{} 当前负载电流为：{}", toFeederPsrId, rateCurrent);
                //查询额定电流
                toFeederPsrId = parsePsrId(toFeederPsrId);
                //转供馈线的额定电流
                double toFeederMaxCurrent = trendCalcMapper.queryMaxmaxCurrent(toFeederPsrId);
                log.info("转供线路：{} 当前额定电流为：{}", toFeederPsrId, toFeederMaxCurrent);
                //问题馈线的额定电流
                double questionFeederMaxCurrent = trendCalcMapper.queryMaxmaxCurrent(questionFeeder.get("feederId").toString());
                log.info("问题线路：{} 当前额定电流为：{}", questionFeeder.get("feederId").toString(), questionFeederMaxCurrent);
                //判断（原线路当前负载电流+目标线路当前负载电流）是否大于目标线路的额定电流
                allCurrent = allCurrent + rateCurrent;
                double toFeederAllCurrent = calculateCurrent(toFeeder.get("tofeederpsrid").toString(), retId);
                ZhTransferInfo zhTransferInfo = new ZhTransferInfo();
                if (allCurrent >= toFeederMaxCurrent) {
                    zhTransferInfo.setTransferStatus("0");
                    // 不可转供
                }
                if (allCurrent < toFeederMaxCurrent) {
                    // 可转供
                    zhTransferInfo.setTransferStatus("1");
                }
                zhTransferInfo.setRetId(retId);
                zhTransferInfo.setCalcId(instanceId);
                zhTransferInfo.setSourceFeeder(parsePsrId(toFeeder.get("feederpsrid").toString()));
                zhTransferInfo.setSourceFeederName(toFeeder.get("feeder").toString());
                zhTransferInfo.setSourceFeederCurrent(NumberUtil.round(allCurrent, 2).doubleValue());
                zhTransferInfo.setSourceFeederBreak(parsePsrId(toFeeder.get("psrid").toString()));
                zhTransferInfo.setSourceFeederBreakName(toFeeder.get("breakname").toString());
                zhTransferInfo.setLinkFeeder(parsePsrId(toFeeder.get("tofeederpsrid").toString()));
                zhTransferInfo.setLinkFeederName(toFeeder.get("tofeeder").toString());
                zhTransferInfo.setLinkFeederCurrent(NumberUtil.round(toFeederMaxCurrent, 2).doubleValue());
                zhTransferInfo.setSourceFeederRate(NumberUtil.round(questionAllCurrent / questionFeederMaxCurrent, 2).doubleValue());
                zhTransferInfo.setLinkFeederRate(NumberUtil.round(allCurrent / toFeederMaxCurrent, 2).doubleValue());
                zhTransferInfo.setLinkFeederOriginalRate(NumberUtil.round(toFeederAllCurrent / toFeederMaxCurrent, 2).doubleValue());
                zhTransferInfo.setTime(date);
                zhTransferInfo.setSourceFeederBreakType(toFeeder.get("breaktype").toString());
                zhTransferInfoMapper.insert(zhTransferInfo);

                //计算每个开关下面的
                //拿问题线路去查询主干路径(根据每一个联络开关和问题馈线)
                String sourceFeeder = zhTransferInfo.getSourceFeeder();
                String sourceFeederBreak = zhTransferInfo.getSourceFeederBreak();
                List<MainPathVO> mainPathVOS = trendCalcMapper.queryMainPath("PD_dkx_" + sourceFeeder, sourceFeederBreak);
                List<ZhTransferBreakInfo> zhTransferBreakInfos = new ArrayList<>();
                for (MainPathVO mainPathVO : mainPathVOS) {
                    ZhTransferBreakInfo zhTransferBreakInfo = new ZhTransferBreakInfo();
                    zhTransferBreakInfo.setBreakPsrId(mainPathVO.getPsrId());
                    zhTransferBreakInfo.setBreakPsrName(mainPathVO.getName());
                    zhTransferBreakInfo.setBreakPsrType(mainPathVO.getPsrType());
                    zhTransferBreakInfo.setLinkBreakPsrId(zhTransferInfo.getSourceFeederBreak());
                    zhTransferBreakInfo.setLinkBreakPsrName(zhTransferInfo.getSourceFeederBreakName());
                    zhTransferBreakInfo.setLinkBreakPsrType(zhTransferInfo.getSourceFeederBreakType());
                    zhTransferBreakInfo.setIndexNum(mainPathVO.getIdx());
                    //暂时随机
                    //1、通过zanpb拓扑计算文档的第6，第7目录查看该开关以及下游开关的配变和负荷
                    //2.按开关顺序 一个一个计算当前开关以及下游开关上面的负荷和变压器电流

                    zhTransferBreakInfo.setLinkFeederRate(RandomDoubleGenerator.generateRandomDouble());
                    zhTransferBreakInfo.setSourceFeederRate(RandomDoubleGenerator.generateRandomDouble());
                    zhTransferBreakInfos.add(zhTransferBreakInfo);
                }
                zhTransferBreakInfoMapper.insertBatch(zhTransferBreakInfos);
            }
        }
    }

    private String parsePsrId(String psrIdStr) {
        if (StringUtils.isBlank(psrIdStr)) {
            return "";
        }
        String[] psrArray = psrIdStr.split("_");
        String simPsrId = psrArray[psrArray.length - 1];
        if (!simPsrId.contains("@")) {
            return simPsrId;
        }
        String[] simPsrArray = simPsrId.split("@");
        if (simPsrArray.length > 1) {
            return simPsrArray[1];
        }
        return simPsrId;
    }

    public double calculateCurrent(String feederId, Long retId) {
        // 查询当前线路所有负荷和配变
        List<Long> trIds = trendCalcMapper.queryTrByFeeder(feederId);
        List<Long> ldIds = trendCalcMapper.queryLdByFeeder(feederId);
        if (CollectionUtil.isEmpty(trIds) && CollectionUtil.isEmpty(ldIds)) {
            return 0.0;
        }
        double allCurrent = 0;
        double p = 0;
        double q = 0;
        for (Long trId : trIds) {
            // 查询最大有功和无功
            Map<String, Double> pqValue = trendCalcMapper.queryMaxPValueAndQValueByTr(trId, retId);
            double maxP = pqValue.get("pvalue");
            double maxQ = pqValue.get("qvalue");
            p = p + maxP;
            q = q + maxQ;
        }
        for (Long ldId : ldIds) {
            // 查询最大有功和无功
            Map<String, Double> pqValue = trendCalcMapper.queryMaxPValueAndQValueByLd(ldId, retId);
            double maxP = pqValue.get("pvalue");
            double maxQ = pqValue.get("qvalue");
            p = p + maxP;
            q = q + maxQ;
        }
        // 计算电流
        allCurrent = calculateCurrent(p, q);
        return allCurrent;
    }

    /**
     * 根据有功功率、无功功率和电压计算电流
     *
     * @param p 有功功率，单位：kW
     * @param q 无功功率，单位：kvar
     * @return 电流值，单位：A
     */
    public static double calculateCurrent(double p, double q) {
        if (p == 0 && q == 0) {
            return 0;
        }
        double voltage = 10;
        // 计算视在功率
        double apparentPower = Math.sqrt(p * p + q * q);
        // 计算电流，公式 I = S / (sqrt(3) * U)，这里 S 是视在功率
        return apparentPower * 1000 / (Math.sqrt(3) * voltage * 1000);
    }


    private void handleBackWards(List<SimPfRet> simPfRets, Map<String, CalcAlarmRule> epsAlarmConfigMap, String instanceId) {
        log.info("开始处理倒送告警");
        boolean flag = false;
        List<CalcAlarmInfo> alarmInfos = new ArrayList<>();
        try {
            CalcAlarmRule overLoad = epsAlarmConfigMap.get(ALARM_TYPE_BACKWARDS);
            if (overLoad == null) {
                log.warn("未找到倒送告警规则配置");
                return;
            }
            Double limitValue = overLoad.getLimitValue();

            // 批量获取所有需要的EMS开关列表
            List<Long> retIds = simPfRets.stream().map(SimPfRet::getId).collect(Collectors.toList());
            Map<Long, List<SimRetPfEmsBreak>> allEmsBreaks = new HashMap<>();

            // 批量查询所有开关列表，避免循环中多次查询
            for (Long retId : retIds) {
                List<SimRetPfEmsBreak> breakList = trendCalcMapper.queryEmsBreakList(retId);
                if (!CollectionUtil.isEmpty(breakList)) {
                    allEmsBreaks.put(retId, breakList);
                }
            }

            // 提前收集所有需要查询的馈线ID
            Set<String> allFeederIds = new HashSet<>();
            for (List<SimRetPfEmsBreak> breakList : allEmsBreaks.values()) {
                for (SimRetPfEmsBreak breakItem : breakList) {
                    String feederId = extractFeederId(breakItem.getFeederId());
                    if (StringUtils.isNotBlank(feederId)) {
                        allFeederIds.add(feederId);
                    }
                }
            }

            // 批量查询所有网格信息
            Map<String, Map<String, Object>> gridInfoMap = new HashMap<>();
            for (String feederId : allFeederIds) {
                Map<String, Object> gridInfo = trendCalcMapper.queryGridCodeByFeederId(feederId);
                if (gridInfo != null) {
                    gridInfoMap.put(feederId, gridInfo);
                }
            }

            for (SimPfRet simPfRet : simPfRets) {
                Long id = simPfRet.getId();
                List<SimRetPfEmsBreak> segmentList = allEmsBreaks.get(id);

                if (CollectionUtil.isEmpty(segmentList)) {
                    continue;
                }

                Map<Long, List<SimRetPfEmsBreak>> segmentListById = segmentList
                        .stream()
                        .collect(Collectors.groupingBy(SimRetPfEmsBreak::getId));

                for (Map.Entry<Long, List<SimRetPfEmsBreak>> longListEntry : segmentListById.entrySet()) {
                    // 获取设备信息
                    SimRetPfEmsBreak firstBreak = longListEntry.getValue().get(0);
                    String name = firstBreak.getName();
                    String psrId = extractPsrId(firstBreak.getPsrId());
                    String psrType = extractPsrType(firstBreak.getPsrId());
                    String feederId = extractFeederId(firstBreak.getFeederId());

                    // 使用预先查询的网格信息
                    Map<String, Object> breakInfo = gridInfoMap.get(feederId);
                    if (breakInfo == null) {
                        continue;
                    }

                    String simuCaseNo = String.format("%s:%s", breakInfo.get("power_grid_code"), id);

                    // 检查是否有倒送情况
                    for (SimRetPfEmsBreak simRetPfEmsBreak : longListEntry.getValue()) {
                        // 获取电流
                        Double iValue = simRetPfEmsBreak.getPIndValue();
                        // 判断有没有超过设定值
                        if (iValue > limitValue) {
                            flag = true;
                            // 创建告警信息
                            CalcAlarmInfo calcAlarmInfo = createBaseAlarmInfo(
                                    id, psrId, psrType, feederId, instanceId,
                                    overLoad.getId(), simPfRet.getStartDt(), simuCaseNo, name
                            );

                            calcAlarmInfo.setPsrParentId(psrId);
                            calcAlarmInfo.setPsrParentType(psrType);
                            calcAlarmInfo.setFeederId(feederId);
                            calcAlarmInfo.setAlarmContent(String.format("出线开关：%s，出现倒送", name));
                            if (StringUtils.isNotBlank(simuCaseNo)) {
                                calcAlarmInfo.setGridId(simuCaseNo.split(":")[0]);
                            }
                            alarmInfos.add(calcAlarmInfo);
                            break;
                        }
                    }
                }
            }

            // 批量插入告警信息
            if (!alarmInfos.isEmpty()) {
                batchInsertAlarms(alarmInfos);
            }

            if (!flag) {
                log.info("无倒送情况");
            } else {
                log.info("发现{}条倒送告警", alarmInfos.size());
            }
        } catch (Exception e) {
            log.error("处理倒送告警异常", e);
        }
    }


    private void handlerSegmentAlarm(List<SimPfRet> simPfRets, Map<String, CalcAlarmRule> epsAlarmConfigMap, String instanceId) {
        log.info("开始处理导线段重过载告警");
        boolean flag = false;
        List<CalcAlarmInfo> alarmInfos = new ArrayList<>();
        try {
            CalcAlarmRule overLoad = epsAlarmConfigMap.get(ALARM_TYPE_OVERLOAD);
            if (overLoad == null) {
                log.warn("未找到重过载告警规则配置");
                return;
            }
            double limitValue = overLoad.getLimitValue() / 100;
            for (SimPfRet simPfRet : simPfRets) {
                Long id = simPfRet.getId();
                // 查询导线段列表
                List<SimRetPfDmsSegment> segmentList = trendCalcMapper.querySegmentListByRetId(id);

                if (CollectionUtil.isEmpty(segmentList)) {
                    continue;
                }
                Map<Long, List<SimRetPfDmsSegment>> segmentListById = segmentList
                        .stream()
                        .collect(Collectors.groupingBy(SimRetPfDmsSegment::getId));
                for (Map.Entry<Long, List<SimRetPfDmsSegment>> longListEntry : segmentListById.entrySet()) {
                    // 获取设备信息
                    SimRetPfDmsSegment firstSegment = longListEntry.getValue().get(0);
                    String psrId = extractPsrId(firstSegment.getPsrId());
                    // 获取导线段信息
                    Map<String, Object> segmentInfo = trendCalcMapper.querySegmentAstInfoByPsrId(psrId);
                    if (segmentInfo == null) {
                        continue;
                    }
                    String simuCaseNo = String.format("%s:%s", segmentInfo.get("power_grid_code"), id);
                    String name = getStr(segmentInfo.get("name"));
                    // 计算最大允许电流
                    BigDecimal maxRatedAmpacity = new BigDecimal(segmentInfo.get("rated_ampacity").toString()).multiply(new BigDecimal(limitValue)).setScale(2, RoundingMode.HALF_UP);
                    for (SimRetPfDmsSegment simRetPfDmsSegment : longListEntry.getValue()) {
                        // 获取电流值
                        Double iIndValue = simRetPfDmsSegment.getIIndValue();
                        // 判断有没有超过设定值
                        if (Double.compare(iIndValue, maxRatedAmpacity.doubleValue()) >= 0) {
                            flag = true;
                            // 创建告警信息
                            CalcAlarmInfo calcAlarmInfo = createBaseAlarmInfo(
                                    id, psrId, getStr(segmentInfo.get("psrType")),
                                    getStr(segmentInfo.get("feeder")), instanceId,
                                    overLoad.getId(), simPfRet.getStartDt(), simuCaseNo, name
                            );
                            calcAlarmInfo.setPsrParentId(segmentInfo.get("startPole") == null ? null : segmentInfo.get("startPole").toString());
                            calcAlarmInfo.setPsrParentType("dkx".equals(segmentInfo.get("psrType")) ? "0103" : "0202");
                            calcAlarmInfo.setAlarmContent(String.format("导线段：%s，出现重过载", name));
                            // 获取累计告警次数
                            int accumulative = queryAccumulative(calcAlarmInfo);
                            calcAlarmInfo.setAccumulative(++accumulative);
                            if (StringUtils.isNotBlank(simuCaseNo)) {
                                calcAlarmInfo.setGridId(simuCaseNo.split(":")[0]);
                            }
                            alarmInfos.add(calcAlarmInfo);
                            break;
                        }
                    }
                }
            }
            // 批量插入告警信息
            if (!alarmInfos.isEmpty()) {
                batchInsertAlarms(alarmInfos);
            }
            if (!flag) {
                log.info("导线无重过载情况");
            } else {
                log.info("发现{}条导线段重过载告警", alarmInfos.size());
            }
        } catch (Exception e) {
            log.error("处理导线段重过载告警异常", e);
        }
    }

    /**
     * 处理开关重过载告警
     */
    private void handlerBreak(List<SimPfRet> simPfRets, Map<String, CalcAlarmRule> epsAlarmConfigMap, String instanceId) {
        log.info("开始处理开关重过载告警");
        boolean flag = false;
        List<CalcAlarmInfo> alarmInfos = new ArrayList<>();
        try {
            CalcAlarmRule overLoad = epsAlarmConfigMap.get(ALARM_TYPE_OVERLOAD);
            if (overLoad == null) {
                log.warn("未找到重过载告警规则配置");
                return;
            }
            double limitValue = overLoad.getLimitValue() / 100;
            for (SimPfRet simPfRet : simPfRets) {
                Long id = simPfRet.getId();
                // 查询开关列表
                List<SimRetPfDmsBreak> simRetPfDmsBreaks = trendCalcMapper.queryBreakListByRetId(id);
                if (CollectionUtil.isEmpty(simRetPfDmsBreaks)) {
                    continue;
                }
                Map<Long, List<SimRetPfDmsBreak>> breakListById = simRetPfDmsBreaks
                        .stream()
                        .collect(Collectors.groupingBy(SimRetPfDmsBreak::getId));
                for (Map.Entry<Long, List<SimRetPfDmsBreak>> longListEntry : breakListById.entrySet()) {
                    // 获取设备信息
                    SimRetPfDmsBreak firstBreak = longListEntry.getValue().get(0);
                    String psrId = extractPsrId(firstBreak.getPsrId());
                    // 获取开关信息
                    Map<String, Object> breakInfo = trendCalcMapper.queryBreakAstInfoByPsrId(psrId);
                    if (breakInfo == null) {
                        continue;
                    }
                    String simuCaseNo = String.format("%s:%s", breakInfo.get("power_grid_code"), id);
                    String name = getStr(breakInfo.get("name"));

                    // 计算最大允许电流
                    BigDecimal maxRatedAmpacity = new BigDecimal(breakInfo.get("rated_current").toString())
                            .multiply(new BigDecimal(limitValue))
                            .setScale(2, RoundingMode.HALF_UP);

                    // 检查是否有重过载情况
                    for (SimRetPfDmsBreak simRetPfDmsBreak : longListEntry.getValue()) {
                        // 获取电流
                        Double iValue = simRetPfDmsBreak.getIValue();
                        // 判断有没有超过设定值
                        if (Double.compare(iValue, maxRatedAmpacity.doubleValue()) >= 0) {
                            flag = true;
                            // 创建告警信息
                            CalcAlarmInfo calcAlarmInfo = createBaseAlarmInfo(
                                    id, psrId, getStr(breakInfo.get("psrType")),
                                    getStr(breakInfo.get("feeder")), instanceId,
                                    overLoad.getId(), simPfRet.getStartDt(), simuCaseNo, name
                            );
                            calcAlarmInfo.setPsrParentId(breakInfo.get("combination") == null ? null : breakInfo.get("combination").toString());
                            calcAlarmInfo.setPsrParentType(breakInfo.get("combination_type") == null ? null : breakInfo.get("combination_type").toString());
                            calcAlarmInfo.setAlarmContent(String.format("开关：%s，出现重过载", name));
                            // 获取累计告警次数
                            int accumulative = queryAccumulative(calcAlarmInfo);
                            calcAlarmInfo.setAccumulative(++accumulative);
                            if (StringUtils.isNotBlank(simuCaseNo)) {
                                calcAlarmInfo.setGridId(simuCaseNo.split(":")[0]);
                            }
                            alarmInfos.add(calcAlarmInfo);
                            break;
                        }
                    }
                }
            }

            // 批量插入告警信息
            if (!alarmInfos.isEmpty()) {
                batchInsertAlarms(alarmInfos);
            }
            if (!flag) {
                log.info("开关无重过载情况");
            } else {
                log.info("发现{}条开关重过载告警", alarmInfos.size());
            }
        } catch (Exception e) {
            log.error("处理开关重过载告警异常", e);
        }
    }

    /**
     * 处理高网损告警
     */
    private void handlerLoss(List<SimPfRet> simPfRets, Map<String, CalcAlarmRule> epsAlarmConfigMap, String instanceId) {
        log.info("开始处理高网损告警");
        boolean flag = false;
        List<CalcAlarmInfo> alarmInfos = new ArrayList<>();
        try {
            CalcAlarmRule overLoad = epsAlarmConfigMap.get(ALARM_TYPE_LOSS);
            if (overLoad == null) {
                log.warn("未找到高网损告警规则配置");
                return;
            }
            Double limitValue = overLoad.getLimitValue();
            for (SimPfRet simPfRet : simPfRets) {
                Long id = simPfRet.getId();
                // 查询导线段列表
                List<SimRetPfDmsSegment> segmentList = trendCalcMapper.querySegmentListByRetId(id);

                if (CollectionUtil.isEmpty(segmentList)) {
                    continue;
                }
                Map<Long, List<SimRetPfDmsSegment>> segmentListById = segmentList
                        .stream()
                        .collect(Collectors.groupingBy(SimRetPfDmsSegment::getId));
                for (Map.Entry<Long, List<SimRetPfDmsSegment>> longListEntry : segmentListById.entrySet()) {
                    // 获取设备信息
                    SimRetPfDmsSegment firstSegment = longListEntry.getValue().get(0);
                    String psrId = extractPsrId(firstSegment.getPsrId());
                    // 获取导线段信息
                    Map<String, Object> segmentInfo = trendCalcMapper.querySegmentAstInfoByPsrId(psrId);
                    if (segmentInfo == null) {
                        continue;
                    }
                    String simuCaseNo = String.format("%s:%s", segmentInfo.get("power_grid_code"), id);
                    String name = getStr(segmentInfo.get("name"));

                    // 检查是否有高网损情况
                    for (SimRetPfDmsSegment simRetPfDmsSegment : longListEntry.getValue()) {
                        // 获取有功损耗
                        Double pLossValue = simRetPfDmsSegment.getPLossValue();
                        // 判断有没有超过设定值
                        if (pLossValue > limitValue) {
                            flag = true;
                            // 创建告警信息
                            CalcAlarmInfo calcAlarmInfo = createBaseAlarmInfo(
                                    id, psrId, getStr(segmentInfo.get("psr_type")),
                                    getStr(segmentInfo.get("feeder")), instanceId,
                                    overLoad.getId(), simPfRet.getStartDt(), simuCaseNo, name
                            );
                            calcAlarmInfo.setPsrParentId(segmentInfo.get("start_pole") == null ? null : segmentInfo.get("start_pole").toString());
                            calcAlarmInfo.setPsrParentType("dkx".equals(segmentInfo.get("psr_type")) ? "0103" : "0202");
                            calcAlarmInfo.setAlarmContent(String.format("导线段：%s，出现高网损", name));
                            if (StringUtils.isNotBlank(simuCaseNo)) {
                                calcAlarmInfo.setGridId(simuCaseNo.split(":")[0]);
                            }
                            alarmInfos.add(calcAlarmInfo);
                            break;
                        }
                    }
                }
            }
            // 批量插入告警信息
            if (!alarmInfos.isEmpty()) {
                batchInsertAlarms(alarmInfos);
            }
            if (!flag) {
                log.info("无高网损情况");
            } else {
                log.info("发现{}条高网损告警", alarmInfos.size());
            }
        } catch (Exception e) {
            log.error("处理高网损告警异常", e);
        }
    }

    /**
     * 处理电压越限告警
     */
    private void handlerVoltgeOver(List<SimPfRet> simPfRets, Map<String, CalcAlarmRule> epsAlarmConfigMap, String instanceId) {
        log.info("开始处理电压越限告警");
        boolean flag = false;
        List<CalcAlarmInfo> alarmInfos = new ArrayList<>();
        try {
            CalcAlarmRule overLoad = epsAlarmConfigMap.get(ALARM_TYPE_VOLTAGE);
            if (overLoad == null) {
                log.warn("未找到电压越限告警规则配置");
                return;
            }
            Double limitValue = overLoad.getLimitValue();
            for (SimPfRet simPfRet : simPfRets) {
                Long id = simPfRet.getId();
                // 查询开关列表
                List<SimRetPfDmsBreak> simRetPfDmsBreaks = trendCalcMapper.queryBreakListByRetId(id);
                if (CollectionUtil.isEmpty(simRetPfDmsBreaks)) {
                    continue;
                }
                Map<Long, List<SimRetPfDmsBreak>> breakListById = simRetPfDmsBreaks
                        .stream()
                        .collect(Collectors.groupingBy(SimRetPfDmsBreak::getId));
                for (Map.Entry<Long, List<SimRetPfDmsBreak>> longListEntry : breakListById.entrySet()) {
                    // 获取设备信息
                    SimRetPfDmsBreak firstBreak = longListEntry.getValue().get(0);
                    String psrId = extractPsrId(firstBreak.getPsrId());
                    // 获取开关信息
                    Map<String, Object> breakInfo = trendCalcMapper.queryBreakAstInfoByPsrId(psrId);
                    if (breakInfo == null) {
                        continue;
                    }
                    String simuCaseNo = String.format("%s:%s", breakInfo.get("power_grid_code"), id);
                    String name = getStr(breakInfo.get("name"));
                    // 检查是否有电压越限情况
                    for (SimRetPfDmsBreak simRetPfDmsBreak : longListEntry.getValue()) {
                        // 获取电压
                        Double vValue = simRetPfDmsBreak.getVValue();
                        // 判断有没有超过设定值
                        if (vValue > limitValue) {
                            flag = true;
                            // 创建告警信息
                            CalcAlarmInfo calcAlarmInfo = createBaseAlarmInfo(
                                    id, psrId, getStr(breakInfo.get("psr_type")),
                                    getStr(breakInfo.get("feeder")), instanceId,
                                    overLoad.getId(), simPfRet.getStartDt(), simuCaseNo, name
                            );
                            calcAlarmInfo.setPsrParentId(breakInfo.get("combination") == null ? null : breakInfo.get("combination").toString());
                            calcAlarmInfo.setPsrParentType(breakInfo.get("combination_type") == null ? null : breakInfo.get("combination_type").toString());
                            calcAlarmInfo.setAlarmContent(String.format("开关：%s，出现电压越限", name));
                            if (StringUtils.isNotBlank(simuCaseNo)) {
                                calcAlarmInfo.setGridId(simuCaseNo.split(":")[0]);
                            }
                            alarmInfos.add(calcAlarmInfo);
                            break;
                        }
                    }
                }
            }
            // 批量插入告警信息
            if (!alarmInfos.isEmpty()) {
                batchInsertAlarms(alarmInfos);
            }
            if (!flag) {
                log.info("无电压越限情况");
            } else {
                log.info("发现{}条电压越限告警", alarmInfos.size());
            }
        } catch (Exception e) {
            log.error("处理电压越限告警异常", e);
        }
    }

    /**
     * 批量插入告警信息
     */
    private void batchInsertAlarms(List<CalcAlarmInfo> alarms) {
        if (CollectionUtil.isEmpty(alarms)) {
            return;
        }
        try {
            calcAlarmInfoMapper.insertBatch(alarms);
            log.info("批量插入{}条告警信息成功", alarms.size());
        } catch (Exception e) {
            log.error("批量插入告警信息异常", e);
        }
    }

    /**
     * 创建基础告警信息
     */
    public static CalcAlarmInfo createBaseAlarmInfo(Long retId, String psrId, String psrType,
                                                    String feederId, String instanceId,
                                                    Long alarmType, Date alarmTime, String simuCaseNo, String modelName) {
        CalcAlarmInfo calcAlarmInfo = new CalcAlarmInfo();
        calcAlarmInfo.setRetId(retId);
        calcAlarmInfo.setPsrId(psrId);
        calcAlarmInfo.setPsrType(psrType);
        calcAlarmInfo.setFeederId(feederId);
        calcAlarmInfo.setAlarmType(alarmType);
        calcAlarmInfo.setAlarmTime(alarmTime);
        calcAlarmInfo.setInstanceId(instanceId);
        calcAlarmInfo.setSimuCaseNo(simuCaseNo);
        calcAlarmInfo.setLevel(1);
        calcAlarmInfo.setIsAuto(0);
        calcAlarmInfo.setModelName(modelName);
        return calcAlarmInfo;
    }

    /**
     * 从PSR ID字符串中提取PSR ID
     */
    public static String extractPsrId(String psrIdStr) {
        if (StringUtils.isBlank(psrIdStr)) {
            return "";
        }

        String[] psrArray = psrIdStr.split("_");
        String simPsrId = psrArray.length > 0 ? psrArray[psrArray.length - 1] : "";

        if (!simPsrId.contains("@")) {
            return simPsrId;
        }

        String[] simPsrArray = simPsrId.split("@");
        return simPsrArray.length > 1 ? simPsrArray[1] : simPsrId;
    }

    /**
     * 从PSR ID字符串中提取PSR类型
     */
    private String extractPsrType(String psrIdStr) {
        if (StringUtils.isBlank(psrIdStr)) {
            return "";
        }

        String[] psrArray = psrIdStr.split("_");
        if (psrArray.length < 2) {
            return "";
        }

        return psrArray[psrArray.length - 2];
    }

    /**
     * 从Feeder ID字符串中提取Feeder ID
     */
    private String extractFeederId(String feederIdStr) {
        if (StringUtils.isBlank(feederIdStr)) {
            return "";
        }

        String[] feederIdArray = feederIdStr.split("_");
        return feederIdArray.length > 0 ? feederIdArray[feederIdArray.length - 1] : "";
    }

    /**
     * 获取字符串值，避免空指针
     */
    public static String getStr(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }


    /**
     * 更新实例状态
     *
     * @param calcInstanceInfo
     * @param instanceId
     */
    public void syncState(CalcInstanceInfo calcInstanceInfo, String instanceId) {
        try {
            int updateCount = calcInstanceInfoMapper.update(calcInstanceInfo, new LambdaQueryWrapper<CalcInstanceInfo>()
                    .eq(CalcInstanceInfo::getInstanceId, instanceId));

            if (updateCount > 0) {
                log.info("当前实例：{}，状态更新成功", instanceId);
            } else {
                log.warn("当前实例：{}，状态更新失败", instanceId);
            }
        } catch (Exception e) {
            log.error("更新实例状态异常：{}", instanceId, e);
        }
    }

    /**
     * 查询累计告警次数
     */
    public int queryAccumulative(CalcAlarmInfo calcAlarmInfo) {
        int accumulative = 0;
        //查询该元件的上次告警数据
        CalcAlarmInfo accumulativeInfo = trendCalcMapper.queryAccumulative(calcAlarmInfo.getPsrId(), calcAlarmInfo.getAlarmType());
        if (accumulativeInfo != null) {
            accumulative = Integer.parseInt(accumulativeInfo.getAlarmContent().toString());
        }
        return accumulative;
    }
}
