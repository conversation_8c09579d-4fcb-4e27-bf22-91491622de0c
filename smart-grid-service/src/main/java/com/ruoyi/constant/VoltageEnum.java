package com.ruoyi.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public enum VoltageEnum {

    AC_6V("01", "交流6V"),
    AC_12V("02", "交流12V"),
    AC_24V("03", "交流24V"),
    AC_36V("04", "交流36V"),
    AC_48V("05", "交流48V"),
    AC_110V("06", "交流110V"),
    AC_220V("07", "交流220V"),
    AC_380V("08", "交流380V(含400V)"),
    AC_660V("09", "交流660V"),
    AC_1000V("10", "交流1000V（含1140V）"),
    AC_600V("11", "交流600V"),
    AC_750V("12", "交流750V"),
    AC_1500V("13", "交流1500V"),
    AC_3000V("14", "交流3000V"),
    AC_2500V("15", "交流2500V"),
    AC_3KV("20", "交流3kV"),
    AC_6KV("21", "交流6kV"),
    AC_10KV("22", "交流10kV"),
    AC_15_75KV("23", "交流15.75kV"),
    AC_20KV("24", "交流20kV"),
    AC_35KV("25", "交流35kV"),
    AC_66KV("30", "交流66kV"),
    AC_72_5KV("31", "交流72.5kV"),
    AC_110KV_SECOND("32", "交流110kV"),
    AC_220KV_SECOND("33", "交流220kV"),
    AC_330KV("34", "交流330kV"),
    AC_500KV("35", "交流500kV"),
    AC_750KV_SECOND("36", "交流750kV"),
    AC_1000KV_SECOND("37", "交流1000kV"),
    DC_6V("51", "直流6V"),
    DC_12V("52", "直流12V"),
    DC_24V("53", "直流24V"),
    DC_36V("54", "直流36V"),
    DC_48V("55", "直流48V"),
    DC_110V("56", "直流110V"),
    DC_220V("60", "直流220V"),
    DC_600V("70", "直流600V"),
    DC_750V("71", "直流750V"),
    DC_1500V("72", "直流1500V"),
    DC_3000V("73", "直流3000V"),
    DC_35KV("76", "直流35kV"),
    DC_30KV("77", "直流30kV"),
    DC_50KV("78", "直流50kV"),
    DC_120KV("80", "直流120kV"),
    DC_125KV("81", "直流125kV"),
    DC_400KV("82", "直流400kV"),
    DC_500KV_SECOND("83", "直流500kV"),
    DC_660KV("84", "直流660kV"),
    DC_800KV("85", "直流800kV"),
    DC_1000KV("86", "直流1000kV"),
    DC_200KV("87", "直流200kV"),
    DC_320KV("88", "直流320kV"),
    DC_166_7KV("90", "直流166.7kV"),
    NO_POWER("99", "无电源");

    private final String value;
    private final String label;

    VoltageEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    // 根据value获取对应的枚举实例
    public static Optional<VoltageEnum> getByValue(String value) {
        for (VoltageEnum enumObj : VoltageEnum.values()) {
            if (enumObj.getValue().equals(value)) {
                return Optional.of(enumObj);
            }
        }
        return Optional.empty();
    }

    // 获取所有枚举实例的列表，便于前端展示等场景使用（如果有需要）
    public static List<VoltageEnum> getAll() {
        List<VoltageEnum> list = new ArrayList<>();
        list.addAll(Arrays.asList(VoltageEnum.values()));
        return list;
    }
}
