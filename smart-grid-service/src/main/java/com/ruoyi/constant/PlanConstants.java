package com.ruoyi.constant;

/**
 * 方案相关常量
 */
public class PlanConstants {

    // 分段内配变数量不合理分类编码
    public static final int SEG_PB_MUSH_LEVEL = 1;
    //站房母线馈供用户过多
    public static final int SEG_ZFMX_MUSH_LEVEL = 2;
    //单辐射线路
    public static final int SEG_DFSXL_MUSH_LEVEL = 3;
    //同母联络
    public static final int SEG_TMLN_MUSH_LEVEL = 6;
    //大分支无联络
    public static final int SEG_DFZWLN_MUSH_LEVEL = 7;
    //线路供电半径过长
    public static final int SEG_XLGDBJ_MUSH_LEVEL = 8;
    //多回架空线路同杆架设
    public static final int SEG_DHJKX_MUSH_LEVEL = 9;
    //线路挂接配变过多
    public static final int SEG_XLGJPB_MUSH_LEVEL = 10;
    //线路可开放能力不足
    public static final int SEG_XLKFNLBZ_MUSH_LEVEL = 11;
    //线路重过载
    public static final int SEG_XLZGZ_MUSH_LEVEL = 13;
}
