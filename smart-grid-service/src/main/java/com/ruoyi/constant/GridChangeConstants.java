package com.ruoyi.constant;

import java.util.*;

import static com.ruoyi.constant.DeviceConstants.*;

/**
 * 网架变更常量类
 *
 */
public class GridChangeConstants {

    /**
     * 设备类型映射
     * 设备类型标识：0主网开关、1配网开关、2刀闸、3熔断器、4母线、5地刀、
     * 6电缆头、7负荷、8杆塔、9馈线段、10配变、11绕组
     */
    public static final Map<String, Integer> DEVICE_TYPE_MAP = new HashMap<>();
    public static final List<String> DEVICE_TYPE_LIST = new ArrayList<>();


    /**
     * 容器类型映射
     * 容器类型标识：1变电站、2馈线、3柜子、4组合开关(可以忽略)
     */
    public static final Map<String, Integer> CONTAINER_TYPE_MAP = new HashMap<>();


    private static final List<String> CONTAINER_TYPES = new ArrayList<>();

    static {
        // 初始化设备类型映射
        DEVICE_TYPE_MAP.put("100", 0);  // 主网开关
        DEVICE_TYPE_MAP.put("204", 1);  // 配网开关
        DEVICE_TYPE_MAP.put("205", 2);  // 刀闸
        DEVICE_TYPE_MAP.put("214", 3);  // 熔断器
        DEVICE_TYPE_MAP.put("211", 4);  // 母线
        DEVICE_TYPE_MAP.put("207", 5);  // 地刀
        DEVICE_TYPE_MAP.put("215", 6);  // 电缆头
        DEVICE_TYPE_MAP.put("209", 7);  // 负荷
        DEVICE_TYPE_MAP.put("212", 8);  // 杆塔
        DEVICE_TYPE_MAP.put("206", 9);  // 馈线段
        DEVICE_TYPE_MAP.put("208", 10); // 配变
        DEVICE_TYPE_MAP.put("210", 11); // 绕组

        DEVICE_TYPE_LIST.add("0305");// 主网开关
        DEVICE_TYPE_LIST.addAll(KG_IN_TYPES);// 配网开关
        DEVICE_TYPE_LIST.addAll(KG_OUT_TYPES);// 配网开关、刀闸、地刀
        DEVICE_TYPE_LIST.addAll(BUS_TYPES);// 母线
        DEVICE_TYPE_LIST.addAll(JUNCTION_TYPES);// 电缆头
        DEVICE_TYPE_LIST.addAll(USER_POINT_TYPES);// 负荷
        DEVICE_TYPE_LIST.addAll(POLE_TYPES);// 杆塔
        DEVICE_TYPE_LIST.addAll(SEG_FEEDER_TYPES);// 馈线段
        DEVICE_TYPE_LIST.addAll(PB_TYPES);// 配变


        // 初始化容器类型映射
        CONTAINER_TYPES.add("3");// 变电站
        CONTAINER_TYPES.add("201");// 馈线
        CONTAINER_TYPES.add("202");// 柜子
        CONTAINER_TYPES.add("203");// 组合开关

        // 初始化容器类型映射
        CONTAINER_TYPE_MAP.put("zf01", 1); // 变电站
        CONTAINER_TYPE_MAP.put("dkx", 2); // 馈线
        CONTAINER_TYPE_MAP.put("0324", 3); // 柜子
        CONTAINER_TYPE_MAP.put("zf07", 3); // 柜子
        CONTAINER_TYPE_MAP.put("0204", 3); // 柜子
        CONTAINER_TYPE_MAP.put("0323", 3); // 柜子
        CONTAINER_TYPE_MAP.put("0305", 4); // 组合开关
        CONTAINER_TYPE_MAP.put("0306", 4); // 组合开关
        CONTAINER_TYPE_MAP.put("0307", 4); // 组合开关
        CONTAINER_TYPE_MAP.put("3301", 4); // 组合开关
        CONTAINER_TYPE_MAP.put("3313", 4); // 组合开关
    }

    /**
     * 开关位置常量
     */
    public static final class SwitchPosition {
        /** 分位 */
        public static final int OPEN = 0;
        /** 合位 */
        public static final int CLOSE = 1;
    }


    /**
     * 设备类型名称映射
     */
    public static final Map<Integer, String> DEVICE_TYPE_NAME_MAP = new HashMap<>();

    static {
        DEVICE_TYPE_NAME_MAP.put(0, "主网开关");
        DEVICE_TYPE_NAME_MAP.put(1, "配网开关");
        DEVICE_TYPE_NAME_MAP.put(2, "刀闸");
        DEVICE_TYPE_NAME_MAP.put(3, "熔断器");
        DEVICE_TYPE_NAME_MAP.put(4, "母线");
        DEVICE_TYPE_NAME_MAP.put(5, "地刀");
        DEVICE_TYPE_NAME_MAP.put(6, "电缆头");
        DEVICE_TYPE_NAME_MAP.put(7, "负荷");
        DEVICE_TYPE_NAME_MAP.put(8, "杆塔");
        DEVICE_TYPE_NAME_MAP.put(9, "馈线段");
        DEVICE_TYPE_NAME_MAP.put(10, "配变");
        DEVICE_TYPE_NAME_MAP.put(11, "绕组");
    }

    /**
     * 容器类型名称映射
     */
    public static final Map<Integer, String> CONTAINER_TYPE_NAME_MAP = new HashMap<>();

    static {
        CONTAINER_TYPE_NAME_MAP.put(1, "变电站");
        CONTAINER_TYPE_NAME_MAP.put(2, "馈线");
        CONTAINER_TYPE_NAME_MAP.put(3, "柜子");
        CONTAINER_TYPE_NAME_MAP.put(4, "组合开关");
    }

    /**
     * 判断是否为容器类型
     */
    public static boolean isContainerType(Long tableNo) {
        return CONTAINER_TYPES.contains(tableNo);
    }

    /**
     * 判断是否为设备类型
     */
    public static boolean isDeviceType(String psrType) {
        return DEVICE_TYPE_LIST.contains(psrType);
    }

    /**
     * 判断是否为开关类型
     */
    public static boolean isSwitchType(Long tableNo) {
        return tableNo.equals(100L) || tableNo.equals(204L);
    }

}
