package com.ruoyi.constant;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class DeviceCurveType {
    public static final String Load = "LoadRate";    // 负载率

    public static final String ElectricCurrent_A_phs = "A_phs";    // 电流(A)

    public static final String ElectricCurrent_A_phsA = "A_phsA";    // A相电流(A)

    public static final String ElectricCurrent_A_phsB = "A_phsB";    // B相电流(A)

    public static final String ElectricCurrent_A_phsC = "A_phsC";    // C相电流(A)

    public static final String Power_TotW = "TotW"; // 有功功率

    public static final String Power_TotVar = "TotVar"; // 无功功率

    private static final Map<String, String> DEVICE_TYPE_MAP = new HashMap<>();

    static {
        // 初始化映射关系
        DEVICE_TYPE_MAP.put("负载率(%)", Load);
        DEVICE_TYPE_MAP.put("电流(A)", ElectricCurrent_A_phs);
        DEVICE_TYPE_MAP.put("A相电流(A)", ElectricCurrent_A_phsA);
        DEVICE_TYPE_MAP.put("B相电流(A)", ElectricCurrent_A_phsB);
        DEVICE_TYPE_MAP.put("C相电流(A)", ElectricCurrent_A_phsC);
        DEVICE_TYPE_MAP.put("有功功率(MW)", Power_TotW);
        DEVICE_TYPE_MAP.put("无功功率(MVar)", Power_TotVar);

    }

    /**
     * 根据设备类型编码获取设备曲线类型名称
     *
     * @param deviceTypeCode 设备类型编码
     * @return 设备类型名称，如果未找到则返回null
     */
    public static String getDeviceCurveType(String deviceTypeCode) {
        return DEVICE_TYPE_MAP.get(deviceTypeCode);
    }
}
