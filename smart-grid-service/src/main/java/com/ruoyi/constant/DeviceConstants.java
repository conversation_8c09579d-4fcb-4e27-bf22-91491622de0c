package com.ruoyi.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 设备一般的常量集合
 */
public final class DeviceConstants {

    /**
     * 站外开关类型集合
     * [直流隔离开关, 直流低压开关, 直流低压隔离开关, 低压柱上开关, 低压柱上隔离开关, 配电柱上负荷开关,
     * 配电柱上隔离开关,直流断路器,IGBT直流断路器,配电柱上断路器]
     */
    public static final List<String> KG_OUT_TYPES = Arrays.asList("2108", "2901", "2913", "3103", "3104", "0112", "0113", "2107", "2128", "0111");


    /**
     * 站内开关类型集合
     * [低压站内开关,低压站内隔离开关,变电站内负荷开关,变电站内隔离开关,变电站内断路器]
     */
    public static final List<String> KG_IN_TYPES = Arrays.asList("3301", "3313", "0307", "0306", "0305");


    /**
     * 站外开关类型集合
     */
    public static final List<String> KG_TYPES = Stream.concat(KG_IN_TYPES.stream(), KG_OUT_TYPES.stream()).collect(Collectors.toList());

    /**
     * 母线
     */
    public static final List<String> BUS_TYPES = Collections.singletonList("0311");

    /**
     * 配变
     */
    public static final List<String> PB_TYPES = Arrays.asList("0302", "0303", "0110");

    /**
     * 中压用户接入点
     */
    public static final List<String> USER_POINT_TYPES = Collections.singletonList("370000");

    /**
     * 电缆中间头 0202（输电电缆终端头）、0203（输电电缆中间接头）（坐标
     */
    public static final List<String> JUNCTION_TYPES = Arrays.asList("0202", "0203");

    /**
     * 杆塔
     */
    public static final List<String> POLE_TYPES = Arrays.asList("0103", "wlgt");

    /**
     * 环网柜 "0324",
     */
    public static final List<String> HWG_TYPES = Arrays.asList("zf07", "0324");

    /**
     * TODO "输电电缆分支箱0204","配电箱变0323" 不是环网柜 不能放里面 有问题滴
     */
    public static final List<String> XIANGBIAN_TYPES = Arrays.asList("0204", "0323");

    /**
     * 开闭所  【开关站】
     */
    public static final List<String> KGZ_TYPES = Collections.singletonList("zf04");

    /**
     * 具备线路联络的站房类型
     */
    public static final List<String> CONTACT_STATION_TYPES = Stream.concat(DeviceConstants.HWG_TYPES.stream(), DeviceConstants.KGZ_TYPES.stream()).collect(Collectors.toList());

    /**
     * 导线段
     */
    public static final List<String> SEG_FEEDER_TYPES = Arrays.asList("dxd", "0201", "0101");

    /**
     * 非真正的设备类型
     */
    public static final List<String> VIRTUAL_TYPES = Arrays.asList("13000000", "36000000", "32000000");
}
