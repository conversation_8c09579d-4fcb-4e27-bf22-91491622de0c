package com.ruoyi.constant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 设备一般的常量集合
 */
public final class NodeConstants {

    /**
     * 前端设备渲染key 柱上负荷开关
     */
    public static final String SHAPE_KEY_POLE_LOAD_KG = "poleLoadKg";

    // 前端设备渲染key 物理杆塔
    public static final String SHAPE_KEY_WLGT = "wlgt";

    // 前端渲染key 架空线
    public static final String SHAPE_KEY_FEEDER_JK = "feederJk";

    // 连接线
    public static final String SHAPE_KEY_LINK_LINE = "linkLine";

    // 电缆线
    public static final String SHAPE_KEY_FEEDER_DL = "feederDl";

    // 直线线型
    public static final String LINE_TYPE_LINEAR = "lineLinear";

    // 电网自身的
    public static final String SHAPE_KEY_PSR = "shapePsrKey";

    //站房母线
    public static final String SHAPE_KEY_BUS_FEEDER = "busFeeder";
    public static final String SHAPE_KEY_LINE_TYPE_BUS = "busLine";

    //环网柜
    public static final String SHAPE_KEY_HWG = "hwg";
    public static final String SHAPE_KEY_STATION_LOAD_KG = "stationLoadKg";
}
