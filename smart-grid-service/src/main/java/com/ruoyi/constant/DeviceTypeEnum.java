package com.ruoyi.constant;

/**
 * 设备类型枚举
 */
public enum DeviceTypeEnum {
    
    // 节点类型
    NODE("NODE", "节点"),
    
    // 配网设备类型
    DMS_BREAKER("DMS_BREAKER", "配网开关"),
    DMS_BUSBAR("DMS_BUSBAR", "配网母线"),
    
    // 主网设备类型
    EMS_BREAKER("EMS_BREAKER", "主网开关"),
    
    // 线路段类型
    SEGMENT("SEGMENT", "线路段"),
    
    // 配变类型
    TRANSFORMER("TRANSFORMER", "配变"),
    // 负荷
    LOAD("LOAD", "负荷"),
    // 配网刀闸
    DMS_DISCONNECTOR("DMS_DISCONNECTOR", "配网刀闸");
    
    private final String code;
    private final String desc;
    
    DeviceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static DeviceTypeEnum getByCode(String code) {
        for (DeviceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为有效的设备类型
     */
    public static boolean isValidType(String code) {
        return getByCode(code) != null;
    }
}