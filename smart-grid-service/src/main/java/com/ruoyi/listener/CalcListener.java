package com.ruoyi.listener;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.entity.calc.SimPfRetNew;
import com.ruoyi.mapper.calc.TrendCalcMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 定时计算监听类
 */
@Slf4j
public class CalcListener extends TimerTask {
    private Timer timer;
    private final TrendCalcMapper trendCalcMapper;
    private final List<SimPfRetNew> simPfRetList;
    private final Map<Long, SimPfRetNew> taskList;
    private final Consumer<List<SimPfRetNew>> consumer;

    public CalcListener(TrendCalcMapper trendCalcMapper, List<SimPfRetNew> simPfRetList, Consumer<List<SimPfRetNew>> consumer) {
        this.trendCalcMapper = trendCalcMapper;
        this.simPfRetList = simPfRetList;
        this.consumer = consumer;
        this.taskList = simPfRetList.stream().collect(Collectors.toMap(SimPfRetNew::getId, o -> o));
    }

    public void start() {
        timer = new Timer();
        // 4秒执行一次
        timer.schedule(this, 0, 4000);
    }

    @Override
    public void run() {
        // 查询计算主表数据
        Iterator<Map.Entry<Long, SimPfRetNew>> iterator = taskList.entrySet().iterator();
        while (iterator.hasNext()) {
            SimPfRetNew simPfRet = iterator.next().getValue();
            Map<String, Object> item = trendCalcMapper.queryOneByRetId(simPfRet.getId());
            if (item == null) {
                break;
            }

            SimPfRetNew pfRet = BeanUtil.toBean(item, SimPfRetNew.class);
            simPfRet.setStartDt(pfRet.getStartDt());
            simPfRet.setCreateDt(pfRet.getCreateDt());
            simPfRet.setEndDt(pfRet.getEndDt());
            simPfRet.setPfRet(pfRet.getPfRet());
            iterator.remove();
        }
        log.info("潮流计算进度：{}/{}", simPfRetList.size() - taskList.size(), simPfRetList.size());
        if (taskList.isEmpty()) {
            timer.cancel();
            consumer.accept(simPfRetList);
        }
    }
}
