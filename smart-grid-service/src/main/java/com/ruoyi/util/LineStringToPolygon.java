package com.ruoyi.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LineStringToPolygon {

    /**
     * 将多个LineString合并为一个Polygon的外围坐标
     * @param lineStrings 由多个LineString组成的列表，每个LineString是double[]点的列表
     * @return Polygon的外围坐标集合（List<List<Double>>格式）
     */
    public static List<List<Double>> convertToPolygon(List<List<double[]>> lineStrings) {
        if (lineStrings == null || lineStrings.isEmpty()) {
            throw new IllegalArgumentException("输入LineString列表不能为空");
        }

        // 1. 收集所有点
        List<double[]> allPoints = new ArrayList<>();
        for (List<double[]> line : lineStrings) {
            allPoints.addAll(line);
        }

        // 2. 计算凸包（外围点）
        List<double[]> hull = computeConvexHull(allPoints);

        // 3. 确保多边形闭合（首尾点相同）
        if (hull.size() > 1 && !Arrays.equals(hull.get(0), hull.get(hull.size() - 1))) {
            hull.add(Arrays.copyOf(hull.get(0), 2));
        }

        // 4. 转换为List<List<Double>>格式
        return convertToDoubleList(hull);
    }

    /**
     * 计算点集的凸包（Andrew's monotone chain算法）
     */
    private static List<double[]> computeConvexHull(List<double[]> points) {
        if (points.size() <= 1) return new ArrayList<>(points);

        points.sort((a, b) -> {
            if (a[0] != b[0]) return Double.compare(a[0], b[0]);
            return Double.compare(a[1], b[1]);
        });

        List<double[]> hull = new ArrayList<>();

        // 构建下凸包
        for (double[] p : points) {
            while (hull.size() >= 2 && cross(hull.get(hull.size() - 2), hull.get(hull.size() - 1), p) <= 0) {
                hull.remove(hull.size() - 1);
            }
            hull.add(p);
        }

        // 构建上凸包
        int t = hull.size() + 1;
        for (int i = points.size() - 1; i >= 0; i--) {
            double[] p = points.get(i);
            while (hull.size() >= t && cross(hull.get(hull.size() - 2), hull.get(hull.size() - 1), p) <= 0) {
                hull.remove(hull.size() - 1);
            }
            hull.add(p);
        }

        hull.remove(hull.size() - 1);
        return hull;
    }

    /**
     * 计算向量叉积
     */
    private static double cross(double[] a, double[] b, double[] c) {
        return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);
    }

    /**
     * 将double[]列表转换为List<List<Double>>
     */
    private static List<List<Double>> convertToDoubleList(List<double[]> points) {
        List<List<Double>> result = new ArrayList<>();
        for (double[] point : points) {
            List<Double> coord = new ArrayList<>();
            coord.add(point[0]);
            coord.add(point[1]);
            result.add(coord);
        }
        return result;
    }
}
