package com.ruoyi.util;

import java.util.ArrayList;
import java.util.List;

public class PolygonExpander {

    // 地球半径(米)
    private static final double EARTH_RADIUS = 6378137.0;

    /**
     * 将多边形向外围扩大指定米数
     * @param polygon 原始多边形坐标，格式：[[x1,y1], [x2,y2], ...] (x:经度, y:纬度)
     * @param meters 扩大的米数
     * @return 扩大后的多边形坐标，格式同上
     */
    public static List<List<Double>> expandPolygon(List<List<Double>> polygon, double meters) {
        List<List<Double>> expandedPolygon = new ArrayList<>();

        // 校验输入
        if (polygon == null || polygon.size() < 3 || meters <= 0) {
            return expandedPolygon;
        }

        // 计算多边形中心点
        List<Double> center = calculateCenter(polygon);
        double centerLng = center.get(0);
        double centerLat = center.get(1);

        // 计算每个点向外扩展后的坐标
        for (List<Double> point : polygon) {
            double lng = point.get(0);
            double lat = point.get(1);

            // 计算当前点与中心点的方向
            double bearing = calculateBearing(centerLat, centerLng, lat, lng);

            // 从原始点向远离中心的方向再移动指定米数
            List<Double> newPoint = movePoint(lat, lng, bearing, meters);
            expandedPolygon.add(newPoint);
        }

        return expandedPolygon;
    }

    // 计算多边形中心点
    private static List<Double> calculateCenter(List<List<Double>> polygon) {
        double sumLng = 0;
        double sumLat = 0;
        int count = polygon.size();

        for (List<Double> point : polygon) {
            sumLng += point.get(0);
            sumLat += point.get(1);
        }

        List<Double> center = new ArrayList<>();
        center.add(sumLng / count);  // 平均经度
        center.add(sumLat / count);  // 平均纬度
        return center;
    }

    // 计算两点之间的方位角(度)
    private static double calculateBearing(double lat1, double lon1, double lat2, double lon2) {
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLon = Math.toRadians(lon2 - lon1);

        double y = Math.sin(deltaLon) * Math.cos(lat2Rad);
        double x = Math.cos(lat1Rad) * Math.sin(lat2Rad)
                - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLon);

        double bearing = Math.atan2(y, x);
        return (Math.toDegrees(bearing) + 360) % 360;  // 转换为0-360度
    }

    // 将点沿指定方向移动指定距离(米)
    private static List<Double> movePoint(double lat, double lon, double bearing, double distance) {
        double latRad = Math.toRadians(lat);
        double lonRad = Math.toRadians(lon);
        double bearingRad = Math.toRadians(bearing);

        double angularDistance = distance / EARTH_RADIUS;

        // 计算新纬度
        double newLatRad = Math.asin(Math.sin(latRad) * Math.cos(angularDistance)
                + Math.cos(latRad) * Math.sin(angularDistance) * Math.cos(bearingRad));

        // 计算新经度
        double newLonRad = lonRad + Math.atan2(Math.sin(bearingRad) * Math.sin(angularDistance) * Math.cos(latRad),
                Math.cos(angularDistance) - Math.sin(latRad) * Math.sin(newLatRad));

        // 转换为度并调整到合适范围
        double newLat = Math.toDegrees(newLatRad);
        double newLon = (Math.toDegrees(newLonRad) + 540) % 360 - 180;

        List<Double> newPoint = new ArrayList<>();
        newPoint.add(newLon);
        newPoint.add(newLat);
        return newPoint;
    }

}
