package com.ruoyi.util;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceNtHighTransformer;
import com.ruoyi.entity.power.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

/**
 * 将负载率或者功率进行修正,以便批量导入
 */
public class Insert {
    public static void test(List<DeviceNtHighTransformer> ntHighTransformerList,
                            List<DeviceFeeder> feederDeviceList,
                            Map<String, List<Double>> returnTransformerLoadMap,
                            Map<String, List<Double>> returnTransformerLoadMWMap,
                            Map<String, List<Double>> returnFeederLoadMap,
                            Map<String, List<Double>> returnFeederLoadMWMap) throws ExecutionException, InterruptedException, TimeoutException {

        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(processors);

        // 2. 预分配结果List



// 3. 按天分片处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (Map.Entry<String, List<Double>> entry : returnTransformerLoadMap.entrySet()) {
            String k = entry.getKey();
            List<Double> v = entry.getValue();
            futures.add(CompletableFuture.runAsync(() -> {

                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(k);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    TransformerLoad transformerLoad = new TransformerLoad();
                    transformerLoad.setData(returnTransformerLoadMap.get(k).toString());
                    transformerLoad.setRecordingTime(parsedDate);
                    transformerLoad.setPsrId(ntHighTransformer.getPsrId());
                }


                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    TransformerLoadMW TransformerLoadMW = new TransformerLoadMW();
                    TransformerLoadMW.setData(returnTransformerLoadMWMap.get(k).toString());
                    TransformerLoadMW.setRecordingTime(parsedDate);
                    TransformerLoadMW.setPsrId(ntHighTransformer.getPsrId());
                }


            }));
        }

        returnFeederLoadMap.forEach((key, value) -> {
            futures.add(CompletableFuture.runAsync(() -> {
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date parsedDate = null;
                try {
                    parsedDate = outputFormat.parse(key);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                for (DeviceFeeder feederDevice : feederDeviceList) {
                    FeederLoad feederLoad = new FeederLoad();
                    feederLoad.setData(returnFeederLoadMap.get(key).toString());
                    feederLoad.setRecordingTime(parsedDate);
                    feederLoad.setPsrId(feederDevice.getPsrId());
                }

                for (DeviceFeeder feederDevice : feederDeviceList) {
                    FeederLoadMW feederLoadMW = new FeederLoadMW();
                    feederLoadMW.setData(returnFeederLoadMWMap.get(key).toString());
                    feederLoadMW.setRecordingTime(parsedDate);
                    feederLoadMW.setPsrId(feederDevice.getPsrId());
                }

            }));
        });

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.MINUTES); // 设置总超时时间
        executor.shutdown();
        System.out.println("结束主变线路负载率");


    }



    /**
     * 主变负载率
     */
    public static List<TransformerLoad> insertTransformerLoad(List<DeviceNtHighTransformer> ntHighTransformerList, Map<String, List<Double>> returnLoadMap) throws ExecutionException, InterruptedException, TimeoutException {
        System.out.println("开始主变线路负载率");
        // 1. 创建线程池
        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(processors);

// 2. 预分配结果List
        List<TransformerLoad> resultList = Collections.synchronizedList(new ArrayList<>());

// 3. 按天分片处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        returnLoadMap.forEach((key, value) -> {
            futures.add(CompletableFuture.runAsync(() -> {
                List<TransformerLoad> tempList = new ArrayList<>();
                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date parsedDate = null;
                    try {
                        parsedDate = outputFormat.parse(key);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    TransformerLoad transformerLoad = new TransformerLoad();
                    transformerLoad.setData(returnLoadMap.get(key).toString());
                    transformerLoad.setRecordingTime(parsedDate);
                    transformerLoad.setPsrId(ntHighTransformer.getPsrId());
                    tempList.add(transformerLoad);
                }
                resultList.addAll(tempList);
            }));
        });

// 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(5, TimeUnit.MINUTES); // 设置总超时时间
        executor.shutdown();
        System.out.println("结束主变线路负载率");
        return resultList;
    }


    /**
     * 主变功率
     */
    public static List<TransformerLoadMW> insertTransformerLoadMW(List<DeviceNtHighTransformer> ntHighTransformerList, Map<String, List<Double>> returnLoadMWMap) throws ExecutionException, InterruptedException, TimeoutException {
        System.out.println("开始主变线路攻率");
        // 1. 创建线程池
        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(processors);

// 2. 预分配结果List
        List<TransformerLoadMW> resultList = Collections.synchronizedList(new ArrayList<>());

// 3. 按天分片处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        returnLoadMWMap.forEach((key, value) -> {
            futures.add(CompletableFuture.runAsync(() -> {
                List<TransformerLoadMW> tempList = new ArrayList<>(651);
                for (DeviceNtHighTransformer ntHighTransformer : ntHighTransformerList) {
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date parsedDate = null;
                    try {
                        parsedDate = outputFormat.parse(key);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    TransformerLoadMW transformerLoadMW = new TransformerLoadMW();
                    transformerLoadMW.setData(returnLoadMWMap.get(key).toString());
                    transformerLoadMW.setRecordingTime(parsedDate);
                    transformerLoadMW.setPsrId(ntHighTransformer.getPsrId());
                    tempList.add(transformerLoadMW);
                }
                resultList.addAll(tempList);
            }));
        });
// 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(5, TimeUnit.MINUTES); // 设置总超时时间

        executor.shutdown();
        System.out.println("结束主变线路攻率");
        return resultList;
    }

    /**
     * 线路负载率
     */
    public static List<FeederLoad> insertFeederLoad(List<DeviceFeeder> feederDeviceList, Map<String, List<Double>> returnLoadMap) throws ExecutionException, InterruptedException, TimeoutException {
        System.out.println("开始线路负载率");
        // 1. 创建线程池
        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(2);

// 2. 预分配结果List
        List<FeederLoad> resultList = Collections.synchronizedList(new ArrayList<>());

// 3. 按天分片处理

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        returnLoadMap.forEach((key, value) -> {
            futures.add(CompletableFuture.runAsync(() -> {
                List<FeederLoad> tempList = new ArrayList<>(5190);
                for (DeviceFeeder feederDevice : feederDeviceList) {
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date parsedDate = null;
                    try {
                        parsedDate = outputFormat.parse(key);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    FeederLoad feederLoad = new FeederLoad();
                    feederLoad.setData(returnLoadMap.get(key).toString());
                    feederLoad.setRecordingTime(parsedDate);
                    feederLoad.setPsrId(feederDevice.getPsrId());
                    tempList.add(feederLoad);
                }
                resultList.addAll(tempList);
            }));
        });
        System.out.println("线路负载率" + futures.size());
// 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(5, TimeUnit.MINUTES); // 设置总超时时间
        executor.shutdownNow();
        System.out.println("结束线路负载率");
        return resultList;
    }


    /**
     * 线路功率
     */
    public static List<FeederLoadMW> insertFeederLoadMW(List<DeviceFeeder> feederDeviceList, Map<String, List<Double>> returnLoadMWMap) throws ExecutionException, InterruptedException, TimeoutException {
        System.out.println("开始线路功率");
        // 1. 创建线程池
        int processors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(processors);
// 2. 预分配结果List
        List<FeederLoadMW> resultList = Collections.synchronizedList(new ArrayList<>());
// 3. 按天分片处理

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        returnLoadMWMap.forEach((key, value) -> {
            futures.add(CompletableFuture.runAsync(() -> {
                List<FeederLoadMW> tempList = new ArrayList<>(5190);

                for (DeviceFeeder feederDevice : feederDeviceList) {
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date parsedDate = null;
                    try {
                        parsedDate = outputFormat.parse(key);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    FeederLoadMW feederLoadMW = new FeederLoadMW();
                    feederLoadMW.setData(returnLoadMWMap.get(key).toString());
                    feederLoadMW.setRecordingTime(parsedDate);
                    feederLoadMW.setPsrId(feederDevice.getPsrId());
                    tempList.add(feederLoadMW);
                }
                resultList.addAll(tempList);

            }));
        });
        System.out.println("线路功率" + futures.size());
// 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(5, TimeUnit.MINUTES); // 设置总超时时间
        executor.shutdownNow();
        System.out.println("结束线路功率");
        return resultList;
    }


}
