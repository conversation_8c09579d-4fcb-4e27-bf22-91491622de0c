package com.ruoyi.util;

import com.ruoyi.util.map.PathSegmentationUtil;

public class GeoDistanceCalculator {
    // 地球半径（单位：米）
    private static final double EARTH_RADIUS = 6371000;

    /**
     * 使用Haversine公式计算两点间的球面距离
     * @param lat1 第一点纬度
     * @param lon1 第一点经度
     * @param lat2 第二点纬度
     * @param lon2 第二点经度
     * @return 距离（单位：米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 将角度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // 计算纬度和经度的差值
        double dLat = lat2Rad - lat1Rad;
        double dLon = lon2Rad - lon1Rad;

        // Haversine公式
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        // 计算距离
        return EARTH_RADIUS * c;
    }

    public static void main(String[] args) {
        PathSegmentationUtil pathSegmentationUtil = new PathSegmentationUtil();
        double lat1 =  31.794086767539;
        double lon1 = 118.5748355357;
        double lat2 = 31.794242812889;
        double lon2 = 118.57488253887;

        double distance = calculateDistance(lat1, lon1, lat2, lon2);
        System.out.printf("两点间的距离: %.2f 米 (约 %.2f 公里)%n", distance, distance/1000);
    }
}
