package com.ruoyi.util;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

// 自定义比较器接口，定义元素匹配规则

public class ListUtils {
    /**
     * 对 List 去重，根据对象的某个属性（如 ID）保持唯一性
     *
     * @param list         原始列表
     * @param keyExtractor 提取对象唯一标识的函数（如 Node::getId）
     * @return 去重后的列表（保持原始顺序）
     */
    public static <T, K> List<T> distinctByKey(List<T> list, Function<? super T, ? extends K> keyExtractor) {
        LinkedHashMap<K, T> map = new LinkedHashMap<>();
        list.forEach(item -> map.put(keyExtractor.apply(item), item));
        return new ArrayList<>(map.values());
    }

    /**
     * 自定义规则去重并修改原列表
     *
     * @param list         待去重的列表（会被直接修改）
     * @param keyExtractor 提取元素唯一标识的函数（如 Person::getId）
     * @param <T>          元素类型
     */
    public static <T> void distinctInPlace(List<T> list, Function<? super T, ?> keyExtractor) {
        if (list == null || list.isEmpty()) return;

        Set<Object> seen = new HashSet<>();
        // 倒序遍历，避免删除元素后索引错乱
        for (int i = list.size() - 1; i >= 0; i--) {
            T item = list.get(i);
            Object key = keyExtractor.apply(item);
            if (!seen.add(key)) {
                list.remove(i); // 删除重复元素
            }
        }
    }

    /**
     * 基于自定义比较逻辑对集合去重，保留第一个出现的元素
     *
     * @param source     源集合
     * @param comparator 自定义比较器，返回true表示两个元素视为重复
     * @param <T>        元素类型
     * @return 去重后的集合
     */
    public static <T> List<T> distinctByCustom(List<T> source, BiPredicate<T, T> comparator) {
        // 校验输入
        if (source == null || source.isEmpty() || comparator == null) {
            return new ArrayList<>(source);
        }

        List<T> result = new ArrayList<>();
        for (T element : source) {
            boolean isDuplicate = false;
            // 与结果集中已存在的元素比较
            for (T existing : result) {
                if (comparator.test(existing, element)) {
                    isDuplicate = true;
                    break; // 找到重复元素，跳出循环
                }
            }
            // 非重复元素则加入结果集
            if (!isDuplicate) {
                result.add(element);
            }
        }
        return result;
    }

    /**
     * 提取对象列表中指定字段的值并去重
     *
     * @param list           源对象列表
     * @param fieldExtractor 字段提取器（通过方法引用指定字段）
     * @param <T>            对象类型
     * @param <F>            字段类型
     * @return 去重后的字段值列表
     */
    public static <T, F> List<F> extractDistinctField(List<T> list, Function<T, F> fieldExtractor) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        // 提取字段 -> 去重 -> 收集为列表
        return list.stream()
                .map(fieldExtractor)       // 提取字段
                .distinct()               // 去重（基于equals）
                .collect(Collectors.toList());
    }

    /**
     * 通用的列表查找方法，返回符合条件的所有元素
     *
     * @param list      待查找的列表
     * @param condition 查找条件，使用 Java 8 的 Predicate 函数式接口
     * @param <T>       列表元素的类型
     * @return 符合条件的元素列表，如果没有找到则返回空列表
     */
    public static <T> List<T> findByCondition(List<T> list, Predicate<T> condition) {
        if (list == null) {
            return new ArrayList<>();
        }

        List<T> result = new ArrayList<>();
        for (T item : list) {
            if (item != null && condition.test(item)) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 查找符合条件的第一个元素
     *
     * @param list      待查找的列表
     * @param condition 查找条件
     * @param <T>       列表元素的类型
     * @return 符合条件的第一个元素，如果没有找到则返回 null
     */
    public static <T> T findFirst(List<T> list, Predicate<T> condition) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        for (T item : list) {
            if (item != null && condition.test(item)) {
                return item;
            }
        }
        return null;
    }


    /**
     * 查找符合条件的第最后一个元素
     *
     * @param list      待查找的列表
     * @param condition 查找条件
     * @param <T>       列表元素的类型
     * @return 符合条件的最后一个元素，如果没有找到则返回 null
     */
    public static <T> T findLast(List<T> list, Predicate<T> condition) {
        // 处理空列表或null
        if (list == null || list.isEmpty()) {
            return null;
        }
        // 从后向前遍历，找到第一个满足条件的元素
        for (int i = list.size() - 1; i >= 0; i--) {
            T element = list.get(i);
            if (element != null && condition.test(element)) {
                return element; // 找到后立即返回
            }
        }
        return null;
    }

    /**
     * 提取二维列表中长度最小的所有子列表
     *
     * @param lists 输入的二维列表
     * @param <T>   列表元素类型
     * @return 长度最小的子列表集合
     */
    public static <T> List<List<T>> extractMinLengthLists(List<List<T>> lists) {
        List<List<T>> result = new ArrayList<>();
        if (lists == null || lists.isEmpty()) {
            return result; // 处理空输入
        }

        int minLength = Integer.MAX_VALUE;
        for (List<T> list : lists) {
            if (list == null) continue; // 跳过 null 子列表
            int currentLength = list.size();

            if (currentLength < minLength) {
                minLength = currentLength;
                result.clear(); // 发现更小长度，清空之前的结果
                result.add(list);
            } else if (currentLength == minLength) {
                result.add(list); // 相同最小长度，添加到结果
            }
        }
        return result;
    }

    /**
     * 提取二维列表中长度最小的所有子列表
     *
     * @param lists 输入的二维列表
     * @param <T>   列表元素类型
     * @return 长度最小的子列表集合
     */
    public static <T> List<T> extractMinLengthLists(List<T> lists, Function<T, Integer> getSizeFunc) {
        List<T> result = new ArrayList<>();
        if (lists == null || lists.isEmpty()) {
            return result; // 处理空输入
        }
        int minLength = Integer.MAX_VALUE;
        for (T data : lists) {
            if (data == null) continue; // 跳过 null 子列表

            int currentLength = getSizeFunc.apply(data);

            if (currentLength < minLength) {
                minLength = currentLength;
                result.clear(); // 发现更小长度，清空之前的结果
                result.add(data);
            } else if (currentLength == minLength) {
                result.add(data); // 相同最小长度，添加到结果
            }
        }
        return result;
    }

    /**
     * 检查sourceList中的元素是否在targetList中存在（使用自定义对比规则）
     *
     * @param sourceList 源集合
     * @param targetList 目标集合
     * @param comparator 内置函数式接口，定义对比规则
     * @return 是否存在匹配元素
     */
    public static <T> boolean hasMatchingData(
            List<T> sourceList,
            List<T> targetList,
            BiPredicate<T, T> comparator) {

        // 处理空集合或空比较器的情况
        if (sourceList == null || sourceList.isEmpty()
                || targetList == null || targetList.isEmpty()
                || comparator == null) {
            return false;
        }

        // 遍历两个List，使用自定义对比规则判断
        for (T sourceElem : sourceList) {
            for (T targetElem : targetList) {
                if (comparator.test(sourceElem, targetElem)) {
                    return true; // 找到匹配元素
                }
            }
        }
        return false; // 无匹配元素
    }

}
