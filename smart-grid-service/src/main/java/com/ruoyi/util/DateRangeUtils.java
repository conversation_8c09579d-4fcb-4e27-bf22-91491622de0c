package com.ruoyi.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * 计算环比同比日期
 */
public class DateRangeUtils {

    private static final DateTimeFormatter FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 计算环比时间范围（上个月同期）
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return String数组 [环比开始时间, 环比结束时间]
     */
    public static String[] getMonthOverMonthRange(String startTime, String endTime) {
        LocalDateTime start = parseDateTime(startTime);
        LocalDateTime end = parseDateTime(endTime);

        LocalDateTime momStart = start.minusMonths(1);
        LocalDateTime momEnd = end.minusMonths(1);

        // 处理月末边界（如3月31日 → 2月可能只有28天）
        momEnd = adjustEndDate(momStart, momEnd);

        return formatDateTimes(momStart, momEnd);
    }

    /**
     * 计算同比时间范围（去年同期）
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return String数组 [同比开始时间, 同比结束时间]
     */
    public static String[] getYearOverYearRange(String startTime, String endTime) {
        LocalDateTime start = parseDateTime(startTime);
        LocalDateTime end = parseDateTime(endTime);

        LocalDateTime yoyStart = start.minusYears(1);
        LocalDateTime yoyEnd = end.minusYears(1);

        // 处理闰年2月（如2020-02-29 → 2019-02-28）
        yoyEnd = adjustEndDate(yoyStart, yoyEnd);

        return formatDateTimes(yoyStart, yoyEnd);
    }

    // 解析字符串为 LocalDateTime
    private static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, FORMATTER);
    }

    // 处理结束时间的边界问题（避免跨月/闰年）
    private static LocalDateTime adjustEndDate(LocalDateTime start, LocalDateTime end) {
        YearMonth startYearMonth = YearMonth.from(start);
        LocalDate endDate = end.toLocalDate();

        if (endDate.isAfter(startYearMonth.atEndOfMonth())) {
            return end.with(TemporalAdjusters.lastDayOfMonth());
        }
        return end;
    }

    // 格式化 LocalDateTime 为字符串
    private static String[] formatDateTimes(LocalDateTime start, LocalDateTime end) {
        return new String[] {
            start.format(FORMATTER),
            end.format(FORMATTER)
        };
    }
}
