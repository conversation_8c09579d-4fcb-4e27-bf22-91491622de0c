package com.ruoyi.util;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineSegment;

public class JTSAngleCalculator {

    private static final GeometryFactory factory = new GeometryFactory();

    /**
     * 计算三个坐标点形成的夹角（度）
     * @param p1 第一个点
     * @param p2 中间点（角的顶点）
     * @param p3 第三个点
     * @return 夹角（度），范围 [0, 180]
     */
    public static double calculateAngle(Coordinate p1, Coordinate p2, Coordinate p3) {
        // 创建从 p2 到 p1 和 p2 到 p3 的向量
        LineSegment segment1 = new LineSegment(p2, p1);
        LineSegment segment2 = new LineSegment(p2, p3);

        // 计算向量的方位角（弧度）
        double angle1 = segment1.angle();
        double angle2 = segment2.angle();

        // 计算夹角（弧度）
        double angleDiff = Math.abs(angle1 - angle2);

        // 确保角度在 [0, π] 范围内
        double angle = angleDiff > Math.PI ? 2 * Math.PI - angleDiff : angleDiff;

        // 转换为角度（度）
        return Math.toDegrees(angle);
    }

    /**
     * 计算有方向的夹角（度）
     * @param p1 第一个点
     * @param p2 中间点（角的顶点）
     * @param p3 第三个点
     * @return 带方向的夹角（度），范围 [-180, 180]，负值表示顺时针
     */
    public static double calculateDirectedAngle(Coordinate p1, Coordinate p2, Coordinate p3) {
        // 创建向量
        Coordinate v1 = new Coordinate(p1.x - p2.x, p1.y - p2.y);
        Coordinate v2 = new Coordinate(p3.x - p2.x, p3.y - p2.y);

        // 计算叉积（确定方向）
        double crossProduct = v1.x * v2.y - v1.y * v2.x;

        // 计算点积
        double dotProduct = v1.x * v2.x + v1.y * v2.y;

        // 计算带符号的角度（弧度）
        double angle = Math.atan2(crossProduct, dotProduct);

        // 转换为角度（度）
        return Math.toDegrees(angle);
    }
}
