package com.ruoyi.util.map;

import com.graphhopper.util.shapes.GHPoint;
import org.locationtech.jts.geom.Coordinate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.ruoyi.util.JTSAngleCalculator.calculateAngle;

/**
 * 杆塔坐标生成器 - 根据经纬度坐标集合生成符合规则的杆塔坐标
 * 规则：
 * 1. 起点和终点默认是杆塔
 * 2. 从起点开始，如果拐弯角度大于100°，每隔50米建立一个杆塔
 * 3. 如果拐弯角度小于等于100°，则在拐弯处建立杆塔
 * 4. 每次建立杆塔后，起点更新为新建的杆塔
 */
public class TowerCoordinateGenerator {

    // 地球半径(km)
    private static final double EARTH_RADIUS = 6371.0;
    // 分段距离阈值(米)
    private static final double SEGMENT_DISTANCE = 50.0;
    // 拐弯角度阈值(度)
    private static final double ANGLE_THRESHOLD = 170;

    /**
     * 主方法：生成杆塔坐标
     *
     * @param originalPoints 原始经纬度坐标点集合
     * @return 符合规则的杆塔坐标集合
     */
    public static List<GHPoint> generateTowerCoordinates(List<GHPoint> originalPoints) {
        if (originalPoints == null || originalPoints.size() < 2) {
            return Collections.emptyList();
        }

        List<GHPoint> towerCoordinates = new ArrayList<>();
        // 添加起点作为第一个杆塔
        towerCoordinates.add(originalPoints.get(0));

        GHPoint currentStart = originalPoints.get(0);
        int currentPathIndex = 0;

        double total = 0.0;

        while (currentPathIndex < originalPoints.size() - 1) {
            GHPoint nextPoint = originalPoints.get(currentPathIndex + 1);
            double distance = calculateDistance(currentStart, nextPoint);

            // 如果当前起点到下一个点的距离小于等于分段距离，直接跳到下一个点
            if (total <= SEGMENT_DISTANCE) {
                total =   distance+total;
            }

            // 计算拐弯角度
            double angle = 180.0; // 默认180度（直线）
            if (currentPathIndex < originalPoints.size() - 2) {
                GHPoint nextNextPoint = originalPoints.get(currentPathIndex + 2);

                angle = calculateAngle(new Coordinate(currentStart.getLon(),currentStart.getLat()), new Coordinate(nextPoint.getLon(),nextPoint.getLat()), new Coordinate(nextNextPoint.getLon(),nextNextPoint.getLat()));
            }

            // 如果拐弯角度小于等于阈值，在下一个点建立杆塔
            if (angle <= ANGLE_THRESHOLD) {
                towerCoordinates.add(nextPoint);
                currentStart = nextPoint;
                currentPathIndex++;
            } else {
                // 否则，每隔指定距离建立杆塔
                int numSegments = (int) (distance / SEGMENT_DISTANCE);
                for (int i = 1; i <= numSegments; i++) {
                    double ratio = (double) i * SEGMENT_DISTANCE / distance;
                    GHPoint tower = interpolateCoordinate(currentStart, nextPoint, ratio);
                    towerCoordinates.add(tower);
                }


               // 几乎为0，跳到下一个点
                currentStart = nextPoint;
                currentPathIndex++;

            }
        }

        // 确保终点被添加为杆塔（如果还没有）
        GHPoint endPoint = originalPoints.get(originalPoints.size() - 1);
        if (!towerCoordinates.get(towerCoordinates.size() - 1).equals(endPoint)) {
            towerCoordinates.add(endPoint);
        }

        return towerCoordinates;
    }

    /**
     * 计算两点间的球面距离（Haversine公式）
     */
    private static double calculateDistance(GHPoint p1, GHPoint p2) {
        double lat1 = Math.toRadians(p1.getLat());
        double lon1 = Math.toRadians(p1.getLon());
        double lat2 = Math.toRadians(p2.getLat());
        double lon2 = Math.toRadians(p2.getLon());

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c * 1000; // 转换为米
    }

    /**
     * 计算从点1到点2的方位角（Bearing）
     *
     * @param point1 起点 [lon, lat]
     * @param point2 终点 [lon, lat]
     * @return 方位角（度数，0°~360°）
     */
//    public static double calculateBearing(double[] point1, double[] point2) {
//        double lon1 = Math.toRadians(point1[0]);
//        double lat1 = Math.toRadians(point1[1]);
//        double lon2 = Math.toRadians(point2[0]);
//        double lat2 = Math.toRadians(point2[1]);
//
//        double y = Math.sin(lon2 - lon1) * Math.cos(lat2);
//        double x = Math.cos(lat1) * Math.sin(lat2) -
//                Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
//
//        double bearing = Math.toDegrees(Math.atan2(y, x));
//        return (bearing + 360) % 360; // 转换为 0°~360°
//    }


    /**
     * 计算三点形成的夹角（度）
     */
    private static double calculateAngle1() {


        // 计算向量
//        double dx1 = p1.getLon() - p2.getLon();
//        double dy1 = p1.getLat() - p2.getLat();
//
//        double dx2 = p3.getLon() - p2.getLon();
//        double dy2 = p3.getLat() - p2.getLat();

        double dx1 =
                118.7628111825 - 118.76312221659;
        double dy1 =
                32.077574190434 - 32.077305069735;

        double dx2 = 118.76324022894 - 118.76312221659;
        double dy2 =
                32.077220027493 - 32.077305069735;

        // 计算点积
        double dotProduct = dx1 * dx2 + dy1 * dy2;

        // 计算叉积（用于判断方向）
        double crossProduct = dx1 * dy2 - dy1 * dx2;

        // 计算模长
        double len1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
        double len2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);

        // 防止除零错误
        if (len1 == 0 || len2 == 0) {
            return 180.0;
        }

        // 计算余弦值
        double cosTheta = dotProduct / (len1 * len2);
        cosTheta = Math.max(-1.0, Math.min(1.0, cosTheta)); // 确保在有效范围内

        // 计算角度（弧度转度）
        double angle = Math.toDegrees(Math.acos(cosTheta));

        return angle;
    }


    /**
     * 在线段上插值生成新坐标点
     */
    private static GHPoint interpolateCoordinate(GHPoint p1, GHPoint p2, double ratio) {
        double lat = p1.getLat() + ratio * (p2.getLat() - p1.getLat());
        double lon = p1.getLon() + ratio * (p2.getLon() - p1.getLon());
        return new GHPoint(lat, lon);
    }


}
