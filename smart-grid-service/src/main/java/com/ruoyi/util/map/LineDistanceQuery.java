package com.ruoyi.util.map;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.operation.distance.DistanceOp;
import org.locationtech.jts.util.GeometricShapeFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class LineDistanceQuery {
    private static final String WGS84_SRID = "EPSG:4326"; // 假设坐标系为 WGS84 经纬度


    // 封装线路及其标记的类
    static class Route {
        @Getter
        @Setter
        private final String label;
        @Getter
        @Setter
        private final MultiLineString geometry;
        @Getter
        @Setter
        private DeviceFeeder feeder;
        @Getter
        @Setter
        private NeedFeederVo needFeederVo;
        @Getter
        @Setter
        private Double distance;

        public Route(String label, MultiLineString geometry, DeviceFeeder feeder, NeedFeederVo needFeederVo, Double distance) {
            this.label = label;
            this.geometry = geometry;
            this.feeder = feeder;
            this.needFeederVo = needFeederVo;
            this.distance = distance;
        }

    }

    public static List<DeviceFeeder> selectLine(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList, List<NeedFeederVo> needFeederVos) {

        // 过滤 psrid=1 的元素，并将剩下的 psrid 和 geoList 组成 Map
        Map<String, DeviceFeeder> labeledData = deviceFeederList.stream().filter(df -> !df.getPsrId().equals(feederRangeQueryBo.getPsrId())) // 过滤掉 psrid 的元素
                .filter(df -> df.getGeoList() != null && !df.getGeoList().isEmpty()).collect(Collectors.toMap(DeviceFeeder::getPsrId,      // 键：psrid
                        Function.identity(),   // 值：geoList
                        (existing, replacement) -> existing // 处理键冲突（若存在）
                ));

        // 解析数据并关联标记(待查询线路)
        List<Route> allRoutes = parseLabeledMultiLine(labeledData);

        Map<String, List<List<double[]>>> target = new HashMap<>();
        target.put(feederRangeQueryBo.getPsrId(), feederRangeQueryBo.getCoordinateList());

        List<Route> targetRoutes = parseLabeledMultiLineStringData(target);

        // 目标线路（示例：取第一条线路作为目标）
        Route targetRoute = targetRoutes.get(0);
//        System.out.println("目标线路: " + targetRoute.getLabel());

        List<DeviceFeeder> feederList = new ArrayList<>();
        if (feederRangeQueryBo.getNum().equals(-1)) {
            List<Route> routeList = findNearbyRoutes(targetRoute, allRoutes, feederRangeQueryBo.getRange());
            List<Route> nearbyRoutes = routeList.stream().collect(Collectors.toMap(Route::getLabel,        // 使用 label 作为键
                    route -> route,         // 值为 Route 本身
                    (existing, replacement) -> existing, // 保留先出现的元素
                    LinkedHashMap::new      // 使用 LinkedHashMap 保持插入顺序
            )).values().stream().collect(Collectors.toList());


            if (!nearbyRoutes.isEmpty()) {
                for (Route route : nearbyRoutes) {
                    DistanceResult distanceResult = calculateDistanceWithPoints(targetRoute.getGeometry(), route.getGeometry());
                    route.setDistance(distanceResult.getDistance());
                    route.getNeedFeederVo().setPoint1(distanceResult.getPoint1());
                    route.getNeedFeederVo().setPoint2(distanceResult.getPoint2());
                    route.getNeedFeederVo().setGeoCoordinateList(split(route.getFeeder().getGeoList()));
                    route.getNeedFeederVo().setPsrId(route.getFeeder().getPsrId());
                    route.getNeedFeederVo().setFeedName(route.getFeeder().getName());
                    route.getNeedFeederVo().setLength(route.getFeeder().getLength());
                    route.getNeedFeederVo().setSupplyArea(route.getFeeder().getSupplyArea());
                    route.getNeedFeederVo().setSupplyRadius(route.getFeeder().getSupplyRadius());
                    route.getNeedFeederVo().setFeederRateCapacity(route.getFeeder().getFeederRateCapacity());
                    route.getNeedFeederVo().setVoltageLevel(route.getFeeder().getVoltageLevel());
                    route.getNeedFeederVo().setStartStation(route.getFeeder().getStartStation());
                }
                nearbyRoutes.sort(Comparator.comparingDouble(Route::getDistance));
                feederList.addAll(nearbyRoutes.stream().map(Route::getFeeder).collect(Collectors.toList()));
                needFeederVos.addAll(nearbyRoutes.stream().map(Route::getNeedFeederVo).collect(Collectors.toList()));
                return feederList;
            }
        }
        List<Route> nearestRoute = findNearestRoutes(targetRoute, allRoutes, feederRangeQueryBo.getNum());
        if (!nearestRoute.isEmpty()) {
            for (Route route : nearestRoute) {
                DistanceResult distanceResult = calculateDistanceWithPoints(targetRoute.getGeometry(), route.getGeometry());
                route.setDistance(distanceResult.getDistance());
                route.getNeedFeederVo().setPoint1(distanceResult.getPoint1());
                route.getNeedFeederVo().setPoint2(distanceResult.getPoint2());
                route.getNeedFeederVo().setGeoCoordinateList(split(route.getFeeder().getGeoList()));
                route.getNeedFeederVo().setPsrId(route.getFeeder().getPsrId());
                route.getNeedFeederVo().setFeedName(route.getFeeder().getName());
                route.getNeedFeederVo().setLength(route.getFeeder().getLength());
                route.getNeedFeederVo().setSupplyArea(route.getFeeder().getSupplyArea());
                route.getNeedFeederVo().setSupplyRadius(route.getFeeder().getSupplyRadius());
                route.getNeedFeederVo().setFeederRateCapacity(route.getFeeder().getFeederRateCapacity());
                route.getNeedFeederVo().setVoltageLevel(route.getFeeder().getVoltageLevel());
                route.getNeedFeederVo().setStartStation(route.getFeeder().getStartStation());
                feederList.add(route.getFeeder());
                needFeederVos.add(route.getNeedFeederVo());

            }
            return feederList;
        } else {
            System.out.println("无任何线路数据");
        }
        return null;
    }


    private static List<List<double[]>> split(String input) {
        // 1. 按逗号分割为子串
        String[] lineStrings = input.split(",");

        List<List<double[]>> groupedCoordinates = new ArrayList<>();

        for (String line : lineStrings) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            // 2. 按空格分割为坐标对
            String[] points = trimmedLine.split(" ");
            List<double[]> coordinates = new ArrayList<>();

            for (int i = 0; i < points.length; i += 2) {
                if (i + 1 >= points.length) {
                    throw new IllegalArgumentException("坐标点数为奇数，无法配对: " + trimmedLine);
                }
                double x = Double.parseDouble(points[i]);
                double y = Double.parseDouble(points[i + 1]);
                coordinates.add(new double[]{x, y});
            }

            // 3. 将坐标按两个一组进行分组

            groupedCoordinates.add(coordinates);
        }
        return groupedCoordinates;
    }

    /**
     * 解析带标记的坐标数据为 Route 列表
     */
    private static List<Route> parseLabeledMultiLine(Map<String, DeviceFeeder> labeledData) {
        List<Route> routes = new ArrayList<>();
        WKTReader wktReader = new WKTReader();
        GeometryFactory factory = new GeometryFactory(new PrecisionModel(), 4326);

        for (Map.Entry<String, DeviceFeeder> entry : labeledData.entrySet()) {
            String label = entry.getKey();
            String coords = entry.getValue().getGeoList();

            try {
                // 将坐标字符串转换为 MultiLineString WKT
                String multiLineWKT = convertToMultiLineStringWKT(coords.trim());
                MultiLineString multiLine = (MultiLineString) wktReader.read(multiLineWKT);

                routes.add(new Route(label, multiLine, entry.getValue(), new NeedFeederVo(), null));
            } catch (ParseException e) {
                System.err.println("解析坐标失败 (标记: " + label + "): " + coords);
                e.printStackTrace();
            } catch (Exception e) {
                System.err.println("处理坐标时出错 (标记: " + label + "): " + e.getMessage());
                e.printStackTrace();
            }
        }
        return routes;
    }

    /**
     * 解析带标记的坐标数据为 Route 列表
     */
    private static List<Route> parseLabeledMultiLineStringData(Map<String, List<List<double[]>>> labeledData) {
        List<Route> routes = new ArrayList<>();
        WKTReader wktReader = new WKTReader();
        GeometryFactory factory = new GeometryFactory(new PrecisionModel(), 4326);

        for (Map.Entry<String, List<List<double[]>>> entry : labeledData.entrySet()) {
            String label = entry.getKey();
            List<List<double[]>> coords = entry.getValue();

            try {
                // 将坐标字符串转换为 MultiLineString WKT
                String multiLineWKT = convertToMultiLineStringWKT(coords);
                MultiLineString multiLine = (MultiLineString) wktReader.read(multiLineWKT);

                routes.add(new Route(label, multiLine, null, null, null));
            } catch (ParseException e) {
                System.err.println("解析坐标失败 (标记: " + label + "): " + coords);
                e.printStackTrace();
            } catch (Exception e) {
                System.err.println("处理坐标时出错 (标记: " + label + "): " + e.getMessage());
                e.printStackTrace();
            }
        }
        return routes;
    }

    /**
     * 将坐标字符串转换为 MultiLineString WKT 格式
     * 输入："x1 y1 x2 y2, x3 y3 x4 y4" → 输出："MULTILINESTRING ((x1 y1, x2 y2), (x3 y3, x4 y4))"
     */
    private static String convertToMultiLineStringWKT(String coords) {
        // 按逗号分割不同的线段
        String[] lineStrings = coords.split(",");

        StringBuilder sb = new StringBuilder("MULTILINESTRING (");

        for (int i = 0; i < lineStrings.length; i++) {
            String line = lineStrings[i].trim();
            if (line.isEmpty()) continue;

            String[] points = line.split(" ");
            if (points.length < 2) {
                throw new IllegalArgumentException("坐标点数不足（至少需要 2 个点）");
            }

            sb.append("(");
            for (int j = 0; j < points.length; j += 2) {
                String x = points[j];
                String y = points[j + 1];
                sb.append(x).append(" ").append(y).append(", ");
            }
            // 去除最后多余的 ", "
            sb.delete(sb.length() - 2, sb.length());
            sb.append("), ");
        }

        // 去除最后多余的 ", " 并闭合括号
        if (sb.length() > 15) { // "MULTILINESTRING (".length() == 15
            sb.delete(sb.length() - 2, sb.length());
        }
        sb.append(")");

        return sb.toString();
    }

    /**
     * 将坐标集合转换为 MultiLineString WKT 格式
     * 输入：List<List<double[]>> → 输出："MULTILINESTRING ((x1 y1, x2 y2), (x3 y3, x4 y4))"
     */
    private static String convertToMultiLineStringWKT(List<List<double[]>> geoCoordinateList) {
        if (geoCoordinateList == null || geoCoordinateList.isEmpty()) {
            return "MULTILINESTRING EMPTY";
        }

        StringBuilder sb = new StringBuilder("MULTILINESTRING (");

        for (int i = 0; i < geoCoordinateList.size(); i++) {
            List<double[]> line = geoCoordinateList.get(i);
            if (line == null || line.isEmpty()) continue;

            sb.append("(");
            for (int j = 0; j < line.size(); j++) {
                double[] point = line.get(j);
                if (point == null || point.length < 2) {
                    throw new IllegalArgumentException("坐标点格式错误（需要至少 2 个维度）");
                }
                sb.append(point[0]).append(" ").append(point[1]);
                if (j < line.size() - 1) {
                    sb.append(", ");
                }
            }
            sb.append(")");
            if (i < geoCoordinateList.size() - 1) {
                sb.append(", ");
            }
        }

        sb.append(")");
        return sb.toString();
    }

    /**
     * 查找目标线路范围内的所有线路
     */
    private static List<Route> findNearbyRoutes(Route target, List<Route> routes, Double km) {
        List<Route> result = new ArrayList<>();
        Geometry targetGeom = target.getGeometry();
        Geometry buffer = createBuffer(targetGeom, km);

        for (Route route : routes) {
            if (route == target) continue; // 排除目标线路自身
            Geometry routeGeom = route.getGeometry();
            if (buffer.intersects(routeGeom)) {
                result.add(route);
            }
        }
        return result;
    }

    /**
     * 查找最近的线路（无论距离）
     */
    private static Route findNearestRoute(Route target, List<Route> routes) {
        if (routes.isEmpty()) return null;
        Route nearest = null;
        double minDistance = Double.MAX_VALUE;

        Geometry targetGeom = target.getGeometry();
        for (Route route : routes) {
            if (route == target) continue;
            Geometry routeGeom = route.getGeometry();
            double distance = targetGeom.distance(routeGeom);

            if (distance < minDistance) {
                minDistance = distance;
                nearest = route;
            }
        }
        return nearest;
    }


    private static List<Route> findNearestRoutes(Route target, List<Route> routes, int count) {
        if (routes.isEmpty() || count <= 0) return Collections.emptyList();

        // 使用优先队列（最大堆）来维护最小的count个元素
        PriorityQueue<RouteDistance> maxHeap = new PriorityQueue<>(count, Comparator.comparingDouble(RouteDistance::getDistance).reversed());

        Geometry targetGeom = target.getGeometry();

        for (Route route : routes) {
            if (route == target) continue; // 跳过自身

            Geometry routeGeom = route.getGeometry();
            double distance = targetGeom.distance(routeGeom);

            // 如果队列未满，直接添加
            if (maxHeap.size() < count) {
                maxHeap.offer(new RouteDistance(route, distance));
            }
            // 否则比较当前距离与堆顶元素
            else if (distance < maxHeap.peek().getDistance()) {
                maxHeap.poll(); // 移除最大元素
                maxHeap.offer(new RouteDistance(route, distance)); // 添加更小的元素
            }
        }

        // 将优先队列转换为按距离升序排列的列表
        List<Route> result = maxHeap.stream().sorted(Comparator.comparingDouble(RouteDistance::getDistance)).map(RouteDistance::getRoute).collect(Collectors.toList());

        return result;
    }

    /**
     * 计算点到线路的最小距离（考虑所有线段）
     * @param point 点（经纬度）
     * @param lineSegments 线路（多个线段）
     * @return 最小距离(km)
     */
    public static double calculateMinDistanceToLine(double[] point, List<List<double[]>> lineSegments) {
        double minDistance = Double.MAX_VALUE;

        // 遍历线路的每个线段
        for (List<double[]> segment : lineSegments) {
            if (segment.size() < 2) continue;

            // 对于每个线段中的每对相邻点
            for (int i = 0; i < segment.size() - 1; i++) {
                double[] start = segment.get(i);
                double[] end = segment.get(i + 1);

                // 计算点到当前线段的最短距离
                double distance = calculateDistanceToSegment(point, start, end);
                if (distance < minDistance) {
                    minDistance = distance;
                }
            }
        }

        return minDistance;
    }

    /**
     * 计算点到线段的最短距离（考虑经纬度）
     * @param point 点
     * @param start 线段起点
     * @param end 线段终点
     * @return 最短距离(km)
     */
    public static double calculateDistanceToSegment(double[] point, double[] start, double[] end) {
        // 线段起点和终点的经纬度
        double lat1 = Math.toRadians(start[1]);
        double lon1 = Math.toRadians(start[0]);
        double lat2 = Math.toRadians(end[1]);
        double lon2 = Math.toRadians(end[0]);

        // 点的经纬度
        double lat0 = Math.toRadians(point[1]);
        double lon0 = Math.toRadians(point[0]);

        // 计算向量（线段起点到终点）
        double dx = lon2 - lon1;
        double dy = lat2 - lat1;

        // 线段长度的平方（球面近似）
        double segmentLengthSq = dx * dx + dy * dy;

        // 如果线段实际上是一个点，直接计算点到点的距离
        if (segmentLengthSq == 0) {
            return calculateHaversineDistance(point[0], point[1], start[0], start[1]);
        }

        // 计算投影比例t：将点投影到线段所在的直线上
        double t = ((lon0 - lon1) * dx + (lat0 - lat1) * dy) / segmentLengthSq;

        if (t < 0) {
            // 投影点在起点之前，最近距离是点到起点的距离
            return calculateHaversineDistance(point[0], point[1], start[0], start[1]);
        } else if (t > 1) {
            // 投影点在终点之后，最近距离是点到终点的距离
            return calculateHaversineDistance(point[0], point[1], end[0], end[1]);
        } else {
            // 投影点在线段上，计算投影点的经纬度
            double projLat = lat1 + t * dy;
            double projLon = lon1 + t * dx;

            // 计算点到投影点的距离
            return calculateHaversineDistance(point[0], point[1], Math.toDegrees(projLon), Math.toDegrees(projLat));
        }
    }


    /**
     * 计算两个经纬度点之间的球面距离（Haversine公式）
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 两点之间的距离，单位：公里
     */
    public static double calculateHaversineDistance(double lon1, double lat1, double lon2, double lat2) {
        final double R = 6371.0; // 地球半径，单位：公里

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2) + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // 返回距离，单位：公里
    }

    // 内部类：用于存储路线及其与目标的距离
    private static class RouteDistance {
        private final Route route;
        private final double distance;

        public RouteDistance(Route route, double distance) {
            this.route = route;
            this.distance = distance;
        }

        public Route getRoute() { return route; }
        public double getDistance() { return distance; }
    }

    private static DistanceResult calculateDistanceWithPoints(MultiLineString line1, MultiLineString line2) {
        DistanceOp distanceOp = new DistanceOp(line1, line2);
        double distance = distanceOp.distance();

        // 获取实现最短距离的点对
        Coordinate[] points = distanceOp.nearestPoints();
        Coordinate point1 = points[0]; // 第一个几何对象上的点
        Coordinate point2 = points[1]; // 第二个几何对象上的点

        return new DistanceResult(distance, point1, point2);
    }

    // 自定义结果类，封装距离和点对
    static class DistanceResult {
        private final double distance;
        private final Coordinate point1;
        private final Coordinate point2;

        public DistanceResult(double distance, Coordinate point1, Coordinate point2) {
            this.distance = distance;
            this.point1 = point1;
            this.point2 = point2;
        }

        public double getDistance() {
            return distance;
        }

        public Coordinate getPoint1() {
            return point1;
        }

        public Coordinate getPoint2() {
            return point2;
        }
    }


    /**
     * 创建缓冲区（单位：公里，适用于 WGS84 坐标系）
     */
    private static Geometry createBuffer(Geometry geom, double radiusKm) {
        // 修正：使用 geom.getCentroid().getCoordinate() 获取 Coordinate
        GeometricShapeFactory factory = new GeometricShapeFactory();
        factory.setNumPoints(32); // 缓冲区边数（精度）
        Coordinate centroid = geom.getCentroid().getCoordinate();
        factory.setCentre(centroid);
        factory.setSize(radiusKm * 2); // 直径

        // 将缓冲区从公里转换为度（近似转换，适用于小范围）
        // 注意：此转换为简化逻辑，实际应用需使用投影或球面距离计算
        double metersPerDegree = 111319.444; // 赤道附近 1 度 ≈ 111.32 公里
        double radiusDegrees = radiusKm / metersPerDegree * 1000;
        return geom.buffer(radiusDegrees);
    }
}
