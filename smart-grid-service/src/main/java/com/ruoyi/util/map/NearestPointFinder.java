package com.ruoyi.util.map;

import java.util.HashMap;
import java.util.Map;

public class NearestPointFinder {

    /**
     * 计算两个点之间的欧几里得距离（简化版，不考虑地球曲率）
     * @param x1 第一个点的经度
     * @param y1 第一个点的纬度
     * @param x2 第二个点的经度
     * @param y2 第二个点的纬度
     * @return 两点之间的距离
     */
    private static double calculateDistance(double x1, double y1, double x2, double y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }
    // 计算两个经纬度点之间的球面距离（更精确，适用于大范围区域）
    private static double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径（公里）
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c; // 公里
    }

    // 找到距离原始点最近的坐标点
    public static Map.Entry<String, String> findNearestPoint(double[] originalPoint, Map<String, String> pointMap, boolean useHaversine) {

        if (originalPoint == null || originalPoint.length != 2 || pointMap == null || pointMap.isEmpty()) {
            return null;
        }

        double minDistance = Double.MAX_VALUE;
        Map.Entry<String, String> nearestEntry = null;

        for (Map.Entry<String, String> entry : pointMap.entrySet()) {
            try {
                // 解析坐标字符串
                String[] coords = entry.getValue().split(",");
                double lat = Double.parseDouble(coords[0].trim());
                double lon = Double.parseDouble(coords[1].trim());

                // 计算距离
                double distance = useHaversine
                        ? calculateHaversineDistance(originalPoint[0], originalPoint[1], lat, lon)
                        : calculateDistance(originalPoint[0], originalPoint[1], lat, lon);

                // 更新最近点
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEntry = entry;
                }
            } catch (NumberFormatException e) {
                // 忽略解析失败的坐标
                System.err.println("Error parsing coordinates: " + entry.getValue());
            }
        }

        return nearestEntry;
    }

    // 使用示例
    public static void main(String[] args) {
        double[] originalPoint = {32.1832, 118.7000}; // 原始点（纬度, 经度）

        Map<String, String> pointMap = new HashMap<>();
        pointMap.put("point1", "32.1833 118.7001");
        pointMap.put("point2", "32.1840 118.7010");
        pointMap.put("point3", "32.1820 118.6995");

        // 使用球面距离计算
        Map.Entry<String, String> nearest = findNearestPoint(originalPoint, pointMap, true);

        if (nearest != null) {
            System.out.println("最近点ID: " + nearest.getKey());
            System.out.println("最近点坐标: " + nearest.getValue());
        } else {
            System.out.println("未找到有效坐标点");
        }
    }


}
