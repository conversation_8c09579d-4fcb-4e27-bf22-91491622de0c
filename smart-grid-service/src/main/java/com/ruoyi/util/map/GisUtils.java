package com.ruoyi.util.map;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class GisUtils {
    private static String token = null;
    private static String baseUrl = "http://21.47.224.119:21100";
    private static String appKey = "bba2a794cf6944228a129647021072d0";
    private static String secretkey = "84e7c1a8-95da-4a77-8a44-f73873a04dfe";

    public String commonGis(Map<String, String> parm) {
        getToken();
        Map<String, Object> params = new HashMap<>();
        params.put("origin", parm.get("origin"));
        params.put("destination", parm.get("destination"));
        params.put("isSgCoordinateRequest", "true");
        params.put("isSgCoordinateRespone", "true");
        String strategy = parm.get("strategy");
        if (StringUtils.isEmpty(strategy)) {
            params.put("strategy", "10");
        } else {
            params.put("strategy", strategy);
        }
        String jsonParams = JSON.toJSONString(params);
        Map<String, String> headers = new HashMap<>(4);
        headers = getHttpHeader(headers);
        HttpResponse httpResponse = null;

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(URLEncodeUtil.encode(entry.getValue().toString()));
        }
        try {
            String url = baseUrl + "/mapservice/direction/walking";
            String fullUrl = url + "?" + sb.toString();
            httpResponse = HttpRequest.get(fullUrl).headerMap(headers, true).form(jsonParams).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!httpResponse.isOk()) {
            log.info("请求失败，状态码：{}, 响应内容：{}", httpResponse.getStatus(), httpResponse.body());
            return null;
        }
        String responseBody = httpResponse.body();
        JSONObject result = JSON.parseObject(responseBody);
        if (result.getInteger("code") != 10000) {
            log.error("API返回错误，错误信息：{}", result.getString("msg"));
            return null;
        }
        return result.get("resultValue").toString();
    }


    private Map<String, String> getHttpHeader(Map<String, String> headers) {

        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        headers.put("random", UUID.randomUUID().toString().replaceAll("-", ""));
        headers.put("secretkey", secretkey);

        if (StringUtils.hasText(token)) {
            headers.put("token", token);
        }
        String sign = getSignStr(headers);
        headers.put("sign", sign);
        headers.remove("secretkey");
        return headers;
    }


    private String getToken() {
        token = null;
        // 存储结果
        Map<String, Object> result = new HashMap<>();
        // 请求地址
        String url = baseUrl + "/authorityservice/login/integrate";
        // 存储请求体
        Map<String, Object> formParams = new HashMap<>(2);
        formParams.put("appkey", appKey);

        try {
            Map<String, String> headers = new HashMap<>(4);
            headers = getHttpHeader(headers);
            String jsonParams = JSON.toJSONString(formParams);
            HttpResponse post = HttpRequest.post(url).headerMap(headers, true).body(jsonParams).execute();

            result.put("httpResponse", post);
            // 取返回值json中的token
            String responseBody = post.body();
            JSONObject result1 = JSON.parseObject(responseBody);
            Map<String, Object> contentMapResult = (Map<String, Object>) result1.get("resultValue");
            token = (String) contentMapResult.get("token");
            return token;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private String getSignStr(Map<String, String> params) {
        StringBuffer buffer = new StringBuffer();
        if (params.containsKey("token")) {
            buffer.append("token").append("=").append(params.get("token").toString()).append("&");
        }
        buffer.append("timestamp").append("=").append(params.get("timestamp").toString()).append("&");
        buffer.append("random").append("=").append(params.get("random").toString()).append("&");
        buffer.append("secretkey").append("=").append(params.get("secretkey").toString());

        String encrypt = encrypt(buffer.toString());
        return encrypt;
    }

    private String encrypt(String paramStr) {
        String resultHexString = "";
        try {
            byte[] srcData = paramStr.getBytes("UTF-8");
            byte[] resultHash = hash(srcData);
            resultHexString = ByteUtils.toHexString(resultHash);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return resultHexString.toUpperCase();
    }

    private byte[] hash(byte[] srcData) {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }


}
