package com.ruoyi.util.map;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.map.LineString;
import com.ruoyi.entity.map.NRCoords;
import com.ruoyi.service.map.impl.SelectCoords;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class DeviceFeederConverter {
    @Autowired
    SelectCoords selectCoords;
    // 匹配经纬度对的正则表达式
    private static final Pattern COORD_PATTERN = Pattern.compile("\\s+");

    /**
     * 将DeviceFeeder对象转换为GeoLineFinder.LineString对象
     */
    public static LineString convertToLineString(DeviceFeeder feeder) {
        if (feeder == null || feeder.getGeoList() == null || feeder.getGeoList().isEmpty()) {
            return null;
        }

        List<double[]> coordinates = parseGeoList(feeder.getGeoList());
        if (coordinates.isEmpty()) {
            return null;
        }

        return new LineString(feeder.getPsrId(), feeder.getName(),coordinates);
    }

    public  LineString convertToLineString2(DeviceFeeder feeder) throws Exception {
        if (feeder == null) {
            return null;
        }
        List<PsrIdAndPsrType> list =new ArrayList<>();
        list.add(new PsrIdAndPsrType(feeder.getPsrId(),"dxd"));
        List<NRCoords> nrCoords =  selectCoords.SelectCoords(list);
        List<double[]> coordinates = parseGeoList(nrCoords.get(0).getCoordinate());
        if (coordinates.isEmpty()) {
            return null;
        }

        return new LineString(feeder.getPsrId(), feeder.getName(),coordinates);
    }

    /**
     * 解析geoList字符串为坐标点列表
     */
    private static List<double[]> parseGeoList(String geoList) {
        List<double[]> coordinates = new ArrayList<>();

        // 处理可能的逗号分隔的多个线段
        String[] segments = geoList.split("\\s*,\\s*");

        for (String segment : segments) {
            // 移除首尾空白
            segment = segment.trim();
            if (segment.isEmpty()) continue;

            // 分割经纬度对
            String[] coordPairs = COORD_PATTERN.split(segment);

            // 确保有偶数个值（经纬度对）
            if (coordPairs.length % 2 != 0) {
                System.err.println("Invalid coordinate pair count: " + segment);
                continue;
            }

            // 解析每对经纬度
            for (int i = 0; i < coordPairs.length; i += 2) {
                try {
                    double lon = Double.parseDouble(coordPairs[i]);
                    double lat = Double.parseDouble(coordPairs[i + 1]);
                    coordinates.add(new double[]{lon, lat});
                } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                    System.err.println("Error parsing coordinate pair: " +
                                       coordPairs[i] + ", " + coordPairs[i + 1]);
                }
            }
        }

        return coordinates;
    }




}
