package com.ruoyi.util.map;

import com.ruoyi.entity.map.LineString;

import java.util.*;

public class GeoLineFinder {
    private static final double EARTH_RADIUS = 6371000; // 地球半径（米）



    /**
     * 最近点结果
     */
    public static class NearestPointResult implements Comparable<NearestPointResult> {
        private final String lineId;
        private final String lineName;
        private final int segmentIndex;
        private final double[] point;
        private final double distance;

        public NearestPointResult(String lineId, String lineName,int segmentIndex, double[] point, double distance) {
            this.lineId = lineId;
            this.lineName = lineName;
            this.segmentIndex = segmentIndex;
            this.point = Arrays.copyOf(point, point.length);
            this.distance = distance;
        }

        public String getLineId() {
            return lineId;
        }
        public String getLineName() {
            return lineName;
        }


        public int getSegmentIndex() {
            return segmentIndex;
        }

        public double[] getPoint() {
            return Arrays.copyOf(point, point.length);
        }

        public double getDistance() {
            return distance;
        }

        @Override
        public int compareTo(NearestPointResult other) {
            return Double.compare(this.distance, other.distance);
        }

        @Override
        public String toString() {
            return String.format("Line %s (Segment %d): Point [%.6f, %.6f], Distance %.2fm",
                    lineId, segmentIndex, point[0], point[1], distance);
        }
    }

    /**
     * 查找最近的三条线
     */
    public static List<NearestPointResult> findTop3NearestLines(double[] point, List<LineString> lines,int num) {
        PriorityQueue<NearestPointResult> maxHeap = new PriorityQueue<>(num, Collections.reverseOrder());

        for (LineString line : lines) {
            NearestPointResult nearest = findNearestPointOnLine(point, line);
            if (nearest != null) {
                maxHeap.offer(nearest);
                if (maxHeap.size() > num) {
                    maxHeap.poll();
                }
            }
        }

        List<NearestPointResult> result = new ArrayList<>(maxHeap);
        Collections.sort(result);
        return result;
    }

    /**
     * 查找点到特定线的最近点
     */
    private static NearestPointResult findNearestPointOnLine(double[] point, LineString line) {
        if(point==null){
            System.out.println("point空");
        }
        if(line==null){
            System.out.println("line"+line.getId());
        }
        List<double[]> coords = line.getCoordinates();
        if (coords.size() < 2) return null;

        double minDistance = Double.MAX_VALUE;
        int nearestSegment = -1;
        double[] nearestPoint = null;

        for (int i = 0; i < coords.size() - 1; i++) {
            double[] p1 = coords.get(i);
            double[] p2 = coords.get(i + 1);
            PointSegmentResult result = pointToSegment(point, p1, p2);

            if (result.distance < minDistance) {
                minDistance = result.distance;
                nearestSegment = i;
                nearestPoint = result.point;
            }
        }

        if (nearestSegment != -1) {
            return new NearestPointResult(line.getId(),line.getName(), nearestSegment, nearestPoint, minDistance);
        }
        return null;
    }

    /**
     * 点到线段的距离计算
     */
    private static PointSegmentResult pointToSegment(double[] point, double[] start, double[] end) {
        double lon = point[0];
        double lat = point[1];
        double startLon = start[0];
        double startLat = start[1];
        double endLon = end[0];
        double endLat = end[1];

        // 计算投影点
        double dx = endLon - startLon;
        double dy = endLat - startLat;
        double lengthSquared = dx * dx + dy * dy;

        double t;
        if (lengthSquared == 0) {
            t = 0; // 起点和终点相同
        } else {
            t = ((lon - startLon) * dx + (lat - startLat) * dy) / lengthSquared;
            t = Math.max(0, Math.min(1, t)); // 限制在[0,1]范围内
        }

        double nearestLon = startLon + t * dx;
        double nearestLat = startLat + t * dy;
        double[] nearestPoint = {nearestLon, nearestLat};

        // 计算球面距离
        double distance = haversineDistance(point, nearestPoint);

        return new PointSegmentResult(nearestPoint, distance);
    }

    /**
     * Haversine距离公式
     */
    private static double haversineDistance(double[] p1, double[] p2) {
        double lon1 = Math.toRadians(p1[0]);
        double lat1 = Math.toRadians(p1[1]);
        double lon2 = Math.toRadians(p2[0]);
        double lat2 = Math.toRadians(p2[1]);

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 内部结果类
     */
    private static class PointSegmentResult {
        final double[] point;
        final double distance;

        PointSegmentResult(double[] point, double distance) {
            this.point = point;
            this.distance = distance;
        }
    }

}
