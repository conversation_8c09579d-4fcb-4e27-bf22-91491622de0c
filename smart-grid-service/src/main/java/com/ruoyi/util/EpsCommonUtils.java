package com.ruoyi.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.spring.SpringUtils;


import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class EpsCommonUtils {
    private final static ObjectMapper objectMapper = SpringUtils.getBean(ObjectMapper.class);

    public static String buildInsertSql(String dbName, Object obj) {
        if (StringUtils.isBlank(dbName)) {
            throw new ServiceException("数据库名称不能为空");
        }
        Class<?> aClass = obj.getClass();
        String tableName = Optional.ofNullable(aClass.getAnnotation(TableName.class)).map(TableName::value).orElseGet(() -> StringUtils.camelToUnderline(aClass.getSimpleName()));
        Field[] declaredFields = aClass.getDeclaredFields();
        Map<String, Method> methodMap = Arrays.stream(aClass.getMethods()).peek(item -> item.setAccessible(true))
                .filter(item -> item.getName().startsWith("get")).collect(Collectors.toMap(Method::getName, o -> o));
        StringBuilder sb = new StringBuilder("insert into ").append(dbName).append(".").append(tableName).append(" ");
        StringBuilder fields = new StringBuilder("(");
        StringBuilder values = new StringBuilder("(");
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String fieldName = declaredField.getName();
            Method method = methodMap.get("get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
            Object val;
            try {
                if (method == null || (val = method.invoke(obj)) == null) {
                    continue;
                }
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new RuntimeException(e);
            }
            TableField annotation = declaredField.getAnnotation(TableField.class);
            if (annotation != null) {
                fieldName = annotation.value();
            }
            if (fields.length() > 1) {
                fields.append(",");
                values.append(",");
            }
            fields.append(StringUtils.camelToUnderline(fieldName));
            if (val instanceof String) {
                values.append("'").append(val).append("'");
                continue;
            }
            values.append(val);
        }
        fields.append(")");
        values.append(")");
        return sb.append(fields).append(" values ").append(values).toString();
    }

    public static Set<String> pmsIdToD5000Id(String psrType, String idKey, List<Map<String, Object>> data) {
        if (CollectionUtil.isEmpty(data) || StringUtils.isBlank(psrType)) {
            return Collections.emptySet();
        }
        Stream<Map<String, Object>> filterAfter = data.stream().filter(item -> item.get(idKey) != null);
        return filterAfter.map(item -> "PD_" + psrType + "_" + item.get(idKey)).collect(Collectors.toSet());
    }

    public static String d5000IdToPmsId(String id) {
        String[] splitStr;
        if (StringUtils.isBlank(id) || (splitStr = id.split("_")).length < 2) {
            return "";
        }
        return splitStr[2];
    }

    public static Set<String> pmsIdToD5000Id(String psrType, Set<String> ids) {
        if (CollectionUtil.isEmpty(ids) || StringUtils.isBlank(psrType)) {
            return Collections.emptySet();
        }
        return ids.stream().map(item -> "PD_" + psrType + "_" + item).collect(Collectors.toSet());
    }

    public static String jsonArrayStrToSplit(Object str) {
        if (str == null) {
            return null;
        }
        JSONArray array = JSONUtil.parseArray(str.toString());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < array.size(); i++) {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append(array.getStr(i));
        }
        return sb.toString();
    }

    public static Double doubleApply(Object data) {
        return data == null ? null : Double.parseDouble(data.toString());
    }

    public static String strApply(Object data) {
        return data == null ? null : data.toString();
    }

    public static <T> List<T> readStrToList(String str, TypeReference<List<T>> reference) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(str, reference);
        } catch (JsonProcessingException ignored) {}
        return Collections.emptyList();
    }

    public static <T> String listToStr(List<T> data) {
        if (CollectionUtil.isEmpty(data)) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException ignored) {}
        return "[]";
    }

    public static Map<String, Object> covertPointList(Object data, Supplier<String> spSupplier, Supplier<String> epSupplier) {
        if (data == null) {
            return Collections.emptyMap();
        }
        String startPoint = spSupplier.get();
        String endPoint = epSupplier.get();
        Map<String, Object> dataMap = BeanUtil.beanToMap(data);
        dataMap.put("startPoint", StringUtils.isBlank(startPoint) ? Collections.emptyList() : readStrToList(startPoint, new TypeReference<List<Double>>() {}));
        dataMap.put("endPoint", StringUtils.isBlank(startPoint) ? Collections.emptyList() : readStrToList(endPoint, new TypeReference<List<Double>>() {}));
        return dataMap;
    }



    public static List<Double> oneHundredPoint(Map<String, Object> beanToMap) {
        if (beanToMap == null) {
            return Collections.emptyList();
        }
        List<Double> sensitivity = new ArrayList<>();
        BigDecimal hundred = new BigDecimal("100");
        for (int i = 1; i <= 100; i++) {
            Object data = beanToMap.get("v" + i);
            if (data == null) {
                sensitivity.add(0d);
                continue;
            }
            Double val = new BigDecimal(data.toString()).multiply(hundred).doubleValue();
            sensitivity.add(val);
        }
        return sensitivity;
    }

    public static Map<String, Double> oneHundredPointToMap(List<Double> pointList) {
        if (CollectionUtil.isEmpty(pointList) || pointList.size() != 100) {
            throw new ServiceException("曲线数据格式异常");
        }
        BigDecimal hundred = new BigDecimal("100");
        Map<String, Double> fieldMapping = new HashMap<>();
        for (int i = 0; i < pointList.size(); i++) {
            Double val = pointList.get(i);
            val = val == null ? 0d : val;
            if (val.compareTo(0d) < 0 || val.compareTo(100d) > 0) {
                throw new ServiceException("曲线数值只能调整为0-100内");
            }
            fieldMapping.put("v" + (i + 1), new BigDecimal(val.toString()).divide(hundred, 2, RoundingMode.HALF_UP).doubleValue());
        }
        return fieldMapping;
    }

    public static LocalDateTime toLocalDateTime(Date calcTime) {
        if (calcTime == null) {
            return null;
        }
        return calcTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static int deptLevel(String ancestors) {
        if (StringUtils.isBlank(ancestors)) {
            return 1;
        }
        return ancestors.split(",").length - 1;
    }

    public static Long splitCity(String ancestorsStr, Long deptId) {
        String[] ancestors = ancestorsStr.split(",");
        if (ancestors.length > 2) {
            return Long.valueOf(ancestors[2]);
        }
        return deptId;
    }

    public static Long splitCityMaintOrg(String ancestorsStr, Long deptId) {
        String[] ancestors = ancestorsStr.split(",");
        if (ancestors.length > 3) {
            return Long.valueOf(ancestors[3]);
        }
        return deptId;
    }

    public static Long splitMaintGroup(String ancestorsStr, Long deptId) {
        String[] ancestors = ancestorsStr.split(",");
        if (ancestors.length > 4) {
            return Long.valueOf(ancestors[4]);
        }
        return deptId;
    }
}
