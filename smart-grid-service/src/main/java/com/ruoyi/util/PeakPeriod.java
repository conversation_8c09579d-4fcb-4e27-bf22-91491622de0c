package com.ruoyi.util;

import lombok.Data;

@Data
public  class PeakPeriod {

    private String timeWindow;      // 高峰时段，如"09:00-10:30"
        private double durationHours;   // 持续时间(小时)，如1.5
        private int exceedCount;        // 超过阈值的次数
        private double averageValue;    // 高峰时段平均值

        public PeakPeriod(String timeWindow, double durationHours,
                          int exceedCount, double averageValue) {

            this.timeWindow = timeWindow;
            this.durationHours = durationHours;
            this.exceedCount = exceedCount;
            this.averageValue = averageValue;
        }

        @Override
        public String toString() {
            return String.format("%s\t%s\t%.1f小时\t%d个点\t%.2f",
                     timeWindow, durationHours, exceedCount, averageValue);
        }
    }
