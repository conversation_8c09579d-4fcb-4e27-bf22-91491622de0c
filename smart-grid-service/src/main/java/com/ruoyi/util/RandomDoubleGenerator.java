package com.ruoyi.util;

import java.util.Random;

public class RandomDoubleGenerator {
    // Random 对象，用于生成随机数
    private static final Random random = new Random();

    // 定义范围
    private static final double MIN = 0.0;
    private static final double MAX = 20.0;

    /**
     * 生成一个 0.0 到 20.0 之间的随机 double 值
     *
     * @return 返回一个 [0.0, 20.0) 之间的随机 double 值
     */
    public static double generateRandomDouble() {
        return MIN + (MAX - MIN) * random.nextDouble();
    }

    /**
     * 生成一个指定范围内 [min, max) 的随机 double 值
     *
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 返回一个 [min, max) 之间的随机 double 值
     * @throws IllegalArgumentException 如果 min >= max，抛出异常
     */
    public static double generateRandomDoubleInRange(double min, double max) {
        if (min >= max) {
            throw new IllegalArgumentException("最小值必须小于最大值");
        }
        return min + (max - min) * random.nextDouble();
    }
}
