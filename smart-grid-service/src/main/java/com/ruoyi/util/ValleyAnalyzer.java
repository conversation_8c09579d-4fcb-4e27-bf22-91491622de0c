package com.ruoyi.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class ValleyAnalyzer {



    /**
     * 分析低谷时段
     * @param timeList 时间列表（5分钟间隔，格式："yyyy-MM-dd HH:mm:ss"）
     * @param loadList 负荷数据列表
     * @param threshold 低谷阈值（低于此值视为低谷）
     * @param minDurationMinutes 最小持续时间(分钟)
     * @return 低谷时段列表
     */
    public static List<PeakPeriod> analyze(List<String> timeList,
                                           List<Double> loadList,
                                           double threshold,
                                           int minDurationMinutes) {
        List<PeakPeriod> results = new ArrayList<>();
        List<TimeSegment> rawSegments = new ArrayList<>();

        // 检测原始低谷段
        boolean inValley = false;
        LocalDateTime segmentStart = null;
        int currentBelowCount = 0;
        double currentSum = 0;

        for (int i = 0; i < loadList.size(); i++) {
            LocalDateTime currentTime = parseTime(timeList.get(i));
            double currentValue = loadList.get(i);

            if (currentValue <= threshold) {
                if (!inValley) {
                    // 新低谷段开始
                    inValley = true;
                    segmentStart = currentTime;
                    currentBelowCount = 1;
                    currentSum = currentValue;
                } else {
                    // 累计当前低谷段
                    currentBelowCount++;
                    currentSum += currentValue;
                }
            } else if (inValley) {
                // 低谷段结束
                inValley = false;
                saveSegment(segmentStart, currentTime,
                          currentBelowCount, currentSum,
                          minDurationMinutes, rawSegments);
            }
        }

        // 处理最后一段可能的低谷
        if (inValley) {
            LocalDateTime endTime = parseTime(timeList.get(timeList.size() - 1));
            saveSegment(segmentStart, endTime,
                      currentBelowCount, currentSum,
                      minDurationMinutes, rawSegments);
        }

        // 合并相邻低谷段（间隔<30分钟）
        return mergeSegments(rawSegments, 30);
    }

    private static void saveSegment(LocalDateTime start, LocalDateTime end,
                                  int belowCount, double sum,
                                  int minDuration,
                                  List<TimeSegment> segments) {
        long durationMinutes = ChronoUnit.MINUTES.between(start, end);
        if (durationMinutes >= minDuration) {
            segments.add(new TimeSegment(
                start, end, belowCount, sum / belowCount));
        }
    }

    private static List<PeakPeriod> mergeSegments(List<TimeSegment> segments,
                                                  int maxGapMinutes) {
        if (segments.isEmpty()) return Collections.emptyList();

        List<PeakPeriod> merged = new ArrayList<>();
        TimeSegment current = segments.get(0);

        for (int i = 1; i < segments.size(); i++) {
            TimeSegment next = segments.get(i);
            if (current.end.plusMinutes(maxGapMinutes).isAfter(next.start)) {
                // 合并时段
                current.end = next.end;
                current.belowCount += next.belowCount;
                current.avgValue = (current.avgValue * (current.belowCount - next.belowCount) +
                                  next.avgValue * next.belowCount) / current.belowCount;
            } else {
                addMergedSegment(current, merged);
                current = next;
            }
        }
        addMergedSegment(current, merged);

        return merged;
    }

    private static void addMergedSegment(TimeSegment segment,
                                       List<PeakPeriod> results) {
        double durationHours = ChronoUnit.MINUTES.between(segment.start, segment.end) / 60.0;

        // 格式化日期时间（同一天显示为"yyyy-MM-dd HH:mm-HH:mm"，跨天显示完整日期）
        String timeWindow;
        if (segment.start.toLocalDate().equals(segment.end.toLocalDate())) {
            timeWindow = String.format("%s %s-%s",
                segment.start.toLocalDate(),
                segment.start.toLocalTime(),
                segment.end.toLocalTime());
        } else {
            timeWindow = String.format("%s %s - %s %s",
                segment.start.toLocalDate(),
                segment.start.toLocalTime(),
                segment.end.toLocalDate(),
                segment.end.toLocalTime());
        }

        results.add(new PeakPeriod(
            timeWindow,
            durationHours,
            segment.belowCount,
            segment.avgValue));
    }

    private static LocalDateTime parseTime(String timeStr) {
        return LocalDateTime.parse(timeStr,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // 辅助类（与高峰分析器结构相同，仅重命名字段）
    private static class TimeSegment {
        LocalDateTime start;
        LocalDateTime end;
        int belowCount;  // 原exceedCount改为belowCount
        double avgValue;

        TimeSegment(LocalDateTime start, LocalDateTime end,
                   int belowCount, double avgValue) {
            this.start = start;
            this.end = end;
            this.belowCount = belowCount;
            this.avgValue = avgValue;
        }
    }
}
