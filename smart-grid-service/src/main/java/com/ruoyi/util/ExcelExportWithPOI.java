package com.ruoyi.util;

import com.ruoyi.entity.power.vo.ExcelLoadMW;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * excel导出
 */
public class ExcelExportWithPOI {
    public static void transformerExportToResponse(List<ExcelLoadMW> dataList,
                                        HttpServletResponse response,
                                        String fileName) throws Exception {
        // 1. 设置响应头
        setupResponseHeaders(response, fileName);

        // 2. 创建工作簿（使用SXSSF实现流式写入，避免OOM）
        try (Workbook workbook = new SXSSFWorkbook(100)) { // 保留100行在内存中

            // 3. 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 4. 创建工作表
            Sheet sheet = workbook.createSheet("变压器数据");

            // 5. 创建表头
            transformerCreateHeaderRow(sheet, headerStyle);

            // 6. 填充数据并处理合并
            transformerFillDataAndMerge(sheet, dataList, dataStyle);

            // 7. 写入响应输出流
            workbook.write(response.getOutputStream());

            // 8. 刷新输出流
            response.getOutputStream().flush();
        }
    }

    public static void feederExportToResponse(List<ExcelLoadMW> dataList,
                                                   HttpServletResponse response,
                                                   String fileName) throws Exception {
        // 1. 设置响应头
        setupResponseHeaders(response, fileName);

        // 2. 创建工作簿（使用SXSSF实现流式写入，避免OOM）
        try (Workbook workbook = new SXSSFWorkbook(100)) { // 保留100行在内存中

            // 3. 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // 4. 创建工作表
            Sheet sheet = workbook.createSheet("变压器数据");

            // 5. 创建表头
            feederCreateHeaderRow(sheet, headerStyle);

            // 6. 填充数据并处理合并
            feederFillDataAndMerge(sheet, dataList, dataStyle);

            // 7. 写入响应输出流
            workbook.write(response.getOutputStream());

            // 8. 刷新输出流
            response.getOutputStream().flush();
        }
    }

    private static void setupResponseHeaders(HttpServletResponse response, String fileName) throws Exception {
        // 设置编码和响应头
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName + ".xlsx");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
    }

    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private static void transformerCreateHeaderRow(Sheet sheet, CellStyle style) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"变电站名称", "主变名称", "时间", "负荷(MW)"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(style);
            sheet.setColumnWidth(i, 15 * 256); // 设置列宽
        }
    }

    private static void feederCreateHeaderRow(Sheet sheet, CellStyle style) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"线路名称", "时间", "负荷(MW)"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(style);
            sheet.setColumnWidth(i, 15 * 256); // 设置列宽
        }
    }

    private static void transformerFillDataAndMerge(Sheet sheet, List<ExcelLoadMW> dataList, CellStyle style) {
        if (dataList == null || dataList.isEmpty()) return;

        // 先按变电站和主变名称排序（确保相同值连续）
        dataList.sort((a, b) -> {
            int compare = a.getSubstationName().compareTo(b.getSubstationName());
            if (compare == 0) {
                return a.getTransformerName().compareTo(b.getTransformerName());
            }
            return compare;
        });

        String currentSubstation = null;
        String currentTransformer = null;
        int substationStartRow = 1; // 数据从第2行开始
        int transformerStartRow = 1;

        for (int i = 0; i < dataList.size(); i++) {
            ExcelLoadMW data = dataList.get(i);
            Row row = sheet.createRow(i + 1); // 第1行是表头

            // 填充数据
            createCell(row, 0, data.getSubstationName(), style);
            createCell(row, 1, data.getTransformerName(), style);
            createCell(row, 2, data.getTime(), style);
            createCell(row, 3, data.getLoadMW(), style);

            // 处理变电站名称合并
            if (currentSubstation == null || !currentSubstation.equals(data.getSubstationName())) {
                if (currentSubstation != null && (i + 1 - substationStartRow) > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(
                            substationStartRow, i, 0, 0));
                }
                currentSubstation = data.getSubstationName();
                substationStartRow = i + 1;
            }

            // 处理主变名称合并
            if (currentTransformer == null || !currentTransformer.equals(data.getTransformerName())) {
                if (currentTransformer != null && (i + 1 - transformerStartRow) > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(
                            transformerStartRow, i, 1, 1));
                }
                currentTransformer = data.getTransformerName();
                transformerStartRow = i + 1;
            }
        }

        // 处理最后一组合并
        if (dataList.size() + 1 - substationStartRow > 1) {
            sheet.addMergedRegion(new CellRangeAddress(
                    substationStartRow, dataList.size(), 0, 0));
        }
        if (dataList.size() + 1 - transformerStartRow > 1) {
            sheet.addMergedRegion(new CellRangeAddress(
                    transformerStartRow, dataList.size(), 1, 1));
        }
        // 在所有数据填充完成后，自动调整时间列宽度
        adjustTimeColumnWidth(sheet, dataList);
    }

    private static void feederFillDataAndMerge(Sheet sheet, List<ExcelLoadMW> dataList, CellStyle style) {
        if (dataList == null || dataList.isEmpty()) return;

        String currentSubstation = null;
        int substationStartRow = 1; // 数据从第2行开始
        int transformerStartRow = 1;

        for (int i = 0; i < dataList.size(); i++) {
            ExcelLoadMW data = dataList.get(i);
            Row row = sheet.createRow(i + 1); // 第1行是表头

            // 填充数据

            createCell(row, 0, data.getTransformerName(), style);
            createCell(row, 1, data.getTime(), style);
            createCell(row, 2, data.getLoadMW(), style);

            // 处理变电站名称合并
            if (currentSubstation == null || !currentSubstation.equals(data.getSubstationName())) {
                if (currentSubstation != null && (i + 1 - substationStartRow) > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(
                            substationStartRow, i, 0, 0));
                }
                currentSubstation = data.getSubstationName();
                substationStartRow = i + 1;
            }

        }

        // 处理最后一组合并
        if (dataList.size() + 1 - substationStartRow > 1) {
            sheet.addMergedRegion(new CellRangeAddress(
                    substationStartRow, dataList.size(), 0, 0));
        }
        if (dataList.size() + 1 - transformerStartRow > 1) {
            sheet.addMergedRegion(new CellRangeAddress(
                    transformerStartRow, dataList.size(), 1, 1));
        }
        // 在所有数据填充完成后，自动调整时间列宽度
        adjustTimeColumnWidth(sheet, dataList);
    }
    // 智能宽度计算方法
    private static void adjustTimeColumnWidth(Sheet sheet, List<ExcelLoadMW> dataList) {
        int maxLength = "时间".getBytes().length; // 初始为表头宽度

        for (ExcelLoadMW data : dataList) {
            if (data.getTime() != null) {
                int length = data.getTime().getBytes().length;
                maxLength = Math.max(maxLength, length);
            }
        }

        // 设置列宽（增加2个字符缓冲，限制最大30字符）
        sheet.setColumnWidth(2, Math.min(30, maxLength + 2) * 256);
    }
    private static void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value);
        cell.setCellStyle(style);
    }

}
