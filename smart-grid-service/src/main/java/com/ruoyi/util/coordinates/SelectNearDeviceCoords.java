package com.ruoyi.util.coordinates;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.map.DeviceCoords;

/**
 * 查询一个点离线最近的设备，前提要将所有设备都封装到DeviceCoords实体里面
 */
public class SelectNearDeviceCoords {

    /**
     * 从设备坐标列表中查找距离目标点最近的指定数量的点，每个线路仅返回一个最近点
     *
     * @param deviceCoords 设备坐标列表
     * @param targetPoint  目标点坐标 [x, y]
     * @param num          需要返回的最近点数量，-1表示返回所有线路的最近点
     * @return 距离目标点最近的指定数量的设备坐标列表
     */
    public static List<DeviceCoords> findNearestPoints(List<DeviceCoords> deviceCoords, double[] targetPoint, Integer num) {
        // 1. 解析坐标并计算距离
        Map<DeviceCoords, Double> distanceMap = new HashMap<>();
        for (DeviceCoords coord : deviceCoords) {
            double[] point = parseCoords(coord.getCoords());
            double distance = haversineDistance(point, targetPoint);
            coord.setDistance(distance);
            distanceMap.put(coord, distance);
        }

        // 2. 按feederID分组，并为每个组选择距离最近的点
        Map<String, DeviceCoords> nearestByFeeder = new HashMap<>();
        for (DeviceCoords coord : deviceCoords) {
            String feederID = coord.getFeederId();
            double currentDistance = distanceMap.get(coord);

            // 如果该feederID还没有点，或者当前点更近，则更新
            if (!nearestByFeeder.containsKey(feederID) ||
                    currentDistance < distanceMap.get(nearestByFeeder.get(feederID))) {
                nearestByFeeder.put(feederID, coord);
            }
        }
        // 3. 对筛选出的最近点按距离排序，并根据num值决定返回全部或前num个
        Stream<DeviceCoords> stream = nearestByFeeder.values().stream()
                .sorted(Comparator.comparingDouble(distanceMap::get));

        return num == -1 ? stream.collect(Collectors.toList()) : stream.limit(num).collect(Collectors.toList());
    }

    /**
     * 根据参考馈线ID列表，从源设备坐标列表中筛选出相同馈线ID的设备，并按馈线ID分组
     *
     * @param nearFeederdeviceCoordsList 参考馈线ID的设备坐标列表
     * @param deviceCoords               源设备坐标列表
     * @return 按馈线ID分组的设备坐标映射，每个馈线ID对应一个设备坐标列表
     */
    public static Map<String, List<DeviceCoords>> findNearestFeederAllDevice(List<DeviceCoords> nearFeederdeviceCoordsList, List<DeviceCoords> deviceCoords) {
        // 1. 提取参考列表中的所有唯一馈线ID
        Set<String> feederIdSet = nearFeederdeviceCoordsList.stream()
                .map(DeviceCoords::getFeederId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 2. 从源列表中筛选出馈线ID在参考集合中的设备，并按馈线ID分组
        Map<String, List<DeviceCoords>> groupedMap = deviceCoords.stream()
                .filter(coords -> feederIdSet.contains(coords.getFeederId()))
                .collect(Collectors.groupingBy(DeviceCoords::getFeederId));

        // 3. 对每个分组内的列表进行排序
        for (List<DeviceCoords> deviceList : groupedMap.values()) {
            deviceList.sort(Comparator.comparingDouble(DeviceCoords::getDistance));
        }

        return groupedMap;

    }


    // 辅助方法：解析坐标字符串 x,y 为double[]
    private static double[] parseCoords(String coords) {
        String[] parts = coords.split(",");
        try {
            return new double[]{Double.parseDouble(parts[0]), Double.parseDouble(parts[1])};
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            return new double[]{0, 0}; // 处理解析错误
        }
    }

    /**
     * 计算两个经纬度点之间的球面距离（Haversine公式）
     *
     * @return 两点之间的距离，单位：米
     */
    public static double haversineDistance(double[] point, double[] targetPoint) {
        final int R = 6371000; // 地球半径，单位：米

        double lat1 = point[1];
        double lon1 = point[0];
        double lat2 = targetPoint[1];
        double lon2 = targetPoint[0];

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return DoubleFormatter.formatToThreeDecimals1(R * c); // 返回距离，单位：米
    }
}
