package com.ruoyi.util.coordinates;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ruoyi.common.utils.StringUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.proj4j.ProjCoordinate;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CoordinateConverter {

    static GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 将经纬度字符串转换为double数组 [lon, lat]
     *
     * @param lon 经度字符串
     * @param lat 纬度字符串
     * @return double数组 [lon, lat]，如果解析失败则返回null
     */
    public static double[] convert(String lon, String lat) {
        if (lon == null || lat == null) {
            return null;
        }

        try {
            double longitude = parseCoordinate(lon);
            double latitude = parseCoordinate(lat);

            // 验证经纬度范围
            if (!isValidLongitude(longitude) || !isValidLatitude(latitude)) {
                throw new IllegalArgumentException("Coordinate out of range");
            }

            return new double[]{longitude, latitude};
        } catch (IllegalArgumentException e) {
            System.err.println("Failed to convert coordinates: " + e.getMessage());
            return null;
        }
    }

    /**
     * 解析单个坐标字符串
     */
    private static double parseCoordinate(String coord) {
        // 去除空格并替换逗号为小数点
        String cleaned = coord.trim().replace(',', '.');

        // 验证是否为合法数字格式
        if (!cleaned.matches("[-+]?[0-9]*\\.?[0-9]+")) {
            throw new NumberFormatException("Invalid coordinate format: " + cleaned);
        }

        return Double.parseDouble(cleaned);
    }

    // 辅助方法：解析坐标字符串 x,y 为double[]

    /**
     * 将经纬度112,32 转为[112,32]
     */
    public static double[] parseLngLat(String coordString) {
        if (coordString == null || coordString.trim().isEmpty()) {
            return null;
        }

        String[] parts = coordString.trim().split(",");
        if (parts.length < 2) {
            return null;
        }
        return new double[]{Double.parseDouble(parts[0].trim()), Double.parseDouble(parts[1].trim())};
    }

    /**
     * 将经纬度112,32 转为[112,32]
     */
    public static Double[] parseLngLatCoords(String coordString) {
        if (coordString == null || coordString.trim().isEmpty()) {
            throw new IllegalArgumentException("坐标字符串不能为空");
        }

        String[] parts = coordString.trim().split(",");
        if (parts.length < 2) {
            throw new IllegalArgumentException("需要至少两个坐标值");
        }

        return new Double[]{
                Double.parseDouble(parts[0].trim()),
                Double.parseDouble(parts[1].trim())
        };
    }

    /**
     * 将字符串(118.75496647568387 32.019883614089096 118.75496739045269 32.01988352622447)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<List<Double>> parseCoordinates(String coordinateStr) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 按空格分割字符串
        String[] tokens = coordinateStr.trim().split("\\s+");

        // 检查是否有偶数个数字（确保可以两两分组）
        if (tokens.length % 2 != 0) {
            throw new IllegalArgumentException("坐标点数量必须为偶数（经度和纬度成对）");
        }

        List<List<Double>> result = new ArrayList<>();
        for (int i = 0; i < tokens.length; i += 2) {
            // 提取经度和纬度
            double longitude = Double.parseDouble(tokens[i]);
            double latitude = Double.parseDouble(tokens[i + 1]);

            // 添加到结果列表
            result.add(Arrays.asList(longitude, latitude));
        }

        return result;
    }

    /**
     * 将字符串[[118.837035324425,31.9339936898715],[118.840276362526,31.9273197976144],[118.83709637547,31.922892588189],[118.818772698846,31.9158938571851],[118.812384951023,31.9151657651413],[118.800061221151,31.9090838980235],[118.798175294444,31.9314032602619],[118.79040969269,31.9544800175526],[118.790549974984,31.9544601542134],[118.79082052315,31.9544204408566],[118.790970706615,31.9544006352857],[118.792203093223,31.9542420261108],[118.798274857511,31.9535189230338],[118.803504964702,31.9528748290018],[118.80495796929,31.9527968143383],[118.806070687945,31.952808615216],[118.809959736638,31.9528449868007],[118.814350774662,31.9528223317898],[118.81958387316,31.952791120581],[118.823342147827,31.952823549658],[118.823337977809,31.9528460491014],[118.823331873824,31.9528789017262],[118.828163492552,31.9522590359993],[118.83350372276,31.941265905828],[118.837035324425,31.9339936898715]]坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<List<Double>> parseCoordinates2(String coordinateStr) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(coordinateStr, List.class);
    }

    /**
     * 将字符串(118.75496647568387,32.019883614089096)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<Double> parseCommaCoordinates(String coordinateStr, String split) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>(); // 或返回空列表，根据业务需求
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }

        // 按逗号分割字符串
        String[] parts = coordinateStr.split(split);

        // 转换为 Double 列表
        return Arrays.stream(parts)
                .map(String::trim) // 去除可能的空格
                .map(Double::parseDouble)
                .collect(Collectors.toList());
    }

    /**
     * 将二维数组的坐标coords转为LineString
     *
     * @param coords [[110,39],[]]
     * @return
     */
    public static LineString toLineString(List<List<Double>> coords) {

        Coordinate[] coordsList = coords.stream().map(lngLat -> new Coordinate(lngLat.get(0), lngLat.get(1))).toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordsList);
    }

    /**
     * 将二维坐标数组转换为LineString
     *
     * @param coords 二维double数组 [[x1,y1], [x2,y2], ...]
     * @return LineString几何对象
     */
    public static LineString toLineString(double[][] coords) {
        Coordinate[] coordinates = Arrays.stream(coords)
                .map(arr -> new Coordinate(arr[0], arr[1]))
                .toArray(Coordinate[]::new);
        return geometryFactory.createLineString(coordinates);
    }

    /**
     * 将 List<List<double[]>> 转换为单个 LineString
     * 假设所有内部列表的点都属于同一条连续的线
     */
    public static LineString convertToSingleLineString(List<List<double[]>> coordinatesList) {
        if (coordinatesList == null || coordinatesList.isEmpty()) {
            return null;
        }

        // 计算所有点的总数
        int totalPoints = 0;
        for (List<double[]> line : coordinatesList) {
            if (line != null) {
                totalPoints += line.size();
            }
        }

        // 至少需要两个点才能构成 LineString
        if (totalPoints < 2) {
            return null;
        }

        // 创建 Coordinate 数组
        Coordinate[] coords = new Coordinate[totalPoints];
        int index = 0;

        // 遍历所有内部列表，将点添加到 Coordinate 数组中
        for (List<double[]> line : coordinatesList) {
            if (line == null) {
                continue;
            }

            for (double[] point : line) {
                if (point != null && point.length >= 2) {
                    coords[index++] = new Coordinate(point[0], point[1]);
                } else {
                    // 处理无效点：设置为 (0, 0) 或跳过
                    coords[index++] = new Coordinate(0, 0);
                }
            }
        }

        // 创建 LineString
        return geometryFactory.createLineString(coords);
    }


    /**
     * 将FastJSON的JSONArray转换为LineString
     *
     * @param jsonArray 坐标JSON数组，支持格式：
     *                  - [[x1,y1], [x2,y2], ...]
     *                  - [x1,y1, x2,y2, ...] (平面数组)
     * @return LineString几何对象
     * @throws IllegalArgumentException 当输入格式无效时抛出
     */
    public static LineString jsonArrayToLineString(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            throw new IllegalArgumentException("坐标数组不能为空");
        }

        try {
            // 情况1：嵌套数组格式 [[x1,y1], [x2,y2], ...]
            if (jsonArray.get(0) instanceof JSONArray) {
                Coordinate[] coordinates = new Coordinate[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONArray point = jsonArray.getJSONArray(i);
                    coordinates[i] = new Coordinate(
                            point.getDoubleValue(0),
                            point.getDoubleValue(1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
            // 情况2：平面数组格式 [x1,y1, x2,y2, ...]
            else if (jsonArray.get(0) instanceof Number) {
                if (jsonArray.size() % 2 != 0) {
                    throw new IllegalArgumentException("平面坐标数组长度必须是偶数");
                }
                Coordinate[] coordinates = new Coordinate[jsonArray.size() / 2];
                for (int i = 0; i < coordinates.length; i++) {
                    coordinates[i] = new Coordinate(
                            jsonArray.getDoubleValue(i * 2),
                            jsonArray.getDoubleValue(i * 2 + 1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON坐标转换失败: " + e.getMessage(), e);
        }

        throw new IllegalArgumentException("不支持的JSON坐标格式");
    }

    /**
     * Point转为数组的坐标系
     */
    public static List<Double> pointToLngLat(Point point) {
        return Arrays.asList(point.getX(), point.getY());
    }

    /**
     * List<Double> 类型转为double类型
     */
    public static double[] listToArr(List<Double> doubles) {
        return doubles.stream().mapToDouble(Double::doubleValue).toArray();
    }


    /**
     * 将三维字符串坐标，解析成List<List<double[]>>的格式坐标，
     *
     * @param geoList 三维坐标字符串
     * @return
     */
    public static List<List<double[]>> split(String geoList) {
        // 1. 按逗号分割为子串
        String[] lineStrings = geoList.split(",");

        List<List<double[]>> groupedCoordinates = new ArrayList<>();

        for (String line : lineStrings) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            // 2. 按空格分割为坐标对
            String[] points = trimmedLine.split(" ");
            List<double[]> coordinates = new ArrayList<>();

            for (int i = 0; i < points.length; i += 2) {
                if (i + 1 >= points.length) {
                    throw new IllegalArgumentException("坐标点数为奇数，无法配对: " + trimmedLine);
                }
                double x = Double.parseDouble(points[i]);
                double y = Double.parseDouble(points[i + 1]);
                coordinates.add(new double[]{x, y});
            }

            // 3. 将坐标按两个一组进行分组

            groupedCoordinates.add(coordinates);
        }
        return groupedCoordinates;
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(double lng, double lat) {
        return geometryFactory.createPoint(new Coordinate(lng, lat));
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(List<Double> coords) {
        return geometryFactory.createPoint(new Coordinate(coords.get(0), coords.get(1)));
    }


    /**
     * 计算两个经纬度坐标的中点
     *
     * @param lon1 第一个点经度
     * @param lat1 第一个点纬度
     * @param lon2 第二个点经度
     * @param lat2 第二个点纬度
     * @return 中点坐标 [经度, 纬度]
     */
    public static List<Double> getMidpoint(double lon1, double lat1, double lon2, double lat2) {
        // 1. 转换为弧度
        double lon1Rad = Math.toRadians(lon1);
        double lat1Rad = Math.toRadians(lat1);
        double lon2Rad = Math.toRadians(lon2);
        double lat2Rad = Math.toRadians(lat2);

        // 2. 计算半差
        double dLon = (lon2Rad - lon1Rad) / 2;
        double dLat = (lat2Rad - lat1Rad) / 2;

        // 3. 球面中点公式（使用半正矢公式）
        double a = Math.sin(dLat) * Math.sin(dLat) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(dLon) * Math.sin(dLon);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double midLat = Math.atan2(
                Math.sin(lat1Rad) + Math.sin(lat2Rad) * Math.cos(c),
                Math.sqrt((Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) *
                        (Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) +
                        Math.sin(lat2Rad) * Math.sin(lat2Rad) * Math.sin(c) * Math.sin(c))
        );
        double midLon = lon1Rad + Math.atan2(
                Math.sin(dLon) * Math.cos(lat2Rad),
                Math.cos(dLat) * Math.cos(lat1Rad) - Math.sin(dLat) * Math.sin(lat2Rad) * Math.cos(dLon)
        );

        // 4. 转换为角度
        double midLonDeg = Math.toDegrees(midLon);
        double midLatDeg = Math.toDegrees(midLat);

        return Arrays.asList(midLonDeg, midLatDeg);
    }


    /**
     * 验证经度范围
     */
    private static boolean isValidLongitude(double lon) {
        return lon >= -180 && lon <= 180;
    }

    /**
     * 验证纬度范围
     */
    private static boolean isValidLatitude(double lat) {
        return lat >= -90 && lat <= 90;
    }


    /**
     * 将Geometry坐标转换成List<Double>，方法总入口下面包含，点，线，面
     *
     * @param geometry
     * @return
     */
    public static List<Double> convertGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        if (geometry instanceof Point) {
            return pointToDoubleList((Point) geometry);
        } else if (geometry instanceof LineString) {
            return geometryToDoubleList(geometry);
        } else if (geometry.getNumGeometries() > 1) {
            return multiGeometryToDoubleList(geometry);
        }

        return Collections.emptyList();
    }

    /**
     * 将Geometry坐标转换成List<Double>，面转换
     *
     * @param geometry
     * @return
     */
    public static List<Double> multiGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        List<Double> result = new ArrayList<>();
        for (int i = 0; i < geometry.getNumGeometries(); i++) {
            Geometry subGeom = geometry.getGeometryN(i);
            result.addAll(geometryToDoubleList(subGeom));
        }

        return result;
    }

    /**
     * 将Geometry坐标转换成List<Double>，线转换
     *
     * @param geometry
     * @return
     */
    public static List<Double> geometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        Coordinate[] coordinates = geometry.getCoordinates();
        List<Double> result = new ArrayList<>(coordinates.length * 2);

        for (Coordinate coord : coordinates) {
            result.add(coord.x);
            result.add(coord.y);
        }

        return result;
    }

    /**
     * 将Point点坐标转换成List<Double>，点坐标转换转换
     *
     * @param point
     * @return
     */
    public static List<Double> pointToDoubleList(Point point) {
        if (point == null || point.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(point.getX(), point.getY());
    }


    /**
     * 将经纬度转换为JSON数组格式
     */
    public static String convertToJson(double lon, double lat) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();

        // 创建JSON数组节点
        ArrayNode arrayNode = objectMapper.createArrayNode();
        arrayNode.add(lon);
        arrayNode.add(lat);

        // 转换为JSON字符串
        return objectMapper.writeValueAsString(arrayNode);
    }


    public static void main(String[] args) {
        // 创建示例数据（三层嵌套结构）
        List<List<double[]>> data = new ArrayList<>();

        // 第一个子列表的点
        List<double[]> subList1 = new ArrayList<>();
        subList1.add(new double[]{0.0, 0.0});
        subList1.add(new double[]{1.0, 1.0});

        // 第二个子列表的点
        List<double[]> subList2 = new ArrayList<>();
        subList2.add(new double[]{2.0, 2.0});
        subList2.add(new double[]{3.0, 3.0});

        data.add(subList1);
        data.add(subList2);

        // 转换为单个 LineString
        LineString lineString = CoordinateConverter.convertToSingleLineString(data);

        // 输出 WKT 格式
        System.out.println(lineString.toText());
        // 输出: LINESTRING (0 0, 1 1, 2 2, 3 3)
    }
}


