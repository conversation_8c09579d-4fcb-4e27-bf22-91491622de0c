package com.ruoyi.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CartesianProduct {

    /**
     * 生成二维List的笛卡尔积（递归实现）
     *
     * @param lists 输入的二维List，每行为一个List<T>
     * @param <T>   泛型类型
     * @return 所有可能的组合
     */
    public static <T> List<List<T>> generate(List<List<T>> lists) {
        List<List<T>> result = new ArrayList<>();
        if (lists == null || lists.isEmpty()) {
            return result;
        }
        backtrack(lists, 0, new ArrayList<>(), result);
        return result;
    }

    private static <T> void backtrack(List<List<T>> lists, int rowIndex, List<T> current, List<List<T>> result) {
        if (rowIndex == lists.size()) {
            result.add(new ArrayList<>(current));
            return;
        }

        for (T element : lists.get(rowIndex)) {
            current.add(element);
            backtrack(lists, rowIndex + 1, current, result);
            current.remove(current.size() - 1); // 回溯
        }
    }

    public static void main(String[] args) {
        // 示例：不同类型的二维List
        List<List<Object>> mixedLists = Arrays.asList(
                Arrays.asList(1, 2),                    // 整数类型
                Arrays.asList("A", "B", "C"),          // 字符串类型
                Arrays.asList(true, false)             // 布尔类型
        );

        // 使用泛型方法生成组合
        List<List<Object>> combinations = generate(mixedLists);

        // 输出结果
        System.out.println("共生成 " + combinations.size() + " 种组合：");
        combinations.forEach(System.out::println);
    }
}
