package com.ruoyi.util;

import com.ruoyi.graph.Node;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.ruoyi.entity.cost.DeviceType.*;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class DeviceCounter {
    // 设备类型常量



    public static void countDevices(List<Node> devicesList) {
        // 初始化策略映射
        Map<String, NodeTypeStrategy> strategyMap = new HashMap<>();
        strategyMap.put(RUNGT, node -> isNotBlank(node.getPsrType()) && node.getPsrType().equals(RUNGT));
        strategyMap.put(WLGT, node -> isNotBlank(node.getPsrType()) && node.getPsrType().equals(WLGT));
        strategyMap.put(ZSFHKG, node -> isNotBlank(node.getShapeKey()) && node.getShapeKey().equals(ZSFHKG));


        // 初始化计数器映射
        Map<String, AtomicInteger> typeCounterMap = new HashMap<>();
        strategyMap.keySet().forEach(type -> typeCounterMap.put(type, new AtomicInteger(0)));

        // 遍历设备列表
        for (Node node : devicesList) {
            if (!node.isPsrNode()) {
                strategyMap.forEach((type, strategy) -> {
                    if (strategy.isMatch(node)) {
                        typeCounterMap.get(type).incrementAndGet();
                    }
                });
            }
        }

        // 获取计数结果
        int rungtCount = typeCounterMap.getOrDefault(RUNGT, new AtomicInteger(0)).get();
        int wlgtCount = typeCounterMap.getOrDefault(WLGT, new AtomicInteger(0)).get();
        int poleLoadKgCount = typeCounterMap.getOrDefault(ZSFHKG, new AtomicInteger(0)).get();

        // 输出结果或进一步处理
        System.out.println("RUNGT数量: " + rungtCount);
        System.out.println("WLGT数量: " + wlgtCount);
        System.out.println("ZSFHKG数量: " + poleLoadKgCount);

    }

    // 定义策略接口
    @FunctionalInterface
    interface NodeTypeStrategy {
        boolean isMatch(Node node);
    }
}
