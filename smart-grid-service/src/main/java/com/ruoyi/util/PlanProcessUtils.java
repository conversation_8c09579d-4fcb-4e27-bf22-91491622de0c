package com.ruoyi.util;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.plan.vo.PlanProblemDescribeVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PlanProcessUtils {
    public static final String TEXT_BTN = "text-button";

    public static final String STRIKING = "striking";

    public static final String SINGMAP_POPUP = "singMapPopup"; // 单线图

    public static final String SINGMAP_SEG_HIGHLIGHT = "singMapSegHighlight"; // 分段路径高亮

    public static final String IGNORE = "ignore"; // 前端忽略不解析展示

    /**
     * 封装TextBtn的map
     *
     * @return
     */
    public static HashMap<String, Object> toStu(String type, Object data, String operateType, Object operateData) {
        HashMap<String, Object> result = new HashMap<>();
        result.put("type", type);
        result.put("data", data);
        if (StringUtils.isNotBlank(operateType)) {
            result.put("operateType", operateType);
            result.put("operateData", operateData);
        }
        return result;
    }

    /**
     * 封装常用的设备TextBtn的map
     *
     * @return
     */
    public static HashMap<String, Object> toDevStu(String type, String devId, String devType, String devName, String operateType, Object operateData) {
        HashMap<String, Object> data = new HashMap<>();
        data.put("devId", devId);
        data.put("devType", devType);
        data.put("devName", devName);

        return toStu(type, data, operateType, operateData);
    }

    /**
     * 封装TextBtn的map
     *
     * @return
     */
    public static HashMap<String, Object> toTextBtnStu(String devId, String devType, String devName, String operateType, Object operateData) {
        return toDevStu(TEXT_BTN, devId, devType, devName, operateType, operateData);
    }

    /**
     * 封装TextBtn的map
     *
     * @return
     */
    public static HashMap<String, Object> toTextBtnStu(String devId, String nodeId, String devType, String devName, String operateType, Object operateData) {
        HashMap<String, Object> devStu = toDevStu(TEXT_BTN, devId, devType, devName, operateType, operateData);
        HashMap<String, Object> data = (HashMap<String, Object>) devStu.get("data");
        data.put("nodeId", nodeId);
        return devStu;
    }

    /**
     * 封装Striking的map
     *
     * @return
     */
    public static HashMap<String, String> toStrikingStu(String value) {
        HashMap<String, String> setMap = new HashMap<>();
        setMap.put("type", STRIKING);
        setMap.put("text", value);
        return setMap;
    }

    /**
     * 获取component组件
     */
    public static HashMap<String, Object> getComponent(String type, Object data) {
        return new HashMap<String, Object>() {{
            put("type", type);
            put("data", data);
        }};
    }

    /**
     * 封装前端忽略类型的map
     *
     * @param data 数据内容
     * @return
     */
    public static HashMap<String, Object> toIgnoreStu(Object data) {
        return toStu(IGNORE, data, null, null);
    }

    public static List<Object> toList(Object... args) {
        List<Object> list = new ArrayList<>();
        for (Object arg : args) {
            list.add(arg);
        }
        return list;
    }

    /**
     * 将 List<Object> 转换为包含 Object 字符串和逗号的新数组
     * 例如：输入 [Obj, Obj, Obj]，输出 [Obj, ",",Obj, ",", Obj]
     */
    public static <T> List<Object> addListPartiStr(List<T> list, String code) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 计算数组大小：每个 Node 对应一个元素，除最后一个 Node 外每个后面加一个逗号
        List<Object> result = new ArrayList();

        for (int i = 0; i < list.size(); i++) {
            // 添加 Node 的字符串表示
            result.add(i * 2, list.get(i));

            // 添加逗号（最后一个 Node 后不需要）
            if (i < list.size() - 1) {
                result.add(i * 2 + 1, code);
            }
        }

        return result;
    }

}
