package com.ruoyi.util.deviceCurve;

import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.constant.DeviceCurveType;
import com.ruoyi.service.device.impl.DeviceCurve;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class SelectDeviceCurveUtil {
    @Autowired
    DeviceCurve deviceCurve;

    /**
     * 查询线路重过载负载率大于阈值的发生时间的方法(查询连续2小时超过阈值的。如果没有连续超过的则返回第一次出现最大负载率的时间)
     *
     * @return
     * @throws Exception
     */
    public HashMap<String, String> selectLoad(Date time, String psrId, Integer threshold, Integer points, String PsrType) throws Exception {
        //获取api方法的参数实体
        Map<String, Object> params = deviceCurve.getStringStringMap(time, psrId, DeviceCurveType.Load, PsrType);

        //获取结果
        String result = deviceCurve.queryEquipPsrData(params);
        if (result != null) {
            //查出负载率的发生时间段或者时间
            List<Double> list = deviceCurve.dataList(result).get("负载率(%)");
            return analyzeLoadRates(list, threshold, points);
        }
        return null;

    }



    /**
     * 查询负载率有没有大于阈值的方法
     *
     * @param feeder
     * @return
     * @throws Exception
     */
    public Boolean selectThreshold(Date defectTime, String feeder, Integer threshold, String PsrType) throws Exception {
        //获取api方法的参数实体
        Map<String, Object> params = deviceCurve.getStringStringMap(defectTime, feeder, DeviceCurveType.Load, PsrType);
        //获取结果
        String result = deviceCurve.queryEquipPsrData(params);
        //查看有没有超过设定阈的方法
        return deviceCurve.dataList(result).get("负载率(%)").stream().anyMatch(value -> value > threshold);

    }

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final int TIME_INTERVAL = 5; // 分钟

    /**
     * 查询超出阈值的连续区间的起始时间，结束时间
     *
     * @param loadRates
     * @param threshold
     * @return
     */
    public static HashMap<String, String> analyzeLoadRates(List<Double> loadRates, Integer threshold, Integer points) {
        if (loadRates == null || loadRates.size() != 288) {
            return null;
        }

        // 1. 查找所有符合条件的连续区间
        List<Interval> qualifiedIntervals = findQualifiedIntervals(loadRates, threshold, points);

        if (!qualifiedIntervals.isEmpty()) {
            // 2. 如果有符合条件的区间，找出最大值最大的区间
            Interval resultInterval = qualifiedIntervals.get(0);
            for (Interval interval : qualifiedIntervals) {
                if (interval.maxValue > resultInterval.maxValue) {
                    resultInterval = interval;
                }
            }
            return formatInterval(resultInterval);
        } else {
            // 3. 如果没有符合条件的区间，找出全局最大值
            return findGlobalMax(loadRates);
        }
    }

    /**
     * 查找所有符合条件的连续区间
     *
     * @param loadRates
     * @param threshold
     * @return
     */
    private static List<Interval> findQualifiedIntervals(List<Double> loadRates, Integer threshold, Integer points) {
        List<Interval> intervals = new ArrayList<>();
        int startIndex = -1;

        for (int i = 0; i < loadRates.size(); i++) {
            if (loadRates.get(i) > threshold) {
                if (startIndex == -1) {
                    startIndex = i;
                }
            } else {
                if (startIndex != -1) {
                    int endIndex = i - 1;
                    if (endIndex - startIndex + 1 >= points) {
                        intervals.add(new Interval(startIndex, endIndex, loadRates));
                    }
                    startIndex = -1;
                }
            }
        }

        // 检查最后一个可能的区间
        if (startIndex != -1 && loadRates.size() - startIndex >= points) {
            intervals.add(new Interval(startIndex, loadRates.size() - 1, loadRates));
        }

        return intervals;
    }

    /**
     * 查询没有连续区间的情况，只查最大值发生时间
     *
     * @param loadRates
     * @return
     */
    private static HashMap<String, String> findGlobalMax(List<Double> loadRates) {
        double maxValue = Double.MIN_VALUE;
        int maxIndex = 0;

        for (int i = 0; i < loadRates.size(); i++) {
            if (loadRates.get(i) > maxValue) {
                maxValue = loadRates.get(i);
                maxIndex = i;
            }
        }

        LocalTime time = indexToTime(maxIndex);
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("maxLoad", String.valueOf(maxValue));
        hashMap.put("maxTime",  formatTime(time.format(TIME_FORMATTER)));
        return hashMap;
    }

    /**
     * 封装查询到的区间的查询结果的属性
     *
     * @param interval
     * @return
     */
    private static HashMap<String, String> formatInterval(Interval interval) {
        LocalTime startTime = indexToTime(interval.startIndex);
        LocalTime endTime = indexToTime(interval.endIndex);
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("maxLoad", String.valueOf(interval.maxValue));
        hashMap.put("startTime", formatTime(startTime.format(TIME_FORMATTER)));
        hashMap.put("endTime",  formatTime(endTime.format(TIME_FORMATTER)));
        hashMap.put("sustainTime", String.valueOf(DoubleFormatter.formatToThreeDecimals1((interval.endIndex - interval.startIndex + 1) * TIME_INTERVAL / 60.0)));
        return hashMap;
    }

    /**
     * 将HH:mm:ss格式的时间字符串转换为时分钟格式
     */
    public static String formatTime(String timeStr) {
        // 使用正则表达式提取小时和分钟
        String[] parts = timeStr.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);

        // 格式化为目标字符串
        StringBuilder formattedTime = new StringBuilder();
        if (hours > 0) {
            formattedTime.append(hours).append("时");
        }
        if (minutes > 0) {
            formattedTime.append(minutes).append("分钟");
        }

        return formattedTime.toString();
    }
    private static LocalTime indexToTime(int index) {
        int totalMinutes = index * TIME_INTERVAL;
        return LocalTime.of(totalMinutes / 60, totalMinutes % 60, 0);
    }

    private static class Interval {
        int startIndex;
        int endIndex;
        double maxValue;

        Interval(int startIndex, int endIndex, List<Double> loadRates) {
            this.startIndex = startIndex;
            this.endIndex = endIndex;
            this.maxValue = calculateMaxValue(startIndex, endIndex, loadRates);
        }

        private double calculateMaxValue(int start, int end, List<Double> loadRates) {
            double max = loadRates.get(start);
            for (int i = start + 1; i <= end; i++) {
                max = Math.max(max, loadRates.get(i));
            }
            return max;
        }
    }
}
