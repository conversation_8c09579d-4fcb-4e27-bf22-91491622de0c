package com.ruoyi.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BufferPolygonCreator {
    private static final double EARTH_RADIUS = 6378137.0; // 地球半径(km)

    /**
     * 将点集扩展为缓冲多边形
     *
     * @param lineStrings 输入的点集(多个LineString)
     * @return 合并后的多边形外围坐标
     */
    public static List<List<Double>> createBufferPolygon(List<List<double[]>> lineStrings, double radio) {
        List<List<Double>> allBuffers = new ArrayList<>();

        // 1. 为每个点创建2km缓冲圆
        for (List<double[]> line : lineStrings) {
            for (double[] point : line) {
                List<List<Double>> circle = createCircle(point[0], point[1], radio);
                allBuffers.addAll(circle);
            }
        }

        // 2. 计算所有缓冲圆的凸包(外围)
        return computeConvexHull(allBuffers);
    }

    /**
     * 创建圆形缓冲区的点集(36个点构成近似圆)
     */
    private static List<List<Double>> createCircle(double lng, double lat, double radius) {
        List<List<Double>> circle = new ArrayList<>();
        for (int i = 0; i < 36; i++) {
            double angle = Math.toRadians(i * 10);
            double[] newPoint = calculateDestinationPoint(lng, lat, angle, radius);
            circle.add(Arrays.asList(newPoint[0], newPoint[1]));
        }
        return circle;
    }

    /**
     * 计算从某点出发指定角度和距离的点(球面计算)
     */
    private static double[] calculateDestinationPoint(double lng, double lat, double angle, double distance) {
        double latRad = Math.toRadians(lat);
        double lngRad = Math.toRadians(lng);

        double angularDistance = distance / EARTH_RADIUS;

        double newLat = Math.asin(Math.sin(latRad) * Math.cos(angularDistance) +
                Math.cos(latRad) * Math.sin(angularDistance) * Math.cos(angle));

        double newLng = lngRad + Math.atan2(
                Math.sin(angle) * Math.sin(angularDistance) * Math.cos(latRad),
                Math.cos(angularDistance) - Math.sin(latRad) * Math.sin(newLat));

        return new double[]{
                Math.toDegrees(newLng),
                Math.toDegrees(newLat)
        };
    }

    /**
     * 计算点集的凸包
     */
    private static List<List<Double>> computeConvexHull(List<List<Double>> points) {
        if (points.size() <= 1) return new ArrayList<>(points);

        // 排序点集
        points.sort((a, b) -> {
            int cmp = Double.compare(a.get(0), b.get(0));
            return cmp != 0 ? cmp : Double.compare(a.get(1), b.get(1));
        });

        List<List<Double>> hull = new ArrayList<>();

        // 构建下凸包
        for (List<Double> p : points) {
            while (hull.size() >= 2 && cross(hull.get(hull.size() - 2), hull.get(hull.size() - 1), p) <= 0) {
                hull.remove(hull.size() - 1);
            }
            hull.add(p);
        }

        // 构建上凸包
        int t = hull.size() + 1;
        for (int i = points.size() - 1; i >= 0; i--) {
            List<Double> p = points.get(i);
            while (hull.size() >= t && cross(hull.get(hull.size() - 2), hull.get(hull.size() - 1), p) <= 0) {
                hull.remove(hull.size() - 1);
            }
            hull.add(p);
        }

        hull.remove(hull.size() - 1); // 移除重复点
        return hull;
    }

    /**
     * 计算叉积
     */
    private static double cross(List<Double> a, List<Double> b, List<Double> c) {
        return (b.get(0) - a.get(0)) * (c.get(1) - a.get(1)) - (b.get(1) - a.get(1)) * (c.get(0) - a.get(0));
    }
    // 使用标准Web墨卡托参数
    private static final double MAX_EXTENT = 20037508.342789244;

    // 保留15位小数
    private static final DecimalFormat df = new DecimalFormat("0.###############");

    /**
     * 经纬度抓为魔卡托
     * @param points 经纬度列表，每个元素是[经度, 纬度]
     * @return 空格分隔的墨卡托坐标字符串
     */
    public static String convertToMercator(List<List<Double>> points) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < points.size(); i++) {
            List<Double> point = points.get(i);
            if (point.size() < 2) continue;
            double[] mercator = strictMercator(point.get(0), point.get(1));

            result.append(df.format(mercator[0]))
                    .append(" ")
                    .append(df.format(mercator[1]));

            if (i < points.size() - 1) {
                result.append(" ");
            }
        }
        return result.toString();
    }

    /**
     * 墨卡托投影算法
     */
    private static double[] strictMercator(double lon, double lat) {
        // 经度直接线性变换
        double x = lon * MAX_EXTENT / 180.0;
        // 纬度变换
        double y = Math.log(Math.tan(Math.PI * 0.25 + Math.toRadians(lat) * 0.5)) * EARTH_RADIUS;
        return new double[]{x, y};
    }

}
