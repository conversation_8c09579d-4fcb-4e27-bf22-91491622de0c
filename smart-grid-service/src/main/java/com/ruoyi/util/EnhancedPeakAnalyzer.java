package com.ruoyi.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 高峰分析
 */
public class EnhancedPeakAnalyzer {



    /**
     * 增强版高峰时段分析
     * @param timeList 时间列表（5分钟间隔）
     * @param loadList 负荷数据列表
     * @param threshold 负荷阈值
     * @param minDurationMinutes 最小持续时间(分钟)
     * @return 高峰时段分析结果
     */
    public static List<PeakPeriod> analyze(List<String> timeList,
                                           List<Double> loadList,
                                           double threshold,
                                           int minDurationMinutes) {

        List<TimeSegment> rawSegments = new ArrayList<>();

        // 1. 检测原始高峰段
        boolean inPeak = false;
        LocalDateTime segmentStart = null;
        int currentExceedCount = 0;
        double currentSum = 0;

        for (int i = 0; i < loadList.size(); i++) {
            LocalDateTime currentTime = parseTime(timeList.get(i));
            double currentValue = loadList.get(i);

            if (currentValue >= threshold) {
                if (!inPeak) {
                    // 开始新高峰段
                    inPeak = true;
                    segmentStart = currentTime;
                    currentExceedCount = 1;
                    currentSum = currentValue;
                } else {
                    // 累计当前高峰段
                    currentExceedCount++;
                    currentSum += currentValue;
                }
            } else {
                if (inPeak) {
                    // 结束当前高峰段
                    inPeak = false;
                    saveSegment(segmentStart, currentTime,
                               currentExceedCount, currentSum,
                               minDurationMinutes, rawSegments);
                }
            }
        }

        // 处理最后一段可能的高峰
        if (inPeak) {
            LocalDateTime endTime = parseTime(timeList.get(timeList.size() - 1));
            saveSegment(segmentStart, endTime,
                      currentExceedCount, currentSum,
                      minDurationMinutes, rawSegments);
        }

        // 2. 合并相邻高峰段
        return mergeSegments(rawSegments, 30); // 合并间隔<30分钟的相邻段
    }

    private static void saveSegment(LocalDateTime start, LocalDateTime end,
                                  int exceedCount, double sum,
                                  int minDuration,
                                  List<TimeSegment> segments) {
        long durationMinutes = ChronoUnit.MINUTES.between(start, end);
        if (durationMinutes >= minDuration) {
            segments.add(new TimeSegment(
                start, end, exceedCount, sum / exceedCount));
        }
    }

    private static List<PeakPeriod> mergeSegments(List<TimeSegment> segments,
                                                int maxGapMinutes) {
        if (segments.isEmpty()) {
            return Collections.emptyList();
        }

        List<PeakPeriod> merged = new ArrayList<>();
        TimeSegment current = segments.get(0);

        for (int i = 1; i < segments.size(); i++) {
            TimeSegment next = segments.get(i);
            if (current.end.plusMinutes(maxGapMinutes).isAfter(next.start)) {
                // 合并时段
                current.end = next.end;
                current.exceedCount += next.exceedCount;
                current.avgValue = (current.avgValue * (current.exceedCount - next.exceedCount) +
                                   next.avgValue * next.exceedCount) / current.exceedCount;
            } else {
                addMergedSegment(current, merged);
                current = next;
            }
        }
        addMergedSegment(current, merged);

        return merged;
    }

    private static void addMergedSegment(TimeSegment segment,
                                       List<PeakPeriod> results) {
        double durationHours = ChronoUnit.MINUTES.between(segment.start, segment.end) / 60.0;

        // 格式化日期时间（同一天显示为"yyyy-MM-dd HH:mm-HH:mm"，跨天显示完整日期）
        String dateStr = segment.start.toLocalDate().toString();
        String timeWindow;
        if (segment.start.toLocalDate().equals(segment.end.toLocalDate())) {
            timeWindow = String.format("%s %s-%s",
                    dateStr,
                    segment.start.toLocalTime(),
                    segment.end.toLocalTime());
        } else {
            timeWindow = String.format("%s %s - %s %s",
                    segment.start.toLocalDate(),
                    segment.start.toLocalTime(),
                    segment.end.toLocalDate(),
                    segment.end.toLocalTime());
        }

        results.add(new PeakPeriod(
                timeWindow,
                durationHours,
                segment.exceedCount,
                segment.avgValue));
    }

    private static LocalDateTime parseTime(String timeStr) {
        return LocalDateTime.parse(timeStr,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    // 辅助类，用于内部处理
    private static class TimeSegment {
        LocalDateTime start;
        LocalDateTime end;
        int exceedCount;
        double avgValue;

        TimeSegment(LocalDateTime start, LocalDateTime end,
                   int exceedCount, double avgValue) {
            this.start = start;
            this.end = end;
            this.exceedCount = exceedCount;
            this.avgValue = avgValue;
        }
    }

}
