package com.ruoyi.util;

import com.ruoyi.entity.power.Load;
import com.ruoyi.entity.power.enuw.TimeEnum288;
import com.ruoyi.entity.power.enuw.TimeEnum96;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * 电力负荷数据扩展工具类
 * 功能：将原始7天的5分钟间隔负荷数据扩展到半年数据
 */
public class LoadDataExpander {

    // 时间格式化器（假设时间格式为"yyyy-MM-dd HH:mm:ss"）
    private static final DateTimeFormatter TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 每天的标准数据点数（5分钟间隔）
    private static final int POINTS_PER_DAY = 288; // 24h * 60min / 5min = 288

    // 半年的天数（约182天）
    private static final int DAYS_IN_HALF_YEAR = 182;

    /**
     * 扩展负荷数据到半年范围
     *
     * @param originalTimes 原始时间列表（至少包含7天数据）
     * @param originalLoads 原始负荷值列表（与时间列表一一对应）
     * @return 包含扩展后数据的LoadData对象
     * @throws IllegalArgumentException 如果输入数据不合法
     */
    public static Load expandToHalfYear(List<String> originalTimes, List<Double> originalLoads) {
        // 1. 参数校验


        // 2. 创建结果集合（初始容量预计算优化）
        int totalPoints = DAYS_IN_HALF_YEAR * POINTS_PER_DAY;
        List<String> expandedTimes = new ArrayList<>(totalPoints);
        List<Double> expandedLoads = new ArrayList<>(totalPoints);

        // 3. 解析最早的时间点
        LocalDateTime baseTime = LocalDateTime.parse(originalTimes.get(0), TIME_FORMATTER);

        // 4. 计算需要生成的周数（182天 ≈ 26周，已有1周数据）
        int weeksToGenerate = 25;

        // 5. 扩展数据（按周循环)
        for (int week = 0; week <= weeksToGenerate; week++) {
            // 计算当前周的基准时间（向前推移）
            LocalDateTime weekStart = baseTime.minusWeeks(week);

            // 生成当前周的数据（每天288个点）
            for (int day = 0; day < 7; day++) {
                Random random = new Random();
                int randomInt = random.nextInt(10) + 1;
                for (int point = 0; point < POINTS_PER_DAY; point++) {
                    // 计算当前时间点
                    LocalDateTime currentTime = weekStart
                            .plusDays(day)
                            .plusMinutes(5L * point);

                    // 获取对应的负荷值（循环使用原始数据）
                    int originalIndex = (day * POINTS_PER_DAY + point) % originalLoads.size();
                    double loadValue = originalLoads.get(originalIndex);

                    // 添加到结果集
                    expandedTimes.add(currentTime.format(TIME_FORMATTER));


                    expandedLoads.add(loadValue + randomInt);


                }
            }
        }

        return new Load(expandedTimes, expandedLoads);
    }


    public static Load expandToHalfYear(List<String> originalTimes, List<Double> originalLoads, Integer inputDays) {

        // 2. 准备结果集合
        List<String> expandedTimes = new ArrayList<>();
        List<Double> expandedLoads = new ArrayList<>();

        // 3. 解析最早的时间点
        LocalDateTime baseTime = LocalDateTime.parse(originalTimes.get(0), TIME_FORMATTER);

        // 4. 计算需要生成的总天数
        int totalDaysToGenerate = DAYS_IN_HALF_YEAR;
        int daysAlreadyHave = inputDays;
        int daysToAdd = totalDaysToGenerate - daysAlreadyHave;

        // 5. 先添加原始数据
        expandedTimes.addAll(originalTimes);
        expandedLoads.addAll(originalLoads);

        // 6. 扩展剩余天数的数据
        for (int day = 1; day <= daysToAdd; day++) {
            // 计算当前天的基准时间（向前推移）
            LocalDateTime dayStart = baseTime.minusDays(day);

            // 复制模式数据（循环使用输入数据）
            int patternDay = day % inputDays; // 选择要复制的模式日
            Random random = new Random();
            int randomInt = random.nextInt(10) + 1;

            for (int point = 0; point < POINTS_PER_DAY; point++) {
                int originalIndex = patternDay * POINTS_PER_DAY + point;
                if (originalIndex >= originalLoads.size()) {
                    originalIndex %= originalLoads.size(); // 确保不越界
                }

                // 创建新时间点
                LocalDateTime newTime = dayStart.plusMinutes(5L * point);

                // 添加到结果集
                expandedTimes.add(0, newTime.format(TIME_FORMATTER)); // 添加到开头
              if(originalLoads.get(originalIndex)== null){
                  expandedLoads.add(0, 0.0 );
              }else {
                  expandedLoads.add(0, originalLoads.get(originalIndex) + randomInt);
              }



            }
        }

        return new Load(expandedTimes, expandedLoads);
    }

    /**
     * 输入参数验证
     */
    private static void validateInput(List<String> times, List<Double> loads) {
        Objects.requireNonNull(times, "时间列表不能为null");
        Objects.requireNonNull(loads, "负荷列表不能为null");

        if (times.size() != loads.size()) {
            throw new IllegalArgumentException("时间和负荷列表长度必须相同");
        }

        if (times.size() < 7 * POINTS_PER_DAY) {
            throw new IllegalArgumentException("输入数据至少需要包含7天的完整数据");
        }

        try {
            LocalDateTime.parse(times.get(0), TIME_FORMATTER);
        } catch (Exception e) {
            throw new IllegalArgumentException("时间格式必须为yyyy-MM-dd HH:mm:ss", e);
        }
    }

    /**
     * 负荷数据持有类
     */


    // ============== 使用示例 ==============
    public static void main(String[] args) {
        TimeEnum288 timeEnum288 = TimeEnum288.fromIndex(255);  // 直接使用枚举常量
        String timeStr = timeEnum288.getTimeString();  // 获取"08:30"
        System.out.println(timeStr);


        System.out.println( TimeEnum96.getTime(95)); // 直接使用枚举常量);

    }
}
