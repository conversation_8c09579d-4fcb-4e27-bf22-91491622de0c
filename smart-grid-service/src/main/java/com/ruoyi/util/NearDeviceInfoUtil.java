package com.ruoyi.util;

import com.ruoyi.entity.device.DevInfo;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.vo.BusbarSwitchVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class NearDeviceInfoUtil {
    public static List<String> getAllFeederIds(List<NearbyDeviceInfoVo> devices) {
        // 当前设备的所有线路ID
        List<String> feederIds = new ArrayList<>();
        for (NearbyDeviceInfoVo device : devices) {
            feederIds.addAll(device.getFeederIds());
        }
        // 去重
        return ListUtils.distinctByKey(feederIds, d -> d);
    }

    /**
     * 根据线路ID集合 过滤设备
     *
     * @param devices         需要过滤掉设备集合
     * @param filterFeederIds 需要排除掉线路ID集合
     * @return
     */
    public static List<NearbyDeviceInfoVo> filterByFeederIds(List<NearbyDeviceInfoVo> devices, List<String> filterFeederIds) {
        Set<String> excludeSet = new HashSet<>(filterFeederIds);
        // 去重
        return devices.parallelStream().filter(dev ->
                // 存在线路 并且 不存在排除的线路ID集合
                !CollectionUtils.isEmpty(dev.getFeederIds()) && dev.getFeederIds().stream().noneMatch(excludeSet::contains)
        ).collect(Collectors.toList());
    }

    /**
     * 由于 nearDev 的线路ID和名称是有可能多个拼接， 要获取当前间隔对应的线路，通过这个方法取
     * （目前这样判断不太对，后续再优化优化）
     * （环网柜有多个联络开关对应的联络线才会出现这种情况，到时候应该看当前环网柜的当前剩余间隔由那条线供电的）
     *
     * @param nearDev        附近设备
     * @param contactDevInfo 需要剩余间隔开关
     * @return
     */
    public static String[] getFeeder(NearbyDeviceInfoVo nearDev, DevInfo contactDevInfo) {
        String feederId, feederName;
        List<String> feederIds = nearDev.getFeederIds();
        List<String> feederNames = nearDev.getFeederNames();
        if (feederIds.size() == 1) {
            feederId = feederIds.get(0);
            feederName = feederNames.get(0);
        } else {
            if (StringUtils.isNotBlank(contactDevInfo.getFeederId())) {
                feederId = contactDevInfo.getFeederId();
                feederName = contactDevInfo.getFeederName();
                if (StringUtils.isBlank(feederName)) {
                    feederName = feederNames.get(feederIds.indexOf(feederId));
                }
            } else {
                // 这样可能不对
                feederId = feederIds.get(feederIds.size() - 1);
                feederName = feederNames.get(feederNames.size() - 1);
            }
        }

        return new String[]{feederId, feederName};
    }

    /**
     * 获取剩余可以的间隔（多个我们就取第一个即可）
     */
    public static BusbarSwitchVo getStationCanBay(NearbyDeviceInfoVo nearDev) {
        List<BusbarSwitchVo> busbarSwitchVoList = nearDev.getBusbarSwitchVoList();
        if (CollectionUtils.isEmpty(busbarSwitchVoList)) {
            return null;
        }
        List<BusbarSwitchVo> remainingBays = busbarSwitchVoList.stream().filter(BusbarSwitchVo::getIsSpare).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(remainingBays)) {
            return null;
        }
        return remainingBays.get(0);
    }

    /**
     * 获取当前附近设备联络点的 联络设备
     * 杆塔：杆塔本身
     * 环网柜和开关：剩余的间隔
     */
    public static DevInfo getContactDevInfo(NearbyDeviceInfoVo nearDev) {
        DevInfo devInfo = null;
        if (nearDev.isPole()) {
            devInfo = new DevInfo(nearDev.getPsrId(), nearDev.getPsrType(), nearDev.getPsrName());
        } else if (nearDev.isContactStation()) {
            // 剩余间隔
            BusbarSwitchVo canBay = getStationCanBay(nearDev);
            if (canBay != null) {
                devInfo = new DevInfo(canBay.getSwitchId(), canBay.getSwitchType(), canBay.getSwitchName());
                devInfo.setStationPsrId(nearDev.getPsrId());
                devInfo.setStationPsrType(nearDev.getPsrType());
                devInfo.setStationPsrName(nearDev.getPsrName());
            }
        }
        if (devInfo != null) {
            String[] feeder = getFeeder(nearDev, devInfo);

            // 线路问题
            devInfo.setFeederId(feeder[0]);
            devInfo.setFeederName(feeder[1]);
            if (devInfo.getLngLat() == null) {
                devInfo.setLngLat(nearDev.getLngLat());
            }
        }
        return devInfo;
    }
}
