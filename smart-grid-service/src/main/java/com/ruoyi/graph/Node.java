package com.ruoyi.graph;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.BdzNewLineInfo;
import com.ruoyi.entity.plan.bo.ReplaceBayNodeProp;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 节点（设备和线路也使用改节点类型）
 */
@Data
public class Node {

    public Node(String id) {
        this.id = id;
    }

    public Node(String id, Geometry geometry) {
        this.id = id;
        this.geometry = geometry;
    }

    public Node(String id, String psrId, String psrType) {
        this.id = id;
        this.psrId = psrId;
        this.psrType = psrType;
    }

    public Node(String id, String psrId, String psrType, String psrName, boolean isEdge, String type, String lineType, String shapeKey) {
        this.id = id;
        this.psrId = psrId;
        this.psrType = psrType;
        this.psrName = psrName;
        this.isEdge = isEdge;
        this.type = type;
        this.lineType = lineType;
        this.shapeKey = shapeKey;
    }

    /**
     * 唯一ID
     */
    private String id;

    /**
     * 唯一ID 电网设备ID
     */
    private String psrId;

    /**
     * 唯一ID 电网设备ID
     */
    private String psrType;

    /**
     * 设备名称
     */
    private String psrName;

    /**
     * 是否为边
     */
    private boolean isEdge = false;


    /**
     * 子节点和连接的边集合
     */
    private List<Node> children, edges = new ArrayList<>();

    /**
     * 父节点
     */
    private Node parent;

    /**
     * 当前的节点类型 详情参考 TYPE_PSR 、 TYPE_SELF
     */
    private String type = TYPE_PSR;

    /**
     * 边的线型
     */
    private String lineType;

    /**
     * 渲染key
     */
    private String shapeKey = NodeConstants.SHAPE_KEY_PSR;

    /**
     * 坐标对象
     */
    private Geometry geometry;

    /**
     * 自定义属性值
     */
    private HashMap<String, Object> properties;

    /**
     * 源节点和目标节点
     */
    private Node source, target;

    /**
     * 容量
     */
    private double cap = 0;

    /**
     * 负荷大小
     */
    private double load = 0;

    /**
     * 公转变标识
     */
    private String pubPrivFlag;

    /**
     * 电网拓扑的节点
     */
    public static final String TYPE_PSR = "typePsr";

    /**
     * 自己自定义的节点
     */
    public static final String TYPE_SELF = "typeSelf";


    private Ports ports;

    /**
     * 起始连接port
     */
    private String sourcePort;

    /**
     * 结束连接port
     */
    private String targetPort;

    /**
     * 移除父节点
     */
    public void removeParent() {
        if (this.parent != null) {
            this.parent.removeChild(this);
        }
    }

    /**
     * 增加子级
     *
     * @param child 子级节点
     * @param index 插入的下标
     */
    public void addChild(Node child, int index) {
        if (children == null) {
            children = new ArrayList<>();
        }
        if (getChildIndex(child) == -1) {
            if (index == -1) {
                children.add(child);
            } else {
                children.add(index, child);
            }
            child.removeParent();
            child.setParent(this);
        }
    }

    /**
     * 移除子级
     *
     * @param node 子级节点
     */
    public void removeChild(Node node) {
        if (children == null || children.isEmpty()) {
            int index = getChildIndex(node);
            if (index > -1) {
                this.children.get(index).setParent(null);
                this.children.remove(node);
            }
        }
    }

    /**
     * 获取当前子级的下标
     *
     * @param child 子节点
     */
    public int getChildIndex(Node child) {
        return children != null ? children.indexOf(child) : -1;
    }

    /**
     * 获取边在节点的下标
     */
    public int getEdgeIndex(Node edge) {
        return edges != null ? edges.indexOf(edge) : -1;
    }

    /**
     * 获取子节点大小
     */
    public int getChildCount() {
        return children != null ? children.size() : 0;
    }

    /**
     * 获取所以子节点  （深度递归获取）
     */
    public List<Node> getAllChildList() {
        List<Node> result = new ArrayList<>();
        loopKeepChild(this, result);
        return result;
    }

    /**
     * 深度递归并保存子节点
     *
     * @param node   需要递归的节点
     * @param result 返回到集合
     */
    public void loopKeepChild(Node node, List<Node> result) {
        if (CollectionUtils.isNotEmpty(node.getChildren())) {
            for (Node child : node.getChildren()) {
                if (child == null) {
                    continue;
                }
                result.add(child);
                loopKeepChild(child, result);
            }
        }
    }

    /**
     * 设置连接节点
     * 获取链接节点
     *
     * @param isSource true: 表示原节点, false 表示目标节点
     */
    public Node getLink(boolean isSource) {
        return isSource == true ? source : target;
    }

    /**
     * 设置连接节点
     *
     * @param node     需要
     * @param isSource true: 表示原节点, false 表示目标节点
     * @return 返回担心设置的节点
     */
    public Node setLink(Node node, boolean isSource) {
        if (isSource) {
            this.setSource(node);
        } else {
            this.setTarget(node);
        }
        return node;
    }

    /**
     * 移除连接节点
     *
     * @param isSource true: 表示原节点, false 表示目标节点
     * @return 返回担心设置的节点
     */
    public void removeLink(boolean isSource) {
        Node node = getLink(isSource);
        if (node != null) {
            node.removeEdge(this, isSource);
        }
    }

    /**
     * 移除连接节点
     *
     * @return 返回担心设置的节点
     */
    public void removeLink(Node node) {
        boolean isSource = source != null && source.equals(node);
        removeLink(isSource);
    }

    /**
     * 当前节点新增连接线边
     *
     * @param edge     需要新增的边节点
     * @param isSource isSource  true：表示获取开始的，false：表示获取尾部的
     */
    public void addEdge(Node edge, boolean isSource) {
        if (edge == null) {
            return;
        }
        // 之前边 连接节点更改
        edge.removeLink(isSource); // 移除之前
        edge.setLink(this, isSource); // 设置新的
        // edges插入
        int index = this.getEdgeIndex(edge);
        if (index == -1) {
            edges.add(edge);
        }
    }

    /**
     * 移除连接线边
     *
     * @param edge     需要新增的边节点
     * @param isSource isSource  true：表示获取开始的，false：表示获取尾部的
     */
    public void removeEdge(Node edge, boolean isSource) {
        int index = getEdgeIndex(edge);
        if (index > -1) {
            edges.remove(index);
        }

        // 该条边也要删除对应节点
        edge.setLink(null, isSource);
    }

    /**
     * 移除连接线边
     */
    public void removeEdge(Node edge) {
        boolean isSource = edge.getSource() != null && edge.getSource().equals(this);
        removeEdge(edge, isSource);
    }

    /**
     * 如果当前连接的边超过三个表示分叉了
     */
    public boolean isBranch() {
        return !isEdge() && CollectionUtils.isNotEmpty(edges) && edges.size() > 2;
    }

    @Override
    public String toString() {
        return "Node{" +
                "id='" + id + '\'' +
                ", psrId='" + psrId + '\'' +
                ", psrType='" + psrType + '\'' +
                ", psrName='" + psrName + '\'' +
                ", isEdge=" + isEdge +
                ", type='" + type + '\'' +
                '}';
    }

    /**
     * 判断开关设备
     *
     * @param type in：表示是否站内开关  out：站外开关  "all"或者否则 只要是开关的开关
     */
    public boolean isKg(String type) {
        if (psrType == null) {
            return false;
        }
        if (StringUtils.equals(type, "in")) {
            return DeviceConstants.KG_IN_TYPES.contains(psrType);
        } else if (StringUtils.equals(type, "out")) {
            return DeviceConstants.KG_OUT_TYPES.contains(psrType);
        } else {
            return DeviceConstants.KG_TYPES.contains(psrType);
        }
    }

    /**
     * 判断配变设备
     */
    public boolean isPb() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.PB_TYPES.contains(psrType);
    }

    /**
     * 判断母线设备
     */
    public boolean isBus() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.BUS_TYPES.contains(psrType);
    }


    /**
     * 判断是否杆塔
     */
    public boolean isPole() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.POLE_TYPES.contains(psrType);
    }

    /**
     * 判断是否电缆中间头 0202（输电电缆终端头）、0203（输电电缆中间接头）
     */
    public boolean isJunction() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.JUNCTION_TYPES.contains(psrType);
    }

    /**
     * 具备线路联络的站房类型
     */
    public boolean isHwg() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.HWG_TYPES.contains(psrType);
    }

    /**
     * 判断是否环网柜
     */
    public boolean isContactStation() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.CONTACT_STATION_TYPES.contains(psrType);
    }

    public boolean isContactStationInNode() {
        Node parent = this.getParent();
        return parent != null && parent.isContactStation();
    }

    /**
     * 是否电网设备
     */
    public boolean isPsrNode() {
        return StringUtils.equals(type, TYPE_PSR);
    }

    /**
     * 是否导线段
     */
    public boolean isSegFeeder() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.SEG_FEEDER_TYPES.contains(psrType);
    }

    /**
     * 中压用户接入点
     */
    public boolean isUserPoint() {
        if (psrType == null) {
            return false;
        }
        return DeviceConstants.USER_POINT_TYPES.contains(psrType);
    }

    public boolean equals(String otherId) {
        return StringUtils.equals(id, otherId);
    }

    public boolean equals(Node node) {
        return this == node;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Node node = (Node) o;
        return equals(node.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    /**
     * 获取设备坐标
     */
    public double[] toDeviceCoords() {
        if (geometry == null) {
            return null;
        }
        Coordinate coordinate = geometry.getCoordinate();
        return new double[]{coordinate.getX(), coordinate.getY()};
    }

    @Override
    public Node clone() {
        Node node = new Node(this.id, this.psrId, this.psrType);
        node.setPsrName(this.psrName);
        node.setEdge(this.isEdge());
        node.setType(this.type);
        node.setShapeKey(this.shapeKey);
        node.setLineType(this.lineType);
        node.setSourcePort(this.sourcePort);
        node.setGeometry(this.geometry != null ? this.geometry.copy() : null);

        node.setChildren(new ArrayList<>());
        node.setEdges(new ArrayList<>());
        node.setParent(null);
        node.setSource(null);
        node.setTarget(null);
        // TODO 这里properties需要深拷贝
        node.setProperties(this.properties != null ? new HashMap<>(this.properties) : null);
        return node;
    }

    public boolean isTmpLink() {
        return id.startsWith("link_");
    }

    public HashMap<String, Object> putProperties(String key, Object value) {
        // 空对象处理
        if (properties == null) {
            properties = new HashMap<>();
        }
        properties.put(key, value);
        return properties;
    }

    // =========================== 修改相关电网设置值 =======================
    public void setStation(String psrId, String psrType, String psrName) {
        this.putProperties("stationPsrId", psrId);
        this.putProperties("stationPsrType", psrType);
        this.putProperties("stationName", psrName);
    }

    public String[] getStation() {
        if (properties == null) {
            return null;
        }
        String stationPsrId = (String) properties.getOrDefault("stationPsrId", "");
        if (StringUtils.isBlank(stationPsrId)) {
            return null;
        }
        return new String[]{
                stationPsrId,
                (String) properties.get("stationPsrType"),
                (String) properties.get("stationName")};
    }

    /**
     * 设置开关的 分闸 和 合闸
     */
    public void setSwitchOpen(boolean switchOpen) {
        putProperties("switchOpen", switchOpen);
        if (StringUtils.isBlank((String) getProperties().getOrDefault("psrName", ""))) {
            putProperties("psrName", psrName);
        }
    }

    /**
     * 获取开关的 分闸 和 合闸
     */
    public Boolean getSwitchOpen() {
        if (properties == null || !properties.containsKey("switchOpen")) {
            return null;
        }

        return (Boolean) properties.get("switchOpen");
    }

    /**
     * 設置變電站新出綫
     */
    public void setBdzNewLine(BusbarSwitchVo busKg) {
        putProperties("isBdzNew", true);

        putProperties("busPsrId", busKg.getBusbarId());
        putProperties("busPsrName", busKg.getBusbarName());

        putProperties("bdzPsrId", busKg.getStationPsrId());
        putProperties("bdzPsrName", busKg.getStationPsrName());

        putProperties("bayKgPsrId", busKg.getSwitchName());
        putProperties("bayKgPsrName", busKg.getSwitchName());
    }

    /**
     * 获取变电站新出线
     */
    public BdzNewLineInfo getBdzNewLine() {
        if (properties == null) {
            return null;
        }

        Boolean isBdzNew = (Boolean) properties.getOrDefault("isBdzNew", false);
        if (!isBdzNew) {
            return null;
        }

        String busPsrId = (String) properties.getOrDefault("busPsrId", "");
        String busPsrName = (String) properties.getOrDefault("busPsrName", "");
        String bdzPsrId = (String) properties.getOrDefault("bdzPsrId", "");
        String bdzPsrName = (String) properties.getOrDefault("bdzPsrName", "");
        String bayKgPsrName = (String) properties.getOrDefault("bayKgPsrName", "");
        String bayKgPsrId = (String) properties.getOrDefault("bayKgPsrId", "");
        return new BdzNewLineInfo(busPsrId, busPsrName, bdzPsrId, bdzPsrName, bayKgPsrId, bayKgPsrName);
    }

    /**
     * 设置联络线
     */
    public void setContactFeeder(String feederId, String feederName) {
        putProperties("contactFeederId", feederId);
        putProperties("contactFeederName", feederName);
    }

    /**
     * 获取联络线
     */
    public String[] getContactFeeder() {
        if (properties == null) {
            return null;
        }
        String contactFeederId = (String) properties.getOrDefault("contactFeederId", "");
        String contactFeederName = (String) properties.getOrDefault("contactFeederName", "");
        if (StringUtils.isBlank(contactFeederId)) {
            return null;
        }
        return new String[]{contactFeederId, contactFeederName};
    }

    /**
     * 移除当前电网设备
     */
    public void setRemoveNode() {
        putProperties("remove", true);
    }

    /**
     * 是否移除当前设备
     */
    public Boolean isRemoveNode() {
        if (properties == null || !properties.containsKey("remove")) {
            return false;
        }
        return (Boolean) properties.getOrDefault("remove", false);
    }

    /**
     * 更换间隔
     */
    public void setReplaceBayNode(String replaceFeederId, String replaceFeederName, BusbarSwitchVo sourceBusKg, BusbarSwitchVo replaceBusKg) {
        putProperties("replaceBay", true);
        putProperties("psrType", this.getPsrType());
        putProperties("psrName", StringUtils.isBlank(replaceFeederName) ? replaceFeederId : replaceFeederName);

        putProperties("sourceKgId", sourceBusKg.getSwitchId());
        putProperties("sourceKgName", sourceBusKg.getSwitchName());
        putProperties("sourceBusId", sourceBusKg.getBusbarId());
        putProperties("sourceBusName", sourceBusKg.getBusbarName());

        putProperties("replaceKgId", replaceBusKg.getSwitchId());
        putProperties("replaceKgName", replaceBusKg.getSwitchName());
        putProperties("replaceBusId", replaceBusKg.getBusbarId());
        putProperties("replaceBusName", replaceBusKg.getBusbarName());

        putProperties("replaceFeederId", replaceFeederId);
        putProperties("replaceFeederName", replaceFeederName);
    }

    /**
     * 更换间隔
     */
    public ReplaceBayNodeProp getReplaceBayNode() {
        if (properties == null) {
            return null;
        }

        Boolean replaceBay = (Boolean) properties.getOrDefault("replaceBay", false);
        if (!replaceBay) {
            return null;
        }
        String sourceKgId = (String) properties.getOrDefault("sourceKgId", "");
        String sourceKgName = (String) properties.getOrDefault("sourceKgName", "");
        String sourceBusId = (String) properties.getOrDefault("sourceBusId", "");
        String sourceBusName = (String) properties.getOrDefault("sourceBusName", "");

        String replaceKgId = (String) properties.getOrDefault("replaceKgId", "");
        String replaceKgName = (String) properties.getOrDefault("replaceKgName", "");
        String replaceBusName = (String) properties.getOrDefault("replaceBusName", "");
        String replaceBusId = (String) properties.getOrDefault("replaceBusId", "");

        String replaceFeederId = (String) properties.getOrDefault("replaceFeederId", "");
        String replaceFeederName = (String) properties.getOrDefault("replaceFeederName", "");

        return new ReplaceBayNodeProp(sourceKgId, sourceKgName, sourceBusId, sourceBusName, replaceKgId, replaceKgName, replaceBusId, replaceBusName, replaceFeederId, replaceFeederName);
    }


    /**
     * 设置运放调整的另一个节点
     */
    public void setRunAdjustAnother(String anotherPsrId) {
        putProperties("runAdjustAnotherPsrId", anotherPsrId);
    }

    /**
     * 获取运放调整的另一个节点
     */
    public String getRunAdjustAnother() {
        if (properties == null) {
            return null;
        }
        return (String) properties.getOrDefault("runAdjustAnotherPsrId", "");
    }

    public static void main(String[] args) {
        HashMap<String, Object> map = new HashMap<>();
        Boolean remove = (Boolean) map.get("remove");
        System.out.println(remove);
        if (remove != null && remove) {
            System.out.println("dd");
        }

    }
}

