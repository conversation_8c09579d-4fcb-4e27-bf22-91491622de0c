package com.ruoyi.graph.utils;

import com.ruoyi.graph.Node;
import org.locationtech.jts.geom.*;

import java.util.Arrays;
import java.util.HashMap;

/**
 * 节点工厂
 */
public class NodeFactory {


    public static Node createDevice(String id, double lng, double lat) {
        GeometryFactory geometryFactory = new GeometryFactory();

        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));
        Node node = new Node(id, point);
        node.setEdge(false);

        return node;
    }

    public static Node createEdge(String id, double[][] coords) {
        GeometryFactory geometryFactory = new GeometryFactory();

        Coordinate[] collectList = Arrays.stream(coords)
                .map(lngLat -> new Coordinate(lngLat[0], lngLat[1]))
                .toArray(Coordinate[]::new);
        LineString lineString = geometryFactory.createLineString(collectList);
        Node node = new Node(id, lineString);
        node.setEdge(true);

        return node;
    }

    static public Node createNode(String id, String psrId, String psrType) {
        Node node = new Node(id, psrId, psrType);
        node.setProperties(new HashMap<String, Object>() {{
            put("psrId", psrId);
            put("psrType", psrType);
        }});
        return node;
    }

    static public Node createNode(String id, Geometry geometry) {
        return new Node(id, geometry);
    }

}


