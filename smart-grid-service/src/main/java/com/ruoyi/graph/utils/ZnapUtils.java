package com.ruoyi.graph.utils;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.graph.Node;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * znap工具类
 */
public class ZnapUtils {

    /**
     * 根据设备类型和设备ID 获取znap专用的设备ID
     */
    public static String getZnapPsrId(String psrId, String psrType) {
        return "PD_" + psrType + "_" + psrId;
    }

    public static String getDkxPsrId(String psrId) {
        return getZnapPsrId(psrId, "dkx");
    }

    public static String parsePsrId(String znapPsrId) {
        String[] strings = parsePsrStr(znapPsrId);
        if (strings == null) {
            return null;
        }
        return strings[1];
    }

    /**
     * 解析字符串并提取目标数字
     * 解析这样的字符串"PD_0307_14000670085006" 、 "PD_0307_03072@14000670085006@1"、
     * "PD_0307_14000670085006"、"PD_0307_0307_14000670085006@1"
     * 并且返回【0307_14000720297092】
     */
    public static String[] parsePsrStr(String psrStr) {
        if (StringUtils.isBlank(psrStr)) return null;
        // 按_分割字符串
        String[] parts = psrStr.split("_");

        if (parts.length >= 3) {
            String prefixCode = parts[1]; // 第二个部分是前缀码
            String lastPart = parts[parts.length - 1];

            // 精确提取动态编码
            String dynamicCode = extractPreciseDynamicCode(lastPart);

            if (dynamicCode != null) {
                return new String[]{
                        prefixCode,
                        dynamicCode
                };
            }
        }
        return null;
    }

    private static String extractPreciseDynamicCode(String input) {
        int firstAt = input.indexOf('@');
        int lastAt = input.lastIndexOf('@');

        // 情况1：没有@符号
        if (firstAt == -1) {
            return input;
        }
        // 情况2：只有一个@符号
        else if (firstAt == lastAt) {
            return input.substring(0, firstAt);
        }
        // 情况3：有两个或多个@符号
        else {
            return input.substring(firstAt + 1, lastAt);
        }
    }


    /**
     * 获取配网绕组的设备ID和类型
     */
    public static String[] parseWindingPsrStr(String psrStr) {
        String[] parts = psrStr.split("_");
        if (parts.length >= 3) {
            String part2 = parts[1];
            String part3And4 = parts[2] + "_" + parts[3];
            return new String[]{part2, part3And4};
        } else {
            return null;
        }
    }

    /**
     * Nap转换方法
     *
     * @param list      原始列表
     * @param keyMapper 键提取函数
     * @param <K>       键类型
     * @param <V>       值类型
     * @return 转换后的Map
     */
    public static <K, V> Map<K, V> convertMap(
            List<V> list,
            Function<? super V, ? extends K> keyMapper
    ) {
        return list.stream()
                .collect(Collectors.toMap(
                        keyMapper,
                        value -> value,
                        (oldVal, newVal) -> newVal // 默认覆盖旧值
                ));
    }

    /**
     * Node转换方法
     *
     * @param list              原始列表
     * @param znapPsrIdMapper   键提设备ID函数
     * @param znapPsrNameMapper 键提设备类型函数
     * @param isLine            是否线路
     */
    public static <T> List<Node> toNodes(List<T> list, Function<T, String> znapPsrIdMapper, Function<T, String> znapPsrNameMapper, boolean isLine) {
        return list.stream().map(data -> toNode(data, znapPsrIdMapper, znapPsrNameMapper, isLine)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * Node转换方法
     */
    public static <T> List<Node> toNodes(List<T> list, Function<T, String> psrIdMapper, Function<T, String> psrTypeMapper, Function<T, String> psrNameMapper, boolean isLine) {
        return list.stream().map(data -> toNode(data, psrIdMapper, psrTypeMapper, psrNameMapper, isLine)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    //
    public static <T> Node toNode(T data, Function<T, String> znapPsrIdMapper, Function<T, String> znapPsrNameMapper, boolean isLine) {
        String znapPsrId = znapPsrIdMapper.apply(data);
        String name = znapPsrNameMapper.apply(data);
        String[] strings = parsePsrStr(znapPsrId);
        if (strings == null) {
            return null;
        }
        Node node = NodeFactory.createNode(znapPsrId, strings[1], strings[0]);
        if (isLine) {
            node.setEdge(true);
        }
        node.setPsrName(name);
        return node;
    }

    public static <T> Node toNode(T data, Function<T, String> psrIdMapper, Function<T, String> psrTypeMapper, Function<T, String> psrNameMapper, boolean isLine) {
        String psrId = psrIdMapper.apply(data);
        String psrType = psrTypeMapper.apply(data);
        String name = psrNameMapper.apply(data);
        Node node = NodeFactory.createNode(psrId, psrId, psrType);
        if (isLine) {
            node.setEdge(true);
        }
        node.setPsrName(name);
        return node;
    }

    public static void main(String[] args) {
        // PD_0112_5085c38e-0d4f-48c4-810c-b5c28c72105e
        // PD_0307_03072@14000670085006@1
        // PD_0307_14000670085006
        // PD_0307_0307_14000670085006@1
        String[] strings = parsePsrStr("PD_0332307_03307_140006333333370085006@1");
        System.out.println(strings.toString());
    }
}
