package com.ruoyi.graph.utils;

import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.StationConfiguration;
import com.ruoyi.graph.*;
import lombok.Data;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.BUS;
import static com.ruoyi.entity.cost.DeviceType.ZNDLQ;
import static com.ruoyi.graph.Node.TYPE_SELF;

public class StationNodeUtils {
    /**
     * 生成变电站节点及其子节点
     *
     * @param stationName          站房名称
     * @param stationType          站房类型
     * @param coordinateList       站房坐标
     * @param num                  间隔数量
     * @param stationConfiguration 站房配置
     * @return 包含站房节点及其子节点的列表
     */
    public static List<Node> generateStationAndChildren(String stationName, String stationType,
                                                        List<Double> coordinateList, Integer num,
                                                        StationConfiguration stationConfiguration) {

        List<Node> nodeList = new ArrayList<>();
        // 生成站房节点
        Node stationNode = createStationNode(stationName, stationType, coordinateList);

        // 生成母线及其他子节点
        List<Node> children = generateStationChildren(coordinateList, num, stationConfiguration);

        // 建立节点间关系（示例：母线连接到站房）
        for (Node child : children) {
            stationNode.addChild(child, -1);
        }
        nodeList.add(stationNode);
        nodeList.addAll(children);
        return nodeList;
    }

    /**
     * 创建站房节点
     */
    private static Node createStationNode(String stationName, String stationType, List<Double> coordinateList) {
        //站房的基础信息
        String stationId = UUID.randomUUID().toString().replace("-", "");
        Node stationNode = new Node(stationId);
        stationNode.setEdge(false);
        stationNode.setPsrName(stationName);
        stationNode.setPsrType(stationType);
        stationNode.setType(TYPE_SELF);
        stationNode.setShapeKey(NodeConstants.SHAPE_KEY_HWG);

        HashMap<String, Object> properties = new HashMap<>();
        properties.put("type", NodeConstants.SHAPE_KEY_HWG);
        properties.put("psrType", stationType);
        stationNode.setProperties(properties);

        // 创建站房几何点
        GeometryFactory factory = new GeometryFactory();
        Geometry geometry = factory.createPoint(new Coordinate(coordinateList.get(0), coordinateList.get(1)));
        stationNode.setGeometry(geometry);

        return stationNode;
    }

    /**
     * 根据站房坐标和间隔数量，生成子集node
     */
    public static List<Node> generateStationChildren(List<Double> coordinateList, Integer num,
                                                     StationConfiguration stationConfiguration) {
        List<Node> children = new ArrayList<>();

        // 生成母线
        Node busBar = createBusBar(coordinateList, stationConfiguration);
        children.add(busBar);

        double busLength = stationConfiguration.getStationWidth() *
                (1 - 2 * stationConfiguration.getStationAndBusSpacing() / 100);

        // 计算母线起点和终点
        double[] offsetStartPoint = calculatePerpendicularOffset(
                coordinateList.get(1), coordinateList.get(0),
                stationConfiguration.getStationAndBusLength(), 90, busLength / 2
        );

        double[] endPoint = calculateEndpoint(
                offsetStartPoint[1], offsetStartPoint[0],
                busLength, 90
        );
        // 生成连接线节点
        List<Node> line = createLine(num, stationConfiguration, offsetStartPoint, endPoint, busLength);
        children.addAll(line);

        // 根据母线和间隔生成母线的动态ports
        makeBusPort(line, busBar);

        // 生成间隔节点.同时处理间隔和母线的ports
        List<Node> intervals = createIntervals(line);
        children.addAll(intervals);

        // 建立连接关系...
        for (int i = 0; i < line.size(); i++) {
            //边的连接关系
            busBar.addEdge(line.get(i), true);
            intervals.get(i).addEdge(line.get(i), false);
            //setSourcePort
            PortItemBus sourcePortItem = (PortItemBus) busBar.getPorts().getItems().get(i);
            line.get(i).setSourcePort(sourcePortItem.getId());
            //setTargetPort
            PortItem targetPortItem = (PortItem) intervals.get(i).getPorts().getItems().get(0);
            line.get(i).setTargetPort(targetPortItem.getId());
        }


        return children;
    }


    /**
     * 创建母线节点
     */
    private static Node createBusBar(List<Double> coordinateList, StationConfiguration stationConfiguration) {
        String busId = UUID.randomUUID().toString().replace("-", "");
        Node busNode = new Node(busId);
        busNode.setEdge(true);
        busNode.setPsrName("母线");
        busNode.setPsrType(BUS);
        busNode.setType(TYPE_SELF);
        busNode.setShapeKey(NodeConstants.SHAPE_KEY_BUS_FEEDER);
        busNode.setLineType(NodeConstants.SHAPE_KEY_LINE_TYPE_BUS);
        HashMap<String, Object> properties = new HashMap<>();
        properties.put("type", NodeConstants.LINE_TYPE_LINEAR);
        properties.put("psrType", BUS);
        busNode.setProperties(properties);

        // 计算母线位置
        GeometryFactory factory = new GeometryFactory();
        double busLength = stationConfiguration.getStationWidth() *
                (1 - 2 * stationConfiguration.getStationAndBusSpacing() / 100);

        // 计算母线起点和终点
        double[] offsetStartPoint = calculatePerpendicularOffset(
                coordinateList.get(1), coordinateList.get(0),
                stationConfiguration.getStationAndBusLength(), 90, busLength / 2
        );

        double[] endPoint = calculateEndpoint(
                offsetStartPoint[1], offsetStartPoint[0],
                busLength, 90
        );

        // 创建母线几何线段
        Coordinate start = new Coordinate(offsetStartPoint[1], offsetStartPoint[0]);
        Coordinate end = new Coordinate(endPoint[1], endPoint[0]);
        Geometry line = factory.createLineString(new Coordinate[]{start, end});
        busNode.setGeometry(line);

        return busNode;
    }


    /**
     * 创建连接线节点
     */
    private static List<Node> createLine(Integer num, StationConfiguration stationConfiguration,
                                         double[] busStart, double[] busEnd, Double length) {
        List<Node> lines = new ArrayList<>();
        List<Geometry> geometryList = generateConnectionLines(busStart, busEnd, num, stationConfiguration, length);
        // 计算间隔位置和分布...
        for (int i = 0; i < num; i++) {
            String lineId = UUID.randomUUID().toString().replace("-", "");
            Node edge = NodeFactory.createNode("link_" + lineId, null, null);
            edge.setEdge(true);
            edge.setType(TYPE_SELF);
            edge.setShapeKey(NodeConstants.SHAPE_KEY_LINK_LINE);
            edge.setLineType(NodeConstants.LINE_TYPE_LINEAR);
            edge.setGeometry(geometryList.get(i));

            HashMap<String, Object> properties = new HashMap<>();
            properties.put("type", NodeConstants.SHAPE_KEY_LINK_LINE);
            edge.setProperties(properties);

            lines.add(edge);
        }
        return lines;
    }

    /**
     * 创建间隔节点
     */
    private static List<Node> createIntervals(List<Node> line) {
        List<Node> intervals = new ArrayList<>();

        // 计算间隔位置和分布...
        for (int i = 0; i < line.size(); i++) {
            //生成node基础信息
            String intervalId = UUID.randomUUID().toString().replace("-", "");
            Node intervalNode = new Node(intervalId);
            intervalNode.setEdge(false);
            intervalNode.setPsrName("备用" + (i + 1));
            intervalNode.setPsrType(ZNDLQ);
            intervalNode.setType(TYPE_SELF);
            intervalNode.setShapeKey(NodeConstants.SHAPE_KEY_STATION_LOAD_KG);

            HashMap<String, Object> properties = new HashMap<>();
            properties.put("type", NodeConstants.SHAPE_KEY_STATION_LOAD_KG);
            properties.put("psrType", ZNDLQ);
            intervalNode.setProperties(properties);

            //计算间隔的坐标信息
            Coordinate[] coordinates = line.get(i).getGeometry().getCoordinates();
            double[] doubles = intervalsPerpendicularOffset(coordinates[1].y, coordinates[1].x, 0.1, 90, 0.09);
            //生成node的坐标信息
            GeometryFactory factory = new GeometryFactory();
            Geometry point = factory.createPoint(new Coordinate(doubles[1], doubles[0]));
            intervalNode.setGeometry(point);


            //生成node的Ports信息
            PortItem portStrItem = new PortItem();
            portStrItem.setId(UUID.randomUUID().toString().replace("-", ""));
            portStrItem.setPoint(new ArrayList<>(Arrays.asList(doubles[1], doubles[0])));
            portStrItem.setRender("render1");
            portStrItem.setSize(new ArrayList<>(Arrays.asList(32, 32)));
            portStrItem.setArgs(new PortItem.Args("88%", "100%"));

            PortItem portEndItem = new PortItem();
            portEndItem.setId(UUID.randomUUID().toString().replace("-", ""));
            portEndItem.setRender("render1");
            portEndItem.setSize(new ArrayList<>(Arrays.asList(32, 32)));
            portEndItem.setArgs(new PortItem.Args("88%", "0%"));

            List<PortItem> portItems = new ArrayList<>();
            portItems.add(portStrItem);
            portItems.add(portEndItem);
            List<Object> objectList = portItems.stream()
                    .map(item -> (Object) item) // 显式转型为 Object
                    .collect(Collectors.toList());
            Ports ports = new Ports(new RenderConfig(), objectList);

            intervalNode.setPorts(ports);
            intervals.add(intervalNode);
        }
        return intervals;
    }

    private static final double EARTH_RADIUS = 6371000; // 地球半径（米）


    /**
     * 生成母线与间隔之间的连接线
     *
     * @param busStart      母线起点坐标 [经度, 纬度]
     * @param busEnd        母线终点坐标 [经度, 纬度]
     * @param intervalCount 间隔数量
     * @return 连接线几何对象列表（每个连接线为LineString）
     */
    public static List<Geometry> generateConnectionLines(
            double[] busStart, double[] busEnd,
            int intervalCount, StationConfiguration stationConfiguration, double busLength
    ) {
        List<Geometry> connectionLines = new ArrayList<>();

        // 1. 计算母线方向向量（dx, dy）：描述母线从起点到终点的变化
        double dx = busEnd[0] - busStart[0];
        double dy = busEnd[1] - busStart[1];

        // 2. 归一化方向向量（单位向量）：后续计算垂直方向用
        double unitDx = dx / busLength;
        double unitDy = dy / busLength;

        // 3. 计算垂直于母线的单位向量（连接线的方向）
        // 垂直向量公式：原向量 (dx, dy) 的垂直向量是 (-dy, dx) 或 (dy, -dx)，这里取 (-dy, dx) 方向
        double perpendicularDx = -unitDy;
        double perpendicularDy = unitDx;

        // 4. 遍历每一段，计算“段中点”并生成连接线
        for (int i = 0; i < intervalCount; i++) {
            // 4.1 计算当前段的起始比例和结束比例
            double startRatio = (double) i / intervalCount;    // 段的起点比例（如 i=0 时是 0）
            double endRatio = (double) (i + 1) / intervalCount;// 段的终点比例（如 i=0 时是 1/intervalCount）

            // 4.2 计算段中点的比例：起点比例 + 段长度的一半
            double midRatio = startRatio + (endRatio - startRatio) / 2;

            // 4.3 计算段中点的坐标
            double midX = busStart[0] + midRatio * dx;
            double midY = busStart[1] + midRatio * dy;

            // 4.4 计算连接线的起点和终点（向垂直方向延伸）
            // stationConfiguration.getBusAndBreakerSpacing() 是连接线的总长度
            double spacing = stationConfiguration.getBusAndBreakerSpacing();
            double endX = midX + spacing / 2 * perpendicularDx;
            double endY = midY + spacing / 2 * perpendicularDy;

            // 4.5 创建几何线段（注意坐标顺序：x 是水平，y 是垂直）
            GeometryFactory factory = new GeometryFactory();
            Coordinate[] coords = new Coordinate[]{
                    new Coordinate(midY, midX),
                    new Coordinate(endY, endX)
            };
            LineString connectionLine = factory.createLineString(coords);
            connectionLines.add(connectionLine);
        }

        return connectionLines;
    }

    /**
     * 计算从起点沿指定方位角和距离的终点坐标
     *
     * @param lat      起点纬度（度）
     * @param lon      起点经度（度）
     * @param distance 距离（米）
     * @param azimuth  方位角（度，正北为0，顺时针增加）
     * @return 包含终点经纬度的数组 [纬度, 经度]
     */
    public static double[] calculateEndpoint(
            double lon, double lat,
            double distance,
            double azimuth
    ) {
        // 转换为弧度
        double latRad = Math.toRadians(lat);
        double lonRad = Math.toRadians(lon);
        double azimuthRad = Math.toRadians(azimuth);

        // 计算角距离（弧度）
        double angularDistance = distance / EARTH_RADIUS;

        // 计算终点纬度
        double newLatRad = Math.asin(
                Math.sin(latRad) * Math.cos(angularDistance) +
                        Math.cos(latRad) * Math.sin(angularDistance) * Math.cos(azimuthRad)
        );

        // 计算终点经度
        double newLonRad = lonRad + Math.atan2(
                Math.sin(azimuthRad) * Math.sin(angularDistance) * Math.cos(latRad),
                Math.cos(angularDistance) - Math.sin(latRad) * Math.sin(newLatRad)
        );

        // 转换回度数
        double newLat = Math.toDegrees(newLatRad);
        double newLon = Math.toDegrees(newLonRad);

        return new double[]{newLat, newLon};
    }

    /**
     * 计算垂直于给定方位角的偏移点
     *
     * @param lat        起点纬度（度）
     * @param lon        起点经度（度）
     * @param offset     偏移距离（米）
     * @param azimuth    原始方位角（度，正北为0，顺时针增加）
     * @param leftOffset 向左偏移的距离
     * @return 包含偏移点经纬度的数组 [纬度, 经度]
     */
    public static double[] calculatePerpendicularOffset(
            double lon, double lat,
            double offset,
            double azimuth,
            double leftOffset
    ) {
        // 1. 计算垂直方向的偏移点
        double perpendicularAzimuth = (azimuth - 90) % 360;
        double[] perpendicularPoint = calculateEndpoint(lat, lon, offset, perpendicularAzimuth);

        // 2. 在垂直偏移点的基础上，向左（270°方位角）水平偏移
        double leftAzimuth = 270; // 正西方向（向左）
        return calculateEndpoint(
                perpendicularPoint[1], // 注意：calculateEndpoint期望的顺序是(lat, lon)
                perpendicularPoint[0],
                leftOffset,
                leftAzimuth
        );
    }


    /**
     * 调整间隔的便宜
     *
     * @param lat        起点纬度（度）
     * @param lon        起点经度（度）
     * @param offset     偏移距离（米）
     * @param azimuth    原始方位角（度，正北为0，顺时针增加）
     * @param leftOffset 向左偏移的距离
     * @return 包含偏移点经纬度的数组 [纬度, 经度]
     */
    public static double[] intervalsPerpendicularOffset(
            double lon, double lat,
            double offset,
            double azimuth,
            double leftOffset
    ) {
        // 1. 计算垂直方向的偏移点
        double perpendicularAzimuth = (azimuth + 90) % 360;
        double[] perpendicularPoint = calculateEndpoint(lat, lon, offset, perpendicularAzimuth);

        // 2. 在垂直偏移点的基础上，向左（270°方位角）水平偏移
        double leftAzimuth = 270; // 正西方向（向左）
        return calculateEndpoint(
                perpendicularPoint[1], // 注意：calculateEndpoint期望的顺序是(lat, lon)
                perpendicularPoint[0],
                leftOffset,
                leftAzimuth
        );
    }

    /**
     * 根据联络线信息和母线信息生成port
     *
     * @param line
     * @param busBar
     */
    private static void makeBusPort(List<Node> line, Node busBar) {

        List<PortItemBus> portItemBuses = new ArrayList<>();
        for (int i = 0; i < line.size(); i++) {
            String portId = UUID.randomUUID().toString().replace("-", "");
            PortItemBus portItemBus = new PortItemBus();
            portItemBus.setId(portId);
            portItemBus.setRender("render1");
            portItemBus.setDynamics(true);
            Coordinate[] doubles = line.get(i).getGeometry().getCoordinates();
            portItemBus.setPoint(new ArrayList<>(Arrays.asList(doubles[0].x, doubles[0].y)));

            Coordinate[] bus = busBar.getGeometry().getCoordinates();
            portItemBus.setLineRatio(calculatePercentage(bus[0].x, bus[0].y, bus[1].x, bus[1].y, doubles[0].x, doubles[0].y));
            portItemBuses.add(portItemBus);
        }
        List<Object> objectList = portItemBuses.stream()
                .map(item -> (Object) item) // 显式转型为 Object
                .collect(Collectors.toList());
        busBar.setPorts(new Ports(new BusRenderConfig(), objectList));


    }


    /**
     * 计算两个经纬度坐标之间的球面距离（Haversine公式）
     *
     * @param lon1 起点经度
     * @param lat1 起点纬度
     * @param lon2 终点经度
     * @param lat2 终点纬度
     * @return 距离，单位：米
     */
    public static double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);

        lat1 = Math.toRadians(lat1);
        lat2 = Math.toRadians(lat2);

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.sin(dLon / 2) * Math.sin(dLon / 2) *
                        Math.cos(lat1) * Math.cos(lat2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 计算起点到目标点的距离占起点到终点总距离的百分比
     *
     * @param startLon  起点经度
     * @param startLat  起点纬度
     * @param endLon    终点经度
     * @param endLat    终点纬度
     * @param targetLon 目标点经度
     * @param targetLat 目标点纬度
     * @return 百分比值（0-100）
     */
    public static double calculatePercentage(double startLon, double startLat,
                                             double endLon, double endLat,
                                             double targetLon, double targetLat) {
        double totalDistance = calculateDistance(startLon, startLat, endLon, endLat);
        double partialDistance = calculateDistance(startLon, startLat, targetLon, targetLat);

        if (totalDistance <= 0) {
            return 0;
        }
        return (partialDistance / totalDistance);
    }
}
