package com.ruoyi.graph.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CartesianProduct {
    public static <T> List<List<T>> cartesianProduct(List<List<T>> lists) {
        if (lists == null || lists.isEmpty()) {
            return Collections.emptyList();
        }

        List<List<T>> result = Arrays.asList(Collections.emptyList());

        for (List<T> list : lists) {
            result = append(result, list);
        }

        return result;
    }

    private static <T> List<List<T>> append(List<List<T>> prefixes, List<T> list) {
        List<List<T>> result = new ArrayList<>();

        for (List<T> prefix : prefixes) {
            for (T element : list) {
                List<T> newList = new ArrayList<>(prefix);
                newList.add(element);
                result.add(newList);
            }
        }

        return result;
    }

    public static void main(String[] args) {
        List<List<Integer>> input = Arrays.asList(
                Arrays.asList(1, 2),  // 集合1
                Arrays.asList(3, 4),  // 集合2
                Arrays.asList(5)     // 集合3
        );

        List<List<Integer>> product = cartesianProduct(input);
        System.out.println(product);
        // 输出: [[1, 3, 5], [1, 4, 5], [2, 3, 5], [2, 4, 5]]
    }
}
