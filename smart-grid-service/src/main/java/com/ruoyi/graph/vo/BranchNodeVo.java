package com.ruoyi.graph.vo;

import com.ruoyi.graph.Node;
import com.ruoyi.graph.Ports;
import lombok.Data;

import java.util.List;

/**
 * BranchNode（返回）
 */
@Data
public class BranchNodeVo {

    private String id;

    /**
     * 分支节点设备ID
     */
    private String psrId;

    /**
     * 分支节点类型
     */
    private String psrType;

    /**
     * 分支节点类型
     */
    private String psrName;

    /**
     * 配变数量
     */
    private int pbNum;

    /**
     * 配变列表
     */
    private List<NodeVo> pbNodes;

}

