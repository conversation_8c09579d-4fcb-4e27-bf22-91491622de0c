package com.ruoyi.graph.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SegBetweenVo {
    public SegBetweenVo(String id, String startPsrId, String startPsrType, String startPsrName, String endPsrId, String endPsrType, String endPsrName) {
        this.id = id;
        this.startPsrId = startPsrId;
        this.startPsrType = startPsrType;
        this.startPsrName = startPsrName;

        this.endPsrId = endPsrId;
        this.endPsrType = endPsrType;
        this.endPsrName = endPsrName;
    }

    String id;

    String startPsrId;

    String endPsrId;

    String startPsrType;

    String endPsrType;

    String startPsrName;

    String endPsrName;

    int pbNum;

    // 分段之间的节点集合
    List<NodeVo> nodes = new ArrayList<>();

    // 分段之间的节点集合
    ArrayList<NodeVo> mainOtherNodes = new ArrayList<>();


    // 分段之间的节点集合
    ArrayList<NodeVo> mainAllOtherNodes = new ArrayList<>();
}
