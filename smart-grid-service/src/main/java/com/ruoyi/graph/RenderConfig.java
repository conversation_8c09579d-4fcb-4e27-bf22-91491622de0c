package com.ruoyi.graph;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RenderConfig {
    private Render render1; // 直接映射到render1对象，移除冗余嵌套

    @Data
    public static class Render {
        private Layer layer;
        private HighlightLayer highlightLayer;
    }

    @Data
    public static class Layer {
        private String type;
        private Paint paint;
    }

    @Data
    public static class HighlightLayer {
        private Paint paint;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Paint {
        @JsonProperty("circle-radius")
        private Integer circleRadius;

        @JsonProperty("circle-color")
        private String circleColor;

        public Paint(String circleColor) {
            this.circleColor = circleColor;
            this.circleRadius = null;
        }

        public Paint(Integer circleRadius, String circleColor) {
            this.circleRadius = circleRadius;
            this.circleColor = circleColor;
        }
    }

    public RenderConfig() {
        Render render = new Render();

        Layer layer = new Layer();
        layer.setType("circle");
        layer.setPaint(new Paint(3, "rgb(255,0,0)"));

        HighlightLayer highlightLayer = new HighlightLayer();
        highlightLayer.setPaint(new Paint("rgb(253,189,0)"));

        render.setLayer(layer);
        render.setHighlightLayer(highlightLayer);

        this.render1 = render; // 直接赋值给render1字段
    }
}
