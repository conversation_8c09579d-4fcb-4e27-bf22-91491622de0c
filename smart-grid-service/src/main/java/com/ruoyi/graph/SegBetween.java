package com.ruoyi.graph;

import com.alibaba.druid.util.StringUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 路径分段
 */
@Data
public class SegBetween {
    public SegBetween(String id, String startPsrId, String startPsrType, String startPsrName, String endPsrId, String endPsrType, String endPsrName) {
        if (id == null) {
            id = getSegId(startPsrId, endPsrId);
        }
        this.id = id;
        this.startPsrId = startPsrId;
        this.startPsrType = startPsrType;
        this.startPsrName = startPsrName;

        this.endPsrId = endPsrId;
        this.endPsrType = endPsrType;
        this.endPsrName = endPsrName;
    }

    String id;

    /**
     * 开始主干开关psrId
     */
    String startPsrId;

    String endPsrId;


    /**
     * 开始主干开关psrType
     */
    String startPsrType;

    String endPsrType;

    String startPsrName;

    String endPsrName;

    /**
     * 开始路径的Node节点ID
     */
    String startNodeId;

    String endNodeId;

    /**
     * 分段内配变数量
     */
    int pbNum;

    // 分段之间的主干路径节点集合
    ArrayList<Node> nodes = new ArrayList<>();

    // 分段之间的排除主干节点的其它节点集合（遇到其它主干开关即可停止）
    ArrayList<Node> mainOtherNodes = new ArrayList<>();

    // 分段之间的排除主干节点的其它所有节点集合（包含主干路径所有的节点，其它主干路径也可当成改分段的节点）
    ArrayList<Node> mainAllOtherNodes = new ArrayList<>();

    public List<Node> getAllNodes() {
        ArrayList<Node> result = new ArrayList<>();
        result.addAll(nodes);
        result.addAll(mainOtherNodes);
        return result;
    }

    /**
     * 获取两个开关之间的分段 分段开关ID
     */
    public static String getSegId(String startPsrId, String endPsrId) {
        return startPsrId + "_" + endPsrId;
    }

    public List<Node> getPoles() {
        return this.nodes.stream().filter(n -> n.isPole()).collect(Collectors.toList());
    }

    // 获取所以的配变
    public List<Node> getAllPb() {
        return mainOtherNodes.stream().filter(Node::isPb).collect(Collectors.toList());
    }

    /**
     * 获取节点
     * @param isBoth 是否包含首尾两端
     * @return
     */
    public List<Node> getBetweenNodes(boolean isBoth) {
        if (CollectionUtils.isEmpty(nodes)) {
            return nodes;
        }
        if (isBoth == true) {
            ArrayList<Node> result = new ArrayList<>(nodes);
            if (StringUtils.equals(startPsrId, result.get(0).getPsrId())) {
                result.remove(0);
            }
            if (StringUtils.equals(endPsrId, result.get(result.size() - 1).getPsrId())) {
                result.remove(result.size() - 1);
            }
            return result;
        } else {
            return nodes;
        }
    }
}
