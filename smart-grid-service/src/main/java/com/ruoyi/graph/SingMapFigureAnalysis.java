package com.ruoyi.graph;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.entity.device.bo.SingMapFigure;
import com.ruoyi.entity.device.bo.SingMapUserData;
import com.ruoyi.graph.utils.NodeFactory;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.util.*;

/**
 * 单线图接口处理的figure
 */
@Data
public class SingMapFigureAnalysis {

    private List<SingMapFigure> figureList = new ArrayList<>();

    private Map<String, SingMapFigure> figureMap = new HashMap<>();

    // 线型的类型集合
    private List<String> lineStrings = Arrays.asList("Polyline", "Line", "Lines");

    // 根节点ID
    final String rootId = "Root";

    public SingMapFigureAnalysis(String feederId) throws IOException {
        if (StringUtils.isNotBlank(feederId)) {
            generateData(feederId);
        }
    }

    /**
     * 从 JSONObject 中获取指定键的值，并尝试转换为 JSONObject
     *
     * @param jsonObject 原始 JSONObject
     * @param key        键名
     * @return 转换后的 JSONObject（若无法转换则返回 null）
     */
    final static JSONObject getValueAsJSONObject(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);  // 获取值（Object 类型）

        // 值为 null（键不存在或值为 null）
        if (value == null) {
            return null;
        }

        // 值本身是 JSONObject 类型 → 直接返回
        if (value instanceof JSONObject) {
            return (JSONObject) value;
        }

        // 值是字符串类型 → 尝试解析为 JSONObject
        if (value instanceof String) {
            String strValue = (String) value;
            return JSON.parseObject(strValue);  // 解析字符串为 JSONObject
        }
        return null;
    }

    /**
     * 递归单线图feature树，并将节点存入 List 和 Map
     *
     * @param figure     当前节点
     * @param figureList 存储所有节点的 List
     * @param figureMap  存储节点ID与节点的 Map
     * @param parentId   父级ID
     */
    public final static void traverseTree(SingMapFigure figure, List<SingMapFigure> figureList, Map<String, SingMapFigure> figureMap, String parentId) {
        if (figure == null) {
            return;
        }

        // 由于单线图返回id会重复  我们需要拼接ID
        if (StringUtils.isNotBlank(parentId)) {
            figure.setId(parentId + "." + figure.getId());
        }

        // 1. 将当前节点加入 List 和 Map
        figureList.add(figure);
        figureMap.put(figure.getId(), figure);
        List<SingMapFigure> childFigures = figure.getChildFigures();

        if (childFigures != null && !childFigures.isEmpty()) {
            // 2. 递归处理子节点（前序遍历）
            for (SingMapFigure child : figure.getChildFigures()) {
                traverseTree(child, figureList, figureMap, figure.getId());
            }
        }
    }

    /**
     * 判断当前figure是为线型
     */
    final boolean isLine(SingMapFigure figure) {
        return lineStrings.contains(figure.getType());
    }

    /**
     * 获取父级ID 我们可以从按道理 从parentFigureID即可
     * 但是数据问题 parentFigureID 可能为空 此时从拼接的ID获取
     */
    String getParentId(SingMapFigure figure) {
        String parentFigureID = figure.getParentFigureID();
        if (StringUtils.isNotBlank(parentFigureID)) {
            return parentFigureID;
        } else {
            // 分割"."获取最后的
            String[] split = StringUtils.split(figure.getId(), ".");
            // 最顶层是Root 所以没必要  类型"Root.F1189.F2223"这样才获取设置吧
            if (split.length > 2) {
                return split[split.length - 2];
            }
        }
        return null;
    }


    /**
     * 根据线路生成figure数据
     *
     * @param feederId 线路ID
     */
    public void generateData(String feederId) throws IOException {
        String s = HttpUtils.get(String.format("http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/amap-app-service/measurement/dms/getPic?psrId=%s&psrType=dkx", feederId));

        JSONObject jsonObject = JSON.parseObject(s);

        if (jsonObject.getJSONObject("reply").getInteger("code") != 1000) {
            return;
        }
        JSONObject result = getValueAsJSONObject(jsonObject, "result");
        if (result == null) {
            return;
        }
        // 获取所有的features集合（有父子级）
        SingMapFigure figure = result.getJSONObject("picData").getObject("figureRoot", SingMapFigure.class);

        // 初始化存储容器
        traverseTree(figure, figureList, figureMap, "");
    }

    /**
     * 根据设备ID 获取figure
     *
     * @param psrId   设备ID
     * @param isGisId 有些设备通过PSRID是没法找的 isGisId：true 表示找不到再通过GIS_OID查找
     * @return
     */
    public SingMapFigure getByPsrId(String psrId, boolean isGisId) {
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            if (userData.psrEquals(psrId, isGisId)) {
                return figure;
            }
        }
        return null; // 未找到
    }

    public Map<String, Node> toNodeMap() {
        Map<String, Node> nodeMap = new HashMap<>();

        // 创建所有节点并存入Map
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            String id = figure.getId();
            String psrId = StringUtils.isNotBlank(userData.getPSRID()) ? userData.getPSRID() : userData.getGIS_OID();
            String psrType = StringUtils.isNotBlank(userData.getPSRType()) ? userData.getPSRType() : userData.getGIS_SBZLX();

            Node node = NodeFactory.createNode(id, psrId, psrType);
            nodeMap.put(id, node);
            if (isLine(figure)) {
                node.setEdge(true);
            }
        }

        // 构建节点树
        for (SingMapFigure figure : figureList) {
            SingMapUserData userData = figure.getUserData();
            String id = figure.getId();
            Node node = nodeMap.get(id);
            if (node == null) {
                continue;
            }
            Node parent = nodeMap.get(getParentId(figure));
            // 设置父节点
            if (parent != null) {
                parent.addChild(node, -1);
            }

            // 线路有设置链接节点
            if (isLine(figure)) {
                Node sourceNode = nodeMap.get(figure.getSourceFigureId());
                Node targetNode = nodeMap.get(figure.getTargetFigureId());

                if (sourceNode != null) {
                    sourceNode.addEdge(node, true);
                }
                if (targetNode != null) {
                    targetNode.addEdge(node, false);
                }
            }
        }

        return nodeMap;
    }

}
