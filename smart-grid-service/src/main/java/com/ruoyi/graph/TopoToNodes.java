package com.ruoyi.graph;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 将解析出来的线路的topo关系（nodes）入库
 */
@Data
@TableName("topo_to_nodes")
@NoArgsConstructor
@AllArgsConstructor
@Accessors
public class TopoToNodes {


    /**
     * 线路id
     */
    private String feederId;

    /**
     * nodeS转成的json
     */
    private String nodesJson;

    /**
     * 入库的时间
     */
    private Date addTime;
}
