package com.ruoyi.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortItem {
    private String id;
    private String render;
    private List<Integer> size;
    private Args args;
    private List<Double> point;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Args {
        private String x;
        private String y;
    }
}
