<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.znap.TpLinkCbMapper">

    <select id="selectLinkCbFeeder" resultType="com.ruoyi.entity.znap.vo.LinkCbFeederVo">
        select a.id,
               a.psrid,
               B.name,
               C.psrid as feederId1,
               C.name  as feederName1,
               D.psrid as feederId2,
               D.name  as feederName2
        from dms.tp_link_cb A,
             dms.dev_dms_breaker B,
             dms.con_dms_feeder C,
             dms.con_dms_feeder D
        where A.id = B.id
          and A.feeder_id0 = C.id
          and A.feeder_id1 = D.id
          and (
            A.feeder_psrid0 = CONCAT('PD_dkx_', #{feederId})
                or A.feeder_psrid1 = CONCAT('PD_dkx_', #{feederId})
            )
    </select>

</mapper>
