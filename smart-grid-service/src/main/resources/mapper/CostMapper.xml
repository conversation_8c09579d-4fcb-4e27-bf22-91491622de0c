<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.cost.CostMapper">

    <!-- 间隔单元 -->
    <select id="selectCostBayUnit" resultType="com.ruoyi.entity.cost.CostBayUnit">
        SELECT *
        FROM cost_bay_unit
        <where>
            <!-- 电压等级模糊查询 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 间隔类型 -->
            <if test="gapType != null and gapType != ''">
                AND gap_type = #{gapType}
            </if>

            <!-- 设备类型 -->
            <if test="facilityType != null and facilityType != ''">
                AND facility_type = #{facilityType}
            </if>

            <!-- 容量 -->
            <if test="capacity != null and capacity != ''">
                AND capacity = #{capacity}
            </if>

            <!-- 最大电流 -->
            <if test="maxCurrent != null">
                AND max_current = #{maxCurrent}
            </if>

            <!-- 最大切断电流 -->
            <if test="maxCutCurrent != null">
                AND max_cut_current = #{maxCutCurrent}
            </if>

            <!-- 配电形式 -->
            <if test="distributionForm != null and distributionForm != ''">
                AND distribution_form  = #{distributionForm}
            </if>
            LIMIT 1
        </where>
    </select>

    <!-- 箱式变电站 -->
    <select id="selectCostBoxTypeSubstation" resultType="com.ruoyi.entity.cost.CostBoxTypeSubstation">
        SELECT *
        FROM cost_box_type_substation
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 母线连接方式 -->
            <if test="busConnection != null and busConnection != ''">
                AND bus_connection = #{busConnection}
            </if>

            <!-- 配变台数-本期 -->
            <if test="currentNumber != null">
                AND current_number = #{currentNumber}
            </if>

            <!-- 配变台数-终期 -->
            <if test="endNumber != null">
                AND end_number = #{endNumber}
            </if>

            <!-- 容量(单位：kVA)-->
            <if test="capacity != null">
                AND capacity = #{capacity}
            </if>

            <!-- 进线间隔 -->
            <if test="inlineInt != null">
                AND inline_int = #{inlineInt}
            </if>

            <!-- 出线间隔 -->
            <if test="outlineInt != null">
                AND outline_int = #{outlineInt}
            </if>
            LIMIT 1

        </where>
    </select>

    <!-- 电缆线路 -->
    <select id="selectCostCableLine" resultType="com.ruoyi.entity.cost.CostCableLine">
        SELECT *
        FROM cost_cable_line
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 回路数 -->
            <if test="loopNumber != null and loopNumber != ''">
                AND loop_number = #{loopNumber}
            </if>

            <!-- 截面 -->
            <if test="section != null">
                AND "section" = #{section}
            </if>

            LIMIT 1

        </where>
    </select>

    <!-- 柱上开关 -->
    <select id="selectCostPoleSwitch" resultType="com.ruoyi.entity.cost.CostPoleSwitch">
        SELECT *
        FROM cost_pole_switch
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 开关类型 -->
            <if test="switchType != null and switchType != ''">
                AND switch_type = #{switchType}
            </if>

            <!-- 最大电流 -->
            <if test="maxCurrent != null">
                AND "max_current" = #{maxCurrent}
            </if>

            <!-- 开断电流 -->
            <if test="cutCurrent != null">
                AND "cut_current" = #{cutCurrent}
            </if>


            LIMIT 1

        </where>
    </select>

    <!-- 柱上变 -->
    <select id="selectCostPoleUbstation" resultType="com.ruoyi.entity.cost.CostPoleUbstation">
        SELECT *
        FROM cost_pole_ubstation
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 容量 -->
            <if test="capacity != null">
                AND capacity = #{capacity}
            </if>

            LIMIT 1

        </where>
    </select>

    <!-- 配电室 -->
    <select id="selectCostPowerDistributionRoom" resultType="com.ruoyi.entity.cost.CostPowerDistributionRoom">
        SELECT *
        FROM cost_power_distribution_room
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 母线连接方式 -->
            <if test="busConnection != null and busConnection != ''">
                AND bus_connection = #{busConnection}
            </if>

            <!-- 配变台数-本期 -->
            <if test="currentNumber != null">
                AND current_number = #{currentNumber}
            </if>

            <!-- 配变台数-终期 -->
            <if test="endNumber != null">
                AND end_number = #{endNumber}
            </if>

            <!-- 容量(单位：kVA)-->
            <if test="capacity != null">
                AND capacity = #{capacity}
            </if>

            <!-- 进线间隔 -->
            <if test="inlineInt != null">
                AND inline_int = #{inlineInt}
            </if>

            <!-- 出线间隔 -->
            <if test="outlineInt != null">
                AND outline_int = #{outlineInt}
            </if>
            LIMIT 1

        </where>
    </select>

    <!-- 电力排管 -->
    <select id="selectCostPowerDuct" resultType="com.ruoyi.entity.cost.CostPowerDuct">
        SELECT *
        FROM cost_power_duct
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 排管类型 -->
            <if test="ductType != null and ductType != ''">
                AND duct_type = #{ductType}
            </if>

            <!-- 孔数规模 -->
            <if test="maxCurrent != null and maxCurrent != ''">
                AND max_current = #{maxCurrent}
            </if>

            LIMIT 1

        </where>
    </select>

    <!-- 电力沟槽 -->
    <select id="selectCostPowerGroove" resultType="com.ruoyi.entity.cost.CostPowerGroove">
        SELECT *
        FROM cost_power_groove
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 孔数规模 -->
            <if test="maxCurrent != null and maxCurrent != ''">
                AND max_current = #{maxCurrent}
            </if>

            LIMIT 1

        </where>
    </select>

    <!--     电力隧道 -->
    <select id="selectCostPowerTunnel" resultType="com.ruoyi.entity.cost.CostPowerTunnel">
        SELECT *
        FROM cost_power_tunnel
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 排管类型 -->
            <if test="ductType != null and ductType != ''">
                AND duct_type = #{ductType}
            </if>

            <!-- 孔数规模 -->
            <if test="maxCurrent != null and maxCurrent != ''">
                AND max_current = #{maxCurrent}
            </if>

            <!-- 建设形式 -->
            <if test="constructForm != null and constructForm != ''">
                AND construct_form = #{constructForm}
            </if>

            LIMIT 1

        </where>
    </select>

    <!-- 环柜 -->
    <select id="selectCostRingCabinet" resultType="com.ruoyi.entity.cost.CostRingCabinet">
        SELECT *
        FROM cost_ring_cabinet
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 母线连接方式 -->
            <if test="busConnection != null and busConnection != ''">
                AND bus_connection = #{busConnection}
            </if>


            <!-- 进线间隔 -->
            <if test="inlineInt != null">
                AND inline_int = #{inlineInt}
            </if>

            <!-- 出线间隔 -->
            <if test="outlineInt != null">
                AND outline_int = #{outlineInt}
            </if>
            LIMIT 1

        </where>
    </select>

    <!-- 开关站 -->
    <select id="selectCostSwitchStation" resultType="com.ruoyi.entity.cost.CostSwitchStation">
        SELECT *
        FROM cost_switch_station
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 母线连接方式 -->
            <if test="busConnection != null and busConnection != ''">
                AND bus_connection = #{busConnection}
            </if>


            <!-- 进线间隔 -->
            <if test="inlineInt != null">
                AND inline_int = #{inlineInt}
            </if>

            <!-- 出线间隔 -->
            <if test="outlineInt != null">
                AND outline_int = #{outlineInt}
            </if>
            LIMIT 1

        </where>
    </select>

    <!-- 变压器 -->
    <select id="selectCostTransformer" resultType="com.ruoyi.entity.cost.CostTransformer">
        SELECT *
        FROM cost_transformer
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 电压比 -->
            <if test="voltageRatio != null and voltageRatio != ''">
                AND voltage_ratio = #{voltageRatio}
            </if>


            <!-- 容量 -->
            <if test="capacity != null">
                AND capacity = #{capacity}
            </if>

            LIMIT 1

        </where>
    </select>

    <!-- 架空线 -->
    <select id="selectCostTrollyWire" resultType="com.ruoyi.entity.cost.CostTrollyWire">
        SELECT *
        FROM cost_trolly_wire
        <where>
            <!-- 电压等级 -->
            <if test="voltageLevel != null and voltageLevel != ''">
                AND voltage_level = #{voltageLevel}
            </if>

            <!-- 回路数 -->
            <if test="loopNumber != null and loopNumber != ''">
                AND loop_number = #{loopNumber}
            </if>

            <!-- 截面 -->
            <if test="section != null">
                AND "section" = #{section}
            </if>

            <!-- 架设方式 -->
            <if test="method != null and method != ''">
                AND "method" = #{method}
            </if>

            LIMIT 1

        </where>
    </select>
</mapper>
