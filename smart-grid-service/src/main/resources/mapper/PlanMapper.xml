<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.plan.PlanMapper">

    <!-- 批量插入方案 -->
    <insert id="insertBatchPlan" parameterType="java.util.List">
        INSERT INTO problem_scheme (
            id,
            load,
            economy,
            reliability,
            exe,
            advantage,
            disadvantage,
            budget,
            n1,
            plan_type,
            operate_data,
            operate,
            require_ments,
            problem_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                <choose>
                    <when test="item.load != null">
                        CAST(#{item.load} AS jsonb)
                    </when>
                    <otherwise>
                        NULL
                    </otherwise>
                </choose>,
                #{item.economy},
                #{item.reliability},
                #{item.exe},
                #{item.advantage},
                #{item.disadvantage},
                #{item.budget},
                #{item.n1},
                #{item.planType},
                #{item.operateData},
                #{item.operate},
                #{item.requireMents},
                #{item.problemId}
            )
        </foreach>
    </insert>



</mapper>
