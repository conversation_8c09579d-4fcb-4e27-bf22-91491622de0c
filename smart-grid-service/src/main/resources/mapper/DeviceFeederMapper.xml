<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.FeederDeviceMapper">

    <select id="selectFeederNtsByFeederIds" resultType="com.ruoyi.entity.device.vo.FeederNtVo">
        SELECT feeder.psr_id               as psrId,
        feeder.name,
        feeder.feeder_rate_capacity as feederRateCapacity,
        nt.his_max_load_rate        as hisMaxLoadRate
        FROM device_feeder feeder
        LEFT JOIN nt_feeder nt ON feeder.psr_id = nt.psr_id
        WHERE feeder.psr_id in
        <foreach collection="feederIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectFeederNtsByFeederId" resultType="com.ruoyi.entity.device.vo.FeederNtVo">
        SELECT feeder.psr_id               as psrId,
               feeder.name,
               feeder.feeder_rate_capacity as feederRateCapacity,
               nt.his_max_load_rate        as hisMaxLoadRate,
               nt.his_max_date as hisMaxDate,
               nt.his_max_current as hisMaxCurrent,
               nt.main_byq_name as mainByqName
        FROM device_feeder feeder
                 LEFT JOIN nt_feeder nt ON feeder.psr_id = nt.psr_id
        WHERE feeder.psr_id = #{feederId} LIMIT 1
    </select>
</mapper>
