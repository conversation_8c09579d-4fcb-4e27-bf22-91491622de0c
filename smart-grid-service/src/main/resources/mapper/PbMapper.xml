<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.PbMapper">

    <!-- 查询参数对象 -->
    <resultMap id="stationPbResultMap" type="com.ruoyi.entity.device.StationPb">
        <result column="join_ec" property="joinEc"/>
        <result column="feeder" property="feeder"/>
        <result column="pub_priv_flag" property="pubPrivFlag"/>
        <result column="name" property="name"/>
        <result column="psr_id" property="psrId"/>
        <result column="psr_state" property="psrState"/>
        <result column="psr_type" property="psrType"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="rated_capacity" property="ratedCapacity"/>
        <result column="station" property="station"/>
        <result column="station_type" property="stationType"/>
        <result column="bay" property="bay"/>
    </resultMap>

    <!-- 查看中压用户接入点 -->
    <sql id="middleUserWhere">
        WHERE psr_state IN ('10', '20') AND feeder = #{feeder}
        <if test="idList != null and idList.size() > 0">
            AND join_ec IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!-- 定义站内公共字段 -->
    <sql id="base_pb">
        feeder
        ,pub_priv_flag,name,psr_id,psr_state,latitude,longitude,rated_capacity,join_ec,station,station_type,bay
    </sql>

    <!-- 查询中压用户接入点下的配变 -->
    <select id="selectMiddleUserPb" resultMap="stationPbResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_pb"/>
        , '0302' AS psr_type FROM device_station_transformer
        <include refid="middleUserWhere"/>

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_pb"/>
        , '0303' AS psr_type
        FROM station_service_transformer
        <include refid="middleUserWhere"/>
    </select>


    <!-- 线路下的配变总数 -->
    <select id="selectFeederPbCount" resultType="java.lang.Integer">
        SELECT (SELECT COUNT(*) FROM station_service_transformer WHERE feeder = #{feederId})
                   + (SELECT COUNT(*) FROM device_station_transformer WHERE feeder = #{feederId})
                   + (SELECT COUNT(*) FROM device_pole_transformer WHERE feeder = #{feederId})
                   AS total_count;
    </select>


    <!-- 根据id查询对应的配变信息 -->


    <!-- 查询参数对象 -->
    <resultMap id="TransformerResultMap" type="com.ruoyi.entity.device.PBEntity">
        <result column="join_ec" property="joinEc"/>
        <result column="feeder" property="feeder"/>
        <result column="pub_priv_flag" property="pubPrivFlag"/>
        <result column="name" property="name"/>
        <result column="psr_id" property="psrId"/>
        <result column="psr_state" property="psrState"/>
        <result column="psr_type" property="psrType"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="rated_capacity" property="ratedCapacity"/>
    </resultMap>
    <!-- 公共条件片段 -->
    <sql id="commonWhere">
        WHERE psr_state IN ('10', '20')
        <if test="psrIdList != null and psrIdList.size() > 0">
            AND psr_id IN
            <foreach collection="psrIdList" item="pdId" open="(" separator="," close=")">
                #{pdId}
            </foreach>
        </if>
    </sql>

    <sql id="pb_all">
        feeder,pub_priv_flag,name,psr_id,psr_state,latitude,longitude,rated_capacity,join_ec,voltage_level
    </sql>

    <!-- 合并三个表的查询 -->
    <select id="selectTransformersById" resultMap="TransformerResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="pb_all"/>
        ,'0302' AS psr_type FROM device_station_transformer
        <include refid="commonWhere"/>

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="pb_all"/>
        ,'0110' AS psr_type FROM device_pole_transformer
        <include refid="commonWhere"/>

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        <include refid="pb_all"/>
        ,'0303' AS psr_type FROM station_service_transformer
        <include refid="commonWhere"/>
    </select>

</mapper>
