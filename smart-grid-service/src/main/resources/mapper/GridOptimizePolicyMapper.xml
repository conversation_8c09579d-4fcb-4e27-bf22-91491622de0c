<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.GridOptimizePolicyMapper">
    <resultMap id="listVo" type="com.ruoyi.vo.GridOptimizePolicyVo" autoMapping="true">
        <result column="constraint_setting" property="constraintSetting" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="target_setting" property="targetSetting" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="listVo" resultMap="listVo">
        select id, target_setting, constraint_setting from grid_optimize_policy
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>