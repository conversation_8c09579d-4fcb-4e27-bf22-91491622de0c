<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.ZhBreakMapper">

    <select id="selectAstInfo" resultType="java.util.Map">
        select ast_id,rated_voltage,ast_nature,rated_current from device_station_breaker_ast where ast_id in
        <foreach collection="collection" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryFeederCurve" resultType="java.util.Map">
        select a.last_update_time as data_time, a.meas_type_code, c.meas_value, c.date_day_point from zh_telemetry_break a
        inner join zh_telemetry_break_curve c on a.meas_point_id = c.meas_point_id and a.last_update_time = c.data_time
        where (meas_type_code = 'TotW' or meas_type_code = 'TotVar') and psr_id = #{psrId}
    </select>

    <select id="queryRateValueById" resultType="java.lang.Integer">
        SELECT COALESCE(rated_current, 0) AS rateValue
        FROM device_station_breaker_ast zab
                 INNER JOIN device_station_breaker zb ON
            zab.ast_id = zb.ast_id
        where zb.psr_id = #{modelId} limit 1
    </select>
</mapper>