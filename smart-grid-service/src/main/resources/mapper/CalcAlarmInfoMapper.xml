<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.CalcAlarmInfoMapper">

    <resultMap type="com.ruoyi.entity.calc.CalcAlarmInfo" id="CalcAlarmInfoResult">
        <result property="id" column="id"/>
        <result property="psrId" column="psr_id"/>
        <result property="psrType" column="psr_type"/>
        <result property="psrParentId" column="psr_parent_id"/>
        <result property="psrParentType" column="psr_parent_type"/>
        <result property="instanceId" column="instance_id"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="alarmContent" column="alarm_content"/>
        <result property="alarmType" column="alarm_type"/>
    </resultMap>

    <insert id="insertEvents">
        INSERT INTO zh_eps_event
        ( model_id, alarm_id, simu_case_no, event_time, event_type, event_context)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.modelId},
            #{item.alarmId},
            #{item.simuCaseNo},
            #{item.eventTime},
            #{item.eventType},
            #{item.eventContext}
            )
        </foreach>
    </insert>
    <select id="queryFeederIdsByInstanceId" resultType="java.lang.String">
        select distinct feeder_id
        from calc_alarm_info cai
        where instance_id = #{instanceId}
        limit 3
    </select>
    <select id="queryContactBreakAndFeederByFeeder" resultType="java.util.Map">
        select a.id,
               a.psrid,
               B.name as breakName,
               C.name as toFeederName,
               D.name as feederName
        from dms.tp_link_cb A,
             dms.dev_dms_breaker B,
             dms.con_dms_feeder C,
             dms.con_dms_feeder D
        where A.id = B.id
          and A.feeder_id0 = C.id
          and A.feeder_id1 = D.id
          and (
            A.feeder_psrid0 = #{feederId}
                or A.feeder_psrid1 = #{feederId}
            )

    </select>
    <select id="queryMainBreakByFeeder" resultType="java.lang.String">
        select name
        from dev_ems_breaker deb
        where feeder_id = (select id from con_dms_feeder cdf where psrid = #{feederId})
        limit 1
    </select>

    <select id="statistics" resultType="java.util.Map">
        select a.id, a.description, sum(case when b.alarm_type is not null then 1 else 0 end) as total
        from zh_eps_alarm_config a
                 left join calc_alarm_info b
                           on a.id = b.alarm_type
        where b.instance_id = #{instanceId}
        group by a.id, a.description
    </select>

    <select id="lastCalcTime" resultType="java.util.Date">
        select start_time
        from calc_instance_info
        where instance_id = #{instanceId}
    </select>
    <select id="historyList" resultType="com.ruoyi.entity.calc.CalcAlarmInfoVo">
        select *
        from calc_alarm_info
    </select>
    <select id="getInstanceIdByGridCode" resultType="java.util.Map">
        select instance_id
        from calc_instance_info cii
        where start_time = (select max(start_time)
                            from calc_instance_info cii
                            where grid_code = #{gridCode}
                              and state = 1
                              and is_auto = 1
                              and is_recycle = 0)
          and is_auto = 1
          and state = 1
          and is_recycle = 0
    </select>

    <select id="historyStatistics" resultType="java.util.Map">
        <choose>
            <when test="dimensionality !=null and dimensionality !='' and dimensionality ==1">
                <![CDATA[
            select
            (
            select
            grid_name
            from
            zh_dms_power_grid
            where
            psr_id = a.grid) as grid_name,
            round((grp::numeric / total) * 100, 2) as ratio
            from
            (
            select
            split_part(simu_case_no, ':', 1) as grid,
            count(*) as grp
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            group by
            split_part(simu_case_no, ':', 1)
            )a
            join (
            select
            count(*) as total
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            ) b
                 ]]>
            </when>
            <when test="dimensionality !=null and dimensionality !='' and dimensionality ==2">
                <![CDATA[
           select
            (
            select
            description
            from
            zh_eps_alarm_config
            where
            id = a.alarm_type) as grid_name,
            round((grp::numeric / total) * 100, 2) as ratio
            from
            (
            select
            alarm_type,
            count(*) as grp
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            group by
            alarm_type
            )a
            join (
            select
            count(*) as total
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            ) b
            ]]>
            </when>
            <when test="dimensionality !=null and dimensionality !='' and dimensionality ==3">
                <![CDATA[
            select
            level,
            round((grp::numeric / total) * 100, 2) as ratio
            from
            (
            select
            level,
            count(*) as grp
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            group by
            level
            )a
            join (
            select
            count(*) as total
            from
            calc_alarm_info a
            where
            DATE(a.alarm_time) < (select max(alarm_time::date) from calc_alarm_info cii where is_auto = 1)
            ) b
            ]]>
            </when>
        </choose>
    </select>

    <select id="queryEventCount" resultType="java.lang.Integer">
        select count(1)
        from zh_eps_event
        where alarm_id = #{id}
    </select>
    <select id="historyExportList" resultType="com.ruoyi.entity.calc.CalcAlarmInfoVo">
        select zdgf.power_grid_name as gridName,
               alarm_time,
               simu_case_no,
               zeac.description     as alarmTypeName,
               alarm_content
        from calc_alarm_info cai
                 inner join zh_eps_alarm_config zeac on
            cai.alarm_type = zeac.id
                 inner join zh_dms_grid_feeder zdgf on
            cai.feeder_id = zdgf.feeder
                and cai.alarm_time BETWEEN #{startTime} and #{endTime}
    </select>
    <select id="selectAlarmInfoPage" resultType="com.ruoyi.entity.calc.CalcAlarmInfoVo">
        select
        cai.*,
        (
        select
        count(1)
        from
        zh_eps_event zee
        where
        cai.id = zee.alarm_id)
        from
        calc_alarm_info cai
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="selectAlarmInfoWithGridPage" resultType="com.ruoyi.entity.calc.CalcAlarmInfoVo">
        SELECT
            cai.*,
            zdpg.grid_name AS gridName
        FROM
            calc_alarm_info cai
        LEFT JOIN
            problem_grid  zdpg ON SPLIT_PART(cai.simu_case_no, ':', 1) = zdpg.psr_id
        <where>
            ${ew.sqlSegment}
            <if test="gridName != null and gridName != ''">
                AND zdpg.grid_name LIKE CONCAT('%', #{gridName}, '%')
            </if>
        </where>
        ORDER BY cai.alarm_time DESC
    </select>

</mapper>
