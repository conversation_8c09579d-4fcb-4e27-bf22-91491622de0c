<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.TrendCalcMapper">
    <select id="queryBreakByRetId" resultType="com.ruoyi.entity.calc.AlarmDoMain">

        select cdf."name"                      feederName,
               split_part(ddb.psrid, '_', '3') psrId,
               split_part(ddb.psrid, '_', '2') psrType,
               ddb."name"                      psrName,
               to_char(spr.create_dt, 'yyyy-mm-dd') || to_char((idx / 4), ' FM00:') ||
               to_char((idx % 4) * 15, 'FM00') happenTime,
               '电压越限' as                   alarmType,
               (case
                    when v_value >= 11 then '电压越上限，高于11 kV'
                    when v_value &lt;= 9.5 then '电压越下限，低于9.5 kV'
                   end)   as                   alarmReason
        from sim_ret_pf_dms_breaker srpdb
                 inner join dev_dms_breaker ddb on srpdb.id = ddb.id
                 inner join sim_pf_ret spr on srpdb.ret_id = spr.id
                 inner join con_dms_feeder cdf on ddb.feeder_id = cdf.id
        where ret_id = #{retId}
          and (v_value >= 11 or v_value &lt;= 9.5)
    </select>
    <select id="queryFeederByGridCode" resultType="com.ruoyi.entity.calc.ZhDmsGridFeeder">
        select distinct concat('PD_dkx_', feeder) as feeder, power_grid_code, power_grid_name
        from zh_dms_grid_feeder
        where power_grid_code = #{gridCode}
    </select>

    <select id="querySimPfRetByMsgIds" resultType="com.ruoyi.entity.calc.SimPfRet">
        select id,msg_id ,pf_ret,start_dt ,end_dt from sim_pf_ret spr where msg_id in
        <foreach collection="msgIds" item="msgId" open="(" close=")" separator=",">
            #{msgId}
        </foreach>
    </select>

    <select id="querySegmentListByRetId" resultType="com.ruoyi.entity.calc.SimRetPfDmsSegment">
        select a.i_ind_value,
               a.ploss_value,
               a.ret_id,
               a.id,
               a.idx,
               b.psrid
        from sim_ret_pf_dms_segment a
                 inner join dev_dms_segment b on
            b.id = a.id
        where a.ret_id = #{retId}
        order by a.id,
                 a.idx
    </select>

    <select id="querySegmentAstInfoByPsrId" resultType="java.util.Map">
        select  rated_ampacity,psr_id, name, start_pole,
                case
                    when a.psr_id in (select psr_id from device_feeder_jk) then 'dkd'
                    when a.psr_id in (select psr_id from device_feeder_cable) then '0201'
                    end as psr_type,
                b.power_grid_code, b.feeder
        from (
                 select  rated_ampacity,psr_id, name, start_pole, feeder from device_feeder_jk
                 union all
                 select  rated_ampacity,psr_id, name, start_position as start_pole, feeder from device_feeder_cable
             ) a
                 inner join zh_dms_grid_feeder b on a.feeder = b.feeder
        where a.psr_id = #{psrId}
    </select>
    <select id="queryBreakListByRetId" resultType="com.ruoyi.entity.calc.SimRetPfDmsBreak">

        select a.i_value,
               a.v_value,
               a.ret_id,
               a.id,
               a.idx,
               b.psrid
        from sim_ret_pf_dms_breaker a
                 inner join dev_dms_breaker b on
            b.id = a.id
        where a.ret_id = #{retId}
        order by a.id,
                 a.idx
    </select>
    <select id="queryBreakAstInfoByPsrId" resultType="java.util.Map">
        SELECT
            b.psr_id,
            b.name,
            a.combination,
            a.combination_type,
            a.ast_type,
            b.feeder,
            a.rated_current,
            c.power_grid_code
        FROM
            device_station_breaker_ast a
                INNER JOIN
            device_station_breaker b ON a.ast_id = b.ast_id AND b.psr_id = #{psrId}
                INNER JOIN
            zh_dms_grid_feeder c ON b.feeder = c.feeder
    </select>
    <select id="queryEmsBreakList" resultType="com.ruoyi.entity.calc.SimRetPfEmsBreak">
        select p_ind_value,
               ret_id,
               a.id,
               idx,
               b.psrid,
               c.psrid feederId,
               b.name
        from sim_ret_pf_ems_breaker a
                 left join dev_ems_breaker b on
            a.id = b.id
                 left join con_dms_feeder c on
            b.feeder_id = c.id
        where a.ret_id = #{retId}
        order by a.id, a.idx
    </select>
    <select id="queryTrByFeeder" resultType="java.lang.Long">
        select distinct A.id
        from dms.tp_tr A
                 left join dms.dev_dms_tr B on
            A.id = B.id
                 left join dms.dev_dms_breaker C on
            A.branch_cb = C.id
                 left join dms.dev_dms_breaker D on
            A.main_cb = D.id
        where A.feeder_psrid = #{feederId}
    </select>
    <select id="queryLdByFeeder" resultType="java.lang.Long">
        select A.id
        from dms.tp_ld A
                 left join dms.dev_dms_ld B on
            A.id = B.id
                 left join dms.dev_dms_breaker C on
            A.branch_cb = C.id
                 left join dms.dev_dms_breaker D on
            A.main_cb = D.id
        where A.feeder_psrid = #{feederId}
    </select>
    <select id="queryMaxPValueAndQValueByTr" resultType="java.util.Map">
        select COALESCE(max(q_value),
                        0) as qValue,
               COALESCE(max(p_value),
                        0) as pValue
        from sim_ret_pf_dms_dt srpdd
        where id = #{trId}
          and ret_id = #{retId}
    </select>
    <select id="queryToFeeder" resultType="java.util.Map">
        select a.id,
               b.psrid,
               b."name"         breakName,
               c.id             feederId,
               c.psrid          feederPsrId,
               c."name"         feeder,
               d.id             toFeederId,
               d.psrid          toFeederPsrId,
               d."name"         toFeeder,
               SPLIT_PART(b.psrid,
                          '_',
                          2) as breakType
        from dms.tp_link_cb a,
             dms.dev_dms_breaker b,
             dms.con_dms_feeder c,
             dms.con_dms_feeder d
        where a.id = b.id
          and a.feeder_id0 = c.id
          and a.feeder_id1 = d.id
          and a.feeder_psrid0 = #{feederId}
    </select>
    <select id="queryMaxPValueAndQValueByLd" resultType="java.util.Map">
        select COALESCE(max(q_value),
                        0) as qValue,
               COALESCE(max(p_value),
                        0) as pValue
        from sim_ret_pf_dms_load srpdd
        where id = #{trId}
          and ret_id = #{retId}
    </select>
    <select id="queryCurveListByPsrId" resultType="java.util.Map">
        select CONCAT('PD_', psr_type, '_', psr_id) as id,
        psr_type,
        group_concat(case when meas_type_code = 'TotW' then value end) vec_p,
        group_concat(case when meas_type_code = 'TotVar' then value end) vec_q
        from pb_curve_template pct
        where psr_id in
        <foreach collection="psrIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        group by psr_id,
        psr_type
    </select>
    <select id="queryMainPath" resultType="com.ruoyi.vo.MainPathVO">
        select
            a.id,
            (case
                    when b.psrid like '%@%' then
                        split_part(
                                split_part(b.psrid,
                                           '_',
                                           3),
                                '@',
                                2
                        )
                    else
                        split_part(b.psrid,
                                   '_',
                                   3)
            end
                   )
                             as psrId,

               SPLIT_PART(b.psrid,
                          '_',
                          2) as psrType,
               B.name,
               A.idx,
               A.path_id,
               A.is_link
        from dms.tp_main_path_cb A
                 left join dms.dev_dms_breaker B on
            A.id = B.id
                 left join dms.dev_ems_breaker C on
            A.id = C.id
        where A.feeder_psrid = #{feederId}
          and a.idx != 0
          and a.path_id = (select path_id
                           from tp_main_path_cb tmpc
                           where id = (select id
                                       from dev_dms_breaker ddb
                                       where psrid like CONCAT('%', #{breakId}, '%'))
                             and feeder_psrid = #{feederId})
          and a.is_link != 1
        order by A.path_id asc,
                 A.idx asc
    </select>
    <select id="queryBreakListById" resultType="java.util.Map">
        select
               psrid
        from dev_dms_segment
        where id = #{id}
    </select>

</mapper>
