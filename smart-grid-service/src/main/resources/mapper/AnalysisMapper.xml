<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.electricity.ElectricityAnalysisMapper">

    <!-- 间隔单元 -->
    <select id="selectVolListWithTypeAndIndustry" resultType="com.ruoyi.entity.electricity.vo.AnalysisCustVo">
        SELECT
        a.*,
        t.code_cls_val_name AS typeName,
        i.code_cls_val_name AS industryName
        FROM analysis_cust a
        LEFT JOIN electricity_analysis_type t ON a.ec_categ = t.code_cls_val
        LEFT JOIN electricity_analysis_industry i ON a.ind_cls = i.code_cls_val
        <where>
            <if test="bo.ecCateg != null and bo.ecCateg != ''">
                AND a.ec_categ = #{bo.ecCateg}
            </if>
            <if test="bo.isDoublePower != null and bo.isDoublePower != ''">
                AND a.is_double_power = #{bo.isDoublePower}
            </if>
            <if test="bo.imptlv != null and bo.imptlv != ''">
                AND a.imptlv = #{bo.imptlv}
            </if>
            <if test="bo.indCls != null and bo.indCls != ''">
                AND a.ind_cls = #{bo.indCls}
            </if>
            <if test="bo.custName != null and bo.custName != ''">
                AND a.cust_name LIKE CONCAT('%', #{bo.custName}, '%')
            </if>
            <if test="bo.code != null and bo.code != ''">
                AND a.grid_code LIKE CONCAT('%', #{bo.code}, '%')
            </if>
            <if test="bo.custCls != null and bo.custCls != ''">
                AND a.cust_cls = #{bo.custCls}
            </if>
        </where>
    </select>


</mapper>
