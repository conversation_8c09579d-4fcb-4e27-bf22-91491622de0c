<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.KgMapper">

    <!-- 查询参数对象 -->
    <resultMap id="stationKgResultMap" type="com.ruoyi.entity.device.StationKg">
        <result column="psr_id" property="psrId"/>
        <result column="psr_type" property="psrType"/>
        <result column="ast_id" property="astId"/>
        <result column="name" property="name"/>
        <result column="station" property="station"/>
        <result column="station_type" property="stationType"/>
        <result column="bay" property="bay"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="psr_state" property="psrState"/>
        <result column="branch_feeder" property="branchFeeder"/>
        <result column="feeder" property="feeder"/>
        <result column="pub_priv_flag" property="pubPrivFlag"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
    </resultMap>

    <!-- 查看中压用户接入点 -->
    <sql id="stationKgWhere">
        WHERE psr_state IN ('10', '20') AND feeder = #{feeder}
        <if test="idList != null and idList.size() > 0">
            AND station IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!-- 定义公共字段 -->
    <sql id="base_kg">
        psr_id
        ,ast_id,name,station,station_type,bay,voltage_level,psr_state,branch_feeder,feeder,pub_priv_flag,longitude,latitude
    </sql>

    <!-- 查询站房下的的开关 -->
    <select id="selectStationKg" resultMap="stationKgResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0307' AS psr_type FROM device_station_load_kg
        <include refid="stationKgWhere"/>

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0305' AS psr_type FROM device_station_breaker
        <include refid="stationKgWhere"/>

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0306' AS psr_type
        FROM device_station_isolate_kg
        <include refid="stationKgWhere"/>
    </select>

    <!-- 查询站房下的的开关 -->
    <select id="selectStationKgByStationId" resultMap="stationKgResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0307' AS psr_type FROM device_station_load_kg
        where station = #{stationId}

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0305' AS psr_type FROM device_station_breaker
        where station = #{stationId}

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0306' AS psr_type
        FROM device_station_isolate_kg
        where station = #{stationId}
    </select>

    <sql id="psrIdsKgWhere">
        WHERE
        <if test="psrIdList != null and psrIdList.size() > 0">
            psr_id IN
            <foreach collection="psrIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!-- 查询的开关集合 根据设备ID集合 -->
    <select id="selectStationKgByPsrIds" resultMap="stationKgResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0307' AS psr_type FROM device_station_load_kg
        <include refid="psrIdsKgWhere"/>

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0305' AS psr_type FROM device_station_breaker
        <include refid="psrIdsKgWhere"/>

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0306' AS psr_type
        FROM device_station_isolate_kg
        <include refid="psrIdsKgWhere"/>
    </select>

    <!-- 线路下的设备查询条件 -->
    <sql id="selectFeederKgWhere">
        WHERE psr_state IN ('10', '20')  AND  "name"  LIKE '%备用%' AND longitude IS NOT NULL AND latitude IS NOT NULL
        <if test="idList != null and idList.size() > 0">
            AND feeder IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!-- 查询线路下的备用开关 -->
    <select id="selectFeederKg" resultMap="stationKgResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0307' AS psr_type FROM device_station_load_kg
        <include refid="selectFeederKgWhere"/>

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0305' AS psr_type FROM device_station_breaker
        <include refid="selectFeederKgWhere"/>

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        <include refid="base_kg"/>
        , '0306' AS psr_type
        FROM device_station_isolate_kg
        <include refid="selectFeederKgWhere"/>


    </select>

    <!-- 查询参数对象 -->
    <resultMap id="stationOutsideKgResultMap" type="com.ruoyi.entity.device.StationKg">
        <result column="psr_id" property="psrId"/>
        <result column="psr_type" property="psrType"/>
        <result column="ast_id" property="astId"/>
        <result column="name" property="name"/>
        <result column="voltage_level" property="voltageLevel"/>
        <result column="psr_state" property="psrState"/>
        <result column="branch_feeder" property="branchFeeder"/>
        <result column="feeder" property="feeder"/>
        <result column="pub_priv_flag" property="pubPrivFlag"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
    </resultMap>



    <select id="selectOutsideFeederKg" resultMap="stationOutsideKgResultMap">
      select  psr_id,ast_id,name,voltage_level,psr_state,branch_feeder,feeder,pub_priv_flag,longitude,latitude from
          device_pole_break
        WHERE psr_state IN ('10', '20')  AND  "name"  LIKE '%备用%' AND longitude IS NOT NULL AND latitude IS NOT NULL
        <if test="idList != null and idList.size() > 0">
            AND feeder IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
