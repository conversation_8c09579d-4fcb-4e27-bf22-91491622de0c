<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.znap.BayQueryMapper">

    <!-- EmsLdLink结果映射 -->
    <resultMap id="EmsLdLinkResult" type="com.ruoyi.entity.znap.EmsLdLink">
        <id property="id" column="id"/>
        <result property="ldName" column="ld_name"/>
        <result property="fdName" column="fd_name"/>
        <result property="psrid" column="psrid"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- TopoLoadNj结果映射 -->
    <resultMap id="TopoLoadNjResult" type="com.ruoyi.entity.znap.TopoLoadNj">
        <id property="mrid" column="mrid"/>
        <result property="num" column="num"/>
        <result property="name" column="name"/>
        <result property="pathname" column="pathname"/>
        <result property="substation" column="substation"/>
        <result property="iNode" column="i_node"/>
        <result property="basevoltage" column="basevoltage"/>
        <result property="voltagelevel" column="voltagelevel"/>
        <result property="bay" column="bay"/>
        <result property="p" column="p"/>
        <result property="q" column="q"/>
        <result property="createtime" column="createtime"/>
    </resultMap>

    <!-- TopoTraceReal结果映射 -->
    <resultMap id="TopoTraceRealResult" type="com.ruoyi.entity.znap.TopoTraceReal">
        <id property="id" column="id"/>
        <result property="loadMrid" column="load_mrid"/>
        <result property="loadName" column="load_name"/>
        <result property="allPathMrids" column="all_path_mrids"/>
        <result property="trwindingPathMrids" column="trwinding_path_mrids"/>
        <result property="historyYx" column="history_yx"/>
        <result property="computedDatetime" column="computed_datetime"/>
        <result property="startBreak" column="start_break"/>
        <result property="startBreakName" column="start_break_name"/>
        <result property="busbar" column="busbar"/>
        <result property="busbarName" column="busbar_name"/>
    </resultMap>


    <!-- TopoSubstationNj结果映射 -->
    <resultMap id="TopoSubstationNjResult" type="com.ruoyi.entity.znap.TopoSubstationNj">
        <id property="mrid" column="mrid"/>
        <result property="name" column="name"/>
    </resultMap>


    <!-- 根据psrId查询ems_ld_link中的线路信息 -->
    <select id="selectLineByPsrId" parameterType="String" resultMap="EmsLdLinkResult">
        SELECT id, ld_name, fd_name, psrid, update_time
        FROM ems_ld_link
        WHERE psrid = CONCAT('PD_dkx_', #{psrId})
        limit 1
    </select>

    <!-- 根据mrid查询ems_ld_link中的线路信息 -->
    <select id="selectLineById" parameterType="Long" resultMap="EmsLdLinkResult">
        SELECT id, ld_name, fd_name, psrid, update_time
        FROM ems_ld_link
        WHERE id = #{id}
    </select>

    <!-- 根据线路ID查询线路拓扑信息 -->
    <select id="selectLineTopoInfo" parameterType="String" resultMap="TopoLoadNjResult">
        SELECT num, mrid, name, pathname, substation, i_node, basevoltage, voltagelevel, bay, p, q, createtime
        FROM topo_load_nj
        WHERE mrid = #{lineId}
        limit 1
    </select>

    <!-- 根据线路ID查询拓扑关系 -->
    <select id="selectLineTopoTrace" parameterType="String" resultMap="TopoTraceRealResult">
        SELECT load_mrid, load_name, all_path_mrids, mf_all_path_mrids,
               trwinding_path_mrids, mf_trwinding_path_mrids, history_yx,
               computed_datetime, id, duration_rate, start_break, start_break_name,
               busbar, busbar_name
        FROM topo_trace_real
        WHERE load_mrid = #{lineId}
        limit 1
    </select>



    <!-- 根据变电站ID查询变电站信息 -->
    <select id="selectSubstationInfo" parameterType="String" resultMap="TopoSubstationNjResult">
        SELECT mrid, name
        FROM topo_substation_nj
        WHERE mrid = #{substationId}
    </select>


    <!-- 根据母线ID查询同母线下的所有线路和间隔 -->
    <select id="selectLinesByBusbar" parameterType="String" resultMap="TopoTraceRealResult">
        SELECT load_mrid, load_name, start_break, start_break_name, busbar, busbar_name
        FROM topo_trace_real
        WHERE busbar = #{busbarId}
    </select>

    <!-- 根据变电站下的线路和间隔获取所有母线ID（去重） -->
    <select id="selectBusbarsBySubstation" parameterType="String" resultMap="TopoTraceRealResult">
        SELECT DISTINCT busbar, busbar_name
        FROM topo_trace_real
        WHERE load_mrid IN (
            SELECT mrid FROM topo_load_nj WHERE substation = #{substationId}
        )
        OR start_break IN (
            SELECT mrid FROM topo_breaker_nj WHERE substation = #{substationId}
        )
    </select>
    <!--根据变电站查询所有开关-->
    <select id="selectSwitchBySubstation" parameterType="String" resultMap="TopoTraceRealResult">
        SELECT DISTINCT start_break, start_break_name
        FROM topo_trace_real
        WHERE load_mrid IN (
            SELECT mrid FROM topo_load_nj WHERE substation = #{substationId}
        )
        OR start_break IN (
            SELECT mrid FROM topo_breaker_nj WHERE substation = #{substationId}
        )
       AND busbar IS NOT NULL
       AND busbar_name IS NOT NULL
    </select>


</mapper>
