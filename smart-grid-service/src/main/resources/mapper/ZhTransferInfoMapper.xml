<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.ZhTransferInfoMapper">

    <resultMap type="com.ruoyi.entity.calc.ZhTransferInfo" id="ZhTransferInfoResult">
        <result property="id" column="id"/>
        <result property="sourceFeeder" column="source_feeder"/>
        <result property="sourceFeederName" column="source_feeder_name"/>
        <result property="sourceFeederCurrent" column="source_feeder_current"/>
        <result property="linkFeederCurrent" column="link_feeder_current"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="sourceFeederBreak" column="source_feeder_break"/>
        <result property="sourceFeederBreakName" column="source_feeder_break_name"/>
        <result property="calcId" column="calc_id"/>
        <result property="retId" column="ret_id"/>
        <result property="time" column="time"/>
        <result property="linkFeederRate" column="link_feeder_rate"/>
    </resultMap>
    <select id="statistics" resultType="com.ruoyi.vo.NReduceOneVo">
        select 'feeder' as type,
        count(1) as number
        from device_feeder mdfd
        where 1=1
        <if test="instanceId !=null and instanceId !=''">
            and grid_code = ( select grid_code from calc_instance_info cii where instance_id=#{instanceId})
        </if>
        <if test="gridCode !=null and gridCode !=''">
            and grid_code = #{gridCode}
        </if>
        and mdfd.psr_state in (10, 20)
    </select>
    <select id="queryMaxCalcId" resultType="java.lang.String">
        select id
        from eps_alarm_version eav
        order by start_time desc
        limit 1
    </select>
    <select id="statisticsByType" resultType="java.util.Map">
        <choose>
            <when test="type != null and type !='' and type=='feeder'">
                select
                a.pass_number,
                b.no_pass_number,
                ROUND(a.pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as pass_rate,
                ROUND(b.no_pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as no_pass_rate
                from
                (
                select
                count(DISTINCT source_feeder) as pass_number
                from
                zh_transfer_info zti
                where
                1 = 1
                and transfer_status = 1
                )a,
                (
                select
                count(DISTINCT psr_id) as no_pass_number
                from
                device_feeder mdfd
                where
                psr_state in (10, 20)
                and psr_id not in (
                select
                DISTINCT source_feeder
                from
                zh_transfer_info zti
                where
                1 = 1
                and transfer_status = 1
                )
                )b
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>
    <select id="statisticsByInstanceId" resultType="java.util.Map">
        <choose>
            <when test="type != null and type !='' and type=='feeder'">
                select
                a.pass_number,
                b.no_pass_number,
                ROUND(a.pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as pass_rate,
                ROUND(b.no_pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as no_pass_rate
                from
                (
                select
                count(DISTINCT source_feeder) as pass_number
                from
                zh_transfer_info zti
                where
                1 = 1
                and zti.calc_id = #{instanceId}
                and transfer_status = 1
                )a,
                (
                select
                count(DISTINCT psr_id) as no_pass_number
                from
                device_feeder mdfd
                where
                psr_state in (10, 20)
                and grid_code =(
                select
                grid_code
                from
                calc_instance_info cii
                where
                instance_id = #{instanceId})
                and psr_id not in (
                select
                DISTINCT source_feeder
                from
                zh_transfer_info zti
                where
                1 = 1
                and zti.calc_id = #{instanceId}
                and transfer_status = 1
                )
                )b
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>
    <select id="statisticsByGridCode" resultType="java.util.Map">
        <choose>
            <when test="type != null and type !='' and type=='feeder'">
                select
                a.pass_number,
                b.no_pass_number,
                ROUND(a.pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as pass_rate,
                ROUND(b.no_pass_number /(a.pass_number + b.no_pass_number), 4) * 100 as no_pass_rate
                from
                (
                select
                count(DISTINCT source_feeder) as pass_number
                from
                zh_transfer_info zti
                where
                1 = 1
                and zti.calc_id = (select max(instance_id) from calc_instance_info where grid_code=#{gridCode} and
                is_auto=1)
                and transfer_status = 1
                )a,
                (
                select
                count(DISTINCT psr_id) as no_pass_number
                from
                device_feeder mdfd
                where
                psr_state in (10, 20)
                and grid_code =#{gridCode}
                and psr_id not in (
                select
                DISTINCT source_feeder
                from
                zh_transfer_info zti
                where
                1 = 1
                and zti.calc_id = (select max(instance_id) from calc_instance_info where grid_code=#{gridCode} and
                is_auto=1)
                and transfer_status = 1
                )
                )b
            </when>
            <otherwise>

            </otherwise>
        </choose>
    </select>
    <select id="selectListById" resultType="com.ruoyi.entity.calc.ZhTransferInfo">
        select *
        from zh_transfer_info
        where calc_id = #{calcId}
          and transfer_status = 1
    </select>


</mapper>
