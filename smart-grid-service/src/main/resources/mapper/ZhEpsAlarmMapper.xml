<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.ZhEpsAlarmMapper">
<!--    <select id="statistics" resultType="java.util.Map">-->
<!--        select a.id, a.description, sum(case when b.alarm_type is not null then 1 else 0 end) as total from zh_eps_alarm_config a-->
<!--        left join zh_eps_alarm b-->
<!--        on a.id = b.alarm_type and version_id = (select id from eps_alarm_version where version = (select max(version) as version from eps_alarm_version))-->
<!--        <where>-->
<!--            <if test="grid!=null and grid!=''">-->
<!--                and b.simu_case_no like concat(#{grid},'%')-->
<!--            </if>-->
<!--        </where>-->
<!--        group by a.id, a.description-->
<!--    </select>-->
    <select id="statistics" resultType="java.util.Map">
        select
        a.id,
        a.description,
        sum(case when b.alarm_type is not null then 1 else 0 end) as total
        from
        zh_eps_alarm_config a
        left join calc_alarm_info b
        on
        a.id = b.alarm_type
        and DATE(b.alarm_time) &lt;= (
        select MAX(alarm_time)::date
        from calc_alarm_info cii
        )
        <where>
            <if test="grid!=null and grid!=''">
                and b.simu_case_no like #{grid}||'%'
            </if>
        </where>
        group by
        a.id,
        a.description
    </select>


    <select id="detailList" resultType="com.ruoyi.entity.calc.EpsEvent">
        select * from zh_eps_event where alarm_id = #{alarmId}
    </select>
    <select id="lastCalcTime" resultType="java.util.Date">
        select start_time from eps_alarm_version where version = (select max(version) from eps_alarm_version)
    </select>

    <select id="queryAlarmPageList" resultType="com.ruoyi.vo.ZhEpsAlarmVo">
        select id,
        model_id,
        simu_case_no,
        alarm_time,
        alarm_context,
        alarm_type,
        model_type,
        psr_id,
        psr_type,
        feeder_id,
        status,
        level,
        (select count(*) from zh_eps_event where alarm_id = zh_eps_alarm.id) as event_count,
        version_id,
        substring_index(simu_case_no,':',-1) as retId
        from zh_eps_alarm
        <where>
            ${ew.sqlSegment}
            and version_id = (SELECT id FROM eps_alarm_version WHERE version = (SELECT max(version) AS version FROM
            eps_alarm_version))
            <if test="bo.gridName!=null and bo.gridName!=''">
                and substring_index(simu_case_no,':',1) in(select psr_id from problem_grid where grid_name like
                concat('%',#{bo.gridName},'%'))
            </if>
            <if test="bo.grid!=null and bo.grid!=''">
                and substring_index(simu_case_no,':',1) = #{bo.grid}
            </if>
            <if test="bo.status!=null">
                and status = #{bo.status}
            </if>
        </where>
    </select>
<!--    <select id="equipmentList" resultType="java.util.Map">-->
<!--        select id,psr_id,psr_type,alarm_type,status,alarm_context from zh_eps_alarm-->
<!--        where psr_id is not null and version_id = (SELECT id FROM eps_alarm_version WHERE version = (SELECT max(version) AS version FROM eps_alarm_version))-->
<!--        <if test="grid!=null and grid!=''">-->
<!--            and substring_index(simu_case_no,':',1) = #{grid}-->
<!--        </if>-->
<!--    </select>-->
    <select id="equipmentList" resultType="java.util.Map">
        select id,psr_id as modelId, psr_type as modelType,psr_parent_id as psrId, psr_parent_type as psrType,alarm_type ,status ,alarm_content as alarmContext from calc_alarm_info cai
        where is_auto = 1
        and DATE(alarm_time) = (
        select MAX(start_time)::date
        from calc_instance_info cii
        where is_auto = 1
        and state = 1
        )
        <if test="grid!=null and grid!=''">
            and SPLIT_PART(simu_case_no,':',1) = #{grid}
        </if>
    </select>

    <select id="queryHistoryPageList" resultType="com.ruoyi.vo.ZhEpsAlarmVo">
        select id,
        model_id,
        simu_case_no,
        alarm_time,
        alarm_context,
        alarm_type,
        model_type,
        psr_id,
        psr_type,
        feeder_id,
        status
        from zh_eps_alarm
        <where>
            ${ew.sqlSegment}
            and version_id in (SELECT id FROM eps_alarm_version WHERE version <![CDATA[<]]> (SELECT max(version) AS version FROM eps_alarm_version))
            <if test="bo.gridName!=null and bo.gridName!=''">
                and SPLIT_PART(simu_case_no,':',1) in(select psr_id from problem_grid where grid_name like '%'||#{bo.gridName}||'%')
            </if>
            <if test="bo.grid!=null and bo.grid!=''">
                and SPLIT_PART(simu_case_no,':',1) = #{bo.grid}
            </if>
            <if test="bo.status!=null">
                and status = #{bo.status}
            </if>
            order by alarm_time desc
        </where>
    </select>

    <select id="queryAlarmFeederList" resultType="java.lang.String">
        select
        distinct
        feeder_id
        from zh_eps_alarm
        <where>
            version_id = (SELECT id FROM eps_alarm_version WHERE version = (SELECT max(version) AS version FROM eps_alarm_version))
            and SPLIT_PART(simu_case_no,':',1) = #{gridCode}
        </where>
    </select>


	<resultMap id="periodAlarm" type="java.util.Map">
        <result typeHandler="org.apache.ibatis.type.IntegerTypeHandler" property="lowPeriod" column="low_period"/>
        <result typeHandler="org.apache.ibatis.type.IntegerTypeHandler" property="highPeriod" column="high_period"/>
    </resultMap>


    <select id="periodAlarm" resultMap="periodAlarm">
        select
        sum(case when a.accumulative <![CDATA[>=]]> b.low_period and a.accumulative <![CDATA[<=]]> b.high_period then 1
        else 0 end) as low_period,
        sum(case when a.accumulative > b.high_period then 1 else 0 end) as high_period
        from calc_alarm_info a
        inner join (select low_period, high_period,id from zh_eps_alarm_config where id = 11) b
        on a.alarm_type = b.id
        and a.is_auto=1
        and DATE(a.alarm_time) = (
        select MAX(start_time)::date
        from calc_instance_info cii
        where is_auto = 1
        and state = 1
        )
        <where>
            <if test="grid!=null and grid!=''">
                and a.simu_case_no like #{grid}||'%'
            </if>
        </where>
    </select>

    <select id="queryLowPeriodList" resultType="com.ruoyi.vo.ZhEpsAlarmVo">
        select id,
        model_id,
        simu_case_no,
        alarm_time,
        alarm_context,
        alarm_type,
        model_type,
        model_name,
        psr_id,
        psr_type,
        feeder_id,
        status
        from zh_eps_alarm
        <where>
            and version_id = (SELECT id FROM eps_alarm_version WHERE version <![CDATA[=]]> (SELECT max(version) AS version FROM eps_alarm_version))
            and accumulative <![CDATA[>=]]> #{lowPeriod} and accumulative <![CDATA[<=]]> #{highPeriod}
            <if test="gridName!=null and gridName!=''">
                and SPLIT_PART(simu_case_no,':',1) in(select psr_id from problem_grid where grid_name like '%'||#{gridName}||'%')
            </if>
            <if test="grid!=null and grid!=''">
                and SPLIT_PART(simu_case_no,':',1) = #{grid}
            </if>
            <if test="status!=null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="queryHightPeriodList" resultType="com.ruoyi.vo.ZhEpsAlarmVo">
        select id,
               model_id,
               simu_case_no,
               alarm_time,
               alarm_context,
               alarm_type,
               model_type,
               model_name,
               psr_id,
               psr_type,
               feeder_id,
               status
        from zh_eps_alarm
        <where>
            and version_id = (SELECT id FROM eps_alarm_version WHERE version <![CDATA[=]]> (SELECT max(version) AS version FROM eps_alarm_version))
            and accumulative > #{highPeriod}
            <if test="gridName!=null and gridName!=''">
                and SPLIT_PART(simu_case_no,':',1) in(select psr_id from problem_grid where grid_name like concat('%',#{gridName},'%'))
            </if>
            <if test="grid!=null and grid!=''">
                and SPLIT_PART(simu_case_no,':',1) = #{grid}
            </if>
            <if test="status!=null">
                and status = #{status}
            </if>
        </where>
    </select>


    <select id="historyStatisticsByLevel" resultType="java.util.Map">
        SELECT
            a.level AS level,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM calc_alarm_info), 2) AS ratio
        FROM calc_alarm_info a
        GROUP BY a.level
    </select>

    <select id="historyStatisticsByGrid" resultType="java.util.Map">
        SELECT
            p.grid_name as grid_Name,
            ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM calc_alarm_info)), 2) AS ratio
        FROM calc_alarm_info a
                 LEFT JOIN problem_grid p ON a.grid_id = p.psr_id
        GROUP BY a.grid_id, p.grid_name
        ORDER BY ratio DESC;
    </select>

    <select id="historyStatisticsByType" resultType="java.util.Map">
        SELECT
            r.description AS description,
            ROUND((COUNT(DISTINCT a.id) * 100.0 /
                   (SELECT COUNT(*) FROM calc_alarm_info)), 2) AS ratio
        FROM calc_alarm_info a
                 LEFT JOIN calc_alarm_rule r ON a.alarm_type = r.id
        GROUP BY a.alarm_type, r.description
    </select>

    <select id="levelAlarmStatistics" resultType="java.util.Map">
        select level,
               round((grp::numeric / total) * 100, 2) as ratio
        from (select level,
                     count(*) as grp
              from calc_alarm_info a
              where is_auto = 1
                and DATE(alarm_time) = to_date((select max(start_time)
                                                from calc_instance_info cii
                                                where is_auto = 1
                                                  and state = 1),
                                               'YYYY-MM-DD')
              group by level) a
                 join (select count(*) as total
                       from calc_alarm_info
                       where is_auto = 1
                         and DATE(alarm_time) = to_date((select max(start_time)
                                                         from calc_instance_info cii
                                                         where is_auto = 1
                                                           and state = 1),
                                                        'YYYY-MM-DD')) b
    </select>


    <select id="queryHistoryList" resultType="com.ruoyi.vo.AlarmHistoryVo">
        <![CDATA[
            SELECT
                p.grid_name as grid_name,
                a.simu_case_no,
                a.alarm_time,
                a.alarm_content as alarmContext,
                r.description as alarmType
            FROM calc_alarm_info a
                     LEFT JOIN problem_grid p ON a.grid_id = p.psr_id
                     LEFT JOIN calc_alarm_rule r ON a.alarm_type = r.id
            where a.alarm_time >= #{start} and a.alarm_time <= #{end}
            ]]>
    </select>

    <select id="queryDmsBreakCurveList" resultType="com.ruoyi.entity.calc.AlarmCurve">
        select distinct idx,
                        to_char(interval '15 minutes' * idx, 'HH24:MI') as time,
                        to_char(spr.create_dt, 'yyyy-mm-dd') as date,
                        srpdb.i_value as iValue,
                        srpdb.v_value as vValue
        from sim_ret_pf_dms_breaker srpdb
                 inner join dev_dms_breaker ddb
                            on
                                srpdb.id = ddb.id
                 inner join sim_pf_ret spr on
            spr.id = srpdb.ret_id
        where spr.id = #{retId}
                  and ddb.psrid like '%' || #{modelId} || '%'
    </select>

    <select id="queryEmsBreakCurveList" resultType="com.ruoyi.entity.calc.AlarmCurve">
        select distinct idx,
                        to_char(interval '15 minutes' * idx, 'HH24:MI') as time,
                        to_char(spr.create_dt,'yyyy-mm-dd') as date,
                        srpeb.p_ind_value as pValue
        from
            sim_ret_pf_ems_breaker srpeb
                inner join dev_ems_breaker deb
                           on
                               srpeb.id = deb.id
                inner join sim_pf_ret spr on
                spr.id = srpeb.ret_id
        where
            spr.id = #{retId}
                and deb.psrid like '%' || #{modelId} || '%'
    </select>


    <select id="querySegmentCurveList" resultType="com.ruoyi.entity.calc.AlarmCurve">
        select distinct idx,
                        to_char(interval '15 minutes' * idx, 'HH24:MI') as time,
                        to_char(spr.create_dt,'yyyy-mm-dd') as date,
                        srpds.i_ind_value as iValue,
                        srpds.ploss_value::float8 as pLossValue
        from
            sim_ret_pf_dms_segment srpds
                inner join dev_dms_segment dds
                           on
                               srpds.id = dds.id
                inner join sim_pf_ret spr on
                spr.id = srpds.ret_id
        where
            spr.id = #{retId}
                and dds.psrid like '%' || #{modelId} || '%'
    </select>
</mapper>
