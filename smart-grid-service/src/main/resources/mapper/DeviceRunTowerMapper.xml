<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.DeviceRunTowerMapper">

    <!-- 间隔单元 -->
    <select id="selectPosition" resultType="com.ruoyi.entity.device.vo.RunTowerPosition">
        SELECT A.psr_id,A.ast_id,A.name,B.geo_positon FROM "device_run_tower" A LEFT JOIN "device_wlgt" B on A.ast_id =
        B.ast_id WHERE A.psr_id in
        <foreach collection="psrIds" item="psrid" open="(" separator="," close=")">
            #{psrid}
        </foreach>
    </select>


</mapper>
