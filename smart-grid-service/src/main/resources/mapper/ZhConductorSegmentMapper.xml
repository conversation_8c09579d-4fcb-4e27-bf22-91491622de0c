<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.ZhConductorSegmentMapper">


    <select id="queryRateValueById" resultType="java.lang.Integer">
        select  COALESCE(rated_ampacity, 0) AS rateValue  from device_feeder_jk
        union all
        select  COALESCE(rated_ampacity, 0) AS rateValue from device_feeder_cable
        where psr_id = 'f063006d-e8cb-47a9-9672-fe8f34f8c059'
    </select>
</mapper>