<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.simulation.SimulationMapper">

    <select id="selectSimRetPfDmsBreaker" resultType="com.ruoyi.entity.simulation.SimRetPfDmsBreaker">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_breaker A
        LEFT JOIN dev_dms_breaker B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfEmsBreaker" resultType="com.ruoyi.entity.simulation.SimRetPfEmsBreaker">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_ems_breaker A
        LEFT JOIN dev_ems_breaker B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfDmsDisconnector" resultType="com.ruoyi.entity.simulation.SimRetPfDmsDisconnector">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_disconnector A
        LEFT JOIN dev_dms_disconnector B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfSegment" resultType="com.ruoyi.entity.simulation.SimRetPfDmsSegment">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_segment A
        LEFT JOIN dev_dms_segment B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfDmsBusbar" resultType="com.ruoyi.entity.simulation.SimRetPfDmsBusbar">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_busbar A
        LEFT JOIN dev_dms_busbar B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfDmsDt" resultType="com.ruoyi.entity.simulation.SimRetPfDmsDt">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_dt A
        LEFT JOIN dev_dms_tr B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectSimRetPfDmsLoad" resultType="com.ruoyi.entity.simulation.SimRetPfDmsLoad">
        SELECT A.*,B.name,B.psrid
        FROM sim_ret_pf_dms_load A
        LEFT JOIN dev_dms_ld B on A.id=B.id
        WHERE ret_id = #{retId}
        <if test="id != null and id != ''">
            AND A.id = #{id}
        </if>
    </select>

    <select id="selectRetId" resultType="java.lang.Long">
        SELECT id
        FROM sim_pf_ret
        WHERE msg_id = #{msgId} AND ret_code = 1
    </select>

</mapper>