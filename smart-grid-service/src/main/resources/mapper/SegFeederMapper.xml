<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.SegFeederMapper">

    <!-- 查询参数对象 -->
    <resultMap id="segFeederResultMap" type="com.ruoyi.entity.device.SegFeeder">
        <result column="psr_id" property="psrId"/>
        <result column="psr_type" property="psrType"/>
        <result column="name" property="name"/>
        <result column="branch_feeder" property="branchFeeder"/>
        <result column="line" property="line"/>
        <result column="coordinate" property="coordinate"/>
        <result column="length" property="length"/>
        <result column="start_position" property="startPosition"/>
        <result column="end_position" property="endPosition"/>
        <result column="start_type" property="startType"/>
        <result column="end_type" property="endType"/>
    </resultMap>

    <!-- 定义公共字段 -->
    <sql id="base_seg">
        psr_id
        ,name, branch_feeder,line,coordinate,coordinate,length
    </sql>

    <!-- 查看导线段 -->
    <select id="selectSegFeeder" resultMap="segFeederResultMap">
        <!-- 第一个表查询 -->
        SELECT
        <include refid="base_seg"/>
        , 'dxd' AS psr_type
        , start_pole AS start_position, '0103' AS start_type
        , stop_pole AS end_position, '0103' AS end_type
        FROM device_feeder_jk WHERE psr_id = #{psrId}

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        <include refid="base_seg"/>
        , '0201' AS psr_type,start_position,start_type,end_position,end_type
        FROM device_feeder_cable WHERE psr_id = #{psrId}
    </select>

</mapper>
