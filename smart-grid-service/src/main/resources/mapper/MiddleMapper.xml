<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.power.PowerMapper">

    <!-- 线路信息 -->
    <select id="selectMiddleDmsFeederDevice" resultType="com.ruoyi.entity.device.DeviceFeeder">
        SELECT * FROM device_feeder where grid_code = #{code}
    </select>

    <!-- 环网柜数量 -->
    <select id="selectSubstationHWG" resultType="java.lang.Integer" >
        SELECT COUNT(*) FROM device_hwg where grid_code = #{code}
    </select>

    <!-- 配电室数量 -->
    <select id="selectSubstationPDS" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM device_pds where grid_code = #{code}
    </select>

    <!-- 变电站内隔离开关 -->
    <select id="selectStationIsolateKg" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM device_station_isolate_kg where feeder in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 变电站内负荷开关 -->
    <select id="selectStationLoadKg" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM device_station_load_kg where feeder in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 开关站 -->
    <select id="selectSubstationKGZ" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM device_kgz where feeder in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <!-- 查询参数对象 -->
    <resultMap id="TransformerResultMap" type="com.ruoyi.entity.device.PBEntity">
        <result column="join_ec" property="joinEc" />
        <result column="feeder" property="feeder" />
        <result column="pub_priv_flag" property="pubPrivFlag" />
        <result column="name" property="name" />
        <result column="psr_id" property="psrId" />
        <result column="psr_state" property="psrState" />
        <result column="psr_type" property="psrType" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="rated_capacity" property="ratedCapacity" />
    </resultMap>

    <!-- 公共条件片段 -->
    <sql id="commonWhere">
        WHERE psr_state IN ('10', '20')
        <if test="feedIdList != null and feedIdList.size() > 0">
            AND feeder IN
            <foreach collection="feedIdList" item="feedId" open="(" separator="," close=")">
                #{feedId}
            </foreach>
        </if>
    </sql>

    <!-- 合并三个表的查询 -->
    <select id="selectTransformersByFeedId" resultMap="TransformerResultMap">
        SELECT * FROM (
        <!-- 第一个表查询 -->
        SELECT
        'device_station_transformer' AS source_table,
        feeder,
        pub_priv_flag,
        name,
        psr_id,
        psr_state,
        latitude,
        longitude,
        rated_capacity,
        join_ec,
        '0302' AS psr_type
        FROM device_station_transformer
        <include refid="commonWhere" />

        UNION ALL

        <!-- 第二个表查询 -->
        SELECT
        'device_pole_transformer' AS source_table,
        feeder,
        pub_priv_flag,
        name,
        psr_id,
        psr_state,
        latitude,
        longitude,
        rated_capacity,
        join_ec,
        '0110' AS psr_type
        FROM device_pole_transformer
        <include refid="commonWhere" />

        UNION ALL

        <!-- 第三个表查询 -->
        SELECT
        'station_service_transformer' AS source_table,
        feeder,
        pub_priv_flag,
        name,
        psr_id,
        psr_state,
        latitude,
        longitude,
        rated_capacity,
        join_ec,
        '0303' AS psr_type
        FROM station_service_transformer
        <include refid="commonWhere" />
        ) AS combined_data
    </select>

    <!-- 网格信息 -->
    <select id="selectPowerGrid" resultType="com.ruoyi.entity.power.PowerGrid">
        SELECT * FROM power_grid where grid_code = #{code}
    </select>






</mapper>
