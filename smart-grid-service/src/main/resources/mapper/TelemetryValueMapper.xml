<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.PmsZhTelemetryValueMapper">
    <select id="queryDeptMaintGroup" resultType="java.util.Map">
        select dept_no,dept_name from sys_dept where parent_id in
        <foreach collection="collection" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectPbValueByPsrIds" resultType="com.ruoyi.entity.calc.VecRunDomain">
        select CONCAT('PD_', a.psr_type, '_', a.psr_id) as id,
        vec_p,
        vec_q
        from (select psr_id,
        psr_type,
        value as vec_p
        from pb_curve_template pct
        where psr_id in
        <foreach collection="feederIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and meas_type_code = 'TotW') a,
        (select psr_id,
        psr_type,
        value as vec_q
        from pb_curve_template pct
        where psr_id in
        <foreach collection="feederIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and meas_type_code = 'TotVar') b
        where a.psr_id = b.psr_id
    </select>
    <select id="selectLdValueByPsrIds" resultType="com.ruoyi.entity.calc.VecRunDomain">
        select CONCAT('PD_', a.psr_type, '_', a.psr_id) as id,
        vec_p,
        vec_q
        from (select psr_id,
        psr_type,
        value as vec_p
        from pb_curve_template pct
        where psr_id in
        <foreach collection="feederIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and meas_type_code = 'TotW') a,
        (select psr_id,
        psr_type,
        value as vec_q
        from pb_curve_template pct
        where psr_id in
        <foreach collection="feederIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and meas_type_code = 'TotVar') b
        where a.psr_id = b.psr_id
    </select>
</mapper>
