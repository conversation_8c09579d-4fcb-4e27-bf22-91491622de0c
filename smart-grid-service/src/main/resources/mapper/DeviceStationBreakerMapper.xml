<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.device.DeviceStationBreakerMapper">

    <sql id="sql" >
        psr_id,ast_id,name,run_dev_name,full_path_name,city,maint_org,maint_group,equipment_owner,station,bay,
        station_type,voltage_level,psr_state,start_time,stop_time,is_rural,importance,regionalism,supply_area,
        switch_role,normal_open,dispatch_jurisdiction,dispatch_operation,dispatch_permission,dispatch_monitor,
        branch_feeder,feeder,ctime,pub_priv_flag,switch_subtype,last_update_time,reliable_segment,longitude,latitude
    </sql>

    <select id="selectByStationId" resultType="com.ruoyi.entity.device.DeviceStationBreaker">
        select
            <include refid="sql"/>
        from device_station_breaker
        where station = #{stationId}
    </select>
</mapper>
