<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.calc.ZhOptPlanMapper">

    <resultMap type="com.ruoyi.entity.calc.ZhOptPlan" id="ZhOptPlanResult">
        <result property="id" column="id"/>
        <result property="policyPlanType" column="policy_plan_type"/>
        <result property="contentType" column="content_type"/>
    </resultMap>

    <select id="queryFeederById" resultType="java.lang.String">
        select distinct feeder_id
        from zh_eps_alarm
        where version_id = (SELECT id
                            FROM eps_alarm_version
                            WHERE version = (SELECT max(version) AS version
                                             FROM eps_alarm_version))
          and split_part(simu_case_no, ':', 1) = #{id}
        union all
        select distinct feeder_id
        from calc_alarm_info cai
        where instance_id = #{id}
        limit 3
    </select>


</mapper>
