<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhr.myapp</groupId>
        <artifactId>smart-grid</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>smart-grid-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.zhr.load-forecast</groupId>
            <artifactId>load-forecast-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>net.objecthunter</groupId>
            <artifactId>exp4j</artifactId>
            <version>0.4.8</version>
        </dependency>

        <dependency>
            <groupId>org.locationtech.proj4j</groupId>
            <artifactId>proj4j</artifactId>
            <version>1.2.1</version>
        </dependency>

        <!-- GraphHopper核心库 -->
        <dependency>
            <groupId>com.graphhopper</groupId>
            <artifactId>graphhopper-core</artifactId>
            <version>3.0</version>
        </dependency>

        <!-- 用于解析OpenStreetMap数据 -->
        <dependency>
            <groupId>com.graphhopper</groupId>
            <artifactId>graphhopper-reader-osm</artifactId>
            <version>2.4</version>
        </dependency>

        <!-- 可选：用于解码Polyline路径 -->
        <dependency>
            <groupId>com.google.maps</groupId>
            <artifactId>google-maps-services</artifactId>
            <version>2.2.0</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.mozilla</groupId>
            <artifactId>rhino</artifactId>
            <version>1.7.14</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>4.8.3</version>
        </dependency>


        <dependency>
            <groupId>net.sourceforge.htmlunit</groupId>
            <artifactId>htmlunit</artifactId>
            <version>2.70.0</version>
        </dependency>

<!--            排除内嵌Tomcat-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- SLF4J API -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.7</version> <!-- 根据Spring Boot版本调整 -->
        </dependency>
        <!-- 处理和分析平面 -->
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.19.0</version>
        </dependency>



        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.0</version>
        </dependency>
        <dependency>
            <groupId>com.zhr</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhr</groupId>
            <artifactId>db-drivers</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zhr</groupId>
            <artifactId>ruoyi-workflow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhr</groupId>
            <artifactId>ruoyi-easy-trans</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.zhr</groupId>-->
<!--            <artifactId>ruoyi-magic</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zhr.myapp</groupId>
            <artifactId>smart-grid-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
