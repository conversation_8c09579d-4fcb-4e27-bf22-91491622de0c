	topo关系是由D5000数据支撑，因此所有设备的拓扑关系是根据D5000ID体系建立，若需要其他体系ID引入关系的话需要额外关联表。以下所述的ID字段说明的都是D5000ID，可能也有别称（mrid等）
	关键表介绍：topo_trace_real（拓扑关系表，里面说明了线路-开关-主变-母线的关系）、topo_load_nj（线路的拓扑信息表）、topo_breaker_nj（间隔的拓扑信息表）、topo_substation_nj（变电站的拓扑信息表）、topo_busbar_section_nj（母线的拓扑信息表）
```sql
create table topo_trace_real
(
    load_mrid               varchar(255),
    load_name               varchar(255),
    all_path_mrids          text,
    trwinding_path_mrids    text,
    history_yx              json,
    computed_datetime       timestamp(6),
    id                      bigint default nextval('topo_trace_real_id_seq'::regclass) not null
        primary key,
    mf_trwinding_path_mrids text,
    mf_all_path_mrids       text,
    duration_rate           numeric(10, 6),
    start_break             varchar(255),
    start_break_name        varchar(255),
    busbar                  varchar(255),
    busbar_name             varchar(255)
);


create table ems_ld_link
(
    id          bigint       not null,
    ld_name     varchar(255),
    fd_name     varchar(255),
    psrid       varchar(255) not null,
    update_time varchar(255),
    primary key (id, psrid),
    unique (id, psrid)
);

create table topo_load_nj
(
    num          varchar(255)                                 not null,
    mrid         varchar(255) default NULL::character varying not null
        primary key,
    name         varchar(255) default NULL::character varying,
    pathname     varchar(255) default NULL::character varying,
    substation   varchar(255) default NULL::character varying,
    i_node       varchar(255) default NULL::character varying,
    basevoltage  varchar(255) default NULL::character varying,
    voltagelevel varchar(255) default NULL::character varying,
    bay          varchar(255) default NULL::character varying,
    p            varchar(255) default NULL::character varying,
    q            varchar(255) default NULL::character varying,
    createtime   date
);

create table topo_breaker_nj
(
    num          varchar(255)                                 not null,
    mrid         varchar(255) default NULL::character varying not null
        primary key,
    name         varchar(255) default NULL::character varying,
    pathname     varchar(255) default NULL::character varying,
    type         varchar(255) default NULL::character varying,
    i_node       varchar(255) default NULL::character varying,
    j_node       varchar(255) default NULL::character varying,
    substation   varchar(255) default NULL::character varying,
    basevoltage  varchar(255) default NULL::character varying,
    voltagelevel varchar(255) default NULL::character varying,
    bay          varchar(255) default NULL::character varying,
    status       varchar(255) default NULL::character varying,
    mvarating    varchar(255) default NULL::character varying,
    createtime   date
);

```
### 1、线路查询
通过线路的 psrId 查询它本身所属母线、变电站以及同母线下的间隔
例如：10DKX-153553（默认已有线路基础信息）
1. 通过 `ems_ld_link` 将`psrId`映射成对应的D5000ID，在`ems_ld_link`表中的字段是`psrid`，这是一个带前缀的 `psrid`，把前缀`PD_dkx_`去掉即可，其 `psrid`关联的`id`就是对应的D5000ID；
2. 根据查询到的D5000ID到 `topo_load_nj` 获取到线路的拓扑信息，其中包括但不限于（变电站ID等）
步骤1~2总结
```SQL
-- 根据psrId查询设备mrid  
SELECT id AS 线路ID, ld_name AS 线路名称, psrid AS 带前缀的psrId  
FROM ems_ld_link  
WHERE psrid = CONCAT('PD_dkx_', ${psrId});  
  
-- 查询线路拓扑信息  
SELECT mrid AS 线路ID, name AS 线路名称, substation AS 变电站ID  
FROM topo_load_nj  
WHERE mrid = ${ems_ld_link_id};
  
-- 关联查询线路拓扑  
SELECT tln.mrid AS 线路ID, tln.substation AS 变电站ID FROM ems_ld_link ell LEFT JOIN topo_load_nj tln ON ell.id = tln.mrid  
WHERE ell.psrid = CONCAT('PD_dkx_', ${psrId});

```
3. 根据步骤1拿到的线路ID查询到`topo_trace_real`查询对应的线路负荷上级全路径以及其他关联的设备ID，其中有母线、主变、对应间隔等
步骤1、3总结
```SQL
-- 根据id拿到线路拓扑关系  
SELECT load_mrid               AS 线路ID,  
       load_name               AS 线路名称,  
       all_path_mrids          AS 上级关系ID全路径,  
       mf_all_path_mrids       AS 上级关系ID全路径（同等）,  
       trwinding_path_mrids    AS 上级主变关系ID全路径,  
       mf_trwinding_path_mrids AS 上级主变关系ID全路径（同等）,  
       start_break             AS 线路对应的间隔ID,  
       start_break_name        AS 线路对应的间隔名称,  
       busbar                  AS 线路对应的母线ID,  
       busbar_name             AS 线路对应的母线名称  
FROM topo_trace_real  
WHERE load_mrid = ${ems_ld_link_id};

-- 根据 psrId 查询对应拓扑关系  
SELECT * FROM topo_trace_real ttr LEFT JOIN ems_ld_link ell ON ttr.load_mrid = ell.id  
WHERE ell.psrid = CONCAT('PD_dkx_', ${psrId});
```
并且可通过`busbar`母线ID在 `topo_trace_real`表中查询其他同母线下的线路和间隔
4. 根据前三个步骤获取的各个ID到对应表查询设备信息，以下ID都是对应表中的`mrid`字段
母线ID `busbar` ：`topo_busbar_section_nj`表
变电站ID `substation`：`topo_substation_nj`表
线路ID `load_mrid`：`topo_load_nj`表
间隔(开关)ID `start_break`：`topo_breaker_nj`表
```SQL
-- 查询母线  
SELECT mrid       AS 母线ID,  
       name       AS 母线名称,  
       substation AS 母线所属变电站ID  
FROM topo_busbar_section_nj  
WHERE mrid = ${topo_trace_real_busbar};  
  
-- 查询变电站  
SELECT mrid AS 变电站ID, name AS 变电站名称  
FROM topo_substation_nj  
WHERE mrid = ${topo_trace_real_substation};  
  
-- 查询线路拓扑信息  
SELECT mrid AS 线路ID, name AS 线路名称, substation AS 变电站ID  
FROM topo_load_nj  
WHERE mrid = ${topo_trace_real_load_mrid};  
  
-- 查询间隔拓扑信息  
SELECT mrid AS 间隔ID, name AS 间隔名称, substation AS 变电站ID  
FROM topo_breaker_nj  
WHERE mrid = ${topo_trace_real_start_break};
```


### 2、变电站查询
通过变电站查询下属所有线路、间隔、母线等内容，由于暂时未知变电站`psrId`和`D5000`体系映射表，所以从名字出发。
1.获取变电站对应的ID
先到`topo_substation_nj`根据名字查询对应的变电站ID
```SQL
-- 根据变电站名字查询ID  
SELECT mrid AS 变电站ID,  
       name AS 变电站名称  
FROM topo_substation_nj  
WHERE name LIKE CONCAT('%', ${变电站名称}, '%');
```
2. 查询变电站下的所有线路和间隔
因为线路和间隔已经有与变电站的直接关系表，因此线路通过`topo_load_nj` 表查询，间隔通过`topo_breaker_nj` 表即可直接查询
```SQL
-- 查询变电站下的线路  
SELECT mrid       AS 线路ID,  
       name       AS 线路名称,  
       substation AS 变电站ID  
FROM topo_load_nj  
WHERE substation = ${变电站ID};

-- 查询变电站下的间隔  
SELECT mrid       AS 间隔ID,  
       name       AS 间隔名称,  
       substation AS 变电站ID  
FROM topo_breaker_nj  
WHERE substation = ${变电站ID};
```
3. 查询变电站下的所有母线
有两种切入方式，可通过线路或者间隔入手。根据线路或间隔的ID到`topo_trace_real` 查询对应的母线ID。
```SQL
-- 根据线路ID查询对应母线ID  
SELECT busbar AS 母线ID, busbar_name AS 母线名称  
FROM topo_trace_real  
WHERE load_mrid = ${线路ID};  
  
-- 根据间隔ID查询对应母线ID  
SELECT busbar AS 母线ID, busbar_name AS 母线名称  
FROM topo_trace_real  
WHERE start_break = ${间隔ID};
```
4. 获取母线信息
最后通过获取到的母线ID到母线表里查询对应的母线信息
```SQL
-- 查询母线信息  
SELECT mrid AS 母线ID, name AS 母线名称  
FROM topo_busbar_section_nj  
WHERE mrid = ${母线ID};
```