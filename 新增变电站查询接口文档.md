# 新增变电站查询接口文档

## 接口概述

新增了一个根据坐标点查询附近变电站的接口，参考了现有的 `queryNearbyDevicesByPoint` 方法的实现模式。

## 接口详情

### 1. 接口路径
```
POST /map/nearbySubstationsByPoint
```

### 2. 请求参数

#### 请求体 (NearbySubstationQueryBo)
```json
{
    "lng": 120.123456,        // 经度，必填，范围：-180.0 到 180.0
    "lat": 30.123456,         // 纬度，必填，范围：-90.0 到 90.0  
    "bufferRadius": 5000.0    // 查询半径（米），必填，最小值：1
}
```

#### 参数说明
- `lng`: 查询点的经度坐标
- `lat`: 查询点的纬度坐标
- `bufferRadius`: 以查询点为中心的搜索半径，单位为米

### 3. 响应结果

#### 成功响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "psrId": "SUBSTATION_001",
            "name": "某某变电站",
            "remainingBayCount": 3,
            "distance": 1250.5,
            "voltageLevel": "110kV",
            "stationCapacity": 50000.0,
            "geoPositon": "13456789.123 4567890.456",
            "lngLat": [120.123456, 30.123456],
            "busbarSwitchVoList": [
                {
                    "switchId": "SWITCH_001",
                    "switchName": "1号备用间隔",
                    "isSpare": true
                }
            ]
        }
    ]
}
```

#### 响应字段说明
- `psrId`: 变电站ID
- `name`: 变电站名称
- `remainingBayCount`: 剩余间隔数量
- `distance`: 距离查询点的距离（米）
- `voltageLevel`: 电压等级
- `stationCapacity`: 变电站容量
- `geoPositon`: 墨卡托坐标
- `lngLat`: 经纬度坐标数组
- `busbarSwitchVoList`: 母线开关信息列表

### 4. 实现特点

1. **参考现有模式**: 参考了 `queryNearbyDevicesByPoint` 方法的实现模式
2. **距离排序**: 返回结果按距离从近到远排序，最近的变电站排在第一位
3. **完整信息**: 包含变电站的基本信息、剩余间隔、距离等完整信息
4. **参数验证**: 对输入参数进行严格的验证
5. **异常处理**: 完善的异常处理和日志记录

### 5. 使用示例

#### cURL 示例
```bash
curl -X POST "http://localhost:8080/map/nearbySubstationsByPoint" \
     -H "Content-Type: application/json" \
     -d '{
         "lng": 120.123456,
         "lat": 30.123456,
         "bufferRadius": 5000.0
     }'
```

#### JavaScript 示例
```javascript
const querySubstations = async () => {
    const response = await fetch('/map/nearbySubstationsByPoint', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            lng: 120.123456,
            lat: 30.123456,
            bufferRadius: 5000.0
        })
    });
    
    const result = await response.json();
    if (result.code === 200) {
        console.log('查询到变电站数量:', result.data.length);
        if (result.data.length > 0) {
            console.log('最近变电站:', result.data[0].name);
            console.log('距离:', result.data[0].distance, '米');
        }
    }
};
```

### 6. 错误处理

#### 参数验证错误
```json
{
    "code": 400,
    "msg": "经度不能为空"
}
```

#### 查询失败
```json
{
    "code": 500,
    "msg": "查询附近变电站失败: 网络连接超时"
}
```

### 7. 技术实现

- **查询方式**: 使用 `AmapSdkCommon.queryPSRByCircle` 进行圆形区域查询
- **设备类型**: 查询 "zf01" 类型的变电站设备
- **距离计算**: 使用墨卡托投影坐标系计算精确距离
- **数据处理**: 包含剩余间隔查询和母线开关信息补充

### 8. 注意事项

1. 查询半径建议不要超过50公里，避免返回数据过多
2. 返回结果已按距离排序，第一个元素为最近的变电站
3. 剩余间隔数量包括备用和预留间隔
4. 坐标系统使用WGS84经纬度坐标
